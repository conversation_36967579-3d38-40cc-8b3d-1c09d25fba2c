import { BaseCommand, flags } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

export default class SnapshotData extends BaseCommand {
  static commandName = 'snapshot:data'

  static description = 'Snapshot data and save to snapshot database'

  static options: CommandOptions = {
    startApp: true,
  }

  @flags.boolean({
    default: false,
    description: 'Bypass time limit check',
  })
  declare force: boolean

  /**
   * List of filters, format: selector:scope:arg1,arg2,arg3
   */
  @flags.array({
    default: [] as string[],
    description: 'List of filters',
    allowEmptyValue: true,
  })
  declare filters: string[]

  async run() {
    this.logger.info('Start snapshot data')
    const { default: Job } = await import('#jobs/snapshot_data_job')
    const job = await this.app.container.make(Job, [{}])
    const filters = this.filters.map((filter) => {
      const [selector, scope, arg] = filter.split(':')
      const args = arg?.split(',') || []
      return { selector, scope, args }
    })

    try {
      await job.handle({ force: this.force, filters })
    } catch (err) {
      this.logger.error(err)
      await job.failed().catch((hookError) => {
        this.logger.error(hookError)
      })
      throw err
    }
  }
}
