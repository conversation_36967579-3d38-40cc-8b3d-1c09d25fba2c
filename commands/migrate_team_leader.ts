import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

export default class UpdateTeamLeaderTeam extends BaseCommand {
  static commandName = 'migrate:team_leader'

  static description = 'Migrate team leader data'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    const { default: Team } = await import('#models/team')
    const teams = await Team.query().preload('leader')
    for (const team of teams) {
      const leader = team.leader
      if (!leader) {
        continue
      }

      leader.teamId = team.id
      await leader.save()
    }
  }
}
