import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import { TransactionClientContract } from '@adonisjs/lucid/types/database'
import { v4 as uuid } from 'uuid'

import type User from '#models/user'

export default class ImportUser extends BaseCommand {
  static commandName = 'import:user'

  static description = 'Import users from config map'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    const db = await this.app.container.make('lucid.db')
    await db.transaction(async (tx) => {
      await this.#run(tx)
    })
  }

  async #run(tx: TransactionClientContract) {
    const { default: User, ToolPermission } = await import('#models/user')

    // const configMapManager = await this.app.container.make('cmap')
    // await configMapManager.load(['import'])

    const configMapRegistry = await this.app.container.make('cmap.registry')
    const userConfigMapCollection = configMapRegistry.get('cmap.user')

    const existUsers = await User.query({ client: tx })
      .whereIn(
        'email',
        userConfigMapCollection.map((user) => user.email)
      )
      .then((us) => new Map(us.map((u) => [u.email, u])))

    const usersToCreate: User[] = []

    for (const userConfigMap of userConfigMapCollection) {
      const existUser = existUsers.get(userConfigMap.email)

      if (existUser) {
        if (existUser.toolPermissions.length === 0) {
          existUser.toolPermissions = userConfigMap.tools.map((t) =>
            new ToolPermission().merge({
              action: t.permission,
              tool: t.name,
            })
          )
        }

        existUser.merge({ fullName: userConfigMap.fullName })
        await existUser.save()

        continue
      }

      const user = new User().merge({
        id: uuid(),
        email: userConfigMap.email,
        fullName: userConfigMap.fullName,
        toolPermissions: userConfigMap.tools.map((t) =>
          new ToolPermission().merge({
            action: t.permission,
            tool: t.name,
          })
        ),
      })

      usersToCreate.push(user)
    }

    await User.createMany(usersToCreate, { client: tx })
  }
}
