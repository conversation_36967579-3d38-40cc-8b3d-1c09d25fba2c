import { BaseCommand, flags } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

import { ConfigMapScope } from '#config/enums'
import { ConfigMapReloader } from '#configmaps/reloader'

export default class SyncGdriveConfigMap extends BaseCommand {
  static commandName = 'sync:gdrive-configmap'

  static description = 'Sync google drive config map to default config map'

  static options: CommandOptions = {
    startApp: true,
    staysAlive: false,
  }

  @flags.string({
    description: 'The driver to sync to',
    allowEmptyValue: true,
    alias: 'd',
  })
  declare driver?: 'redis' | 'local'

  async run() {
    this.logger.info(`Syncing config map to ${this.driver}`)

    const container = this.app.container

    const configMapReloader = await container.make(ConfigMapReloader)

    for (const scope of Object.values(ConfigMapScope)) {
      await configMapReloader.perform(scope, this.driver)
    }
  }
}
