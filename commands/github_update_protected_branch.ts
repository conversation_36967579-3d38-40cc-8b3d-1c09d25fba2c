import { BaseCommand, args } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

export default class GithubUpdateProtectedBranch extends BaseCommand {
  static commandName = 'github:update:pbranch'

  static description = ''

  static options: CommandOptions = {
    startApp: true,
  }

  @args.spread({
    description: 'Repository names',
    allowEmptyValue: true,
  })
  repos: string[] = []

  async run() {
    const githubService = await this.app.container.make('github')
    await githubService.updateProtectedBranch(this.repos)
  }
}
