import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

export default class ImportProjectMembership extends BaseCommand {
  static commandName = 'import:project_membership'

  static description = ''

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    const { default: DashboardRoleMembership } = await import('#models/dashboard_role_membership')
    const { default: DashboardRoleMembershipUser } = await import(
      '#models/dashboard_role_membership_user'
    )

    // const configMapManager = await this.app.container.make('cmap')
    // await configMapManager.load(['import'])

    const configMapRegistry = await this.app.container.make('cmap.registry')
    const projectMembershipConfigMapCollection = configMapRegistry.get('cmap.dash.membership')

    for (const projectMembershipConfigMap of projectMembershipConfigMapCollection) {
      for (const roleConfigMap of projectMembershipConfigMap.roles) {
        if (roleConfigMap.memberEmails.length === 0) {
          continue
        }

        await DashboardRoleMembership.firstOrCreate(
          {
            roleId: roleConfigMap.roleId,
            storeId: projectMembershipConfigMap.storeId,
          },
          {
            users: roleConfigMap.memberEmails.map((email) =>
              new DashboardRoleMembershipUser().merge({ email })
            ),
          }
        )
      }
    }
  }
}
