import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

export default class GithubUpdateArchive extends BaseCommand {
  static commandName = 'github:update:archive'

  static description = ''

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    const githubService = await this.app.container.make('github')
    await githubService.updateArchives()
  }
}
