import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import GameStudio from '#models/game_studio'

export default class GameStudioResolver {
  @inject()
  async index({
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}>) {
    await bouncer.with('dash.game_studio_metric').authorize('index')
    const studios = await GameStudio.query()
      .withScopes((s) => s.default())
      .orderBy('id', 'asc')
    return { collection: studios.map((s) => s.serialize()) }
  }
}
