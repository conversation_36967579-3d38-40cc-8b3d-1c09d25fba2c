import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { ConfigMapBindings, ConfigMapRegistry } from '@munkit/main'
import { z } from 'zod'
import { RedisPublisher } from '@mirai-game-studio/adonis-sdk/pubsub'

import {
  MutationUpdateConfigMapsArgs,
  QueryConfigMapCollectionsArgs,
  QueryConfigMapsArgs,
} from '#graphql/main'
import { ConfigMapScope } from '#config/enums'
import { ConfigMapConsumer } from '#consumers/config_map_consumer'

const indexValidator = vine.compile(
  vine.object({
    ids: vine.array(vine.string()).minLength(1),
  })
)

const showValidator = vine.compile(
  vine.object({
    ids: vine.array(vine.string()).minLength(1),
  })
)

const updateValidator = z.object({
  where: z.object({
    scopes: z.array(z.nativeEnum(ConfigMapScope)).min(1),
  }),
})

export default class ConfigMapResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<QueryConfigMapCollectionsArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('cmap').authorize('index')

    const { ids } = await indexValidator.validate(args)
    return ids.map((id) => ({
      id,
      items: configMapRegistry.get(id as keyof ConfigMapBindings),
    }))
  }

  @inject()
  async show(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<QueryConfigMapsArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('cmap').authorize('index')

    const { ids } = await showValidator.validate(args)
    return ids.map((id) => ({
      id,
      sole: configMapRegistry.get(id as keyof ConfigMapBindings).sole(),
    }))
  }

  @inject()
  async update(
    {
      args,
      context: {
        http: { bouncer, request },
      },
    }: GraphQLHttpContext<MutationUpdateConfigMapsArgs>,
    redisPublisher: RedisPublisher
  ) {
    const { where } = await updateValidator.parseAsync(args)

    for (const scope of where.scopes) {
      await bouncer.with('cmap').authorize('update', scope)
    }

    await redisPublisher.publish(ConfigMapConsumer, {
      data: {
        scopes: where.scopes,
        requestId: request.id()!,
      },
    })
  }
}
