import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { DateTime } from 'luxon'
import { last } from 'lodash-es'
import { Database } from '@adonisjs/lucid/database'

import BudgetRequest from '#models/budget_request'
import {
  FilterOperator,
  MutationCreateBudgetRequestArgs,
  MutationDeleteBudgetRequestsArgs,
  MutationUpdateBudgetRequestsArgs,
  MutationUpdateBudgetRequestsStateArgs,
} from '#graphql/main'
import Workflow from '#models/workflow'
import { createFilterSchema } from '#validators/create_filter_schema'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      assigneeId: vine.string().optional(),
      createdAt: createFilterSchema({
        ops: [FilterOperator.Between],
        valueSchema: vine.date({ formats: ['YYYY-MM-DD'] }),
        required: true,
      }),
      stepId: createFilterSchema({
        ops: [
          FilterOperator.Lte,
          FilterOperator.Lt,
          FilterOperator.Gte,
          FilterOperator.Gt,
          FilterOperator.Eq,
          FilterOperator.Ne,
        ],
        valueSchema: vine.number(),
        required: false,
      }),
      workflowId: createFilterSchema({
        ops: [FilterOperator.In],
        valueSchema: vine.number(),
        required: false,
      }),
      gameId: createFilterSchema({
        ops: [FilterOperator.In],
        valueSchema: vine.string(),
        required: false,
      }),
      lastAction: createFilterSchema({
        ops: [FilterOperator.In],
        valueSchema: vine.string(),
        required: false,
      }),
    }),
    offset: vine.object({
      page: vine.number(),
      perPage: vine.number(),
    }),
  })
)

const storeValidator = vine.compile(
  vine.object({
    form: vine.object({
      workflowId: vine.number(),
      amount: vine.number(),
      gameId: vine.string(),
      expirationDate: vine.date({ formats: ['YYYY-MM-DD'] }),
      description: vine.string().optional(),
    }),
  })
)

const updateValidator = vine.compile(
  vine.object({
    forms: vine.array(
      vine.object({
        id: vine.number(),
        amount: vine.number(),
        expirationDate: vine.date({ formats: ['YYYY-MM-DD'] }),
        description: vine.string().optional(),
      })
    ),
  })
)

const updateStateValidator = vine.compile(
  vine.object({
    forms: vine.array(
      vine.object({
        id: vine.number(),
        stepId: vine.number(),
        lastAction: vine.string().optional(),
      })
    ),
  })
)

const deleteValidator = vine.compile(
  vine.object({
    where: vine.object({
      ids: vine.array(vine.number()),
    }),
  })
)

export default class BudgetRequestResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<{}>,
    db: Database
  ) {
    await bouncer.with('dash.budget_request').authorize('index')
    const {
      where: { assigneeId, ...where },
      offset,
    } = await indexValidator.validate(args)
    const { page, perPage } = offset
    // Base query
    const query = BudgetRequest.query()
      .select(`${BudgetRequest.table}.*`)
      .innerJoin(
        Workflow.table,
        Workflow.columnName('id', Workflow.table),
        BudgetRequest.columnName('workflowId', BudgetRequest.table)
      )
      .where((qAnd) => {
        qAnd
          .orWhere((q) => {
            q.where('createdById', auth.user!.id)
          })
          .orWhere((q) => {
            if (assigneeId) {
              q.whereRaw(`step->>'assignee_id' = ?`, [assigneeId])
            }
          })
          .orWhere((q) => {
            q.whereExists(
              db.raw(
                `SELECT 1 FROM jsonb_array_elements(${Workflow.quote('steps')}) AS s(step) WHERE s.step->>'assignee_id' = ?`,
                [auth.user!.id]
              )
            )
          })
      })
      .withScopes((s) =>
        s.filter(where as any, {
          createdAt: BudgetRequest.columnName('createdAt', BudgetRequest.table),
        })
      )
      .whereNull('deletedAt')
      .orderBy(BudgetRequest.columnName('createdAt', BudgetRequest.table), 'desc')

    // Apply pagination
    const paginator = await query.paginate(page, perPage)
    return {
      collection: paginator.map((r) => r.serialize()),
      pageInfo: paginator.getMeta(),
    }
  }

  @inject()
  async game({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, BudgetRequest>) {
    return dataloaders.game.load(parent.gameId)
  }

  @inject()
  async assignee({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, BudgetRequest>) {
    return dataloaders.user.load(parent.step.assigneeId)
  }

  async createdBy({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, BudgetRequest>) {
    return dataloaders.user.load(parent.createdById)
  }

  async workflow({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, BudgetRequest>) {
    return dataloaders.workflow.load(parent.workflowId)
  }

  @inject()
  async store({
    args,
    context: {
      http: { bouncer, auth },
    },
  }: GraphQLHttpContext<MutationCreateBudgetRequestArgs>) {
    const { form } = await storeValidator.validate(args)
    const workflow = await Workflow.findOrFail(form.workflowId)
    await bouncer.with('dash.budget_request').authorize('store', workflow)
    const budgetRequest = await BudgetRequest.create({
      amount: form.amount,
      expirationDate: DateTime.fromJSDate(form.expirationDate),
      gameId: form.gameId,
      stepId: 0,
      step: workflow.steps[0],
      workflowId: workflow.id,
      createdById: auth.user!.id,
      description: form.description,
    })
    return budgetRequest.serialize()
  }

  @inject()
  async update({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationUpdateBudgetRequestsArgs>) {
    const { forms } = await updateValidator.validate(args)
    const ids = forms.map((f) => f.id)
    const budgetRequests = await BudgetRequest.query().whereIn('id', ids).preload('workflow')
    await BudgetRequest.transaction(async (trx) => {
      for (const request of budgetRequests) {
        await bouncer.with('dash.budget_request').authorize('update', request)

        const form = forms.find((f) => f.id === request.id)!
        request.merge({
          amount: form.amount,
          expirationDate: DateTime.fromJSDate(form.expirationDate),
          description: form.description,
        })
        await request.useTransaction(trx).save()
      }
    })
    return budgetRequests.map((b) => b.serialize())
  }

  @inject()
  async updateState({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationUpdateBudgetRequestsStateArgs>) {
    const { forms } = await updateStateValidator.validate(args)
    const ids = forms.map((f) => f.id)
    const budgetRequests = await BudgetRequest.query().whereIn('id', ids).preload('workflow')
    await BudgetRequest.transaction(async (trx) => {
      for (const request of budgetRequests) {
        await bouncer.with('dash.budget_request').authorize('updateState', request)

        const form = forms.find((f) => f.id === request.id)!
        const stepId = form.stepId >= request.workflow.steps.length ? -1 : form.stepId
        request.merge({
          stepId,
          step: request.workflow.steps[stepId] || last(request.workflow.steps),
          lastAction: form.lastAction,
        })
        await request.useTransaction(trx).save()
      }
    })
    return budgetRequests.map((b) => b.serialize())
  }

  @inject()
  async destroy({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationDeleteBudgetRequestsArgs>) {
    const { where } = await deleteValidator.validate(args)
    const requests = await BudgetRequest.query().whereIn('id', where.ids)
    for (const budgetRequest of requests) {
      await bouncer.with('dash.budget_request').authorize('delete', budgetRequest)
    }
    await BudgetRequest.query().whereIn('id', where.ids).update({ deletedAt: DateTime.now() })
    return
  }
}
