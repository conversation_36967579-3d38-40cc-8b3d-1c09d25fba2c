import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { ConfigMapRegistry } from '@munkit/main'
import pLimit from 'p-limit'

import { QueryAccessControlArgs, MutationUpdateGameRolesArgs } from '#graphql/main'
import ACL from '#models/acl'

const showValidator = vine.compile(
  vine.object({
    where: vine.object({ subject: vine.string() }),
  })
)

const updateValidator = vine.compile(
  vine.object({
    where: vine.object({ subject: vine.string() }),
    form: vine.object({
      roles: vine.array(
        vine.object({
          id: vine.string(),
          permits: vine.array(vine.string()).optional(),
        })
      ),
    }),
  })
)

const updateLimit = pLimit(3)

export default class AccessControlResolver {
  @inject()
  async show(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<QueryAccessControlArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('AclPolicy').authorize('show')

    const { where } = await showValidator.validate(args)
    const accessControls = await ACL.query().where('subject', where.subject)
    const roleConfigMap = configMapRegistry.get('cmap.dash.role')

    return {
      roles: roleConfigMap.map((role) => {
        const acl = accessControls.find((a) => a.roleId === role.id)
        return (
          acl || new ACL().merge({ roleId: role.id, permits: [], subject: where.subject })
        ).serialize()
      }),
    }
  }

  @inject()
  async update(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<MutationUpdateGameRolesArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('AclPolicy').authorize('update')

    const { where, form } = await updateValidator.validate(args)
    const { subject } = where
    const { roles } = form

    // Cập nhật các ACL
    await Promise.all(
      roles.map((role) =>
        updateLimit(async () => {
          await ACL.updateOrCreate({ roleId: role.id, subject }, { permits: role.permits || [] })
        })
      )
    )

    // Lấy lại dữ liệu đã cập nhật
    const accessControls = await ACL.query().where('subject', subject)
    const roleConfigMap = configMapRegistry.get('cmap.dash.role')

    // Trả về dữ liệu đã cập nhật
    return {
      roles: roleConfigMap.map((role: any) => {
        const acl = accessControls.find((a) => a.roleId === role.id)
        return (acl || new ACL().merge({ roleId: role.id, permits: [], subject })).serialize()
      }),
    }
  }
}
