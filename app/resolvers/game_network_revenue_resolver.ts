import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { AggregateGameNetworkRevenueWhere, QueryGameNetworkRevenuesArgs } from 'graphql/main.js'
import vine from '@vinejs/vine'
import { first, last } from 'lodash-es'

import Game from '#models/game'
import GameNetworkRevenue from '#models/game_network_revenue'
import { orderDirectionRule } from '#validators/rules/order_direction_rule'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
    }),
    offset: vine.object({
      perPage: vine.number(),
      page: vine.number(),
    }),
    order: vine.object({
      direction: vine.string().use(orderDirectionRule()),
    }),
  })
)

const aggregateValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      dateTo: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
    }),
  })
)

export default class GameNetworkRevenueResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
      dataloaders,
    },
  }: GraphQLHttpContext<QueryGameNetworkRevenuesArgs>) {
    const {
      where: { gameId, dateFrom, dateTo },
      offset: { page, perPage },
      order: { direction },
    } = await indexValidator.validate(args)
    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.revenue').authorize('index', game)

    const dates = await GameNetworkRevenue.query()
      .withScopes((s) =>
        s.default({
          gameId: game.id,
          from: dateFrom,
          to: dateTo,
        })
      )
      .select(GameNetworkRevenue.columnName('date', GameNetworkRevenue.table))
      .groupBy(GameNetworkRevenue.columnName('date', GameNetworkRevenue.table))
      .orderBy(GameNetworkRevenue.columnName('date', GameNetworkRevenue.table), 'desc')
      .orderBy('date', direction as any)
      .paginate(page, perPage)

    if (dates.length === 0) {
      return {
        collection: [],
        pageInfo: dates.getMeta(),
      }
    }

    const [from, to] = [first(dates)!.date, last(dates)!.date].sort()

    const collection = await GameNetworkRevenue.query()
      .withScopes((s) => {
        s.default({
          gameId: game.id,
          from: from.toJSDate(),
          to: to.toJSDate(),
        })

        s.selectMediationRevenue()
      })
      .select(`${GameNetworkRevenue.table}.*`)

    dataloaders.gameMetric.gameId = game.id

    return { collection: collection.map((e) => e.serialize()), pageInfo: dates.getMeta() }
  }

  @inject()
  async aggregate({
    args,
    context: {
      http: { bouncer },
      dataloaders,
    },
  }: GraphQLHttpContext<AggregateGameNetworkRevenueWhere>) {
    const { where } = await aggregateValidator.validate(args)
    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.revenue').authorize('index', game)

    const revs = await GameNetworkRevenue.query()
      .from(
        GameNetworkRevenue.query()
          .withScopes((s) => {
            s.default({
              gameId: game.id,
              from: where.dateFrom,
              to: where.dateTo,
            })

            s.selectMediationRevenue()
          })
          .select(`${GameNetworkRevenue.table}.*`)
          .as(GameNetworkRevenue.table)
      )
      .groupBy('networkId')
      .select('networkId')
      .sum('mediation_revenue', 'mediation_revenue')
      .sum(GameNetworkRevenue.columnName('revenue'), GameNetworkRevenue.columnName('revenue'))

    dataloaders.gameMetric.gameId = game.id

    return {
      collection: revs.map((r) => r.serialize()),
    }
  }

  @inject()
  async network({ context: { dataloaders }, parent }: GraphQLHttpContext<{}, GameNetworkRevenue>) {
    return dataloaders.adNetwork.load(parent.networkId)
  }

  @inject()
  async metric({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, GameNetworkRevenue>) {
    return dataloaders.gameMetric.load(parent.date)
  }
}
