import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { z } from 'zod'
import { Database } from '@adonisjs/lucid/database'

import { MutationUpdateTeamArgs, QueryTeamArgs } from '#graphql/main'
import Team from '#models/team'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import User from '#models/user'
import { UserService } from '#services/user_service'
import { update } from '#utils/pure'

const deleteValidator = z.object({
  where: z.object({
    id: z.number(),
  }),
})

const updateValidator = z.object({
  where: z.object({
    id: z.number(),
  }),
  form: z.object({
    name: z.string(),
    memberEmails: z.array(z.string().email()),
    leaderEmail: z.string().email().optional(),
  }),
})

const storeValidator = z.object({
  form: z.object({
    name: z.string(),
    roleId: z.string(),
    leaderEmail: z.string().email().optional(),
    memberEmails: z.array(z.string().email()),
  }),
})

export default class TeamResolver {
  @inject()
  async show({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryTeamArgs>) {
    const team = await Team.query().where('id', args.id).firstOrFail()
    await bouncer.with('TeamPolicy').authorize('show', team)
    return team
  }

  @inject()
  async index({
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext) {
    await bouncer.with('TeamPolicy').authorize('index')

    const teamsQuery = Team.query()
    await bouncer.with('TeamPolicy').execute('scope', teamsQuery)
    const teams = await teamsQuery.orderBy('id', 'desc')

    return teams
  }

  @inject()
  async update(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<MutationUpdateTeamArgs>,
    userService: UserService,
    db: Database
  ) {
    await bouncer.with('TeamPolicy').authorize('update')

    const validatedArgs = await updateValidator.parseAsync(args)
    const { where, form } = validatedArgs
    const { name, memberEmails, leaderEmail } = form
    const users = await userService.findManyByEmails(memberEmails)
    const leader = users.find((u) => u.email === leaderEmail)

    await db.transaction(async (tx) => {
      const team = await Team.findOrFail(where.id, {
        client: tx,
      })
      team.merge({ name, leaderId: leader?.id || null })
      await team.save()

      await team.load('members')

      const { removes, adds } = update(team.members, users, (u) => u.id)

      for (const userRemoved of removes) {
        const memberships = await DashboardRoleMembership.query()
          .withScopes((s) => s.withUsers())
          .where('user', userRemoved.email)

        for (const membership of memberships) {
          membership.users = membership.users.filter((u) => u.email !== userRemoved.email)
          await membership.save()
        }
      }

      await User.query({ client: tx })
        .whereIn(
          'id',
          removes.map((u) => u.id)
        )
        .update({ teamId: null })

      await User.query({ client: tx })
        .whereIn(
          'id',
          adds.map((u) => u.id)
        )
        .update({ teamId: team.id })
    })

    // Trả về team đã cập nhật
    return await this.#getTeam(where.id)
  }

  @inject()
  async destroy({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<any>) {
    await bouncer.with('TeamPolicy').authorize('destroy')

    const { where } = await deleteValidator.parseAsync(args)
    const team = await Team.findOrFail(where.id)

    // Kiểm tra xem team có members đang phụ trách games không
    const memberships = await DashboardRoleMembership.query().withScopes((s) =>
      s.selectManyGameInChargedByTeam(team)
    )

    if (memberships.length > 0) {
      throw new Error('Team cannot be destroyed because it has members in charge of games.')
    }

    await team.delete()
  }

  #getTeam(id: number) {
    return Team.query().where('id', id).firstOrFail()
  }

  @inject()
  async leader({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, Team>) {
    return dataloaders.user.load(parent.leaderId || '')
  }

  @inject()
  async members({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, Team>) {
    await parent.load('members')
    return parent.members.map((member) => dataloaders.user.load(member.id))
  }

  @inject()
  async store(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<any>,
    userService: UserService,
    db: Database
  ) {
    await bouncer.with('TeamPolicy').authorize('store')

    const validatedArgs = await storeValidator.parseAsync(args)
    const { name, roleId, memberEmails, leaderEmail } = validatedArgs.form

    const users = await userService.findManyByEmails(memberEmails)
    const leader = users.find((u) => u.email === leaderEmail)

    const teamId = await db.transaction(async (tx) => {
      const team = await Team.create(
        {
          name,
          roleId,
          leaderId: leader?.id || null,
        },
        {
          client: tx,
        }
      )

      await User.query({ client: tx })
        .whereIn(
          'id',
          users.map((u) => u.id)
        )
        .update({ teamId: team.id })

      return team.id
    })
    return await this.#getTeam(teamId)
  }
}
