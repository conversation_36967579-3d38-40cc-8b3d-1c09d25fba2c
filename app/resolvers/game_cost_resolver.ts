import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { Database } from '@adonisjs/lucid/database'
import z from 'zod'
import { isNil } from 'lodash-es'
import { DateTime } from 'luxon'

import Game from '#models/game'
import GameSpend from '#models/game_spend'
import AdNetwork from '#models/ad_agency'
import GameMetric from '#models/game_metric'
import { MutationUpdateGameCostArgs, QueryGameCostsArgs } from '#graphql/main'
import { GameSpendType } from '#config/enums'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({ formats: ['YYYY-MM-DD'] }),
      dateTo: vine.date({ formats: ['YYYY-MM-DD'] }),
    }),
    offset: vine
      .object({
        perPage: vine.number().optional(),
        page: vine.number().optional(),
      })
      .optional(),
  })
)

const updateValidator = z.object({
  where: z.object({
    networkId: z.coerce.number(),
    date: z.coerce.date(),
    gameId: z.string(),
  }),
  form: z.object({
    preTaxAmount: z.coerce.number().nullable(),
  }),
})

export default class GameCostResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<QueryGameCostsArgs>,
    db: Database
  ) {
    const {
      where: { gameId, dateFrom, dateTo },
      offset = {},
    } = await indexValidator.validate(args)

    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.cost').authorize('index', game)
    const direction = 'desc'
    const networks = await AdNetwork.query()
      .withScopes((s) => s.default())
      .select('id')
    const networkIds = networks.map((n) => n.id)
    const page = offset.page || 1
    const perPage = offset.perPage || 50
    const dates = await GameSpend.query()
      .select('date')
      .where('storeId', game.storeId)
      .whereIn('networkId', networkIds)
      .where((q) => {
        if (dateFrom) {
          q.where('date', '>=', dateFrom)
        }

        if (dateTo) {
          q.where('date', '<=', dateTo)
        }
      })
      .groupBy('date')
      .orderBy('date', direction as any)
      .paginate(page!, perPage!)

    const spends = await GameSpend.query()
      .withScopes((q) => q.withOverride())
      .where(GameSpend.columnName('storeId', GameSpend.table), game.storeId)
      .whereIn(GameSpend.columnName('networkId', GameSpend.table), networkIds)
      .whereIn(
        GameSpend.columnName('date', GameSpend.table),
        dates.map((d) => d.date.toJSDate())
      )
      .leftJoin(GameMetric.table, (q) => {
        q.on(
          GameMetric.columnName('storeId', GameMetric.table),
          '=',
          GameSpend.columnName('storeId', GameSpend.table)
        ).andOn(
          GameMetric.columnName('date', GameMetric.table),
          '=',
          GameSpend.columnName('date', GameSpend.table)
        )
      })
      .orderBy('date', direction as any)
      .select(
        db.raw(
          `CASE WHEN ${GameMetric.columnName('paidInstalls')} IS NULL THEN 0 ELSE ${GameMetric.columnName('paidInstalls')} * ?::float END as mmp`,
          [0.0012]
        )
      )
      .select(`${GameSpend.table}.*`)
    const aggregation = await GameSpend.query()
      .from(
        GameSpend.query()
          .withScopes((q) => q.withAggregation())
          .where('storeId', game.storeId)
          .whereIn('networkId', networkIds)
          .as(GameSpend.table)
      )
      .sum(GameSpend.columnName('totalAmount'), GameSpend.columnName('totalAmount'))
      .sum(GameSpend.columnName('preTaxAmount'), GameSpend.columnName('preTaxAmount'))
      .sum('mmp', 'mmpAmount')
      .firstOrFail()

    return {
      collection: spends.map((spend) => spend.serialize()),
      meta: {
        aggregation: {
          mmpAmount: aggregation.$extras.mmpAmount || 0,
          totalAmount: aggregation.totalAmount || 0,
          preTaxAmount: aggregation.preTaxAmount || 0,
        },
        pageInfo: dates.getMeta(),
      },
    }
  }

  @inject()
  async update({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationUpdateGameCostArgs>) {
    const {
      where: { networkId, gameId, date },
      form,
    } = await updateValidator.parseAsync(args)

    const game = await Game.findOrFail(gameId)

    await bouncer.with('dash.game.cost').authorize('update', game, networkId)

    if (isNil(form.preTaxAmount)) {
      await GameSpend.query()
        .where('networkId', networkId)
        .where('storeId', gameId)
        .where('date', date)
        .delete()
      return {}
    }

    const cost = await GameSpend.updateOrCreate(
      {
        networkId,
        storeId: gameId,
        date: DateTime.fromJSDate(date),
      },
      {
        preTaxAmount: form.preTaxAmount!,
        type: GameSpendType.Manual,
        tax: 0,
      }
    )

    return cost.serialize()
  }

  @inject()
  async network({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, GameSpend>) {
    return dataloaders.adAgency.load(parent.networkId)
  }
}
