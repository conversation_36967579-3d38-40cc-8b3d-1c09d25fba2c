import { inject } from '@adonisjs/core'
import { present } from '@mirai-game-studio/adonis-sdk/presenter'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import jwt from 'jsonwebtoken'
import config from 'config'
import { ConfigMapRegistry } from '@munkit/main'
import { z } from 'zod'
import parseDuration from 'parse-duration'
import { DateTime } from 'luxon'

import { QueryMetabaseChartArgs } from '#graphql/main'
import { MetabaseChartPresenter } from '#presenters/metabase_chart_presenter'

const showValidator = z.object({
  where: z.object({
    id: z.string(),
    params: z.array(
      z.object({
        name: z.string(),
        value: z.any(),
      })
    ),
  }),
})

export default class MetabaseChartResolver {
  @inject()
  async show(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<QueryMetabaseChartArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('metabase.chart').authorize('show')
    const { where } = await showValidator.parseAsync(args)

    const { baseUrl, secretKey, expiresIn } = config.get('metabase')
    const metabaseChartConfigMap = configMapRegistry.get('cmap.dash.metabasechart').get(where.id)

    const payload = {
      resource: { dashboard: metabaseChartConfigMap.no },
      params: Object.fromEntries(
        where.params
          .filter((param) => metabaseChartConfigMap.params.tryGet(param.name))
          .map((param) => [param.name, param.value])
      ),
      exp: DateTime.now()
        .plus({ seconds: parseDuration(expiresIn, 'seconds')! })
        .toUnixInteger(),
    }
    const token = jwt.sign(payload, secretKey)

    const iframeUrl = [
      `${baseUrl}/embed/dashboard/${token}`,
      metabaseChartConfigMap.featureFlags,
    ].join('#')

    return present(
      new MetabaseChartPresenter().merge({
        id: metabaseChartConfigMap.no,
        url: iframeUrl,
      })
    )
  }
}
