import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'

import { V2QueryAggregateGameMetricsArgs, V2QueryGameMetricsArgs } from '#graphql/main'
import GameMetric from '#models/v2/game_metric'
import GameMetricPolicy from '#policies/dashboard/game/game_metric_policy'
import Game from '#models/game'
import { dataSourceRule } from '#validators/rules/data_source_rule'

const aggregateValidator = vine.compile(
  vine.object({
    id: vine.string(),
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      dateTo: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
    }),
    source: vine.any().use(dataSourceRule()).optional(),
  })
)

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateTo: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      dateFrom: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
    }),
    offset: vine.object({
      page: vine.number(),
      perPage: vine.number(),
    }),
    source: vine.any().use(dataSourceRule()).optional(),
  })
)

export default class GameMetricResolver {
  @inject()
  async index(
    { args, context }: GraphQLHttpContext<V2QueryGameMetricsArgs>,
    gameMetricPolicy: GameMetricPolicy
  ) {
    const {
      http: { auth, bouncer },
    } = context
    const { where, offset, source } = await indexValidator.validate(args)

    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.metric_v2').authorize('index', game, source)
    context.source = source

    const gameMetricsQuery = GameMetric.query({
      connection: GameMetric.connectionInContext(source),
    })
      .withScopes((s) => s.default(game.id))
      .where('gameId', where.gameId)
      .where((q) => {
        if (where.dateFrom) {
          q.where('date', '>=', where.dateFrom)
        }
        if (where.dateTo) {
          q.where('date', '<=', where.dateTo)
        }
      })
      .orderBy('date', 'desc')

    const gameMetrics = await gameMetricsQuery.paginate(offset.page, offset.perPage)

    const cherryPick = await gameMetricPolicy.cherryPickV2(auth.user!, game, source)

    return {
      collection: gameMetrics.map((m) => m.serialize(cherryPick)),
      pageInfo: gameMetrics.getMeta(),
    }
  }

  @inject()
  async adPerformances({
    context: {
      dataloaders,
      http: { auth },
      source,
    },
    parent,
  }: GraphQLHttpContext<{}, GameMetric>) {
    return dataloaders.adPerformance.load({
      user: auth.user!,
      gameId: parent.gameId,
      date: parent.date,
      isAggregation: parent.isAggregation,
      source,
    })
  }

  @inject()
  async aggregate(
    { args, context }: GraphQLHttpContext<V2QueryAggregateGameMetricsArgs>,
    gameMetricPolicy: GameMetricPolicy
  ) {
    const {
      http: { auth, bouncer },
    } = context
    const { where, source, id } = await aggregateValidator.validate(args)

    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.metric_v2').authorize('index', game, source)

    context.source = source

    const aggregationQuery = GameMetric.query({
      connection: GameMetric.connectionInContext(source),
    })
      .withScopes((s) => s.default(game.id))
      .where('gameId', where.gameId)
      .where((q) => {
        if (where.dateFrom) {
          q.where('date', '>=', where.dateFrom)
        }
        if (where.dateTo) {
          q.where('date', '<=', where.dateTo)
        }
      })
      .sum(GameMetric.columnName('paidInstalls'), GameMetric.columnName('paidInstalls'))
      .sum(GameMetric.columnName('organicInstalls'), GameMetric.columnName('organicInstalls'))
      .sum(GameMetric.columnName('totalInstalls'), GameMetric.columnName('totalInstalls'))
      .sum(GameMetric.columnName('cost'), GameMetric.columnName('cost'))
      .sum(GameMetric.columnName('revenue'), GameMetric.columnName('revenue'))
      .sum(GameMetric.columnName('profit'), GameMetric.columnName('profit'))
      .sum(GameMetric.columnName('dailyActiveUsers'), GameMetric.columnName('dailyActiveUsers'))
      .avg(GameMetric.columnName('retentionRateDay1'), GameMetric.columnName('retentionRateDay1'))
      .avg(GameMetric.columnName('retentionRateDay3'), GameMetric.columnName('retentionRateDay3'))
      .avg(GameMetric.columnName('retentionRateDay7'), GameMetric.columnName('retentionRateDay7'))
      .avg(GameMetric.columnName('sessions'), GameMetric.columnName('sessions'))
      .avg(GameMetric.columnName('playtime'), GameMetric.columnName('playtime'))

    const aggregation = await aggregationQuery.firstOrFail()

    const cherryPick = await gameMetricPolicy.cherryPickV2(auth.user!, game, source)

    aggregation.id = id
    return aggregation.merge({ isAggregation: true, gameId: where.gameId }).serialize(cherryPick)
  }
}
