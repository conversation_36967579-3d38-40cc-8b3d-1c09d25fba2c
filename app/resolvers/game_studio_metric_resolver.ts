import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'

import { QueryGameStudioMetricsArgs } from '#graphql/main'
import Game from '#models/game'
import GameMetric from '#models/v2/game_metric'
import GameStudio from '#models/game_studio'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      dateFrom: vine.date({ formats: ['YYYY-MM-DD'] }),
      dateTo: vine.date({ formats: ['YYYY-MM-DD'] }),
      studioId: vine.number(),
    }),
  })
)

export default class GameStudioMetricResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameStudioMetricsArgs>) {
    await bouncer.with('dash.game_studio_metric').authorize('index')
    const { where } = await indexValidator.validate(args)

    const gameIds = await Game.query()
      .innerJoin(
        GameStudio.table,
        GameStudio.columnName('id', GameStudio.table),
        Game.columnName('studioId', Game.table)
      )
      .select(`${Game.table}.*`)
      .where('studioId', where.studioId)
      .where(GameStudio.columnName('isVisible', GameStudio.table), true)
      .select('storeId')
      .then((rs) => rs.map((r) => r.id))

    const metrics = await GameMetric.query()
      .withScopes((s) => s.default(...gameIds))
      .whereBetween('date', [where.dateFrom, where.dateTo])
      .whereIn('gameId', gameIds)
      .groupBy('gameId')
      .select('gameId')
      .sum(GameMetric.columnName('totalInstalls'), GameMetric.columnName('totalInstalls'))
      .sum(GameMetric.columnName('mmpCostAmount'), GameMetric.columnName('mmpCostAmount'))
      .sum(GameMetric.columnName('totalAgencyCost'), GameMetric.columnName('totalAgencyCost'))
      .sum(GameMetric.columnName('revenue'), GameMetric.columnName('revenue'))
      .sum(GameMetric.columnName('profit'), GameMetric.columnName('profit'))

    return { collection: metrics.map((m) => m.serialize()) }
  }

  @inject()
  async game({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, GameMetric>) {
    return dataloaders.game.load(parent.gameId)
  }
}
