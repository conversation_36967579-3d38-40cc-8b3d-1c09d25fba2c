import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'

import { QueryGameCreativeMetricsArgs } from '#graphql/main'
import Game from '#models/game'
import GameCreativeMetric from '#models/game_creative_metric'
import { orderDirectionRule } from '#validators/rules/order_direction_rule'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      dateFrom: vine.date({ formats: ['YYYY-MM-DD'] }),
      dateTo: vine.date({ formats: ['YYYY-MM-DD'] }),
      gameId: vine.string(),
    }),
    offset: vine.object({
      perPage: vine.number(),
      page: vine.number(),
    }),
    order: vine.object({
      direction: vine.string().use(orderDirectionRule()),
    }),
  })
)

export default class GameCreativeMetricResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameCreativeMetricsArgs>) {
    const { where, offset, order } = await indexValidator.validate(args)
    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.creative_metric').authorize('index', game)

    const metrics = await GameCreativeMetric.query()
      .withScopes((s) => s.default(where.gameId))
      .where('gameId', where.gameId)
      .where((q) => {
        if (where.dateTo) {
          q.where('date', '<=', where.dateTo)
        }

        if (where.dateFrom) {
          q.where('date', '>=', where.dateFrom)
        }
      })
      .orderBy('date', order.direction as any)
      .paginate(offset.page, offset.perPage)

    return { collection: metrics.map((m) => m.serialize()), pageInfo: metrics.getMeta() }
  }

  @inject()
  async agency({ context: { dataloaders }, parent }: GraphQLHttpContext<{}, GameCreativeMetric>) {
    return dataloaders.adAgency.load(parent.agencyId)
  }
}
