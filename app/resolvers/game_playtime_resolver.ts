import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { BaseModel, column } from '@adonisjs/lucid/orm'

import {
  PlaytimeAggregation,
  QueryAggregatePlaytimeArgs,
  QueryGamePlaytimeVersionsArgs,
} from '#graphql/main'
import Game from '#models/game'
import GamePlaytime from '#models/game_playtime'
import { safeDivide } from '#utils/math'
import { compareSemver } from '#utils/semver'

const aggregateValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      activeDateFrom: vine.date({ formats: ['YYYY-MM-DD'] }),
      activeDateTo: vine.date({ formats: ['YYYY-MM-DD'] }),
      installDateFrom: vine.date({ formats: ['YYYY-MM-DD'] }).optional(),
      installDateTo: vine.date({ formats: ['YYYY-MM-DD'] }).optional(),
      versions: vine.array(vine.string()),
    }),
  })
)

export default class GamePlaytimeResolver {
  @inject()
  async versions({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGamePlaytimeVersionsArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const versions = await GamePlaytime.query()
      .where('gameId', game.id)
      .select('version')
      .where('isValidSemver', true)
      .distinctOn('version')
      .then((rs) => rs.map((r) => r.version).sort((a, b) => 0 - compareSemver(a, b)))

    return {
      collection: versions,
    }
  }

  @inject()
  async aggregate({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryAggregatePlaytimeArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const {
      where: { activeDateFrom, activeDateTo, gameId, installDateFrom, installDateTo, versions },
    } = await aggregateValidator.validate(args)

    const aggregationQuery = GamePlaytime.query()
      .where('gameId', gameId)
      .whereBetween('activeDate', [activeDateFrom, activeDateTo])
      .where((q) => {
        if (installDateFrom) {
          q.where('installDate', '>=', installDateFrom)
        }

        if (installDateTo) {
          q.where('installDate', '<=', installDateTo)
        }
      })

    const aggregation = await GamePlaytime.query()
      .from(
        aggregationQuery
          .clone()
          .groupBy('version', 'installDate')
          .select('version')
          .where((q) => {
            if (versions.length > 0) {
              q.whereIn('version', versions)
            }
          })
          .sum('engagementDurationSec', GamePlaytime.columnName('engagementDurationSec'))
          .sum('engagementSessionCount', GamePlaytime.columnName('engagementSessionCount'))
          .max('activeUserCount', GamePlaytime.columnName('activeUserCount'))
          .as('cohort_playtime')
      )
      .sum('engagementDurationSec', GamePlaytime.columnName('engagementDurationSec'))
      .sum('engagementSessionCount', GamePlaytime.columnName('engagementSessionCount'))
      .sum('activeUserCount', GamePlaytime.columnName('activeUserCount'))
      .firstOrFail()

    console.log(aggregation.serialize())

    return new PlaytimeAggregationPresenter().merge({
      playtimeSec: safeDivide(
        aggregation.engagementDurationSec || 0,
        aggregation.activeUserCount || 0
      ),
      engagementSessionCount: safeDivide(
        aggregation.engagementSessionCount || 0,
        aggregation.activeUserCount || 0
      ),
    })
  }
}

class PlaytimeAggregationPresenter extends BaseModel implements PlaytimeAggregation {
  @column()
  declare playtimeSec: number

  @column()
  declare engagementSessionCount: number
}
