import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { inject } from '@adonisjs/core'
import vine from '@vinejs/vine'

import { QueryGameRetentionRatesArgs, QueryGameRetentionRateVersionsArgs } from '#graphql/main'
import Game from '#models/game'
import { compareSemver } from '#utils/semver'
import GameRetentionRate from '#models/game_retention_rate'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      versions: vine.array(vine.string()),
    }),
  })
)

const versionsValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
    }),
  })
)

export default class GameRetentionRateResolver {
  @inject()
  async versions({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameRetentionRateVersionsArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const { where } = await versionsValidator.validate(args)

    const versions = await GameRetentionRate.query()
      .where('gameId', game.id)
      .whereBetween('date', [where.dateFrom, where.dateTo])
      .where('isValidSemver', true)
      .select('version')
      .distinctOn('version')
      .then((rs) => rs.map((r) => r.version).sort((a, b) => 0 - compareSemver(a, b)))

    return {
      collection: versions,
    }
  }

  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameRetentionRatesArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const { where } = await indexValidator.validate(args)

    const collection = await GameRetentionRate.query()
      .where('gameId', game.id)
      .whereBetween('date', [where.dateFrom, where.dateTo])
      .where('isValidSemver', true)
      .whereIn('version', where.versions)
      .groupBy('gameId', 'date')
      .select('date', 'gameId')
      .sum(GameRetentionRate.columnName('newUsers'), GameRetentionRate.columnName('newUsers'))
      .sum(GameRetentionRate.columnName('usersDay1'), GameRetentionRate.columnName('usersDay1'))
      .sum(GameRetentionRate.columnName('usersDay2'), GameRetentionRate.columnName('usersDay2'))
      .sum(GameRetentionRate.columnName('usersDay3'), GameRetentionRate.columnName('usersDay3'))
      .sum(GameRetentionRate.columnName('usersDay4'), GameRetentionRate.columnName('usersDay4'))
      .sum(GameRetentionRate.columnName('usersDay5'), GameRetentionRate.columnName('usersDay5'))
      .sum(GameRetentionRate.columnName('usersDay6'), GameRetentionRate.columnName('usersDay6'))
      .sum(GameRetentionRate.columnName('usersDay7'), GameRetentionRate.columnName('usersDay7'))

    return { collection }
  }
}
