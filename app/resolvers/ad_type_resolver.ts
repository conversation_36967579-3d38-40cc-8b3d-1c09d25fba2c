import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import AdType from '#models/ad_type'

export default class AdTypeResolver {
  @inject()
  async index({
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext) {
    await bouncer.with('DashboardGamePolicy').authorize('index')
    const adTypes = await AdType.all()

    return { collection: adTypes }
  }
}
