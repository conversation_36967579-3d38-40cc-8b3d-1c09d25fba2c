import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import DashboardNotification from '#models/dashboard_notification'

export default class ConfigMapResolver {
  @inject()
  async index({
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext) {
    await bouncer.with('dash.notification').authorize('index')
    const notificationsQuery = DashboardNotification.query()
    await bouncer.with('dash.notification').authorize('scope', notificationsQuery)
    const notifications = await notificationsQuery.orderBy('id', 'desc')

    return {
      collection: notifications.map((n) => n.serialize()),
    }
  }
}
