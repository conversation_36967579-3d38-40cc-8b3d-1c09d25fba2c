import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { ConfigMapRegistry } from '@munkit/main'
import { DateTime } from 'luxon'
import { uniq } from 'lodash-es'

import Game from '#models/game'
import { FilterOperator, QueryFirebaseMetricsArgs } from '#graphql/main'
import FirebaseMetric from '#models/firebase_metric'
import FirebaseVersionVariant from '#models/firebase_version_variant'
import ViewPreset from '#models/view_preset'
import { createFilterSchema } from '#validators/create_filter_schema'
import FieldValidationException from '#exceptions/field_validation_exception'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      date: createFilterSchema({
        ops: [FilterOperator.Between],
        valueSchema: vine.date({ formats: ['YYYY-MM-DD'] }),
        required: true,
      }),
      experiment: createFilterSchema({
        ops: [FilterOperator.In, FilterOperator.Eq],
        valueSchema: vine.string(),
        required: true,
      }),
      countryCode: createFilterSchema({
        ops: [FilterOperator.In, FilterOperator.Eq],
        valueSchema: vine.string(),
        required: false,
      }),
      variantId: createFilterSchema({
        ops: [FilterOperator.In, FilterOperator.Eq],
        valueSchema: vine.string(),
        required: false,
      }),
      version: createFilterSchema({
        ops: [FilterOperator.In, FilterOperator.Eq],
        valueSchema: vine.string(),
      }),
    }),
    group: vine.object({
      fields: vine
        .array(vine.string().in(['date', 'countryCode', 'variantId', 'version']))
        .minLength(1),
    }),
    preset: vine
      .object({
        viewPresetId: vine.number().optional(),
      })
      .optional(),
    // group: vine.group(['date', 'countryCode', 'variantId', 'version']),
    // preset: vine.viewPreset(),
  })
)

export default class FirebaseMetricResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer, auth },
        dataloaders,
      },
    }: GraphQLHttpContext<QueryFirebaseMetricsArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    const {
      where: { gameId, ...filters },
      group,
      preset,
    } = await indexValidator.validate(args)

    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.fb_experiment').authorize('index', game)

    // TODO Can we make it more declarative?
    if (
      DateTime.fromJSDate(filters.date!.values[0] as Date).diff(
        DateTime.fromJSDate(filters.date!.values[1] as Date),
        'days'
      ).days > 60
    ) {
      throw new FieldValidationException('date', 'Between filter range must be less than 60 days')
    }

    const pageId = 'dashboard.games.firebase_experiments.index'
    const viewPresetConfigMap = configMapRegistry.get('cmap.viewpreset').get(pageId)

    const viewPreset = await ViewPreset.findOrDefault(
      viewPresetConfigMap,
      auth.user!,
      preset?.viewPresetId
    )
    let query = FirebaseMetric.query()
      .innerJoin(
        FirebaseVersionVariant.table,
        FirebaseVersionVariant.columnName('id', FirebaseVersionVariant.table),
        FirebaseMetric.columnName('variantId')
      )
      .where(FirebaseVersionVariant.columnName('gameId', FirebaseVersionVariant.table), gameId)
      .withScopes((s) => {
        s.filter(filters as any, {
          date: FirebaseMetric.columnName('date', FirebaseMetric.table),
          countryCode: FirebaseMetric.columnName('countryCode', FirebaseMetric.table),
          variantId: FirebaseMetric.columnName('variantId', FirebaseMetric.table),
          version: FirebaseMetric.columnName('version', FirebaseMetric.table),
        })
      })

      .select(...group.fields.map((a) => FirebaseMetric.columnName(a as any, FirebaseMetric.table)))

    if (group.fields?.length) {
      query = query
        .groupBy(group.fields.map((a) => FirebaseMetric.columnName(a as any, FirebaseMetric.table)))
        .withScopes((s) => {
          s.withComputedMetricsJoin({
            groupBy: group.fields,
            viewPreset,
            viewPresetConfigMap,
            gameId,
            filters,
          })
          s.joinSumAdMetric(viewPreset, viewPresetConfigMap, gameId, filters)
          viewPreset.attributes.forEach((vpa) => {
            s.selectAggregationPreset(vpa, viewPresetConfigMap.attributes.get(vpa.name))
          })
        })
    } else {
      query = query.withScopes((s) => {
        viewPreset.attributes.forEach((vpa) => {
          s.selectPreset(vpa, viewPresetConfigMap.attributes.get(vpa.name))
        })
      })
    }
    let variants: FirebaseVersionVariant[] = []
    const metrics = await query
    if (group.fields.includes('variantId')) {
      const variantIds = uniq(metrics.map((m) => m.variantId))
      variants = await FirebaseVersionVariant.query().whereIn('id', variantIds)
    }

    dataloaders.firebaseAdMetric.args = args

    return {
      collection: metrics.map((m) =>
        m.withSerializationContext({ viewPreset, viewPresetConfigMap }).serialize()
      ),
      viewPreset: viewPreset.serialize(),
      variants: variants?.map((v) => v.serialize()) || [],
    }
  }

  @inject()
  async ads({
    parent,
    context: { dataloaders },
  }: GraphQLHttpContext<QueryFirebaseMetricsArgs, FirebaseMetric>) {
    return await dataloaders.firebaseAdMetric.load(parent)
  }
}
