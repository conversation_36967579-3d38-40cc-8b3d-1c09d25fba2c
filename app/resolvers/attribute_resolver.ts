import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import Game from '#models/game'
import { AttributeService } from '#services/attribute_service'
import { QueryAttributesArgs } from '#graphql/main'

export default class GameMetricResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { auth },
      },
    }: GraphQLHttpContext<QueryAttributesArgs>,
    attributeService: AttributeService
  ) {
    const attributes = await attributeService.getAttributes(
      args.where.modelName,
      auth.user!,
      args.where.gameId ? await Game.findOrFail(args.where.gameId) : undefined
    )

    return {
      collection: attributes,
    }
  }
}
