import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { v4 as uuid } from 'uuid'
import { HttpContext } from '@adonisjs/core/http'
import { z } from 'zod'
import { present } from '@mirai-game-studio/adonis-sdk/presenter'

import {
  MutationCreateUserArgs,
  MutationDeleteUsersArgs,
  MutationUpdateUserArgs,
  QueryUsersArgs,
  UserKind,
} from '#graphql/main'
import User, { ToolPermission, UserSerializationContext } from '#models/user'
import { Permission, Tool } from '#config/enums'
import UserPolicy from '#policies/user_policy'

const updateUserValidator = vine.compile(
  vine.object({
    form: vine.object({
      password: vine.string().minLength(6).optional(),
      toolPermissions: vine.array(
        vine.object({
          tool: vine.enum(Object.values(Tool)),
          action: vine.enum(Object.values(Permission)),
        })
      ),
      kind: vine.enum(Object.values(UserKind)),
      hasPassword: vine.boolean(),
      note: vine.string().trim().optional(),
    }),
    where: vine.object({
      id: vine.string(),
    }),
  })
)

const userStoreValidator = vine.compile(
  vine.object({
    form: vine.object({
      email: vine.string().trim().email(),
      password: vine.string().trim().minLength(6).optional(),
      fullName: vine.string().trim(),
      toolPermissions: vine.array(
        vine.object({
          tool: vine.enum(Object.values(Tool)),
          action: vine.enum(Object.values(Permission)),
        })
      ),
      kind: vine.enum(Object.values(UserKind)),
      note: vine.string().trim().optional(),
    }),
  })
)

const indexValidator = z.object({
  where: z.object({
    email: z.string().email().optional(),
  }),
  offset: z.object({
    page: z.number().int().min(1).optional().default(1),
    perPage: z.number().int().min(1).optional().default(200),
  }),
})

const destroyValidator = vine.compile(
  vine.object({
    where: vine.object({
      ids: vine.array(vine.string()).minLength(1),
    }),
  })
)

export default class UserResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<Partial<QueryUsersArgs>>,
    userPolicy: UserPolicy
  ) {
    await bouncer.with('UserPolicy').authorize('index')

    const serializationContext = await this.#userSerializationContext(bouncer)

    const { where, offset } = await indexValidator.parseAsync(args)
    const usersQuery = User.query()
      .where((q) => {
        if (where.email) {
          q.where('email', where.email)
        }
      })
      .orderBy('email', 'asc')

    await userPolicy.scope(auth.user!, usersQuery)

    const users = await usersQuery.paginate(offset?.page || 1, offset?.perPage || 200)

    return present(users, serializationContext)
  }

  @inject()
  async update({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<Partial<MutationUpdateUserArgs>>) {
    await bouncer.with('UserPolicy').authorize('update')

    const serializationContext = await this.#userSerializationContext(bouncer)

    const {
      where: { id },
      form: { password, toolPermissions, hasPassword, ...attributes },
    } = await updateUserValidator.validate(args)

    const user = await User.findOrFail(id)

    user.merge(attributes).merge({
      toolPermissions: await Promise.all(
        toolPermissions
          .map((tp) => new ToolPermission().merge(tp))
          .filter(async (t) => await bouncer.with('ToolPermissionPolicy').allows('update', t))
      ),
    })

    if (!hasPassword) {
      user.merge({
        password: null,
      })
    } else if (password) {
      user.merge({
        password,
      })
    }

    await user.save()

    return present(user.withSerializationContext(serializationContext))
  }

  @inject()
  async destroy({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationDeleteUsersArgs>) {
    await bouncer.with('UserPolicy').authorize('destroy')

    const { where } = await destroyValidator.validate(args)
    await User.query().whereIn('id', where.ids).update({ deletedAt: new Date() })
  }

  @inject()
  async store({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<Partial<MutationCreateUserArgs>>) {
    await bouncer.with('UserPolicy').authorize('store')

    const serializationContext = await this.#userSerializationContext(bouncer)

    const {
      form: { toolPermissions, ...attrs },
    } = await userStoreValidator.validate(args)
    const user = new User().merge(attrs).merge({
      id: uuid(),
      toolPermissions: await Promise.all(
        toolPermissions
          .map((tp) => new ToolPermission().merge(tp))
          .filter(async (t) => await bouncer.with('ToolPermissionPolicy').allows('store', t))
      ),
    })
    await user.save()
    return present(user.withSerializationContext(serializationContext))
  }

  @inject()
  async team({
    parent,
    context: {
      dataloaders,
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}, User>) {
    return (await bouncer.with('UserPolicy').allows('team'))
      ? dataloaders.team.load(parent.teamId)
      : null
  }

  @inject()
  async inchargedGames({
    parent,
    context: {
      dataloaders,
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}, User>) {
    return (await bouncer.with('UserPolicy').allows('inchargedGames'))
      ? dataloaders.userGameMembership.load(parent.email)
      : []
  }

  async #userSerializationContext(bouncer: HttpContext['bouncer']) {
    return {
      canViewNote: await bouncer.with('UserPolicy').allows('note'),
    } as UserSerializationContext
  }
}
