import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import { QueryGamePerformanceSettingArgs } from '#graphql/main'
import Game from '#models/game'
import GamePerformanceSetting from '#models/game_performance_setting'

export default class GamePerformanceSettingResolver {
  @inject()
  async show({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGamePerformanceSettingArgs>) {
    const game = await Game.query().where('storeId', args.gameId).firstOrFail()
    await bouncer.with('dash.game.review').authorize('index', game)
    const performanceSettings = await GamePerformanceSetting.query()
      .where('gameId', game.storeId)
      .orWhere('gameId', 'default')
    return (
      performanceSettings.find((setting) => setting.gameId === game.storeId) ??
      performanceSettings.find((setting) => setting.gameId === 'default')!
    )
  }
}
