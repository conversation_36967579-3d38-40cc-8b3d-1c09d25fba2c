import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { DateTime } from 'luxon'
import { chain, isUndefined } from 'lodash-es'
import { ConfigMapRegistry } from '@munkit/main'

import { FilterOperator, QueryAdMetricsArgs } from '#graphql/main'
import AdMetric from '#models/ad_metric'
import { createFilterSchema } from '#validators/create_filter_schema'
import Game from '#models/game'
import FieldValidationException from '#exceptions/field_validation_exception'
import Ad from '#models/ad'
import Campaign from '#models/campaign'
import AdAgency from '#models/ad_agency'
import MediaSource from '#models/media_source'
import AdGroup from '#models/ad_group'
import ViewPreset from '#models/view_preset'

const metricOps = [FilterOperator.Between, FilterOperator.Lte, FilterOperator.Gte]

const AVAILABLE_GROUP_DIMENSIONS = [
  'groupId',
  'adId',
  'campaignId',
  'countryCode',
  'date',
  'agencyId',
]

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      date: createFilterSchema({
        ops: [FilterOperator.Between],
        valueSchema: vine.date({ formats: ['YYYY-MM-DD'] }),
        required: true,
      }),
      adRevGrossAmount: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      adCostNonTaxAmount: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      impressionCount: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      clickCount: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      installCount: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      cpi: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      ctr: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      cvr: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      cpc: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      ipm: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      roas: createFilterSchema({
        ops: metricOps,
        valueSchema: vine.number(),
      }),
      campaignId: createFilterSchema({
        ops: [FilterOperator.Eq],
        valueSchema: vine.string(),
      }),
      adId: createFilterSchema({
        ops: [FilterOperator.Eq],
        valueSchema: vine.string(),
      }),
      groupId: createFilterSchema({
        ops: [FilterOperator.Eq],
        valueSchema: vine.string(),
      }),
      countryCode: createFilterSchema({
        ops: [FilterOperator.Eq],
        valueSchema: vine.string(),
      }),
      agencyId: createFilterSchema({
        ops: [FilterOperator.Eq],
        valueSchema: vine.number(),
      }),
    }),
    group: vine.object({
      fields: vine.array(vine.string().in(AVAILABLE_GROUP_DIMENSIONS)).optional(),
    }),
    preset: vine
      .object({
        viewPresetId: vine.number().optional(),
      })
      .optional(),
  })
)

const groupFilterRequires = {
  countryCode: [],
  date: [],
  agencyId: [],
  campaignId: [],
  groupId: ['campaignId'],
  adId: ['campaignId', 'groupId'],
}

export default class AdMetricResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<QueryAdMetricsArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    const {
      where: { gameId, date, agencyId, campaignId, groupId, adId, countryCode, ...metricFilters },
      group,
      preset,
    } = await indexValidator.validate(args)

    const pageId = 'dashboard.games.campaign_metrics.index'
    const viewPresetConfigMap = configMapRegistry.get('cmap.viewpreset').get(pageId)

    const viewPreset = await ViewPreset.findOrDefault(
      viewPresetConfigMap,
      auth.user!,
      preset?.viewPresetId
    )

    const dimensionFilters = chain({
      agencyId,
      date,
      campaignId,
      groupId,
      adId,
      countryCode,
    })
      .omitBy(isUndefined)
      .value()

    const normalizeGroupDimensions = () => {
      if (!group.fields || group.fields.length === 0) {
        return ['campaignId']
      }

      return group.fields
    }

    const groupDimensions = normalizeGroupDimensions()

    Object.entries(groupFilterRequires).forEach(([dimension, requiresFilter]) => {
      if (groupDimensions.includes(dimension)) {
        requiresFilter.forEach((filter) => {
          if (dimensionFilters[filter as keyof typeof dimensionFilters] === undefined) {
            throw new FieldValidationException(
              filter,
              `Group dimension ${groupDimensions} requires filter ${filter}`
            )
          }
        })
      }
    })

    // TODO Can we make it more declarative?
    if (
      DateTime.fromJSDate(dimensionFilters.date!.values[0] as any).diff(
        DateTime.fromJSDate(dimensionFilters.date!.values[1] as any),
        'days'
      ).days > 60
    ) {
      throw new FieldValidationException('date', 'Between filter range must be less than 60 days')
    }

    const game = await Game.findOrFail(gameId)

    await bouncer.with('dash.game.campaign_metric').authorize('index', game)

    const attrToQueryAttr = {
      campaignId: AdMetric.columnFullname('campaignId'),
      groupId: Ad.columnFullname('groupId'),
      adId: AdMetric.columnFullname('adId'),
      countryCode: AdMetric.columnFullname('countryCode'),
      date: AdMetric.columnFullname('date'),
      agencyId: MediaSource.columnFullname('agencyId'),
    }

    let query = AdMetric.query()
      .innerJoin(
        Campaign.table,
        Campaign.columnName('id', Campaign.table),
        AdMetric.columnFullname('campaignId')
      )
      .innerJoin(
        MediaSource.table,
        MediaSource.columnFullname('id'),
        Campaign.columnFullname('mediaSourceId')
      )
      .innerJoin(Ad.table, Ad.columnFullname('id'), AdMetric.columnFullname('adId'))
      .where(Campaign.columnName('gameId', Campaign.table), game.id)
      .withScopes((s) => {
        s.filter(dimensionFilters as any, attrToQueryAttr)
        s.withComputedMetricsJoin({
          groupBy: groupDimensions,
          gameId,
          viewPreset,
          attrToQueryAttr,
        })
      })

    // FIXME More generic solution
    if (groupDimensions.length < AVAILABLE_GROUP_DIMENSIONS.length) {
      query = AdMetric.query()
        .select('*')
        .from(
          query
            .select(
              ...groupDimensions.map((d) => attrToQueryAttr[d as keyof typeof attrToQueryAttr] || d)
            )
            .withScopes((s) => {
              viewPreset.attributes.forEach((vpa) => {
                s.selectAggregationPreset(vpa, viewPresetConfigMap.attributes.get(vpa.name)!)
              })
            })
            .groupBy(
              ...groupDimensions.map((d) => attrToQueryAttr[d as keyof typeof attrToQueryAttr] || d)
            )
        )
    } else {
      query = AdMetric.query()
        .select('*')
        .from(
          query
            .select(Ad.columnFullname('groupId'), MediaSource.columnFullname('agencyId'))
            .withScopes((s) => {
              viewPreset.attributes.forEach((vpa) => {
                s.selectPreset(vpa, viewPresetConfigMap.attributes.get(vpa.name)!)
              })
              s.selectKey()
            })
        )
    }

    query = query.withScopes((s) => s.filter(metricFilters as any, attrToQueryAttr))

    const metrics = await query

    // TODO: Refactor
    const relations = metrics.map((m) => ({
      adId: m.adId,
      groupId: m.groupId,
      campaignId: m.campaignId,
      agencyId: m.agencyId,
    }))

    const adIds = chain(relations)
      .map((m) => m.adId)
      .uniq()
      .reject(isUndefined)
      .value()
    const ads = adIds.length > 0 ? await Ad.query().whereIn('id', adIds) : []

    const campaignIds = chain(relations)
      .map((m) => m.campaignId)
      .uniq()
      .reject(isUndefined)
      .value()
    const campaigns =
      campaignIds.length > 0 ? await Campaign.query().whereIn('id', campaignIds) : []

    const adGroupIds = chain(relations)
      .map((m) => m.groupId)
      .uniq()
      .reject(isUndefined)
      .value()
    const adGroups = adGroupIds.length > 0 ? await AdGroup.query().whereIn('id', adGroupIds) : []

    const agencyIds = chain(relations)
      .map((m) => m.agencyId)
      .uniq()
      .reject(isUndefined)
      .value()
    const agencies = agencyIds.length > 0 ? await AdAgency.query().whereIn('id', agencyIds) : []

    return {
      collection: metrics.map((m) =>
        m.withSerializationContext({ viewPreset, viewPresetConfigMap }).serialize()
      ),
      ads: ads.map((a) => a.serialize()),
      adGroups: adGroups.map((a) => a.serialize()),
      campaigns: campaigns.map((a) => a.serialize()),
      agencies: agencies.map((a) => a.serialize()),
      viewPreset: viewPreset.serialize(),
    }
  }
}
