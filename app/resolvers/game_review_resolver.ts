import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { QueryGameReviewArgs } from 'graphql/main.js'
import vine from '@vinejs/vine'
import { DateTime } from 'luxon'

import Game from '#models/game'
import GameReview from '#models/game_review'

const gameReviewShowValidator = vine.compile(
  vine.object({
    gameId: vine.string(),
    date: vine.date({
      formats: ['YYYY-MM-DD'],
    }),
  })
)

export default class GameReviewResolver {
  @inject()
  async show({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameReviewArgs>) {
    const { date, gameId } = await gameReviewShowValidator.validate(args)

    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.review').authorize('index', game)
    const gameReview = await GameReview.query().where('gameId', game.id).where('date', date).first()
    return (
      gameReview || new GameReview().merge({ gameId: game.id, date: DateTime.fromJSDate(date) })
    )
  }
}
