import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { QueryGameArgs } from 'graphql/main.js'
import { z } from 'zod'
import { present } from '@mirai-game-studio/adonis-sdk/presenter'

import Game from '#models/game'
const indexValidator = z.object({
  where: z.object({
    keyword: z.string().trim().optional(),
    userIds: z.array(z.string()).optional(),
  }),
  offset: z.object({
    page: z.number().optional().default(1),
    perPage: z.number().optional().default(200),
  }),
})

export default class GameResolver {
  @inject()
  async show({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameArgs>) {
    const game = await Game.query().where('storeId', args.id).firstOrFail()
    await bouncer.with('DashboardGamePolicy').authorize('show', game)
    return present(game)
  }

  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}>) {
    await bouncer
      .with('DashboardGamePolicy')
      .authorize('index')
      .catch(() => bouncer.with('partner.game').authorize('index'))

    const { where, offset } = await indexValidator.parseAsync(args)

    const gamesQuery = Game.query()
      .where((q) => {
        if (where.keyword) {
          q.whereILike('name', `%${where.keyword}%`)
        }
      })
      .where('isActive', true)
      .orderBy('name', 'asc')

    await bouncer.with('GameQueryPolicy').authorize('scope', gamesQuery, where)

    const games = await gamesQuery.paginate(offset.page, offset.perPage)

    return present(games)
  }
}
