import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { Database } from '@adonisjs/lucid/database'
import { present } from '@mirai-game-studio/adonis-sdk/presenter'

import { MutationCreateWorkflowArgs, MutationUpdateWorkflowArgs } from '#graphql/main'
import Workflow, { WorkflowStep } from '#models/workflow'
import BudgetRequest from '#models/budget_request'
import WorkflowPolicy from '#policies/workflow_policy'

const storeValidator = vine.compile(
  vine.object({
    form: vine.object({
      name: vine.string(),
      roleId: vine.string(),
      steps: vine.array(
        vine.object({
          name: vine.string(),
          assigneeId: vine.string(),
          action: vine.string(),
          alternateAction: vine.string(),
        })
      ),
    }),
  })
)

const updateValidator = vine.compile(
  vine.object({
    form: vine.object({
      id: vine.number(),
      name: vine.string(),
      steps: vine.array(
        vine.object({
          name: vine.string(),
          assigneeId: vine.string(),
          action: vine.string(),
          alternateAction: vine.string(),
        })
      ),
    }),
  })
)

export default class WorkflowResolver {
  @inject()
  async stepActionIndex(
    {
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<{}>,
    db: Database
  ) {
    await bouncer.with('dash.budget_request').authorize('index')

    const workflowSteps = await WorkflowStep.query()
      .from(
        WorkflowStep.query()
          .from(
            db.raw(
              `
            ${Workflow.table},
            jsonb_array_elements(${Workflow.quote('steps')}) AS step,
            LATERAL (VALUES (step->>'action'), (step->>'alternate_action')) AS workflow_steps(action)
          `.trim()
            ) as any
          )
          .select('action')
          .union((q) => {
            const lastActionCol = BudgetRequest.columnName('lastAction')
            q.from(BudgetRequest.table)
              .select(db.raw(`${lastActionCol} as action`))
              .whereNotNull(lastActionCol)
          })
          .as('workflow_steps')
      )
      .select('action')
      .distinctOn('action')
      .whereNotNull('action')
      .whereNot('action', '')
      .orderBy('action', 'asc')

    return present(workflowSteps)
  }

  @inject()
  async index(
    {
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<{}>,
    workflowPolicy: WorkflowPolicy
  ) {
    await bouncer.with('workflow').authorize('index')
    const workflowsQuery = Workflow.query()
    await workflowPolicy.scope(auth.user!, workflowsQuery)
    const workflows = await workflowsQuery.orderBy('id', 'desc')
    return present(workflows)
  }

  @inject()
  async assginee({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, WorkflowStep>) {
    return dataloaders.user.loadById(parent.assigneeId)
  }

  @inject()
  async store({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationCreateWorkflowArgs>) {
    await bouncer.with('workflow').authorize('store')
    const { form } = await storeValidator.validate(args)
    const workflow = await Workflow.create({
      name: form.name,
      roleId: form.roleId,
      steps: form.steps.map((s) => new WorkflowStep().merge(s)),
    })
    return present(workflow)
  }

  @inject()
  async update({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<MutationUpdateWorkflowArgs>) {
    await bouncer.with('workflow').authorize('update')
    const { form } = await updateValidator.validate(args)
    const workflow = await Workflow.findOrFail(form.id)
    workflow.merge({
      name: form.name,
      steps: form.steps.map((s) => new WorkflowStep().merge(s)),
    })
    await workflow.save()
    return present(workflow)
  }
}
