import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import Ad from '#models/ad'

export default class AdResolver {
  @inject()
  async group({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, Ad>) {
    return dataloaders.adGroup.load(parent.groupId)
  }

  @inject()
  async campaign({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, Ad>) {
    return dataloaders.campaign.load(parent.campaignId)
  }
}
