import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'

import User from '#models/user'
import { MutationUpdateProfileArgs } from '#graphql/main'

const updateValidator = vine.compile(
  vine.object({
    form: vine.object({
      password: vine.string().minLength(6),
      passwordConfirmation: vine.string().confirmed({ confirmationField: 'password' }),
    }),
  })
)

export default class ProfileResolver {
  @inject()
  async show({
    context: {
      http: { auth },
    },
  }: GraphQLHttpContext) {
    const user = auth.user! as User
    await user.load('ledTeam')
    await user.load('team')
    return user
  }

  @inject()
  async update({
    args,
    context: {
      http: { auth },
    },
  }: GraphQLHttpContext<MutationUpdateProfileArgs>) {
    // Validate input using VineJS
    await updateValidator.validate(args)

    const user = auth.user! as User
    const { password } = args.form

    await user.merge({ password }).save()

    await user.load('ledTeam')
    await user.load('team')

    return user
  }
}
