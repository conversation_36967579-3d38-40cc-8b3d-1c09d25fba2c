import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { AggregateGameAgencyCostWhere, QueryGameAgencyCostsArgs } from 'graphql/main.js'
import vine from '@vinejs/vine'
import { first, last } from 'lodash-es'

import Game from '#models/game'
import { orderDirectionRule } from '#validators/rules/order_direction_rule'
import GameAgencyCost from '#models/game_agency_cost'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
    }),
    offset: vine.object({
      perPage: vine.number(),
      page: vine.number(),
    }),
    order: vine.object({
      direction: vine.string().use(orderDirectionRule()),
    }),
  })
)

const aggregateValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      dateTo: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
    }),
  })
)

export default class GameAgencyCostResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
      dataloaders,
    },
  }: GraphQLHttpContext<QueryGameAgencyCostsArgs>) {
    const {
      where: { gameId, dateFrom, dateTo },
      offset: { page, perPage },
      order: { direction },
    } = await indexValidator.validate(args)
    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.cost').authorize('index', game)

    const dates = await GameAgencyCost.query()
      .withScopes((s) =>
        s.default({
          gameId: game.id,
          from: dateFrom,
          to: dateTo,
        })
      )
      .select(GameAgencyCost.columnName('date', GameAgencyCost.table))
      .groupBy(GameAgencyCost.columnName('date', GameAgencyCost.table))
      .orderBy(GameAgencyCost.columnName('date', GameAgencyCost.table), 'desc')
      .orderBy('date', direction as any)
      .paginate(page, perPage)

    if (dates.length === 0) {
      return {
        collection: [],
        pageInfo: dates.getMeta(),
      }
    }

    const [from, to] = [first(dates)!.date, last(dates)!.date].sort()

    const collection = await GameAgencyCost.query()
      .withScopes((s) => {
        s.default({
          gameId: game.id,
          from: from.toJSDate(),
          to: to.toJSDate(),
        })
        s.selectTotalCost()
        s.selectMediationCost()
      })
      .select(`${GameAgencyCost.table}.*`)

    dataloaders.gameMetric.gameId = game.id

    return { collection: collection.map((e) => e.serialize()), pageInfo: dates.getMeta() }
  }

  @inject()
  async aggregate({
    args,
    context: {
      http: { bouncer },
      dataloaders,
    },
  }: GraphQLHttpContext<AggregateGameAgencyCostWhere>) {
    const { where } = await aggregateValidator.validate(args)
    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.cost').authorize('index', game)

    const revs = await GameAgencyCost.query()
      .from(
        GameAgencyCost.query()
          .withScopes((s) => {
            s.default({
              gameId: game.id,
              from: where.dateFrom,
              to: where.dateTo,
            })
            s.selectMediationCost()
            s.selectTotalCost()
          })
          .select(`${GameAgencyCost.table}.*`)
          .as(GameAgencyCost.table)
      )
      .groupBy('agencyId')
      .select('agencyId')
      .sum(GameAgencyCost.columnName('mediationCost'), GameAgencyCost.columnName('mediationCost'))
      .sum(GameAgencyCost.columnName('totalCost'), GameAgencyCost.columnName('totalCost'))

    dataloaders.gameMetric.gameId = game.id

    return {
      collection: revs.map((e) => e.serialize()),
    }
  }

  @inject()
  async agency({ context: { dataloaders }, parent }: GraphQLHttpContext<{}, GameAgencyCost>) {
    return dataloaders.adAgency.load(parent.agencyId)
  }

  @inject()
  async metric({ parent, context: { dataloaders } }: GraphQLHttpContext<{}, GameAgencyCost>) {
    return dataloaders.gameMetric.load(parent.date)
  }
}
