import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import Mediation from '#models/mediation'

export default class MediationResolver {
  @inject()
  async index({
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}>) {
    await bouncer.with('DashboardGamePolicy').authorize('index')
    const mediations = await Mediation.all()

    return {
      collection: mediations.map((e) => e.serialize()),
    }
  }
}
