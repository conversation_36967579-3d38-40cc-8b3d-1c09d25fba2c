import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { z } from 'zod'
import { DateTime } from 'luxon'
import { isEmpty } from 'lodash-es'

import { MutationUpdateGameMetricArgs, QueryGameMetricsArgs } from '#graphql/main'
import Game from '#models/game'
import GameMetric from '#models/game_metric'
import GameMetricPolicy from '#policies/dashboard/game/game_metric_policy'
import { getAvgColumns, getSumColumns } from '#models/extensions/aggregation'
import GameMetricOverride from '#models/game_metric_override'
import GameMetricMetadatum from '#models/game_metric_metadatum'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateGte: vine.date().optional(),
      dateLte: vine.date().optional(),
    }),
    order: vine
      .object({
        direction: vine
          .enum(['asc', 'desc'])
          .parse((v: any) => v?.toLowerCase())
          .optional(),
      })
      .optional(),
    offset: vine
      .object({
        page: vine.number().optional(),
        perPage: vine.number().optional(),
      })
      .optional(),
  })
)

const updateValidator = z.object({
  where: z.object({
    gameId: z.string(),
    date: z.coerce.date(),
  }),
  form: z.object({
    override: z.object({
      revenue: z.number().optional().nullable(),
      cost: z.number().optional().nullable(),
    }),
    metadata: z.object({
      uaNote: z.string().optional().nullable(),
      monetNote: z.string().optional().nullable(),
      productNote: z.string().optional().nullable(),
      versionNote: z.string().optional().nullable(),
      note: z.string().optional().nullable(),
    }),
  }),
})

export default class GameMetricResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer, auth },
        dataloaders,
      },
    }: GraphQLHttpContext<QueryGameMetricsArgs>,
    gameMetricPolicy: GameMetricPolicy
  ) {
    const { offset, where, order } = await indexValidator.validate(args)

    const game = await Game.findOrFail(where.gameId)
    await bouncer
      .with('dash.game.metric')
      .authorize('index', game)
      .catch(() => bouncer.with('dash.game.review').authorize('index', game))

    const metricsQuery = GameMetric.query()
      .withScopes((s) => s.default(game.id))
      .where(GameMetric.columnName('storeId', GameMetric.table), game.id)
      .where((q) => {
        if (where?.dateGte) {
          q.where(GameMetric.columnName('date', GameMetric.table), '>=', where.dateGte)
        }

        if (where?.dateLte) {
          q.where(GameMetric.columnName('date', GameMetric.table), '<=', where.dateLte)
        }
      })
      .orderBy(GameMetric.columnName('date', GameMetric.table), order?.direction || 'desc')

    const metrics = await metricsQuery.paginate(offset?.page || 1, offset?.perPage || 200)

    const { data, meta } = metrics.serialize(await gameMetricPolicy.cherryPick(auth.user!, game))

    dataloaders.gameMetricMetadata.gameId = game.id

    return {
      collection: data,
      pageInfo: meta,
    }
  }

  @inject()
  async aggregate(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<QueryGameMetricsArgs>,
    gameMetricPolicy: GameMetricPolicy
  ) {
    const { where } = await indexValidator.validate(args)

    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.metric').authorize('index', game)

    const query = GameMetric.query()
      .from(
        GameMetric.query()
          .withScopes((s) => s.default(game.id))
          .as(GameMetric.table)
      )
      .where(GameMetric.columnName('storeId', GameMetric.table), game.id)
      .where((q) => {
        if (where?.dateGte) {
          q.where(GameMetric.columnName('date', GameMetric.table), '>=', where.dateGte)
        }

        if (where?.dateLte) {
          q.where(GameMetric.columnName('date', GameMetric.table), '<=', where.dateLte)
        }
      })

    getSumColumns(GameMetric).forEach((col) => {
      query.sum(GameMetric.columnName(col as any), GameMetric.columnName(col as any))
    })

    getAvgColumns(GameMetric).forEach((col) => {
      query.avg(GameMetric.columnName(col as any), GameMetric.columnName(col as any))
    })

    const aggregation = await query.firstOrFail()

    return aggregation.serialize(await gameMetricPolicy.cherryPick(auth.user!, game))
  }

  @inject()
  async update(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<MutationUpdateGameMetricArgs>,
    gameMetricPolicy: GameMetricPolicy
  ) {
    const { where, form } = await updateValidator.parseAsync(args)
    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.metric').authorize('update', game)

    if (!isEmpty(form.override)) {
      const override = await GameMetricOverride.firstOrNew({
        date: DateTime.fromJSDate(where.date),
        gameId: game.id,
      })

      override.merge(form.override)
      await override.save()
    }

    if (!isEmpty(form.metadata)) {
      const metadata = await GameMetricMetadatum.firstOrNew({
        date: DateTime.fromJSDate(where.date),
        gameId: game.id,
      })

      metadata.merge(form.metadata)
      await metadata.save()
    }

    const gameMetric = await GameMetric.query()
      .withScopes((s) => s.default(game.id))
      .where(GameMetric.columnFullname('storeId'), game.id)
      .where(GameMetric.columnFullname('date'), where.date)
      .firstOrFail()

    return gameMetric.serialize(await gameMetricPolicy.cherryPick(auth.user!, game))
  }

  async metadata({ context: { dataloaders }, parent }: GraphQLHttpContext<{}, GameMetric>) {
    return dataloaders.gameMetricMetadata.load(parent.id)
  }
}
