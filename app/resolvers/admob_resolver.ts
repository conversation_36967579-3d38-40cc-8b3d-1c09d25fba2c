import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { inject } from '@adonisjs/core'
import { admob, admob_v1beta } from '@googleapis/admob'
import config from 'config'
import vine from '@vinejs/vine'
import { DateTime } from 'luxon'
import { OAuth2Client } from 'google-auth-library'

import FieldValidationException from '#exceptions/field_validation_exception'
import { QueryAdmobMetricsArgs } from '#graphql/main'
import { VaultKeyClient } from '#services/vaultkey_client'

const admobConfig = config.get('admob')

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({ formats: ['YYYY-MM-DD'] }),
      dateTo: vine.date({ formats: ['YYYY-MM-DD'] }),
      formats: vine.array(vine.string()).minLength(1),
    }),
  })
)

export default class AdmobResolver {
  @inject()
  async games(
    {
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<{}>,
    vaultkeyClient: VaultKeyClient
  ) {
    await bouncer.with('dash.monet.admob').authorize('index')

    const client = await this.#getClient(vaultkeyClient)

    let allApps: admob_v1beta.Schema$App[] = []
    let nextPageToken: string = ''

    do {
      const res = await client.accounts.apps.list({
        parent: `accounts/${admobConfig.publisherId}`,
        pageSize: 1000, // Adjust based on API limits
        pageToken: nextPageToken || undefined,
      })

      // Add apps from the current page to our collection
      if (res.data.apps && res.data.apps.length > 0) {
        allApps = [...allApps, ...res.data.apps]
      }

      const pageToken = res.data.nextPageToken
      if (pageToken) {
        nextPageToken = pageToken
      }
    } while (nextPageToken)

    return {
      collection: allApps.filter((app) => app.appApprovalState === 'APPROVED'),
    }
  }

  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<QueryAdmobMetricsArgs>,
    vaultkeyClient: VaultKeyClient
  ) {
    await bouncer.with('dash.monet.admob').authorize('index')

    const { where } = await indexValidator.validate(args)

    if (
      DateTime.fromJSDate(where.dateFrom).diff(DateTime.fromJSDate(where.dateTo), 'days').days > 14
    ) {
      throw new FieldValidationException('dateFrom', 'Date range cannot be more than 14 days')
    }

    const client = await this.#getClient(vaultkeyClient)

    const networkReport = await client.accounts.networkReport.generate({
      parent: `accounts/${admobConfig.publisherId}`,
      requestBody: {
        reportSpec: {
          dimensions: ['APP', 'DATE', 'AD_UNIT', 'COUNTRY', 'APP_VERSION_NAME', 'FORMAT'],
          dimensionFilters: [
            {
              dimension: 'APP',
              matchesAny: {
                values: [where.gameId],
              },
            },
            {
              dimension: 'FORMAT',
              matchesAny: {
                values: where.formats,
              },
            },
          ],
          dateRange: {
            startDate: {
              day: where.dateFrom.getDate(),
              month: where.dateFrom.getMonth() + 1,
              year: where.dateFrom.getFullYear(),
            },
            endDate: {
              day: where.dateTo.getDate(),
              month: where.dateTo.getMonth() + 1,
              year: where.dateTo.getFullYear(),
            },
          },
          metrics: [
            'CLICKS',
            'ESTIMATED_EARNINGS',
            'IMPRESSIONS',
            'IMPRESSION_CTR',
            'MATCHED_REQUESTS',
            'SHOW_RATE',
            // 'OBSERVED_ECPM',
          ],
        },
      },
    })

    const mediationReport = await client.accounts.mediationReport.generate({
      parent: `accounts/${admobConfig.publisherId}`,
      requestBody: {
        reportSpec: {
          dimensions: ['APP', 'DATE', 'AD_UNIT', 'COUNTRY', 'APP_VERSION_NAME', 'FORMAT'],
          dimensionFilters: [
            {
              dimension: 'APP',
              matchesAny: {
                values: [where.gameId],
              },
            },
            {
              dimension: 'FORMAT',
              matchesAny: {
                values: where.formats,
              },
            },
          ],
          dateRange: {
            startDate: {
              day: where.dateFrom.getDate(),
              month: where.dateFrom.getMonth() + 1,
              year: where.dateFrom.getFullYear(),
            },
            endDate: {
              day: where.dateTo.getDate(),
              month: where.dateTo.getMonth() + 1,
              year: where.dateTo.getFullYear(),
            },
          },
          metrics: ['OBSERVED_ECPM'],
        },
      },
    })

    return {
      mediation: mediationReport.data,
      network: networkReport.data,
    }
  }

  async #getClient(vaultkeyClient: VaultKeyClient) {
    const credentials = await vaultkeyClient.getOAuth2Credentials(admobConfig.vaultkeyId)

    const auth = new OAuth2Client(
      credentials.clientId,
      credentials.clientSecret,
      credentials.redirectUri
    )
    auth.setCredentials({
      access_token: credentials.accessToken,
      scope: credentials.scope,
    })

    const client = admob({
      version: 'v1beta',
      auth: auth as any,
    })

    return client
  }
}
