import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { inject } from '@adonisjs/core'
import vine from '@vinejs/vine'

import { QueryFirebaseExperimentsArgs } from '#graphql/main'
import FirebaseVersionVariant from '#models/firebase_version_variant'
import { FirebaseExperimentPresenter } from '#presenters/firebase_experiment_presenter'
import Game from '#models/game'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
    }),
  })
)

export default class FirebaseExperimentResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryFirebaseExperimentsArgs>) {
    const { where } = await indexValidator.validate(args)

    const game = await Game.findOrFail(where.gameId)
    await bouncer.with('dash.game.fb_experiment').authorize('index', game)

    const experiments = await FirebaseVersionVariant.query()
      .distinctOn('experiment')
      .where('gameId', where.gameId)

    return {
      collection: experiments.map((e) => FirebaseExperimentPresenter.fromVariant(e).serialize()),
    }
  }
}
