import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { inject } from '@adonisjs/core'
import vine from '@vinejs/vine'
import { groupBy, toPairs } from 'lodash-es'
import { HttpContext } from '@adonisjs/core/http'

import { QueryGameLevelDropsArgs, QueryGameLevelDropVersionsArgs } from '#graphql/main'
import Game from '#models/game'
import { compareSemver } from '#utils/semver'
import GameDailyLevelDrop from '#models/game_daily_level_drop'
import GameLevelDrop from '#models/game_level_drop'
import { GameLevelDropPresenter } from '#presenters/game_level_drop_presenter'

const gameLevelDropIndexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      dateTo: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      installDateFrom: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      installDateTo: vine
        .date({
          formats: ['YYYY-MM-DD'],
        })
        .optional(),
      versions: vine.array(vine.string()),
    }),
  })
)

export default class GameLevelDropResolver {
  @inject()
  async versions({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameLevelDropVersionsArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.lv_drop').authorize('index', game)

    const versions = await GameLevelDrop.query()
      .where('gameId', game.id)
      .where('isValidSemver', true)
      .select('version')
      .distinctOn('version')
      .then((rs) => rs.map((r) => r.version).sort((a, b) => 0 - compareSemver(a, b)))

    return {
      collection: versions,
    }
  }

  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameLevelDropsArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    const { where } = await gameLevelDropIndexValidator.validate(args)
    const drops = await this.#getDrops(where, bouncer, game)
    const collection = toPairs(groupBy(drops, (d) => d.version)).flatMap(([, levels]) =>
      GameLevelDropPresenter.presentList(levels)
    )

    return { collection }
  }

  async #getDrops(
    where: QueryGameLevelDropsArgs['where'],
    bouncer: HttpContext['bouncer'],
    game: Game
  ) {
    if (where.dateFrom && where.dateTo) {
      await bouncer.with('dash.game.product_metric').authorize('index', game)
      const drops = await GameDailyLevelDrop.query()
        .where('gameId', where.gameId)
        .whereBetween('date', [where.dateFrom, where.dateTo])
        .where((q) => {
          if (where.installDateFrom) {
            q.where('installDate', '>=', where.installDateFrom)
          }

          if (where.installDateTo) {
            q.where('installDate', '<=', where.installDateTo)
          }
        })
        .where('isValidSemver', true)
        .whereIn('version', where.versions)
        .groupBy('world', 'level')
        .select('world', 'level')
        .sum('attemptCount', GameDailyLevelDrop.columnName('attemptCount'))
        .sum('skipCount', GameDailyLevelDrop.columnName('skipCount'))
        .sum('winCount', GameDailyLevelDrop.columnName('winCount'))
        .sum('activeUserCount', GameDailyLevelDrop.columnName('activeUserCount'))
        .sum('completeCount', GameDailyLevelDrop.columnName('completeCount'))
        .sum('loseCount', GameDailyLevelDrop.columnName('loseCount'))
        .sum('totalPlaytimeSec', GameDailyLevelDrop.columnName('totalPlaytimeSec'))

      drops.forEach((d) => {
        d.version = where.versions.join(', ')
      })

      return drops
    } else {
      await bouncer.with('dash.game.lv_drop').authorize('index', game)

      return await GameLevelDrop.query()
        .where('gameId', where.gameId)
        .where('isValidSemver', true)
        .whereIn('version', where.versions)
    }
  }
}
