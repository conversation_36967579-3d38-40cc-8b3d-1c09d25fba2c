import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { inject } from '@adonisjs/core'
import vine from '@vinejs/vine'

import { QueryGameRewardUsagesArgs, QueryGameRewardUsageVersionsArgs } from '#graphql/main'
import Game from '#models/game'
import GameRewardUsage from '#models/game_reward_usage'
import { compareSemver } from '#utils/semver'

const gameRewardUsageIndexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      versions: vine.array(vine.string()),
    }),
    group: vine.object({
      fields: vine.array(vine.string().in(['location', 'level', 'world'])),
    }),
    order: vine.object({
      direction: vine
        .enum(['asc', 'desc'])
        .parse((v) => (v as string | null)?.toLowerCase())
        .optional(),
    }),
  })
)

const versionsValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
    }),
  })
)

export default class GameRewardUsageResolver {
  @inject()
  async versions({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameRewardUsageVersionsArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const { where } = await versionsValidator.validate(args)

    const versions = await GameRewardUsage.query()
      .where('gameId', game.id)
      .whereBetween('date', [where.dateFrom, where.dateTo])
      .where('isValidSemver', true)
      .select('version')
      .distinctOn('version')
      .then((rs) => rs.map((r) => r.version).sort((a, b) => 0 - compareSemver(a, b)))

    return {
      collection: versions,
    }
  }

  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameRewardUsagesArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const { where, group } = await gameRewardUsageIndexValidator.validate(args)

    const usages = await GameRewardUsage.query()
      .where('gameId', game.id)
      .whereBetween('date', [where.dateFrom, where.dateTo])
      .where('isValidSemver', true)
      .whereIn('version', where.versions)
      .select(group.fields)
      .groupBy(group.fields.map((f) => GameRewardUsage.columnName(f as any)))
      .sum('useCount', GameRewardUsage.columnName('useCount'))

    return { collection: usages }
  }
}
