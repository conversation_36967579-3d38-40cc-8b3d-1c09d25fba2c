import { inject } from '@adonisjs/core'
import router from '@adonisjs/core/services/router'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import { MenuPresenter } from '#presenters/menu_presenter'
export default class SideMenuResolver {
  @inject()
  async index({
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}>) {
    return {
      collection: [
        new MenuPresenter().merge({
          name: 'All Games',
          icon: 'ManageSearchIcon',
          path: router.makeUrl('dashboard.games.index'),
          isPermitted: await bouncer.with('DashboardGamePolicy').allows('index'),
          group: '1',
        }),
        new MenuPresenter().merge({
          name: 'Game Revenue',
          icon: 'RequestQuoteIcon',
          path: router.makeUrl('dashboard.game_revenues.index'),
          isPermitted: await bouncer.with('DashboardGameRevenuePolicy').allows('index'),
          group: '1',
        }),
        new MenuPresenter().merge({
          name: 'Manager Reports',
          icon: 'AssessmentIcon',
          path: router.makeUrl('dashboard.revenue_reports.index'),
          isPermitted: await bouncer.with('DashboardRevenueReportPolicy').allows('index'),
          group: '1',
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('dashboard.monet.admobs.index', {}, { qs: { ui_next: 'true' } }),
          icon: 'CurrencyExchangeIcon',
          name: 'Monet Admobs',
          group: '1',
          isPermitted: await bouncer.with('dash.monet.admob').allows('index'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('dashboard.budget_requests.index'),
          icon: 'WalletIcon',
          name: 'Budget Requests',
          group: '1',
          isPermitted: await bouncer.with('dash.budget_request').allows('index'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('system.users.index'),
          icon: 'ManageAccountsIcon',
          name: 'Users',
          group: '2',
          isPermitted: await bouncer.with('UserPolicy').allows('update'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('system.teams.index'),
          icon: 'GroupIcon',
          name: 'Teams',
          group: '2',
          isPermitted: await bouncer.with('TeamPolicy').allows('update'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('dashboard.acls.show', { subject: 'route' }),
          icon: 'ShieldAlert',
          name: 'ACL',
          group: '2',
          isPermitted: await bouncer.with('AclPolicy').allows('update'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('workflows.index'),
          icon: 'AccountTreeIcon',
          name: 'Workflows',
          group: '2',
          isPermitted: await bouncer.with('workflow').allows('update'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('documents.index'),
          icon: 'DescriptionIcon',
          name: 'Documents',
          group: '2',
          isPermitted: await bouncer.with('LandingPageDocumentPolicy').allows('update'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('changelog.index'),
          icon: 'AutoAwesomeIcon',
          name: 'Changelog',
          group: '2',
          isPermitted: await bouncer.with('ChangelogPolicy').allows('index'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('github.acl.index'),
          icon: 'RuleIcon',
          name: 'GitHub ACL',
          group: '3',
          isPermitted: await bouncer.with('GitHubAclPolicy').allows('index'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('game.release_proposals.index'),
          icon: 'NewReleasesIcon',
          name: 'Release Requests',
          group: '3',
          isPermitted: await bouncer.with('GameReleaseProposalPolicy').allows('index'),
        }),
        new MenuPresenter().merge({
          path: router.makeUrl('github.protected_branches.index'),
          icon: 'ShieldIcon',
          name: 'GitHub Protected Branch',
          group: '3',
          isPermitted: await bouncer.with('GitHubProtectedBranchPolicy').allows('index'),
        }),
      ]
        .filter((m) => m.isPermitted)
        .map((m) => m.serialize()),
    }
  }
}
