import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'

import ReleaseMetric from '#models/release_metric'
import { QueryReleaseVersionsArgs } from '#graphql/main'

export default class ReleaseVersionResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryReleaseVersionsArgs>) {
    await bouncer.with('DashboardGamePolicy').authorize('index')
    const releaseVersions = await ReleaseMetric.query()
      .where('gameId', args.where.gameId)
      .distinct('version')
      .select('version')
    return {
      collection: releaseVersions.map((e) => e.serialize()),
    }
  }
}
