import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { uniq } from 'lodash-es'
import { Database } from '@adonisjs/lucid/database'
import { ConfigMapRegistry } from '@munkit/main'
import { z } from 'zod'
import { present } from '@mirai-game-studio/adonis-sdk/presenter'

import DashboardRoleMembership from '#models/dashboard_role_membership'
import DashboardRoleMembershipUser from '#models/dashboard_role_membership_user'
import { UserService } from '#services/user_service'
import { MutationUpdateGameRolesArgs } from '#graphql/main'
import { GameRolePresenter } from '#presenters/game_role_presenter'

export const updateGameRolesSchema = z.object({
  forms: z.array(
    z.object({
      gameId: z.string(),
      roles: z.array(
        z.object({
          id: z.string(),
          users: z.array(z.string()).optional(),
        })
      ),
    })
  ),
})

@inject()
export default class GameRoleResolver {
  constructor(private configMapRegistry: ConfigMapRegistry) {}

  @inject()
  async index({ args }: GraphQLHttpContext<{ gameIds: string[] }>) {
    const { gameIds } = args
    const memberships = await this.#getRoles(gameIds)
    return present(GameRolePresenter.present(memberships))
  }

  @inject()
  async update(
    {
      args,
      context: {
        http: { bouncer },
      },
    }: GraphQLHttpContext<MutationUpdateGameRolesArgs>,
    userService: UserService,
    db: Database
  ) {
    await bouncer.with('DashboardRolePolicy').authorize('update')

    const validatedData = updateGameRolesSchema.parse(args)
    const { forms } = validatedData

    const allEmails = uniq(forms.flatMap((form) => form.roles.flatMap((role) => role.users || [])))
    const gameIds = forms.map((form) => form.gameId)

    const users = await userService.findManyByEmails(allEmails)

    const tx = await db.transaction()

    try {
      await Promise.all(
        forms.map(async (form) => {
          const { gameId, roles } = form

          await Promise.all(
            roles.map(async ({ id, users: emails }) => {
              if (!emails) return
              const membership = await DashboardRoleMembership.firstOrNew({
                storeId: gameId,
                roleId: id,
              })

              const journal = membership.makeJournal()

              membership.merge({
                users: emails
                  .filter((email) => {
                    const user = users.find((u) => u.email === email)
                    // Cho phép thêm nếu user không có team hoặc có team với roleId phù hợp
                    return !user?.team || user.team.roleId === id
                  })
                  .map((email: string) => new DashboardRoleMembershipUser().merge({ email })),
              })

              if (membership.$isPersisted) {
                await journal.save()
              }

              return await membership.save()
            })
          )
        })
      )

      await tx.commit()
    } catch (err) {
      await tx.rollback()
      throw err
    }

    const memberships = await this.#getRoles(gameIds)
    return present(GameRolePresenter.present(memberships))
  }

  // Phương thức private để lấy danh sách vai trò
  async #getRoles(storeIds: string[]) {
    const roles = await DashboardRoleMembership.query().whereIn('storeId', storeIds)
    const roleConfigMapCollection = this.configMapRegistry.get('cmap.dash.role')

    return storeIds.flatMap((storeId) => {
      const memberships = roles.filter((role) => role.storeId === storeId)

      return roleConfigMapCollection.map((roleConfigMap) => {
        const role = memberships.find((r) => r.roleId === roleConfigMap.id)
        return (
          role ||
          new DashboardRoleMembership().merge({
            users: [],
            roleId: roleConfigMap.id,
            storeId,
          })
        )
      })
    })
  }
}
