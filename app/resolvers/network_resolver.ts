import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { z } from 'zod'

import Network from '#models/network'
import { AdNetworkCategory } from '#config/enums'

const indexValidator = z.object({
  where: z.object({
    category: z.nativeEnum(AdNetworkCategory).optional(),
  }),
})

export default class NetworkResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<{}>) {
    await bouncer.with('DashboardGamePolicy').authorize('index')

    const { where } = await indexValidator.parseAsync(args)

    const networks = await Network.query().where((q) => {
      if (where.category) {
        q.where('category', where.category)
      }
    })

    return {
      collection: networks.map((e) => e.serialize()),
    }
  }
}
