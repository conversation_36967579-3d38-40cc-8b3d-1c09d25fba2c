import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { inject } from '@adonisjs/core'
import vine from '@vinejs/vine'

import { QueryGameDailyLevelDropVersionsArgs } from '#graphql/main'
import Game from '#models/game'
import { compareSemver } from '#utils/semver'
import GameDailyLevelDrop from '#models/game_daily_level_drop'

const versionsValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      dateFrom: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
      dateTo: vine.date({
        formats: ['YYYY-MM-DD'],
      }),
    }),
  })
)

export default class GameDailyLevelDropResolver {
  @inject()
  async versions({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<QueryGameDailyLevelDropVersionsArgs>) {
    const game = await Game.findOrFail(args.where.gameId)
    await bouncer.with('dash.game.product_metric').authorize('index', game)

    const { where } = await versionsValidator.validate(args)

    const versions = await GameDailyLevelDrop.query()
      .where('gameId', game.id)
      .whereBetween('date', [where.dateFrom, where.dateTo])
      .where('isValidSemver', true)
      .select('version')
      .distinctOn('version')
      .then((rs) => rs.map((r) => r.version).sort((a, b) => 0 - compareSemver(a, b)))

    return {
      collection: versions,
    }
  }
}
