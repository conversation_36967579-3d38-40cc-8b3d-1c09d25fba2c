import { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import { z } from 'zod'

import FirebaseVersionVariant from '#models/firebase_version_variant'
import { FilterOperator } from '#graphql/main'

const indexValidator = z.object({
  where: z.object({
    gameId: z.string(),
    experiment: z.object({
      operator: z.enum([FilterOperator.In]),
      values: z.array(z.string()),
    }),
  }),
})

export default class FirebaseVersionVaritansResolver {
  async index({
    args,
    context: {
      http: { bouncer },
    },
  }: GraphQLHttpContext<any>) {
    const { where } = await indexValidator.parseAsync(args)

    await bouncer.with('DashboardGamePolicy').authorize('index')

    const variants = await FirebaseVersionVariant.query()
      .where('gameId', where.gameId)
      .where('experiment', where.experiment.operator, where.experiment.values)
      .distinctOn('name')
      .select('name', 'id', 'experiment')

    return {
      collection: variants.map((v) => ({
        id: v.id,
        name: v.name,
        experiment: v.experiment,
      })),
    }
  }
}
