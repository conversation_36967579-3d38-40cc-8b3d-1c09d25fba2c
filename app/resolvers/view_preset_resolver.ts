import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { ConfigMapRegistry } from '@munkit/main'

import {
  MutationCreateViewPresetArgs,
  MutationDeleteViewPresetsArgs,
  QueryViewPresetsArgs,
} from '#graphql/main'
import { configMapRule } from '#validators/rules/config_map_rule'
import ViewPreset, { ViewPresetAttribute, ViewPresetSchema } from '#models/view_preset'
import FieldValidationException from '#exceptions/field_validation_exception'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      pageId: vine.string().use(
        configMapRule({
          id: 'cmap.viewpreset',
        })
      ),
    }),
  })
)

const storeValidator = vine.compile(
  vine.object({
    form: vine.object({
      name: vine.string(),
      pageId: vine.string().use(
        configMapRule({
          id: 'cmap.viewpreset',
        })
      ),
      attributes: vine
        .array(
          vine.object({
            name: vine.string(),
            cohortDays: vine.array(vine.number()),
          })
        )
        .minLength(1),
    }),
  })
)

const destroyValidator = vine.compile(
  vine.object({
    where: vine.object({
      ids: vine.array(vine.number()).minLength(1),
    }),
  })
)

export default class ViewPresetResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<QueryViewPresetsArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('DashboardGamePolicy').authorize('index')
    const { where } = await indexValidator.validate(args)
    const viewPresetConfigMap = configMapRegistry.get('cmap.viewpreset').get(where.pageId)

    const viewPresets = await ViewPreset.query()
      .where('pageId', where.pageId)
      .where('userId', auth.user!.id)

    const defaultPreset = ViewPreset.makeDefault(viewPresetConfigMap)

    return {
      collection: [defaultPreset, ...viewPresets].map((vp) => vp.serialize()),
    }
  }

  @inject()
  async store(
    {
      args,
      context: {
        http: { bouncer, auth },
      },
    }: GraphQLHttpContext<MutationCreateViewPresetArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('DashboardGamePolicy').authorize('index')

    const { form } = await storeValidator.validate(args)

    const viewPresetConfigMap = configMapRegistry.get('cmap.viewpreset').get(form.pageId)

    form.attributes.forEach((attr) => {
      const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.tryGet(attr.name)
      if (!viewPresetAttributeConfigMap) {
        throw new FieldValidationException(
          'attributes',
          `Attribute ${attr.name} is not valid for this page`
        )
      }

      if (viewPresetAttributeConfigMap.isCohort && attr.cohortDays.length === 0) {
        throw new FieldValidationException(
          'attributes',
          `Cohort days are required for attribute ${attr.name}`
        )
      }
    })

    const viewPreset = await ViewPreset.create({
      userId: auth.user!.id,
      pageId: form.pageId,
      name: form.name,
      schema: new ViewPresetSchema().merge({
        attributes: form.attributes.map((attr) => {
          const isCohort = viewPresetConfigMap.attributes.get(attr.name).isCohort
          return new ViewPresetAttribute().merge({
            name: attr.name,
            isCohort,
            cohortDays: isCohort ? attr.cohortDays : [],
          })
        }),
      }),
    })

    return viewPreset.serialize()
  }

  @inject()
  async destroy({
    args,
    context: {
      http: { bouncer, auth },
    },
  }: GraphQLHttpContext<MutationDeleteViewPresetsArgs>) {
    await bouncer.with('DashboardGamePolicy').authorize('index')

    const { where } = await destroyValidator.validate(args)
    await ViewPreset.query().where('userId', auth.user!.id).whereIn('id', where.ids).delete()
  }
}
