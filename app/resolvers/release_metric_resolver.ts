import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { ConfigMapRegistry } from '@munkit/main'
import { DateTime } from 'luxon'

import { createFilterSchema } from '#validators/create_filter_schema'
import Game from '#models/game'
import { FilterOperator, QueryReleaseMetricsArgs } from '#graphql/main'
import FieldValidationException from '#exceptions/field_validation_exception'
import ViewPreset from '#models/view_preset'
import ReleaseMetric from '#models/release_metric'

const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      gameId: vine.string(),
      date: vine.object({
        operator: vine.string(),
        values: vine.array(vine.string()),
      }),
      countryCode: createFilterSchema({
        ops: [FilterOperator.In, FilterOperator.Eq],
        valueSchema: vine.string(),
        required: false,
      }),
      version: createFilterSchema({
        ops: [FilterOperator.In, FilterOperator.Eq],
        valueSchema: vine.string(),
      }),
    }),
    group: vine.object({
      fields: vine.array(vine.string()),
    }),
    preset: vine
      .object({
        viewPresetId: vine.number().optional(),
      })
      .optional(),
  })
)

export default class ReleaseIntelResolver {
  @inject()
  async index(
    {
      args,
      context: {
        http: { auth, bouncer },
        dataloaders,
      },
    }: GraphQLHttpContext<QueryReleaseMetricsArgs>,
    configMapRegistry: ConfigMapRegistry
  ) {
    const {
      where: { gameId, ...filters },
      group,
      preset,
    } = await indexValidator.validate(args)
    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.release_metric').authorize('index', game)

    if (
      DateTime.fromISO(filters.date!.values[0]).diff(
        DateTime.fromISO(filters.date!.values[1]),
        'days'
      ).days > 60
    ) {
      throw new FieldValidationException('date', 'Between filter range must be less than 60 days')
    }

    const pageId = 'dashboard.games.release_metrics.index'
    const viewPresetConfigMap = configMapRegistry.get('cmap.viewpreset').get(pageId)
    const viewPreset = await ViewPreset.findOrDefault(
      viewPresetConfigMap,
      auth.user!,
      preset?.viewPresetId
    )

    const colMapping = {
      date: ReleaseMetric.columnFullname('date'),
      version: ReleaseMetric.columnFullname('version'),
      countryCode: ReleaseMetric.columnFullname('countryCode'),
    }

    let query = ReleaseMetric.query()
      .withScopes((s) => {
        s.filter(filters as any, colMapping)
      })
      .select(...group.fields.map((a) => colMapping[a as keyof typeof colMapping] || a))
      .where(ReleaseMetric.columnFullname('gameId'), gameId)
    if (group.fields?.length) {
      query = query
        .groupBy(group.fields.map((a) => ReleaseMetric.columnFullname(a as any)))
        .withScopes((s) => {
          s.withComputedMetricsJoin({
            groupBy: group.fields,
            filters,
            gameId,
            viewPreset,
            viewPresetConfigMap,
          })
          s.joinSumAdMetric(viewPreset, viewPresetConfigMap, filters, gameId)
          viewPreset.attributes.forEach((vpa) => {
            s.selectAggregationPreset(vpa, viewPresetConfigMap.attributes.get(vpa.name))
          })
        })
    } else {
      query = query.withScopes((s) => {
        viewPreset.attributes.forEach((vpa) => {
          s.selectPreset(vpa, viewPresetConfigMap.attributes.get(vpa.name))
        })
      })
    }
    dataloaders.releaseAdMetric.args = args
    const metrics = await query
    return {
      collection: metrics.map((m) =>
        m.withSerializationContext({ viewPreset, viewPresetConfigMap }).serialize()
      ),
      viewPreset: viewPreset.serialize(),
    }
  }

  @inject()
  async ads({
    parent,
    context: { dataloaders },
  }: GraphQLHttpContext<QueryReleaseMetricsArgs, ReleaseMetric>) {
    return await dataloaders.releaseAdMetric.load(parent)
  }
}
