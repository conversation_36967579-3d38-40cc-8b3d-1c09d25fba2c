import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { journal } from '@mirai-game-studio/adonis-sdk/journal'
import { compose } from '@adonisjs/core/helpers'

import GameMetricOverride from './game_metric_override.js'
import User from './user.js'

export default class GameMetricOverrideJournal extends compose(
  BaseModel,
  journal<GameMetricOverride>()
) {
  static connection = 'dataWarehouse'

  @column()
  declare revenue: any

  @column()
  declare cost: any

  @column()
  declare createdById: User['id']

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
