import { BaseModel, column, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { DateTime } from 'luxon'

export default class GameInstallation extends compose(BaseModel, DatabaseNaming) {
  static table = 'fct_installs'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare gameId: string

  @column({ columnName: 'paid_installs' })
  declare paidInstalls: number

  @column({ columnName: 'organic_installs' })
  declare organicInstalls: number

  static date = scope((query, ...args: string[]) => {
    const op = args[0]
    const offset = Number(args[1])
    if (offset > 0) {
      return query.where('date', op, DateTime.now().plus({ days: offset }).toISODate())
    }

    return query.where(
      'date',
      op,
      DateTime.now()
        .minus({ days: Math.abs(offset) })
        .toISODate()
    )
  })
}
