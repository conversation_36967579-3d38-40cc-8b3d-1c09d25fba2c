import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { journal } from '@mirai-game-studio/adonis-sdk/journal'

import DashboardRoleMembership from './dashboard_role_membership.js'

export default class DashboardRoleMembershipJournal extends compose(
  BaseModel,
  journal<DashboardRoleMembership>()
) {
  @column()
  declare users: any

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
