import { DateTime } from 'luxon'
import {
  BaseModel,
  belongsTo,
  computed,
  hasOne,
  scope,
  column,
  beforeDelete,
  hasMany,
} from '@adonisjs/lucid/orm'
import parseDuration from 'parse-duration'
import { RedisRefreshTokenProvider } from '@mirai-game-studio/adonis-sdk/auth/refresh_token'
import redis from '@adonisjs/redis/services/main'
import { JwtAccessTokenProvider, JwtSecret } from '@mirai-game-studio/adonis-sdk/auth/jwt'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import hash from '@adonisjs/core/services/hash'
import type { HasOne, BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming, withSerializationContext } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import { Permission, Tool } from '#config/enums'
import { UserKind } from '#graphql/main'

import Team from './team.js'
import DashboardRoleMembership from './dashboard_role_membership.js'

export type UserSerializationContext = {
  canViewNote: boolean
}

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

export class ToolPermission extends BaseModel {
  @column()
  declare tool: Tool

  @column()
  declare action: Permission
}

export default class User extends compose(
  BaseModel,
  AuthFinder,
  DatabaseNaming,
  withSerializationContext<UserSerializationContext>()
) {
  @column({ isPrimary: true })
  declare id: string

  @column({ serializeAs: null })
  declare oauthProvider: string | null

  @column({ serializeAs: null })
  declare oauthUserId: string | null

  @column()
  declare fullName: string

  @column()
  declare email: string

  @column()
  declare kind: UserKind

  @column({ serializeAs: null })
  declare password: string | null

  @column({
    serialize: (v, _a, model) => {
      const canViewNote = (model as User).serializationContext?.canViewNote
      return canViewNote ? v : null
    },
  })
  declare note: string | null

  @column.dateTime({ autoCreate: true, serializeAs: null })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, serializeAs: null })
  declare updatedAt: DateTime | null

  @column.dateTime({ serializeAs: null })
  declare deletedAt: DateTime | null

  @column()
  declare teamId?: number | null

  @belongsTo(() => Team)
  declare team: BelongsTo<typeof Team>

  @hasOne(() => Team, { foreignKey: 'leaderId' })
  declare teamLead: HasOne<typeof Team>

  @hasOne(() => Team, { foreignKey: 'leaderId' })
  declare ledTeam: HasOne<typeof Team>

  @hasMany(() => DashboardRoleMembership, {
    foreignKey: 'user',
    localKey: 'email',
    onQuery: (query) => {
      query.withScopes((s) => s.withUsers())
    },
  })
  declare inchargedGames: HasMany<typeof DashboardRoleMembership>

  @column({
    prepare: (ps: ToolPermission[]) =>
      // @ts-ignore
      JSON.stringify(ps.map((p) => p['prepareForAdapter'](p.$attributes))),
    consume: (users: any[]) => ToolPermission.$createMultipleFromAdapterResult(users),
  })
  toolPermissions: ToolPermission[] = []

  @computed()
  get hasPassword() {
    return !!this.password?.trim()
  }

  // @computed()
  // get inchargedGames(): DashboardRoleMembership[] {
  //   return this.$extras['inchargedGames']
  // }

  @computed()
  get isDeleted() {
    return !!this.deletedAt
  }

  isLeader(team: Team) {
    return team.leaderId === this.id
  }

  static accessTokens = JwtAccessTokenProvider.forModel(User, {
    expiresInMillis: parseDuration('1 day')!,
    key: new JwtSecret('BjBZ-s9JFJTBwUsOo1Ml-fzkCqja_byX'),
    primaryKey: 'id',
    algorithm: 'HS256',
    audience: 'https://client.example.com',
    issuer: 'https://server.example.com',
  })

  static refreshToken = RedisRefreshTokenProvider.forModel(
    User,
    {
      expiresInMills: parseDuration('7 days')!,
      primaryKey: 'id',
      connection: 'main',
    },
    redis
  )

  static withLeaderTeamId = scope((query) => {
    query
      .leftJoin(
        Team.table,
        this.columnName('id', this.table),
        Team.columnName('leaderId', Team.table)
      )
      .select(
        query.client.raw(
          `case when ${this.quote('teamId')} is null then ${Team.quote('id')} else ${this.quote('teamId')} end as ${this.columnName('teamId')}`
        )
      )
  })

  // static async preloadInchargedGames(users: User[]) {
  //   const memberships = await DashboardRoleMembership.query()
  //     .withScopes((s) => s.withUsers())
  //     .whereIn(
  //       'user',
  //       users.map((u) => u.email)
  //     )
  //     .select('storeId', 'roleId', 'id', 'user')

  //   const emailToMemberships = groupBy(memberships, (m) => m.$extras['user'])
  //   for (const user of users) {
  //     user.$extras['inchargedGames'] = emailToMemberships[user.email] || []
  //   }

  //   return users
  // }

  @beforeDelete()
  static async removeAssociations(user: User) {
    const memberships = await DashboardRoleMembership.query()
      .distinctOn('roleId', 'storeId')
      .withScopes((s) => s.withUsers())
      .where('user', user.email)

    for (const membership of memberships) {
      membership.users = membership.users.filter((u) => u.email !== user.email)
      await membership.useTransaction(user.$trx!).save()
    }

    await Team.query({ client: user.$trx! }).where('leaderId', user.id).update({ leaderId: null })
  }
}
