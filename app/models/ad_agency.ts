import { BaseModel, column, scope } from '@adonisjs/lucid/orm'

import { AdNetworkInputType } from '#config/enums'

export class AdAgency extends BaseModel {
  static table = 'md_agency'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true, columnName: 'id' })
  declare id: number

  @column({ columnName: 'agency_name' })
  declare name: string

  @column({ columnName: 'status' })
  declare inputType: AdNetworkInputType

  @column({ columnName: 'is_show' })
  declare isVisible: boolean

  static default = scope(async (query) => {
    query.where('isVisible', true).orderBy('inputType', 'asc').orderBy('id', 'asc')
  })
}

export default AdAgency
