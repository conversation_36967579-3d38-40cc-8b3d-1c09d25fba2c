import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { Metadata } from '@munkit/main'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import { safeDivide } from '#utils/math'
import { AclModelName } from '#config/enums'
import { GamePerformanceCriteriaConclusion } from '#graphql/main'

import GameMetricMetadatum from './game_metric_metadatum.js'
import GameSpend from './game_spend.js'
import Game from './game.js'
import { getAvgQueryAttributes, getSumQueryAttributes } from './extensions/aggregation.js'
import GameMetricOverride from './game_metric_override.js'

declare module '@munkit/main' {
  interface MetadataDefinitions {
    'metrics.perf.comparer': {
      [GamePerformanceCriteriaConclusion.Drop]: 'lt' | 'gt' | 'gte' | 'lte'
      [GamePerformanceCriteriaConclusion.Pass]: 'lt' | 'gt' | 'gte' | 'lte'
    }
  }
}

@Metadata('model.authorize', AclModelName.GameMetric)
export default class GameMetric extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'app_analysis'

  @column({ columnName: 'id', isPrimary: true })
  declare id: number

  @column.date({ columnName: 'date', serialize: (v: DateTime) => v.toFormat('yyyy-MM-dd') })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare storeId: string

  @column({ columnName: 'paid_installs', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'sum')
  @Metadata('attr.authorize')
  declare paidInstalls: number

  @column({ columnName: 'organic_installs', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'sum')
  @Metadata('attr.authorize')
  declare organicInstalls: number

  @column({ columnName: 'total_installs', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'sum')
  @Metadata('agg.computed')
  @Metadata('attr.authorize')
  declare totalInstalls: number

  @column({ columnName: 'dont_used_1', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'sum')
  @Metadata('agg.computed')
  @Metadata('attr.authorize')
  declare cost: number

  @column({ columnName: 'total_rev', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'sum')
  @Metadata('agg.computed')
  @Metadata('attr.authorize')
  declare revenue: number

  @column({ columnName: 'dont_used_4', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'sum')
  @Metadata('agg.computed')
  @Metadata('attr.authorize')
  declare profit: number

  @column({ columnName: 'rr_d1', serialize: (v) => v || 0 })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare retentionRateDay1: number

  @column({ columnName: 'rr_d3', serialize: (v) => v || 0 })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare retentionRateDay3: number

  @column({ columnName: 'rr_d7', serialize: (v) => v || 0 })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare retentionRateDay7: number

  @column({ columnName: 'banner_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare bannerImpsDau: number

  @column({ columnName: 'inter_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare interImpsDau: number

  @column({ columnName: 'reward_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare rewardImpsDau: number

  @column({ columnName: 'aoa_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare aoaImpsDau: number

  @column({ columnName: 'mrec_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare mrecImpsDau: number

  @column({ columnName: 'audio_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare audioImpsDau: number

  @column({ columnName: 'admob_aoa_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare aoaAdmobImpsDau: number

  @column({ columnName: 'admob_collapse_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare collapseAdmobImpsDau: number

  @column({ columnName: 'admob_native_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare nativeAdmobImpsDau: number

  @column({ columnName: 'admob_adaptive_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare adaptiveAdmobImpsDau: number

  @column({ columnName: 'admob_mrec_impsdau', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare mrecAdmobImpsDau: number

  @column({ columnName: 'avg_sessions', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare averageSession: number

  @column({ columnName: 'avg_engagement_time', consume: (v) => Number(v) })
  @Metadata('agg.prefer', 'avg')
  @Metadata('attr.authorize')
  declare playtime: number

  @column()
  declare note: string

  @column()
  declare uaNote: string

  @column()
  declare monetNote: string

  @column()
  declare versionNote: string

  @column()
  declare productNote: string

  @belongsTo(() => Game, {
    foreignKey: 'storeId',
  })
  declare game: BelongsTo<typeof Game>

  @computed()
  @Metadata('attr.authorize')
  get organicPercentage() {
    return safeDivide(this.organicInstalls, this.totalInstalls)
  }

  @computed()
  @Metadata('attr.authorize')
  get cpi() {
    return safeDivide(this.cost, this.totalInstalls)
  }

  @computed()
  @Metadata('attr.authorize')
  get roas() {
    return safeDivide(this.revenue, this.cost)
  }

  @computed()
  get externalProfit(): number {
    return Number(this.$extras['external_profit'] ?? 0)
  }

  @computed()
  get internalProfit(): number {
    return Number(this.$extras['internal_profit'] ?? 0)
  }

  @computed()
  get monetImpsDau(): number {
    return this.interImpsDau + this.rewardImpsDau
  }

  @computed()
  get gameId(): string {
    return this.storeId
  }

  static default = scope(async (query, ...gameIds: string[]) => {
    const client = query.client

    const baseCols = [
      'id',
      'date',
      'storeId',
      'paidInstalls',
      'organicInstalls',
      'retentionRateDay1',
      'retentionRateDay3',
      'retentionRateDay7',
      'bannerImpsDau',
      'interImpsDau',
      'rewardImpsDau',
      'aoaImpsDau',
      'mrecImpsDau',
      'audioImpsDau',
      'aoaAdmobImpsDau',
      'collapseAdmobImpsDau',
      'nativeAdmobImpsDau',
      'adaptiveAdmobImpsDau',
      'mrecAdmobImpsDau',
      'averageSession',
      'playtime',
    ].map((e) => GameMetric.columnName(e as any, GameMetric.table))

    query
      .from(
        GameMetric.query()
          .leftJoin(GameMetricOverride.table, (q) => {
            q.on(
              GameMetric.columnName('date', GameMetric.table),
              '=',
              GameMetricOverride.columnName('date', GameMetricOverride.table)
            ).andOn(
              GameMetric.columnName('storeId', GameMetric.table),
              '=',
              GameMetricOverride.columnName('gameId', GameMetricOverride.table)
            )
          })
          .leftJoin(GameMetricMetadatum.table, (q) => {
            q.on(
              GameMetric.columnName('date', GameMetric.table),
              '=',
              GameMetricMetadatum.columnName('date', GameMetricMetadatum.table)
            ).andOn(
              GameMetric.columnName('storeId', GameMetric.table),
              '=',
              GameMetricMetadatum.columnName('gameId', GameMetricMetadatum.table)
            )
          })
          .joinRaw(
            `left join (${GameSpend.query()
              .from(
                GameSpend.query()
                  .from(
                    GameSpend.query()
                      .withScopes((s) => s.withOverride(...gameIds))
                      .as('net_spends')
                  )
                  .groupBy('storeId', 'date')
                  .select('storeId', 'date')
                  .sum(GameSpend.columnName('totalAmount'), GameSpend.columnName('totalAmount'))
                  .as('game_costs')
              )
              .toQuery()}) costs on ${GameMetric.quote('date')} = ${GameSpend.quote('date', 'costs')} and ${GameMetric.quote('storeId')} = ${GameSpend.quote('storeId', 'costs')}`
          )
          .select(
            ...baseCols,
            client.raw(
              `CASE WHEN ${GameMetricOverride.quote('revenue')} IS NULL THEN coalesce(${GameMetric.quote('revenue')}, 0) ELSE coalesce(${GameMetricOverride.quote('revenue')}, 0) END AS "${GameMetric.columnName('revenue')}"`
            ),
            client.raw(
              `CASE WHEN ${GameMetricOverride.quote('cost')} IS NULL THEN coalesce(${GameSpend.quote('totalAmount', 'costs')}, 0) + ${GameMetric.columnName('paidInstalls')} * :rate::float ELSE coalesce(${GameMetricOverride.quote('cost')}, 0) END AS "${GameMetric.columnName('cost')}"`,
              { rate: 0.0012 }
            ),
            client.raw(`${GameMetricMetadatum.quote('note')} AS ${GameMetric.columnName('note')}`),
            client.raw(
              `${GameMetricMetadatum.quote('uaNote')} AS ${GameMetric.columnName('uaNote')}`
            ),
            client.raw(
              `${GameMetricMetadatum.quote('productNote')} AS ${GameMetric.columnName('productNote')}`
            ),
            client.raw(
              `${GameMetricMetadatum.quote('monetNote')} AS ${GameMetric.columnName('monetNote')}`
            ),
            client.raw(
              `${GameMetricMetadatum.quote('versionNote')} AS ${GameMetric.columnName('versionNote')}`
            )
          )
          .as(GameMetric.table)
      )
      .select(
        ...baseCols,
        GameMetric.columnName('cost', GameMetric.table),
        GameMetric.columnName('revenue', GameMetric.table),
        query.client.raw(
          `${GameMetric.quote('revenue')} - ${GameMetric.quote('cost')} as "${GameMetric.columnName('profit')}"`
        ),
        query.client.raw(
          `CASE WHEN ${Game.quote('isInhouse')} IS TRUE THEN 0 ELSE (${GameMetric.quote('revenue')} - ${GameMetric.quote('cost')}) * (1.0 - ?) END as "external_profit"`,
          [0.5]
        ),
        query.client.raw(
          `CASE WHEN ${Game.quote('isInhouse')} IS TRUE THEN ${GameMetric.quote('revenue')} - ${GameMetric.quote('cost')} ELSE 0 END as "internal_profit"`
        ),
        query.client.raw(
          `${GameMetric.quote('paidInstalls')} + ${GameMetric.quote('organicInstalls')} AS ${GameMetric.columnName('totalInstalls')}`
        ),
        'note',
        'uaNote',
        'monetNote',
        'productNote',
        'versionNote'
      )
      .innerJoin(
        Game.table,
        Game.columnName('storeId', Game.table),
        GameMetric.columnName('storeId', GameMetric.table)
      )
  })

  static async aggregate(storeId: string) {
    const aggregationQuery = GameMetric.query()
      .from(
        GameMetric.query()
          .withScopes((s) => s.default())
          .as('agg')
      )
      .groupBy('storeId')
      .where('storeId', storeId)
      .select('storeId')
      .sum(this.columnName('totalInstalls'), this.columnName('totalInstalls'))
      .sum(this.columnName('cost'), this.columnName('cost'))

    getSumQueryAttributes(GameMetric).forEach((col) => {
      aggregationQuery.sum(GameMetric.columnName(col as any), GameMetric.columnName(col as any))
    })

    getAvgQueryAttributes(GameMetric).forEach((col) => {
      aggregationQuery.avg(GameMetric.columnName(col as any), GameMetric.columnName(col as any))
    })

    aggregationQuery.sum(GameMetric.columnName('revenue'), GameMetric.columnName('revenue'))

    const aggregation = await aggregationQuery.firstOrFail()

    return aggregation
  }
}

export class SubjectMetric extends GameMetric {
  @computed()
  get subject() {
    return this.$extras['subject']
  }

  declare hierarchy?: string[]
}
