import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { compose } from '@adonisjs/core/helpers'

import Game from './game.js'

export default class FirebaseVersionVariant extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare gameId: Game['id']

  @belongsTo(() => Game, { localKey: 'storeId', foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column()
  declare experiment: string

  @column()
  declare name: string
}
