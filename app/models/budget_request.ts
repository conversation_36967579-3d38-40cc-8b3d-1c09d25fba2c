import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import Workflow, { WorkflowStep } from './workflow.js'
import Game from './game.js'
import User from './user.js'
import { createFilterScope } from './extensions/filter.js'

export default class BudgetRequest extends compose(BaseModel, DatabaseNaming) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare amount: number

  @column()
  declare stepId: number

  @column({
    consume: (record) => WorkflowStep.$createFromAdapterResult(record),
    prepare: (record: WorkflowStep) => JSON.stringify(record.prepareForAdapter(record.$attributes)),
  })
  declare step: WorkflowStep

  @column()
  declare lastAction: string

  @column()
  declare gameId: Game['id']

  @column()
  declare workflowId: Workflow['id']

  @belongsTo(() => Workflow)
  declare workflow: BelongsTo<typeof Workflow>

  @column.date()
  declare expirationDate: DateTime

  @column()
  declare createdById: User['id']

  @belongsTo(() => User)
  declare createdBy: BelongsTo<typeof User>

  @column()
  declare description: string

  @column.dateTime()
  declare deletedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  static filter = createFilterScope(BudgetRequest)
}
