import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { Metadata } from '@munkit/main'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class GameMetricMetadatum extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column.date()
  declare date: DateTime

  @column()
  declare gameId: string

  @column()
  @Metadata('attr.authorize')
  declare note: string | null

  @column()
  @Metadata('attr.authorize')
  declare uaNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare monetNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare productNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare versionNote: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
