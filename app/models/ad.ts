import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import AdGroup from './ad_group.js'
import Campaign from './campaign.js'

export default class Ad extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'ads'

  @column({ isPrimary: true })
  declare id: string

  @column({
    // @ts-expect-error lib error
    serialize: (v, attr, model: Ad) => model.name || model.id,
  })
  declare name: string

  @column()
  declare campaignId: string

  @column()
  declare groupId: string

  @belongsTo(() => AdGroup, { foreignKey: 'groupId' })
  declare group: BelongsTo<typeof AdGroup>

  @belongsTo(() => Campaign)
  declare campaign: BelongsTo<typeof Campaign>
}
