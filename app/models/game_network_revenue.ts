import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import { safeDivide } from '#utils/math'

import AdAgency from './ad_agency.js'
import Game from './game.js'
import GameRevenue from './game_revenue.js'

export default class GameNetworkRevenue extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'fct_network_rev'

  @column({ isPrimary: true })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare gameId: Game['id']

  @belongsTo(() => Game, { foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column({ columnName: 'network_id' })
  declare networkId: AdAgency['id']

  @belongsTo(() => AdAgency, { foreignKey: 'networkId' })
  declare network: BelongsTo<typeof AdAgency>

  @column({ columnName: 'revenue', consume: (v) => Number(v) })
  declare revenue: number

  @column({ columnName: 'impressions', consume: (v) => Number(v) })
  declare impressionCount: number

  @computed()
  get mediationRevenue() {
    return Number(this.$extras['mediation_revenue'] || 0)
  }

  @computed()
  get weeklyRevenue() {
    return Number(this.$extras['weekly_revenue'] || 0)
  }

  @computed()
  get varianceRate() {
    return safeDivide(this.revenue - this.mediationRevenue, this.mediationRevenue)
  }

  static default = scope((query, where: { gameId: Game['id']; from?: Date; to?: Date }) => {
    const gameRevenuesQuery = GameRevenue.query()
      .from(
        GameRevenue.query()
          .where('storeId', where.gameId)
          .where((q) => {
            if (where.from) q.where('date', '>=', where.from)
            if (where.to) q.where('date', '<=', where.to)
          })
          .select('*')
          .as(GameRevenue.table)
      )
      .select('date', 'networkId')
      .groupBy('date', 'networkId')
      .sum(GameRevenue.columnName('revenue'), GameRevenue.columnName('revenue'))

    return query
      .where('gameId', where.gameId)
      .where((q) => {
        const date = GameNetworkRevenue.columnName('date', GameNetworkRevenue.table)
        if (where.from) q.where(date, '>=', where.from)
        if (where.to) q.where(date, '<=', where.to)
      })
      .joinRaw(
        `FULL OUTER JOIN (${gameRevenuesQuery.toQuery()}) AS ${GameRevenue.table} ON ${GameNetworkRevenue.quote('networkId')} = ${GameRevenue.quote('networkId')} AND ${GameRevenue.quote('date')} = ${GameNetworkRevenue.quote('date')}`
      )
  })

  static selectMediationRevenue = scope((query) => {
    query.select(query.client.raw(`${GameRevenue.quote('revenue')} AS mediation_revenue`))
  })
}
