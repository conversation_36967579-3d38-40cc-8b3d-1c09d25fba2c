import { getAllMetadata } from '@munkit/main'

export type AggregationType = 'sum' | 'avg'

export type AggregationOperator = AggregationType | 'min' | 'max' | 'divide'

export type AggregationFormular = {
  operator: AggregationOperator
  depends?: AggregationFormular[]
}

declare module '@munkit/main' {
  interface MetadataDefinitions {
    'agg.prefer': AggregationType
    'agg.computed': string
    'agg.query': boolean
    'agg.op': AggregationFormular
  }
}

export const getComputedAggregationColumns = <T>(klass: T) => {
  return getAllMetadata('agg.computed', klass).map((attr) => attr.name)
}

// TODO: Cache
export const getAggregationColumns = <T>(klass: T) => {
  return getAllMetadata('agg.prefer', klass).filter((attr) => !!attr.metadata)
}

export const getAvgColumns = <T>(klass: T) => {
  return getAggregationColumns(klass)
    .filter((attr) => attr.metadata === 'avg')
    .map((attr) => attr.name)
}

export const getSumColumns = <T>(klass: T) => {
  return getAggregationColumns(klass)
    .filter((attr) => attr.metadata === 'sum')
    .map((attr) => attr.name)
}

export const getSumQueryAttributes = <T>(klass: T) => {
  const queryAttributes = new Set(getAllMetadata('agg.query', klass).map((e) => e.name))

  return getSumColumns(klass).filter((attr) => queryAttributes.has(attr))
}

export const getAvgQueryAttributes = <T>(klass: T) => {
  const queryAttributes = new Set(getAllMetadata('agg.query', klass).map((e) => e.name))

  return getAvgColumns(klass).filter((attr) => queryAttributes.has(attr))
}
