import { scope } from '@adonisjs/lucid/orm'
import { LucidModel } from '@adonisjs/lucid/types/model'

import { Filter, FilterOperator } from '#graphql/main'
import FieldValidationException from '#exceptions/field_validation_exception'

export function createFilterScope<TModel extends LucidModel>(_Model: TModel) {
  return scope(
    (query, filters: Record<string, Filter>, customMappings: Record<string, string> = {}) => {
      // TODO Assert values.length
      Object.entries(filters).forEach(([attr, filter]) => {
        const attrForQuery = customMappings[attr] ?? attr
        switch (filter.operator) {
          case FilterOperator.Eq:
            query.where(attrForQuery, filter.values[0])
            break

          case FilterOperator.Ne:
            query.whereNot(attrForQuery, filter.values[0])
            break

          case FilterOperator.Gt:
            query.where(attrForQuery, '>', filter.values[0])
            break

          case FilterOperator.Gte:
            query.where(attrForQuery, '>=', filter.values[0])
            break

          case FilterOperator.Lt:
            query.where(attrForQuery, '<', filter.values[0])
            break

          case FilterOperator.Lte:
            query.where(attrForQuery, '<=', filter.values[0])
            break

          case FilterOperator.Between: {
            if (filter.values.length === 0) {
              break
            }

            if (filter.values.length !== 2) {
              throw new FieldValidationException(attr, 'Between filter must have 2 values')
            }

            query.whereBetween(attrForQuery, [filter.values[0], filter.values[1]])
            break
          }

          case FilterOperator.In: {
            if (filter.values.length === 0) {
              break
            }

            query.whereIn(attrForQuery, filter.values)
            break
          }

          default:
            break
        }
      })
    }
  )
}
