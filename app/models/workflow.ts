import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import User from './user.js'

export class WorkflowStep extends compose(BaseModel, DatabaseNaming) {
  @column()
  declare assigneeId: User['id']

  @column()
  declare name: string

  @column()
  declare action: string

  @column()
  alternateAction: string = ''

  @belongsTo(() => User, { foreignKey: 'assigneeId' })
  declare assignee: BelongsTo<typeof User>
}

export default class Workflow extends compose(BaseModel, DatabaseNaming) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare roleId: string

  @column({
    prepare: (records: WorkflowStep[]) =>
      JSON.stringify(records.map((r) => r.prepareForAdapter(r.$attributes))),
    consume: (records) => WorkflowStep.$createMultipleFromAdapterResult(records),
  })
  steps: WorkflowStep[] = []

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
