import { BaseModel, column, computed, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming, withSerializationContext } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { Metadata } from '@munkit/main'
import { DateTime } from 'luxon'
import { ColumnOptions, QueryScopeCallback } from '@adonisjs/lucid/types/model'
import format from 'string-template'
import { chain, uniq } from 'lodash-es'
import { snakeCase } from 'lodash-es'

import { AclModelName } from '#config/enums'
import { createFilterScope } from '#models/extensions/filter'
import ViewPresetConfigMap, {
  ViewPresetAttributeConfigMap,
} from '#configmaps/dashboard/view_preset'
import { raw } from '#utils/database'

import ViewPreset, { ViewPresetAttribute } from './view_preset.js'
import MediaSource from './media_source.js'
import Campaign from './campaign.js'

type SerializationContext = {
  viewPreset: ViewPreset
  viewPresetConfigMap: ViewPresetConfigMap
}

const computedMetricsQuery = 'computed_metrics_query'

const numberColumnOptions: Partial<ColumnOptions> = {
  consume: (v) => Number(v ?? 0),
  serialize: (v) => Number(v ?? 0),
}

@Metadata('model.authorize', AclModelName.AdMetric)
export default class AdMetric extends compose(
  BaseModel,
  DatabaseNaming,
  withSerializationContext<SerializationContext>()
) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare countryCode: string

  @column.date()
  declare date: DateTime

  @column()
  declare adId: string

  @column()
  declare campaignId: string

  @column(numberColumnOptions)
  adCostNonTaxAmount: number = 0

  @column(numberColumnOptions)
  installCount: number = 0

  @column(numberColumnOptions)
  impressionCount: number = 0

  @column(numberColumnOptions)
  clickCount: number = 0

  @column(numberColumnOptions)
  adRevGrossAmount: number = 0

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as AdMetric),
  })
  adRevNthDayGrossAmounts: Record<string, number> = {}

  @column(numberColumnOptions)
  activeUserCount: number = 0

  @column({ consume: (v) => Number(v) })
  dailyActiveUserCount: number = 0

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as AdMetric),
  })
  activeUserNthDayCounts: Record<string, number> = {}

  @column(numberColumnOptions)
  sessionCount: number = 0

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as AdMetric),
  })
  sessionNthDayCounts: Record<string, number> = {}

  /**
   * Computed virtual columns
   */
  @column(numberColumnOptions)
  roas: number = 0

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as AdMetric),
  })
  roasNthDayRates: Record<string, number> = {}

  @column(numberColumnOptions)
  retentionRate: number = 0

  @column({
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as AdMetric)
    },
  })
  retentionNthDayRates: Record<string, number> = {}

  @column(numberColumnOptions)
  cpi: number = 0

  @column(numberColumnOptions)
  ctr: number = 0

  @column(numberColumnOptions)
  cvr: number = 0

  @column(numberColumnOptions)
  cpc: number = 0

  @column(numberColumnOptions)
  ipm: number = 0

  @computed()
  get groupId() {
    return this.$extras['group_id']
  }

  @computed()
  get agencyId() {
    return this.$extras['agency_id']
  }

  static filter = createFilterScope(AdMetric)

  static selectKey = scope<typeof AdMetric, QueryScopeCallback<typeof AdMetric>>((query) => {
    query.select(
      ['countryCode', 'date', 'adId', 'campaignId'].map((col) =>
        AdMetric.columnName(col as any, AdMetric.table)
      )
    )
  })

  static selectAggregationPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      switch (name) {
        case 'adRevGrossAmount':
        case 'adCostNonTaxAmount':
        case 'impressionCount':
        case 'clickCount':
        case 'sessionCount':
        case 'installCount':
          query.sum(AdMetric.columnFullname(name), AdMetric.columnName(name))
          break

        case 'dailyActiveUserCount':
          query.select(
            raw`COALESCE(AVG(NULLIF(${AdMetric.columnName(name, computedMetricsQuery)}, 0)), 0) AS ${AdMetric.columnName(name)}`
          )
          break
        case 'roas':
          query.select(
            safeDivide(
              `SUM(${AdMetric.quote('adRevGrossAmount')})`,
              `SUM(${AdMetric.quote('adCostNonTaxAmount')})`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'retentionRate':
          query.select(
            safeDivide(
              `COALESCE(AVG(NULLIF(${AdMetric.columnName('dailyActiveUserCount', computedMetricsQuery)}, 0)), 0)`,
              `SUM(${AdMetric.quote('installCount')})`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'cpi':
          query.select(
            safeDivide(
              `SUM(${AdMetric.quote('adCostNonTaxAmount')})`,
              `SUM(${AdMetric.quote('installCount')})`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'ctr':
          query.select(
            safeDivide(
              `SUM(${AdMetric.quote('clickCount')})`,
              `SUM(${AdMetric.quote('impressionCount')})`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'cvr':
          query.select(
            safeDivide(
              `SUM(${AdMetric.quote('installCount')})`,
              `SUM(${AdMetric.quote('clickCount')})`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'cpc':
          query.select(
            safeDivide(
              `SUM(${AdMetric.quote('clickCount')})`,
              `SUM(${AdMetric.quote('impressionCount')})`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'ipm':
          query.select(
            safeDivide(
              `SUM(${AdMetric.quote('installCount')})`,
              `SUM(${AdMetric.quote('impressionCount')}) * 1000`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'adRevNthDayGrossAmounts':
        case 'sessionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`SUM((coalesce(${AdMetric.quote(name)}->>'${day}', '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case 'activeUserNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format(substituteAttributeTemplate, [day])}, 0)), 0) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case 'roasNthDayRates':
          cohortDays.forEach((day) => {
            query.select(
              safeDivide(
                `SUM((coalesce(${AdMetric.quote('adRevNthDayGrossAmounts')}->>'${day}', '0'))::float)`,
                `SUM(${AdMetric.quote('adCostNonTaxAmount')})`,
                format(substituteAttributeTemplate, [day])
              )
            )
          })
          break

        case 'retentionNthDayRates':
          cohortDays.forEach((day) => {
            query.select(
              raw`COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format(substituteAttributeTemplate, [day])}, 0)), 0) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
      }
    }
  )

  static selectPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      switch (name) {
        case 'adRevGrossAmount':
        case 'adCostNonTaxAmount':
        case 'impressionCount':
        case 'clickCount':
        case 'activeUserCount':
        case 'dailyActiveUserCount':
        case 'sessionCount':
        case 'installCount':
          query.select(`${AdMetric.quote(name)} AS ${AdMetric.columnName(name)}`)
          break

        case 'roas':
          query.select(
            safeDivide(
              `${AdMetric.quote('adRevGrossAmount')}::float`,
              AdMetric.quote('adCostNonTaxAmount'),
              AdMetric.columnName(name)
            )
          )
          break

        case 'retentionRate':
          query.select(
            safeDivide(
              `${AdMetric.quote('activeUserCount')}::float`,
              AdMetric.quote('installCount'),
              AdMetric.columnName(name)
            )
          )
          break

        case 'cpi':
          query.select(
            safeDivide(
              `${AdMetric.quote('adCostNonTaxAmount')}::float`,
              AdMetric.quote('installCount'),
              AdMetric.columnName(name)
            )
          )
          break

        case 'ctr':
          query.select(
            safeDivide(
              `${AdMetric.quote('impressionCount')}::float`,
              AdMetric.quote('clickCount'),
              AdMetric.columnName(name)
            )
          )
          break

        case 'cvr':
          query.select(
            safeDivide(
              `${AdMetric.quote('installCount')}::float`,
              AdMetric.quote('clickCount'),
              AdMetric.columnName(name)
            )
          )
          break

        case 'cpc':
          query.select(
            safeDivide(
              `${AdMetric.quote('clickCount')}::float`,
              AdMetric.quote('impressionCount'),
              AdMetric.columnName(name)
            )
          )
          break

        case 'ipm':
          query.select(
            safeDivide(
              `${AdMetric.quote('installCount')}::float`,
              `${AdMetric.quote('impressionCount')} * 1000`,
              AdMetric.columnName(name)
            )
          )
          break

        case 'adRevNthDayGrossAmounts':
        case 'sessionNthDayCounts':
        case 'activeUserNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`(coalesce(${AdMetric.quote(name)}->>'${day}', '0'))::float AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break

        case 'roasNthDayRates':
          cohortDays.forEach((day) => {
            query.select(
              safeDivide(
                `(coalesce(${AdMetric.quote('adRevNthDayGrossAmounts')}->>'${day}', '0'))::float`,
                AdMetric.quote('adCostNonTaxAmount'),
                format(substituteAttributeTemplate, [day])
              )
            )
          })
          break

        case 'retentionNthDayRates':
          cohortDays.forEach((day) => {
            query.select(
              safeDivide(
                `(coalesce(${AdMetric.quote('activeUserNthDayCounts')}->>'${day}', '0'))::float`,
                AdMetric.quote('installCount'),
                format(substituteAttributeTemplate, [day])
              )
            )
          })
          break
      }
    }
  )

  static withComputedMetricsJoin = scope(
    (
      query,
      {
        groupBy,
        gameId,
        viewPreset,
        attrToQueryAttr,
      }: {
        groupBy: string[]
        gameId: string
        viewPreset: ViewPreset
        attrToQueryAttr: Record<string, string>
      }
    ) => {
      const groupByFields = uniq(groupBy.concat('date'))
      const cohortAttrs = chain(viewPreset.attributes).filter('isCohort').value()
      const subQuery = AdMetric.query()
        .select(
          ...groupByFields.map((d) => attrToQueryAttr[d as keyof typeof attrToQueryAttr] || d)
        )
        .select(
          raw`SUM(COALESCE(${AdMetric.columnName('activeUserCount')}, 0)) AS ${AdMetric.columnName('dailyActiveUserCount')}`
        )

      cohortAttrs.forEach(({ name, cohortDays }) => {
        switch (name) {
          case 'activeUserNthDayCounts':
            cohortDays.forEach((day) => {
              subQuery.select(
                raw`SUM(coalesce(${AdMetric.quote(name)}->>'${day}', '0')::float) AS activeUserDay${day}`
              )
            })
            break

          case 'retentionNthDayRates':
            cohortDays.forEach((day) => {
              subQuery.select(
                safeDivide(
                  `SUM(coalesce(${AdMetric.quote('activeUserNthDayCounts')}->>'${day}', '0')::float)`,
                  `SUM(${AdMetric.quote('installCount')})`,
                  `retentionRateDay${day}`
                )
              )
            })
            break
        }
      })
      subQuery
        .innerJoin(
          Campaign.table,
          Campaign.columnFullname('id'),
          AdMetric.columnFullname('campaignId')
        )
        .innerJoin(
          MediaSource.table,
          MediaSource.columnFullname('id'),
          Campaign.columnFullname('mediaSourceId')
        )
        .where(Campaign.columnName('gameId', Campaign.table), gameId)
        .groupBy(groupByFields.map((d) => attrToQueryAttr[d as keyof typeof attrToQueryAttr] || d))

      query.joinRaw(
        raw`
          LEFT JOIN (${subQuery.toQuery()}) AS ${computedMetricsQuery}
          ON ${groupByFields
            .map(
              (field: any) =>
                `${attrToQueryAttr[field as keyof typeof attrToQueryAttr] || field} = ${computedMetricsQuery}.${snakeCase(field)}`
            )
            .join(' AND ')}
        `
      )
    }
  )
}

function safeDivide(dividend: string, divisor: string, quotient: string) {
  return raw`CASE WHEN ${divisor} = 0 THEN 0 ELSE ${dividend}::float / ${divisor} END AS ${quotient}`
}

function serializeCohortAttribute(_values: any, name: string, model: AdMetric) {
  if (!model.serializationContext) {
    return {}
  }

  const { viewPreset, viewPresetConfigMap } = model.serializationContext
  const attribute = viewPreset.attributes.find((a) => a.name === name)
  if (!attribute) {
    return {}
  }

  const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.get(name)
  if (!viewPresetAttributeConfigMap.isCohort) {
    throw new Error(`Attribute ${name} is not a cohort attribute`)
  }

  return Object.fromEntries(
    attribute.cohortDays.map((day) => {
      return [
        day.toString(),
        model.$extras[
          // database column name is case-insensitive so we must lowercase it to get the raw value
          format(viewPresetAttributeConfigMap.substituteAttributeTemplate, [day]).toLowerCase()
        ] || 0,
      ]
    })
  )
}
