import { DateTime } from 'luxon'
import { BaseModel, column, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class GameFirebaseMetric extends compose(BaseModel, DatabaseNaming) {
  static table = 'fct_firebase_metrics'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'store_id' })
  declare gameId: string

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'rr_d1' })
  declare retentionRateDay1: number

  @column({ columnName: 'rr_d3' })
  declare retentionRateDay3: number

  @column({ columnName: 'rr_d7' })
  declare retentionRateDay7: number

  @column({ columnName: 'avg_sessions' })
  declare averageSession: number

  @column({ columnName: 'avg_engagement_time' })
  declare playtime: number

  static date = scope((query, ...args: string[]) => {
    const op = args[0]
    const offset = Number(args[1])
    if (offset > 0) {
      return query.where('date', op, DateTime.now().plus({ days: offset }).toISODate())
    }

    return query.where(
      'date',
      op,
      DateTime.now()
        .minus({ days: Math.abs(offset) })
        .toISODate()
    )
  })
}
