import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { QueryScopeCallback } from '@adonisjs/lucid/types/model'

import Game from './game.js'
import AdAgency from './ad_agency.js'

export default class GameCreativeMetric extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'fct_creative_performance'

  @column({ isPrimary: true })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare gameId: Game['id']

  @column({ columnName: 'campaign_name' })
  declare campaign: string

  @column({ columnName: 'ad_group_name' })
  declare adGroup: string

  @column.date({ columnName: 'ad_group_start_date' })
  declare adGroupStartDate: DateTime

  @column({ columnName: 'ad_set' })
  declare adSet: string

  @column({ columnName: 'camp_target_roas', consume: (v) => Number(v) })
  declare targetRoasRate: number

  @column({ columnName: 'cost', consume: (v) => Number(v) })
  declare preTaxCostAmount: number

  @column({ columnName: 'clicks' })
  declare clickCount: number

  @column({ columnName: 'impressions', consume: (v) => Number(v) })
  declare impressionCount: number

  @column({ columnName: 'revenue', consume: (v) => Number(v) })
  declare grossRevenueAmount: number

  @column({ columnName: 'installs' })
  declare installCount: number

  @column({ columnName: 'editor' })
  declare editor: string

  @column({ columnName: 'playable' })
  declare isPlayable: boolean

  @column({ columnName: 'agency_id' })
  declare agencyId: AdAgency['id']

  @column({ columnName: 'roas_rate', consume: (v) => Number(v || 0) })
  declare roasRate: number

  @column({ columnName: 'clickthrough_rate', consume: (v) => Number(v || 0) })
  declare clickthroughRate: number

  @column({ columnName: 'conversion_rate', consume: (v) => Number(v || 0) })
  declare conversionRate: number

  @column({ columnName: 'cpi', consume: (v) => Number(v || 0) })
  declare cpi: number

  @belongsTo(() => Game, { localKey: 'storeId', foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @belongsTo(() => AdAgency, { foreignKey: 'agencyId' })
  declare agency: BelongsTo<typeof AdAgency>

  static default = scope<typeof GameCreativeMetric, QueryScopeCallback<typeof GameCreativeMetric>>(
    (query, gameId: Game['id']) => {
      const raw = query.client.raw

      const adGroupStartDates = GameCreativeMetric.query()
        .where('gameId', gameId)
        .groupBy('adGroup')
        .select('adGroup')
        .min(
          GameCreativeMetric.columnName('date'),
          GameCreativeMetric.columnName('adGroupStartDate')
        )

      query.from(
        GameCreativeMetric.query()
          .where('gameId', gameId)
          .joinRaw(
            `INNER JOIN (${adGroupStartDates.toQuery()}) agsd ON ${GameCreativeMetric.quote('adGroup')} = ${GameCreativeMetric.quote('adGroup', 'agsd')}`
          )
          .select(
            `${GameCreativeMetric.table}.*`,
            raw(
              `${GameCreativeMetric.quote('adGroupStartDate', 'agsd')} AS ${GameCreativeMetric.columnName('adGroupStartDate')}`
            ),
            raw(
              `CASE WHEN ${GameCreativeMetric.quote('installCount')} = 0 THEN 0 ELSE ${GameCreativeMetric.quote('preTaxCostAmount')} / ${GameCreativeMetric.quote('installCount')} END AS ${GameCreativeMetric.columnName('cpi')}`
            ),
            raw(
              `CASE WHEN ${GameCreativeMetric.quote('clickCount')} = 0 THEN 0 ELSE ${GameCreativeMetric.quote('installCount')}::numeric / ${GameCreativeMetric.quote('clickCount')} END AS ${GameCreativeMetric.columnName('conversionRate')}`
            ),
            raw(
              `CASE WHEN ${GameCreativeMetric.quote('impressionCount')} = 0 THEN 0 ELSE ${GameCreativeMetric.quote('clickCount')}::numeric / ${GameCreativeMetric.quote('impressionCount')} END AS ${GameCreativeMetric.columnName('clickthroughRate')}`
            ),
            raw(
              `CASE WHEN ${GameCreativeMetric.quote('preTaxCostAmount')} = 0 THEN 0 ELSE ${GameCreativeMetric.quote('grossRevenueAmount')} / ${GameCreativeMetric.quote('preTaxCostAmount')} END AS ${GameCreativeMetric.columnName('roasRate')}`
            )
          )
      )
    }
  )
}
