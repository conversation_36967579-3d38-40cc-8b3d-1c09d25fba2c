import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, computed, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming, withSerializationContext } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import format from 'string-template'

import AdType from '#models/v2/ad_type'
import { AdTypeCategory } from '#config/enums'
import ViewPresetConfigMap, {
  ViewPresetAttributeConfigMap,
} from '#configmaps/dashboard/view_preset'
import { safeDivide } from '#utils/math'

import Game from './game.js'
import Network from './network.js'
import ReleaseMetric from './release_metric.js'
import ViewPreset, { ViewPresetAttribute } from './view_preset.js'
import { createFilterScope } from './extensions/filter.js'

type SerializationContext = {
  parent: ReleaseMetric
  viewPreset: ViewPreset
  viewPresetConfigMap: ViewPresetConfigMap
}

export default class ReleaseAdMetric extends compose(
  BaseModel,
  DatabaseNaming,
  withSerializationContext<SerializationContext>()
) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare gameId: Game['id']

  @column.date()
  declare date: DateTime

  @column()
  declare networkId: Network['id']

  @column()
  declare adTypeId: AdType['id']

  @column()
  declare version: string

  @column()
  declare countryCode: string

  @column()
  impressionCount: number = 0

  @column()
  adRevGrossAmount: number = 0

  @belongsTo(() => Game, { localKey: 'storeId', foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @belongsTo(() => Network)
  declare network: BelongsTo<typeof Network>

  @belongsTo(() => AdType)
  declare adType: BelongsTo<typeof AdType>

  static filter = createFilterScope(ReleaseAdMetric)

  @computed()
  get adTypeCategory() {
    return this.$extras['category'] ?? AdTypeCategory.Unknown
  }

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as ReleaseAdMetric),
  })
  impressionNthDayCounts: Record<string, number> = {}

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as ReleaseAdMetric),
  })
  adRevNthDayGrossAmounts: Record<string, number> = {}

  @computed()
  get impressionCountPerActiveUser() {
    return safeDivide(this.impressionCount, this.serializationContext?.parent?.activeUserCount || 0)
  }

  @computed()
  get adRevGrossAmountPerActiveUser() {
    return safeDivide(
      this.adRevGrossAmount,
      this.serializationContext?.parent?.activeUserCount || 0
    )
  }

  static selectAggregationPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      const attrName = name.replace('ad_', '')
      const { raw } = query.client
      switch (attrName) {
        case 'impressionCount':
        case 'adRevGrossAmount':
          query.select(
            query.client.raw(
              `SUM(${ReleaseAdMetric.columnFullname(attrName)})::float AS ${ReleaseAdMetric.columnName(attrName)}`
            )
          )
          break
        case 'impressionNthDayCounts':
        case 'adRevNthDayGrossAmounts':
          cohortDays.forEach((day) => {
            query.select(
              raw(
                `SUM((coalesce(${ReleaseAdMetric.columnFullname(attrName)}->>'${day}', '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
              )
            )
          })
          break
        case 'adRevGrossAmountPerActiveUser':
          query.select(
            query.client.raw(
              `SUM(${ReleaseAdMetric.columnFullname('adRevGrossAmount')})::float AS ${ReleaseAdMetric.columnName('adRevGrossAmount')}`
            )
          )
          break

        case 'impressionCountPerActiveUser':
          query.select(
            query.client.raw(
              `SUM(${ReleaseAdMetric.columnFullname('impressionCount')})::float AS ${ReleaseAdMetric.columnName('impressionCount')}`
            )
          )
          break
        default:
          break
      }
    }
  )

  static selectPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      const attrName = name.replace('ad_', '')
      const { raw } = query.client
      switch (attrName) {
        case 'impressionNthDayCounts':
        case 'adRevNthDayGrossAmounts':
          cohortDays.forEach((day) => {
            query.select(
              raw(
                `SUM((coalesce(${ReleaseAdMetric.quote(attrName)}->>'${day}', '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
              )
            )
          })
          break
      }
    }
  )
}

function serializeCohortAttribute(_values: any, name: string, model: ReleaseAdMetric) {
  if (!model.serializationContext) {
    return {}
  }

  const { viewPreset, viewPresetConfigMap } = model.serializationContext
  const attribute = viewPreset.attributes.find((a) => a.name === name)
  if (!attribute) {
    return {}
  }

  const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.get(name)
  if (!viewPresetAttributeConfigMap.isCohort) {
    throw new Error(`Attribute ${name} is not a cohort attribute`)
  }

  return Object.fromEntries(
    attribute.cohortDays.map((day) => {
      return [
        day.toString(),
        model.$extras[
          format(viewPresetAttributeConfigMap.substituteAttributeTemplate, [day]).toLowerCase()
        ] || 0,
      ]
    })
  )
}
