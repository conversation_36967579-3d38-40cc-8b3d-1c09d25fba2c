import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import { AdNetworkCategory, AdTypeCategory } from '#config/enums'

export default class AdType extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare name: string

  @column()
  declare category: AdTypeCategory

  @column()
  declare networkCategory: AdNetworkCategory
}
