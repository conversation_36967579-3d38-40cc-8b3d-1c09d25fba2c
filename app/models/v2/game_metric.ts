import { BaseModel, belongsTo, column, computed, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { DateTime } from 'luxon'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { Metadata } from '@munkit/main'

import { safeDivide } from '#utils/math'
import GameInstallation from '#models/game_installation'
import GameSpend from '#models/game_spend'
import GameRevenue from '#models/game_revenue'
import GameFirebaseMetric from '#models/game_firebase_metric'
import GameMetricOverride from '#models/game_metric_override'
import Game from '#models/game'
import GameMetricMetadatum from '#models/game_metric_metadatum'
import { AclModelName } from '#config/enums'

@Metadata('model.authorize', AclModelName.GameMetricV2)
export default class GameMetric extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'

  @column()
  isAggregation: boolean = false

  @column.date()
  declare date: DateTime

  @column()
  declare gameId: string

  @belongsTo(() => Game)
  declare game: BelongsTo<typeof Game>

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare paidInstalls: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare organicInstalls: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare totalInstalls: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare cost: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare revenue: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare profit: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare dailyActiveUsers: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare retentionRateDay1: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare retentionRateDay3: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare retentionRateDay7: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare sessions: number

  @column({ consume: (v) => Number(v ?? 0) })
  @Metadata('attr.authorize')
  declare playtime: number

  @column()
  @Metadata('attr.authorize')
  declare uaNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare versionNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare productNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare monetNote: string | null

  @column()
  @Metadata('attr.authorize')
  declare note: string | null

  @column({ consume: (v) => Number(v ?? 0) })
  declare mmpCostAmount: number

  @column({ consume: (v) => Number(v ?? 0) })
  declare totalAgencyCost: number

  @Metadata('attr.authorize')
  @computed()
  get organicPercentage() {
    return safeDivide(this.organicInstalls, this.totalInstalls)
  }

  @Metadata('attr.authorize')
  @computed()
  get cpi() {
    return safeDivide(this.cost, this.totalInstalls)
  }

  @Metadata('attr.authorize')
  @computed()
  get roas() {
    return safeDivide(this.revenue, this.cost)
  }

  #id: string | null = null

  set id(value: string) {
    this.#id = value
  }

  @computed()
  get id() {
    return this.#id ? this.#id : `${this.date?.toFormat('yyyy-MM-dd')}-${this.gameId}`
  }

  static default = scope((query, ...gameIds: string[]) => {
    const db = query.client

    query.from(
      GameInstallation.query()
        .whereIn(GameInstallation.columnName('gameId', GameInstallation.table), gameIds)
        .joinRaw(
          `FULL OUTER JOIN (${GameSpend.query()
            .from(
              GameSpend.query()
                .withScopes((s) => s.withOverride(...gameIds))
                .as(GameSpend.table)
            )
            .groupBy(GameSpend.columnName('storeId'), GameSpend.columnName('date'))
            .select(GameSpend.columnName('storeId'), GameSpend.columnName('date'))
            .sum(GameSpend.columnName('totalAmount'), GameSpend.columnName('totalAmount'))
            .as(GameSpend.table)
            .toQuery()}) ${GameSpend.table} ON (${GameSpend.quote('date')} = ${GameInstallation.quote('date')} AND ${GameSpend.quote('storeId')} = ${GameInstallation.quote('gameId')})`
        )
        .joinRaw(
          `FULL OUTER JOIN (${GameRevenue.query()
            .from(
              GameRevenue.query()
                .withScopes((s) => s.selectNetRevenue())
                .where((q) => {
                  if (gameIds.length > 0) {
                    q.whereIn('storeId', gameIds)
                  }
                })
                .select('*')
            )
            .groupBy('storeId', 'date')
            .select('storeId', 'date')
            .max(
              GameRevenue.columnName('dailyActiveUserCount'),
              GameRevenue.columnName('dailyActiveUserCount')
            )
            .sum(
              GameRevenue.columnName('impressionCount'),
              GameRevenue.columnName('impressionCount')
            )
            .sum('net_revenue', GameRevenue.columnName('revenue'))
            .as(GameRevenue.table)
            .toQuery()}) ${GameRevenue.table} ON ${GameRevenue.quote('date')} = COALESCE(${GameSpend.quote('date')}, ${GameInstallation.quote('date')}) AND ${GameRevenue.quote('storeId')} = COALESCE(${GameSpend.quote('storeId')}, ${GameInstallation.quote('gameId')})`
        )
        .joinRaw(
          `FULL OUTER JOIN (${GameFirebaseMetric.query()
            .where((q) => {
              if (gameIds.length > 0) {
                q.whereIn('gameId', gameIds)
              }
            })
            .toQuery()}) AS ${GameFirebaseMetric.table} ON ${GameFirebaseMetric.quote('date')} = COALESCE(${GameSpend.quote('date')}, ${GameInstallation.quote('date')}, ${GameRevenue.quote('date')}) AND ${GameFirebaseMetric.quote('gameId')} = COALESCE(${GameSpend.quote('storeId')}, ${GameInstallation.quote('gameId')}, ${GameRevenue.quote('storeId')})`
        )
        .joinRaw(
          `LEFT JOIN ${GameMetricOverride.table} ON ${GameMetricOverride.quote('date')} = COALESCE(${GameInstallation.quote('date')}, ${GameRevenue.quote('date')}, ${GameSpend.quote('date')}, ${GameFirebaseMetric.quote('date')}) AND ${GameMetricOverride.quote('gameId')} = COALESCE(${GameInstallation.quote('gameId')}, ${GameRevenue.quote('storeId')}, ${GameSpend.quote('storeId')}, ${GameFirebaseMetric.quote('gameId')})`
        )
        .joinRaw(
          `LEFT JOIN ${GameMetricMetadatum.table} ON ${GameMetricMetadatum.quote('date')} = COALESCE(${GameInstallation.quote('date')}, ${GameRevenue.quote('date')}, ${GameSpend.quote('date')}, ${GameFirebaseMetric.quote('date')}) AND ${GameMetricMetadatum.quote('gameId')} = COALESCE(${GameInstallation.quote('gameId')}, ${GameRevenue.quote('storeId')}, ${GameSpend.quote('storeId')}, ${GameFirebaseMetric.quote('gameId')})`
        )
        .select(
          db.raw(
            `COALESCE(${GameInstallation.quote('date')}, ${GameRevenue.quote('date')}, ${GameSpend.quote('date')}, ${GameFirebaseMetric.quote('date')}) AS ${GameMetric.columnName('date')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameInstallation.quote('gameId')}, ${GameRevenue.quote('storeId')}, ${GameSpend.quote('storeId')}, ${GameFirebaseMetric.quote('gameId')}) AS ${GameMetric.columnName('gameId')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameInstallation.quote('paidInstalls')}, 0) AS ${GameMetric.columnName('paidInstalls')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameInstallation.quote('organicInstalls')}, 0) AS ${GameMetric.columnName('organicInstalls')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameInstallation.quote('organicInstalls')}, 0) + COALESCE(${GameInstallation.quote('paidInstalls')}, 0) as ${GameMetric.columnName('totalInstalls')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameMetricOverride.quote('cost')}, ${GameSpend.columnName('totalAmount')} + 0.0012 * ${GameInstallation.quote('paidInstalls')}, 0) AS ${GameMetric.columnName('cost')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameMetricOverride.quote('cost')}, ${GameSpend.columnName('totalAmount')} + 0.0012 * ${GameInstallation.quote('paidInstalls')}, 0) - 0.0012 * ${GameInstallation.quote('paidInstalls')} AS ${GameMetric.columnName('totalAgencyCost')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameMetricOverride.quote('revenue')}, ${GameRevenue.quote('revenue')}, 0) AS ${GameMetric.columnName('revenue')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameMetricOverride.quote('revenue')}, ${GameRevenue.quote('revenue')}, 0) - COALESCE(${GameMetricOverride.quote('cost')}, ${GameSpend.columnName('totalAmount')} + 0.0012 * ${GameInstallation.quote('paidInstalls')}, 0) as ${GameMetric.columnName('profit')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameRevenue.quote('dailyActiveUserCount')}, 0) as ${GameMetric.columnName('dailyActiveUsers')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameFirebaseMetric.quote('retentionRateDay1')}, 0) as ${GameMetric.columnName('retentionRateDay1')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameFirebaseMetric.quote('retentionRateDay3')}, 0) as ${GameMetric.columnName('retentionRateDay3')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameFirebaseMetric.quote('retentionRateDay7')}, 0) as ${GameMetric.columnName('retentionRateDay7')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameFirebaseMetric.quote('averageSession')}, 0) as ${GameMetric.columnName('sessions')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameFirebaseMetric.quote('playtime')}, 0) as ${GameMetric.columnName('playtime')}`
          )
        )
        .select(
          db.raw(
            `COALESCE(${GameInstallation.quote('paidInstalls')}, 0) * 0.0012 AS ${GameMetric.columnName('mmpCostAmount')}`
          )
        )
        .select(GameMetricMetadatum.columnName('uaNote'), GameMetric.columnName('uaNote'))
        .select(GameMetricMetadatum.columnName('versionNote'), GameMetric.columnName('versionNote'))
        .select(GameMetricMetadatum.columnName('productNote'), GameMetric.columnName('productNote'))
        .select(GameMetricMetadatum.columnName('monetNote'), GameMetric.columnName('monetNote'))
        .select(GameMetricMetadatum.columnName('note'), GameMetric.columnName('note'))
    )
  })
}
