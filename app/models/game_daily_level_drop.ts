import { DateTime } from 'luxon'
import { belongsTo, column, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import Game from './game.js'
import GameLevelDrop from './game_level_drop.js'

export default class GameDailyLevelDrop extends GameLevelDrop {
  static table = 'fct_level_drop'
  static connection = 'dataWarehouse'

  @belongsTo(() => Game, { foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column.date({ columnName: 'install_date' })
  declare installDate: DateTime

  static default = scope((q) => {
    q.where('isValidSemver', true)
  })
}
