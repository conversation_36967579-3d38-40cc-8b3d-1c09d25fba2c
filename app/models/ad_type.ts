import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class AdType extends compose(BaseModel, DatabaseNaming) {
  static table = 'md_ads_type'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'show_order' })
  declare order: number

  @column({ columnName: 'ads_type' })
  declare name: string
}
