import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { compose } from '@adonisjs/core/helpers'

import { safeDivide } from '#utils/math'

import Game from './game.js'

export default class GameRetentionRate extends compose(BaseModel, DatabaseNaming) {
  static table = 'fct_version_rr'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare gameId: Game['id']

  @belongsTo(() => Game, { foreignKey: 'gameId', localKey: 'storeId' })
  declare game: BelongsTo<typeof Game>

  @column({ columnName: 'version' })
  declare version: string

  @column({ columnName: 'new_users', consume: (value) => Number(value) })
  declare newUsers: number

  @column({ columnName: 'users_d1', consume: (value) => Number(value) })
  declare usersDay1: number

  @column({ columnName: 'users_d2', consume: (value) => Number(value) })
  declare usersDay2: number

  @column({ columnName: 'users_d3', consume: (value) => Number(value) })
  declare usersDay3: number

  @column({ columnName: 'users_d4', consume: (value) => Number(value) })
  declare usersDay4: number

  @column({ columnName: 'users_d5', consume: (value) => Number(value) })
  declare usersDay5: number

  @column({ columnName: 'users_d6', consume: (value) => Number(value) })
  declare usersDay6: number

  @column({ columnName: 'users_d7', consume: (value) => Number(value) })
  declare usersDay7: number

  @column({ columnName: 'flag_version' })
  declare isValidSemver: boolean

  @computed()
  get day1() {
    return safeDivide(this.usersDay1, this.newUsers)
  }

  @computed()
  get day2() {
    return safeDivide(this.usersDay2, this.newUsers)
  }

  @computed()
  get day3() {
    return safeDivide(this.usersDay3, this.newUsers)
  }

  @computed()
  get day4() {
    return safeDivide(this.usersDay4, this.newUsers)
  }

  @computed()
  get day5() {
    return safeDivide(this.usersDay5, this.newUsers)
  }

  @computed()
  get day6() {
    return safeDivide(this.usersDay6, this.newUsers)
  }

  @computed()
  get day7() {
    return safeDivide(this.usersDay7, this.newUsers)
  }
}
