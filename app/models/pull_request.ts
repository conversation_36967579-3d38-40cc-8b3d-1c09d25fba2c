import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class PullRequest extends BaseModel {
  @column({ isPrimary: true })
  declare id: string

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
  })
  declare reviewers: string[]

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
  })
  requestedReviewers: string[] = []

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
