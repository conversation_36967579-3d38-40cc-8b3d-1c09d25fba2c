import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming, withSerializationContext } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { BigNumber } from 'bignumber.js'
import format from 'string-template'
import { QueryScopeCallback } from '@adonisjs/lucid/types/model'
import { uniq } from 'lodash-es'

import ViewPresetConfigMap, {
  ViewPresetAttributeConfigMap,
} from '#configmaps/dashboard/view_preset'
import { raw } from '#utils/database'
import ReleaseAdMetric from '#models/release_ad_metric'

import Game from './game.js'
import ViewPreset, { ViewPresetAttribute } from './view_preset.js'
import { createFilterScope } from './extensions/filter.js'
type SerializationContext = {
  viewPreset: ViewPreset
  viewPresetConfigMap: ViewPresetConfigMap
}

const computedMetricsQuery = 'computed_metrics_query'
export default class ReleaseMetric extends compose(
  BaseModel,
  DatabaseNaming,
  withSerializationContext<SerializationContext>()
) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare gameId: Game['id']

  @column.date()
  declare date: DateTime

  @column()
  declare version: string

  @column()
  declare countryCode: string

  @column({ consume: (v) => Number(v) })
  installCount: number = 0

  @column({ consume: (v) => Number(v) })
  activeUserCount: number = 0

  @column({ consume: (v) => Number(v) })
  sessionCount: number = 0

  @column({
    consume: (v) => BigInt(new BigNumber(v).integerValue().toString()),
    serialize: (v) => v.toString(),
  })
  playtimeMsec: BigInt = BigInt(0)

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as ReleaseMetric),
  })
  activeUserNthDayCounts: Record<string, number> = {}

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as ReleaseMetric),
  })
  sessionNthDayCounts: Record<string, number> = {}

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as ReleaseMetric),
  })
  playtimeNthDayMsecs: Record<string, number> = {}

  /**
   * Computed columns
   */
  @column({ consume: (v) => Number(v) })
  sessionCountPerActiveUser: number = 0

  @column({ consume: (v) => Number(v) })
  dailyActiveUserCount: number = 0

  @column({ consume: (v) => Number(v) })
  retentionRate: number = 0

  @column({ consume: (v) => Number(v) })
  adRevGrossAmount: number = 0

  @column({ consume: (v) => Number(v) })
  impressionCount: number = 0

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as ReleaseMetric),
  })
  lifetimeNthDayValues: Record<string, number> = {}

  @column({ consume: (v) => Number(v) })
  adRevGrossAmountPerActiveUser: number = 0

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as ReleaseMetric)
    },
  })
  adRevNthDayGrossAmounts: Record<string, number> = {}

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as ReleaseMetric)
    },
  })
  impressionNthDayCounts: Record<string, number> = {}

  @column({ consume: (v) => Number(v) })
  impressionCountPerActiveUser: number = 0

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as ReleaseMetric)
    },
  })
  retentionNthDayRates: Record<string, number> = {}

  @belongsTo(() => Game, { localKey: 'storeId', foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>
  static filter = createFilterScope(ReleaseMetric)

  static joinSumAdMetric = scope(
    (
      query,
      viewPreset: ViewPreset,
      viewPresetConfigMap: ViewPresetConfigMap,
      filters: any,
      gameId: string
    ) => {
      const adMetricQuery = ReleaseAdMetric.query()
        .groupBy('gameId', 'date', 'countryCode', 'version')
        .select('gameId', 'date', 'countryCode', 'version')
        .sum(
          ReleaseAdMetric.columnFullname('adRevGrossAmount'),
          ReleaseAdMetric.columnName('adRevGrossAmount')
        )
        .sum(
          ReleaseAdMetric.columnFullname('impressionCount'),
          ReleaseAdMetric.columnName('impressionCount')
        )
      const colMapping = {
        date: ReleaseAdMetric.columnFullname('date'),
        version: ReleaseAdMetric.columnFullname('version'),
        countryCode: ReleaseAdMetric.columnFullname('countryCode'),
      }
      adMetricQuery.where(ReleaseAdMetric.columnFullname('gameId'), gameId).withScopes((s) => {
        s.filter(filters as any, colMapping)
      })
      viewPreset.attributes.forEach((vpa) => {
        const name = vpa.name
        switch (name) {
          case 'impressionNthDayCounts':
          case 'adRevNthDayGrossAmounts':
            vpa.cohortDays.forEach((day) => {
              adMetricQuery.select(
                raw`SUM((coalesce(${ReleaseAdMetric.columnFullname(name)}->>'${day}', '0'))::float) AS ${format(viewPresetConfigMap.attributes.get(name).substituteAttributeTemplate, [day])}`
              )
            })
            break
          default:
            break
        }
      })
      query.joinRaw(
        raw`
        LEFT JOIN (${adMetricQuery.toQuery()}) AS ${ReleaseAdMetric.table}
          ON ${ReleaseMetric.columnFullname('gameId')} = ${ReleaseAdMetric.columnFullname('gameId')}
          AND ${ReleaseMetric.columnFullname('date')} = ${ReleaseAdMetric.columnFullname('date')}
          AND ${ReleaseMetric.columnFullname('countryCode')} = ${ReleaseAdMetric.columnFullname('countryCode')}
          AND ${ReleaseMetric.columnFullname('version')} = ${ReleaseAdMetric.columnFullname('version')}
      `
      )
    }
  )

  static selectAggregationPreset = scope<
    typeof ReleaseMetric,
    QueryScopeCallback<typeof ReleaseMetric>
  >(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      switch (true) {
        case name === 'impressionCount':
        case name === 'adRevGrossAmount':
          query.select(
            raw`SUM(COALESCE(${ReleaseAdMetric.columnFullname(name)}, 0)) AS ${ReleaseMetric.columnName(name)}`
          )
          break
        case name === 'adRevGrossAmountPerActiveUser':
          query.select(
            this.createSafeDivision(
              `SUM(COALESCE(${ReleaseAdMetric.columnFullname('adRevGrossAmount')}, 0))`,
              `SUM(COALESCE(${ReleaseMetric.columnName('activeUserCount')}, 0))`,
              ReleaseMetric.columnName(name)
            )
          )
          break

        case name === 'impressionCountPerActiveUser':
          query.select(
            this.createSafeDivision(
              `SUM(COALESCE(${ReleaseAdMetric.columnName('impressionCount')}, 0))`,
              `SUM(COALESCE(${ReleaseMetric.columnName('activeUserCount')}, 0))`,
              ReleaseMetric.columnName(name)
            )
          )
          break
        case name === 'adRevNthDayGrossAmounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`SUM((coalesce(${format(substituteAttributeTemplate, [day])}, '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'impressionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`SUM((coalesce(${format(substituteAttributeTemplate, [day])}, '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'dailyActiveUserCount':
          query.select(
            raw`COALESCE(AVG(NULLIF(${ReleaseMetric.columnName(name, computedMetricsQuery)}, 0)), 0) AS ${ReleaseMetric.columnName(name)}`
          )
          break
        case name === 'activeUserCount':
          query.sum(
            ReleaseMetric.columnFullname('activeUserCount'),
            ReleaseMetric.columnName('activeUserCount')
          )
          break

        case name === 'sessionCount':
        case name === 'installCount':
          query.sum(ReleaseMetric.columnFullname(name), ReleaseMetric.columnName(name))
          break

        case name === 'playtimeMsec':
          query.select(
            raw`COALESCE(AVG(NULLIF(${ReleaseMetric.columnName(name, computedMetricsQuery)}, 0)), 0 ) AS ${ReleaseMetric.columnName(name)}`
          )
          break

        case name === 'activeUserNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format(substituteAttributeTemplate, [day])}, 0)), 0) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'sessionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`SUM((coalesce(${ReleaseMetric.quote(name)}->>'${day}', '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'playtimeNthDayMsecs':
          cohortDays.forEach((day) => {
            query.select(
              raw`COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format(substituteAttributeTemplate, [day])}, 0)), 0) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'sessionCountPerActiveUser':
          query.select(
            this.createSafeDivision(
              `SUM(${this.quote('sessionCount')})`,
              `COALESCE(AVG(NULLIF(${this.quote('dailyActiveUserCount', computedMetricsQuery)}, 0)), 0)`,
              this.columnName(name)
            )
          )
          break

        case name === 'retentionRate':
          query.select(
            this.createSafeDivision(
              `COALESCE(AVG(NULLIF(${this.quote('dailyActiveUserCount', computedMetricsQuery)}, 0)), 0)`,
              `SUM(${this.quote('installCount')})`,
              this.columnName(name)
            )
          )
          break
        case name === 'retentionNthDayRates':
          cohortDays.forEach((day) => {
            query.select(
              raw`COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format(substituteAttributeTemplate, [day])}, 0)), 0) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'lifetimeNthDayValues':
          cohortDays.forEach((day) => {
            const activeUserDn = `COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format('activeUserDay{0}', [day])}, 0)), 0)`
            const rrDn = `${activeUserDn} / SUM(${this.quote('installCount')})`
            const lifetimeDn = `(CASE WHEN SUM(${this.quote('installCount')}) = 0 OR ${rrDn} >= 1 THEN 0 ELSE 1.0 / (1.0 - (${rrDn})) END)`
            const arpdauDn = `(SUM(${ReleaseAdMetric.columnFullname('adRevGrossAmount')})::float / SUM(COALESCE(${ReleaseMetric.columnName('activeUserCount')}, 0)))`
            query.select(
              raw`
                CASE
                  WHEN SUM(${this.quote('installCount')}) = 0 OR ${rrDn} >= 1 OR ${activeUserDn} = 0
                    THEN 0
                  ELSE 
                    COALESCE(${arpdauDn} * ${lifetimeDn}, 0)
                END AS ${format(substituteAttributeTemplate, [day])}
              `
            )
          })
          break
        default:
          break
      }
    }
  )

  static selectPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      switch (true) {
        case name === 'activeUserCount':
          query.select(
            raw`${ReleaseMetric.columnFullname('activeUserCount')} AS ${ReleaseMetric.columnName('activeUserCount')}`
          )
          break

        case name === 'sessionCount':
        case name === 'installCount':
        case name === 'playtimeMsec':
          query.select(
            raw`${ReleaseMetric.columnFullname(name)} AS ${ReleaseMetric.columnName(name)}`
          )
          break
        case name === 'adRevGrossAmount':
        case name === 'impressionCount':
          query.select(
            raw`${ReleaseAdMetric.columnFullname(name)} AS ${ReleaseMetric.columnName(name)}`
          )
          break

        case name === 'activeUserNthDayCounts':
        case name === 'playtimeNthDayMsecs':
        case name === 'sessionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`(coalesce(${ReleaseMetric.quote(name)}->>'${day}', '0'))::float AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'adRevNthDayGrossAmounts':
        case name === 'impressionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`(coalesce(${ReleaseAdMetric.columnFullname(name)}->>'${day}', '0'))::float AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case name === 'sessionCountPerActiveUser':
          query.select(
            this.createSafeDivision(
              `${ReleaseMetric.columnFullname('sessionCount')}::float`,
              ReleaseMetric.columnName('dailyActiveUserCount', computedMetricsQuery),
              ReleaseMetric.columnName('sessionCountPerActiveUser')
            )
          )
          break

        case name === 'retentionRate':
          query.select(
            this.createSafeDivision(
              ReleaseMetric.columnName('dailyActiveUserCount', computedMetricsQuery),
              ReleaseMetric.columnFullname('installCount'),
              ReleaseMetric.columnName('retentionRate')
            )
          )
          break
        default:
          break
      }
    }
  )

  private static createSafeDivision(
    numerator: string,
    denominator: string,
    columnName: string
  ): any {
    return raw`
        CASE
          WHEN ${denominator} = 0
            THEN 0
          ELSE ${numerator}::float / ${denominator}
        END AS ${columnName}
      `
  }

  static withComputedMetricsJoin = scope(
    (
      query,
      {
        groupBy,
        filters,
        gameId,
        viewPreset,
        viewPresetConfigMap,
      }: {
        groupBy: string[]
        filters: any
        gameId: string
        viewPreset: ViewPreset
        viewPresetConfigMap: ViewPresetConfigMap
      }
    ) => {
      const groupByFields = uniq(groupBy.concat('date'))
      const subQuery = ReleaseMetric.query()
        .select(groupByFields)
        .select(
          raw`
            SUM(COALESCE(${ReleaseMetric.columnName('activeUserCount')}, 0)) AS ${ReleaseMetric.columnName('dailyActiveUserCount')}
          `
        )
        .select(
          this.createSafeDivision(
            `SUM(${ReleaseMetric.columnName('playtimeMsec')})`,
            `SUM(${ReleaseMetric.columnName('activeUserCount')})`,
            ReleaseMetric.columnName('playtimeMsec')
          )
        )
      viewPreset.attributes.forEach((vpa) => {
        const name = vpa.name
        switch (name) {
          case 'activeUserNthDayCounts':
            vpa.cohortDays.forEach((day) => {
              subQuery.select(
                raw`SUM(coalesce(${ReleaseMetric.quote(name)}->>'${day}', '0')::float) AS activeUserDay${day}`
              )
            })
            break
          case 'playtimeNthDayMsecs':
            vpa.cohortDays.forEach((day) => {
              subQuery.select(
                this.createSafeDivision(
                  `SUM(coalesce(${ReleaseMetric.quote(name)}->>'${day}', '0')::float)`,
                  `SUM(coalesce(${ReleaseMetric.quote('activeUserNthDayCounts')}->>'${day}', '0')::float)`,
                  format(viewPresetConfigMap.attributes.get(name).substituteAttributeTemplate, [
                    day,
                  ])
                )
              )
            })
            break
          case 'retentionNthDayRates':
            vpa.cohortDays.forEach((day) => {
              subQuery.select(
                this.createSafeDivision(
                  `SUM(coalesce(${ReleaseMetric.quote('activeUserNthDayCounts')}->>'${day}', '0')::float)`,
                  `SUM(${ReleaseMetric.quote('installCount')})`,
                  format(viewPresetConfigMap.attributes.get(name).substituteAttributeTemplate, [
                    day,
                  ])
                )
              )
            })
            break
        }
      })
      const colMapping = {
        date: ReleaseMetric.columnFullname('date'),
        version: ReleaseMetric.columnFullname('version'),
        countryCode: ReleaseMetric.columnFullname('countryCode'),
      }
      subQuery
        .where(ReleaseMetric.columnFullname('gameId'), gameId)
        .withScopes((s) => {
          s.filter(filters as any, colMapping)
        })
        .groupBy(groupByFields.map((col: any) => ReleaseMetric.columnFullname(col)))

      query.joinRaw(
        raw`
          LEFT JOIN (${subQuery.toQuery()}) AS ${computedMetricsQuery}
            ON ${groupByFields
              .map(
                (col: any) =>
                  `${ReleaseMetric.columnFullname(col)} = ${computedMetricsQuery}.${ReleaseMetric.columnName(col)}`
              )
              .join(' AND ')}
        `
      )
    }
  )
}

function serializeCohortAttribute(_values: any, name: string, model: ReleaseMetric) {
  if (!model.serializationContext) {
    return {}
  }

  const { viewPreset, viewPresetConfigMap } = model.serializationContext
  const attribute = viewPreset.attributes.find((a) => a.name === name)
  if (!attribute) {
    return {}
  }

  const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.get(name)
  if (!viewPresetAttributeConfigMap.isCohort) {
    throw new Error(`Attribute ${name} is not a cohort attribute`)
  }

  return Object.fromEntries(
    attribute.cohortDays.map((day) => {
      return [
        day.toString(),
        model.$extras[
          // database column name is case-insensitive so we must lowercase it to get the raw value
          format(viewPresetAttributeConfigMap.substituteAttributeTemplate, [day]).toLowerCase()
        ] || 0,
      ]
    })
  )
}
