import { BaseModel, column } from '@adonisjs/lucid/orm'

import { RedisStringKey } from '#services/redis/redis_service'

export default class ConfigMapRevision extends BaseModel {
  @column()
  declare scope: string

  @column()
  declare sheetToCsv: Record<string, string>

  @column()
  declare checksum: string
}

export class ConfigMapRevisionKey extends RedisStringKey {
  constructor(scope: string) {
    super(`config_map_revision:${scope}:data`)
  }
}

export class ConfigMapRevisionChecksumKey extends RedisStringKey {
  constructor(scope: string) {
    super(`config_map_revision:${scope}:checksum`)
  }
}
