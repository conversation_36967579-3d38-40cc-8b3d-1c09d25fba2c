import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class SnapshotCursor extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare table: string

  @column()
  declare connection: string

  @column()
  declare current: string | null

  @column.dateTime()
  declare finishedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
