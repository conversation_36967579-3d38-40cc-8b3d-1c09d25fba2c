import { BaseModel, belongsTo, column, computed } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { compose } from '@adonisjs/core/helpers'

import { GamePlatform } from '#config/enums'

import GameStudio from './game_studio.js'

export default class Game extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'

  static table = 'md_app_information'

  @column({ columnName: 'store_id', isPrimary: true })
  declare storeId: string

  @column({ columnName: 'package_name' })
  declare packageName: string

  @column({ columnName: 'platform' })
  declare platform: GamePlatform

  @column({ columnName: 'application_name' })
  declare name: string

  @column({ columnName: 'partner_id' })
  declare studioId: GameStudio['id']

  @column({ columnName: 'is_active' })
  declare isActive: boolean

  @column({ columnName: 'project_id' })
  declare googleCloudProjectId: string

  @column({ columnName: 'properties_id' })
  declare googleAnalyticsPropertyId: string

  @column({ columnName: 'is_inhouse' })
  declare isInhouse: boolean

  @belongsTo(() => GameStudio, { foreignKey: 'studioId' })
  declare studio: BelongsTo<typeof GameStudio>

  @computed()
  get id() {
    return this.storeId
  }
}
