import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class AdNetwork extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'md_ads_network'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'network_name' })
  declare name: string
}
