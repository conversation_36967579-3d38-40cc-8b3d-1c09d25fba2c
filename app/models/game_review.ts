import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

import Game from './game.js'

export default class GameReview extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare gameId: Game['id']

  @column.date()
  declare date: DateTime

  @column()
  declare marketingNote: string

  @column()
  declare productNote: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
