import { DateTime } from 'luxon'
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import type { HasMany } from '@adonisjs/lucid/types/relations'

import { GamePlatform, GameReleaseProposalStatus } from '#config/enums'

import GameReleaseApproval from './game_release_approval.js'

export class AndroidExtra extends BaseModel {
  @column()
  declare versionCode: number

  @column()
  declare symbolsDownloadUrl: string

  @column()
  platform: GamePlatform.Android = GamePlatform.Android
}

export class IOSExtra extends BaseModel {
  @column()
  platform: GamePlatform.iOS = GamePlatform.iOS
}

export default class GameReleaseProposal extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare repository: string

  @column()
  declare revision: string

  @column()
  declare semver: string

  @column()
  declare installableDownloadUrl: string

  @column()
  declare platform: GamePlatform

  @column()
  declare status: GameReleaseProposalStatus

  @column()
  declare changelog?: string

  @column({
    prepare: (value) => JSON.stringify(value['prepareForAdapter'](value.$attributes)),
    consume: (value, _, model) => {
      switch (model.$attributes['platform']) {
        case GamePlatform.Android:
        default:
          return AndroidExtra.$createFromAdapterResult(value)
      }
    },
  })
  declare extra: AndroidExtra | IOSExtra

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @hasMany(() => GameReleaseApproval, { foreignKey: 'proposalId' })
  declare approvals: HasMany<typeof GameReleaseApproval>
}
