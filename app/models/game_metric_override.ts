import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { JournalAttribute, JournalModel, withJournal } from '@mirai-game-studio/adonis-sdk/journal'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import GameMetricOverrideJournal from './game_metric_override_journal.js'

@JournalModel(GameMetricOverrideJournal)
export default class GameMetricOverride extends compose(
  BaseModel,
  withJournal<GameMetricOverrideJournal>(),
  DatabaseNaming
) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column.date()
  declare date: DateTime

  @column()
  declare gameId: string

  @column()
  @JournalAttribute()
  declare revenue: number | null

  @column()
  @JournalAttribute()
  declare cost: number | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
