import { BaseModel, belongsTo, column, computed, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming, withSerializationContext } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import format from 'string-template'

import { AdTypeCategory } from '#config/enums'
import { safeDivide } from '#utils/math'
import ViewPresetConfigMap, {
  ViewPresetAttributeConfigMap,
} from '#configmaps/dashboard/view_preset'

import FirebaseVersionVariant from './firebase_version_variant.js'
import Network from './network.js'
import Mediation from './mediation.js'
import AdType from './v2/ad_type.js'
import FirebaseMetric from './firebase_metric.js'
import ViewPreset, { ViewPresetAttribute } from './view_preset.js'
import { createFilterScope } from './extensions/filter.js'

type SerializationContext = {
  parent: FirebaseMetric
  viewPreset: ViewPreset
  viewPresetConfigMap: ViewPresetConfigMap
}

export default class FirebaseAdMetric extends compose(
  BaseModel,
  DatabaseNaming,
  withSerializationContext<SerializationContext>()
) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: string

  @column.date()
  declare date: DateTime

  @column()
  declare countryCode: string

  @column()
  declare version: string

  @column()
  declare variantId: FirebaseVersionVariant['id']

  @belongsTo(() => FirebaseVersionVariant, { foreignKey: 'variantId' })
  declare variant: BelongsTo<typeof FirebaseVersionVariant>

  @column()
  declare adTypeId: AdType['id']

  @belongsTo(() => AdType, { foreignKey: 'adTypeId' })
  declare adType: BelongsTo<typeof AdType>

  @column()
  declare networkId: Network['id']

  @belongsTo(() => Network, { foreignKey: 'networkId' })
  declare network: BelongsTo<typeof Network>

  @column()
  declare mediationId: Mediation['id']

  @belongsTo(() => Mediation, { foreignKey: 'mediationId' })
  declare mediation: BelongsTo<typeof Mediation>

  @column()
  adRevGrossAmount: number = 0

  @column()
  impressionCount: number = 0

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as FirebaseAdMetric),
  })
  impressionNthDayCounts: Record<string, number> = {}

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as FirebaseAdMetric),
  })
  adRevNthDayGrossAmounts: Record<string, number> = {}

  @computed()
  get adRevGrossAmountPerActiveUser() {
    return safeDivide(
      this.adRevGrossAmount,
      this.serializationContext?.parent?.activeUserCount || 0
    )
  }

  @computed()
  get impressionCountPerActiveUser() {
    return safeDivide(this.impressionCount, this.serializationContext?.parent?.activeUserCount || 0)
  }

  @computed()
  get adTypeCategory() {
    return this.$extras['category'] ?? AdTypeCategory.Unknown
  }

  static filter = createFilterScope(FirebaseAdMetric)

  static selectAggregationPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      const attrName = name.replace('ad_', '')
      const { raw } = query.client
      switch (attrName) {
        case 'adRevGrossAmount':
        case 'impressionCount':
          query.select(
            raw(
              `SUM(${FirebaseAdMetric.columnFullname(attrName)})::float AS ${FirebaseAdMetric.columnName(attrName)}`
            )
          )
          break
        case 'impressionNthDayCounts':
        case 'adRevNthDayGrossAmounts':
          cohortDays.forEach((day) => {
            query.select(
              raw(
                `SUM((coalesce(${FirebaseAdMetric.columnFullname(attrName)}->>'${day}', '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
              )
            )
          })
          break
        case 'adRevGrossAmountPerActiveUser':
          query.select(
            raw(
              `SUM(${FirebaseAdMetric.quote('adRevGrossAmount')})::float AS ${FirebaseAdMetric.columnName('adRevGrossAmount')}`
            )
          )
          break

        case 'impressionCountPerActiveUser':
          query.select(
            raw(
              `SUM(${FirebaseAdMetric.columnFullname('impressionCount')})::float AS ${FirebaseAdMetric.columnName('impressionCount')}`
            )
          )
          break
        default:
          break
      }
    }
  )
}

function serializeCohortAttribute(_values: any, name: string, model: FirebaseAdMetric) {
  if (!model.serializationContext) {
    return {}
  }

  const { viewPreset, viewPresetConfigMap } = model.serializationContext
  const attribute = viewPreset.attributes.find((a) => a.name === name)
  if (!attribute) {
    return {}
  }

  const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.get(name)
  if (!viewPresetAttributeConfigMap.isCohort) {
    throw new Error(`Attribute ${name} is not a cohort attribute`)
  }

  return Object.fromEntries(
    attribute.cohortDays.map((day) => {
      return [
        day.toString(),
        model.$extras[
          format(viewPresetAttributeConfigMap.substituteAttributeTemplate, [day]).toLowerCase()
        ] || 0,
      ]
    })
  )
}
