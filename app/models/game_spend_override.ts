import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import GameSpend from './game_spend.js'

export default class GameSpendOverride extends compose(BaseModel, DatabaseNaming) {
  static table = 'game_cost_overrides'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'cost_id' })
  declare spendId: number

  @belongsTo(() => GameSpend, { foreignKey: 'spendId' })
  declare spend: BelongsTo<typeof GameSpend>

  @column({ columnName: 'cost' })
  declare preTaxAmount: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
