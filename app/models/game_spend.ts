import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import { GameSpendType } from '#config/enums'

import GameMetric from './game_metric.js'
import Game from './game.js'

export default class GameSpend extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'fct_cost'

  @column({ isPrimary: true, columnName: 'id' })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare storeId: string

  @column({ columnName: 'agency_id' })
  declare networkId: number

  @column({ columnName: 'cost', consume: (v) => Number(v ?? 0) })
  declare preTaxAmount: number

  @column({ columnName: 'total_amount', consume: (v) => Number(v ?? 0) })
  declare totalAmount: number

  @column({ columnName: 'tax', serializeAs: 'taxAmount' })
  declare tax: number

  @column({ columnName: 'flag_is_cal' })
  declare type: GameSpendType

  @computed({ serializeAs: 'mmpAmount' })
  get mmp() {
    return Number(this.$extras['mmp'] || 0)
  }

  @belongsTo(() => Game, { foreignKey: 'storeId', localKey: 'storeId' })
  declare game: BelongsTo<typeof Game>

  static withOverride = scope((query, ...gameIds: string[]) => {
    query.from(
      GameSpend.query()
        .select(
          this.columnName('id', this.table),
          this.columnName('date', this.table),
          this.columnName('networkId', this.table),
          this.columnName('storeId', this.table),
          this.columnName('tax', this.table),
          this.columnFullname('preTaxAmount'),
          query.client.raw(
            `${this.quote('preTaxAmount')} * (1 + "${this.columnName('tax')}") AS ${this.columnName('totalAmount')}`
          ),
          query.client.raw(`${this.quote('type')} AS ${this.columnName('type')}`)
        )
        .where((q) => {
          if (gameIds.length) {
            q.whereIn(this.columnName('storeId', this.table), gameIds)
          }
        })
        .as(GameSpend.table)
    )
  })

  static withAggregation = scope((query) => {
    query
      .from(
        this.query()
          .withScopes((q) => q.withOverride())
          .leftJoin(GameMetric.table, (q) => {
            q.on(
              GameMetric.columnName('storeId', GameMetric.table),
              '=',
              GameSpend.columnName('storeId', GameSpend.table)
            ).andOn(
              GameMetric.columnName('date', GameMetric.table),
              '=',
              GameSpend.columnName('date', GameSpend.table)
            )
          })
          .select(
            query.client.raw(
              `CASE WHEN ${GameMetric.columnName('paidInstalls')} IS NULL THEN 0 ELSE ${GameMetric.columnName('paidInstalls')} * ?::float END as mmp`,
              [0.0012]
            )
          )
          .select(`${GameSpend.table}.*`)
          .as(GameSpend.table)
      )
      .groupBy('date')
      .sum(this.columnName('totalAmount'), this.columnName('totalAmount'))
      .sum(this.columnName('preTaxAmount'), this.columnName('preTaxAmount'))
      .avg('mmp', 'mmp')
  })

  static date = scope((query, ...args: string[]) => {
    const op = args[0]
    const offset = Number(args[1])
    if (offset > 0) {
      return query.where('date', op, DateTime.now().plus({ days: offset }).toISODate())
    }

    return query.where(
      'date',
      op,
      DateTime.now()
        .minus({ days: Math.abs(offset) })
        .toISODate()
    )
  })
}
