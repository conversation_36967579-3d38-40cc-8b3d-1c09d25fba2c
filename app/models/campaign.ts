import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import Game from './game.js'

export default class Campaign extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'campaigns'

  @column({ isPrimary: true })
  declare id: string

  @column({
    // @ts-expect-error lib error
    serialize: (v, attr, model: Campaign) => model.name || model.id,
  })
  declare name: string

  @column()
  declare gameId: string

  @belongsTo(() => Game, { foreignKey: 'gameId', localKey: 'storeId' })
  declare game: BelongsTo<typeof Game>

  @column()
  declare mediaSourceId: number
}
