import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class DashboardNotification extends compose(BaseModel, DatabaseNaming) {
  static table = 'toolkit_notification'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'notification' })
  declare message: string

  @column({ columnName: 'is_show' })
  declare isVisible: boolean

  @column({ columnName: 'is_pin' })
  declare isPinned: boolean
}
