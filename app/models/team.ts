import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import User from './user.js'

export default class Team extends compose(BaseModel, DatabaseNaming) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare roleId: string

  @column()
  declare leaderId: string | null

  @belongsTo(() => User, { foreignKey: 'leaderId' })
  declare leader: BelongsTo<typeof User>

  @hasMany(() => User)
  declare members: HasMany<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
