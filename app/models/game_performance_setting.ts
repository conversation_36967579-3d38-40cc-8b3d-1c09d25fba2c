import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import { GamePerformanceCriteriaConclusion } from '#graphql/main'

import Game from './game.js'

export class GamePerformanceCriteria extends BaseModel {
  @column()
  declare conclusion: GamePerformanceCriteriaConclusion

  @column()
  declare metrics: Record<string, number>
}

export default class GamePerformanceSetting extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({
    prepare: (users: GamePerformanceCriteria[]) =>
      // @ts-ignore
      JSON.stringify(users.map((user) => user['prepareForAdapter'](user.$attributes))),
    consume: (users: any[]) => GamePerformanceCriteria.$createMultipleFromAdapterResult(users),
  })
  declare criterias: GamePerformanceCriteria[]

  @column()
  declare gameId: Game['storeId']

  @belongsTo(() => Game, { foreignKey: 'storeId', localKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
