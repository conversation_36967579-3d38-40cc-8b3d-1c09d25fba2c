import { DateTime } from 'luxon'
import { BaseModel, column, scope } from '@adonisjs/lucid/orm'
import { getMetadata } from '@munkit/main'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import { AclSubject } from '#config/enums'
import { aclAttributeSubject } from '#utils/acl'

export default class ACL extends compose(BaseModel, DatabaseNaming) {
  @column({ isPrimary: true, serializeAs: null })
  declare id: number

  @column()
  declare roleId: string

  @column()
  declare subject: string

  @column({
    prepare: (value) => JSON.stringify(value),
    columnName: 'data',
  })
  declare permits: string[]

  @column.dateTime({ autoCreate: true, serializeAs: null })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, serializeAs: null })
  declare updatedAt: DateTime

  static subject = (subject: AclSubject, model: any) => {
    const name = getMetadata('model.authorize', model)
    if (!name) {
      throw new Error(`Invalid model`)
    }

    return aclAttributeSubject(subject, name)
  }

  static route = scope((query) => {
    query.from(
      ACL.query()
        .select('*')
        .select(query.client.raw(`jsonb_array_elements_text(data) AS route`))
        .where('subject', AclSubject.Route)
        .as(ACL.table)
    )
  })
}
