import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { DateTime } from 'luxon'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import Game from './game.js'

export default class GamePlaytime extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'fct_cohort_timeplay'

  @column({ isPrimary: true })
  declare id: number

  @belongsTo(() => Game, { foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column({ columnName: 'store_id ' })
  declare gameId: string

  @column({ columnName: 'version' })
  declare version: string

  @column.date({ columnName: 'install_date' })
  declare installDate: DateTime

  @column.date({ columnName: 'date' })
  declare activeDate: DateTime

  @column({ columnName: 'engagement_duration', consume: (v) => Number(v) })
  declare engagementDurationSec: number

  @column({ columnName: 'engaged_sessions', consume: (v) => Number(v) })
  declare engagementSessionCount: number

  @column({ columnName: 'active_users', consume: (v) => Number(v) })
  declare activeUserCount: number

  @column({ columnName: 'flag_version' })
  declare isValidSemver: boolean
}
