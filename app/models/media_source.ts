import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import AdAgency from './ad_agency.js'

export default class MediaSource extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'md_media_source'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'media_source_name' })
  declare name: string

  @column()
  declare agencyId: number

  @belongsTo(() => AdAgency, { foreignKey: 'agencyId' })
  declare agency: BelongsTo<typeof AdAgency>
}
