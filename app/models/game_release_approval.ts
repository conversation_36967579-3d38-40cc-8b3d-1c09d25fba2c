import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import { GameReleaseStatus } from '#config/enums'

import GameReleaseProposal from './game_release_proposal.js'
import User from './user.js'

export default class GameReleaseApproval extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare proposalId: GameReleaseProposal['id']

  @belongsTo(() => GameReleaseProposal, { foreignKey: 'proposalId' })
  declare proposal: BelongsTo<typeof GameReleaseProposal>

  @column()
  declare publisherId: string

  @column()
  declare reviewerId: User['id']

  @belongsTo(() => User, { foreignKey: 'reviewerId' })
  declare reviewer: BelongsTo<typeof User>

  @column()
  declare releaseStatus: GameReleaseStatus

  @column()
  declare jobId?: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
