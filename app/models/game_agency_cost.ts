import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { QueryScopeCallback } from '@adonisjs/lucid/types/model'

import { safeDivide } from '#utils/math'

import Game from './game.js'
import AdAgency from './ad_agency.js'
import GameSpend from './game_spend.js'

export default class GameAgencyCost extends compose(BaseModel, DatabaseNaming) {
  static table = 'fct_agency_cost'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare gameId: Game['id']

  @belongsTo(() => Game, { localKey: 'storeId', foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column({ columnName: 'agency_id' })
  declare agencyId: AdAgency['id']

  @belongsTo(() => AdAgency, { foreignKey: 'agencyId' })
  declare agency: BelongsTo<typeof AdAgency>

  @column({ columnName: 'cost', consume: (v) => Number(v) })
  declare preTaxCost: number

  @column({ columnName: 'total_cost', consume: (v) => Number(v) })
  declare totalCost: number

  @column({ columnName: 'mediation_cost', consume: (v) => Number(v || 0) })
  declare mediationCost: number

  @column({ columnName: 'tax' })
  declare taxRate: number

  @computed()
  get weeklyCost() {
    return Number(this.$extras['weekly_cost'] || 0)
  }

  @computed()
  get varianceRate() {
    return safeDivide(this.mediationCost - this.totalCost, this.totalCost)
  }

  static default = scope<typeof GameAgencyCost, QueryScopeCallback<typeof GameAgencyCost>>(
    (query, where: { gameId: Game['id']; from?: Date; to?: Date }) => {
      const mediationCostsQuery = GameSpend.query()
        .from(
          GameSpend.query()
            .where('storeId', where.gameId)
            .where((q) => {
              if (where.from) q.where('date', '>=', where.from)
              if (where.to) q.where('date', '<=', where.to)
            })
            .select('networkId', 'date')
            .select(
              query.client.raw(
                `${GameSpend.quote('preTaxAmount')} * (1.0 + ${GameSpend.quote('tax')}) AS ${GameSpend.columnName('totalAmount')}`
              )
            )
            .as(GameSpend.table)
        )
        .select('date', 'networkId')
        .groupBy('date', 'networkId')
        .sum(GameSpend.columnName('totalAmount'), GameSpend.columnName('totalAmount'))

      return query
        .where('gameId', where.gameId)
        .where((q) => {
          const date = GameAgencyCost.columnName('date', GameAgencyCost.table)
          if (where.from) q.where(date, '>=', where.from)
          if (where.to) q.where(date, '<=', where.to)
        })
        .joinRaw(
          `FULL OUTER JOIN (${mediationCostsQuery.toQuery()}) AS ${GameSpend.table} ON ${GameAgencyCost.quote('agencyId')} = ${GameSpend.quote('networkId')} AND ${GameAgencyCost.quote('date')} = ${GameSpend.quote('date')}`
        )
    }
  )

  static selectTotalCost = scope((query) => {
    query.select(
      query.client.raw(
        `${GameAgencyCost.quote('preTaxCost')} * (1.0 + ${GameAgencyCost.quote('taxRate')}) AS ${GameAgencyCost.columnName('totalCost')}`
      )
    )
  })

  static selectMediationCost = scope((query) => {
    query.select(
      query.client.raw(`${GameSpend.quote('totalAmount')} AS ${this.columnName('mediationCost')}`)
    )
  })
}
