import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, scope } from '@adonisjs/lucid/orm'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import { JournalAttribute, JournalModel, withJournal } from '@mirai-game-studio/adonis-sdk/journal'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { compose } from '@adonisjs/core/helpers'

import DashboardRoleMembershipUser from './dashboard_role_membership_user.js'
import User from './user.js'
import Team from './team.js'
import DashboardRoleMembershipJournal from './dashboard_role_membership_journal.js'

@JournalModel(DashboardRoleMembershipJournal)
export default class DashboardRoleMembership extends compose(
  BaseModel,
  withJournal<DashboardRoleMembershipJournal>(),
  DatabaseNaming
) {
  @column({ isPrimary: true, serializeAs: null })
  declare id: number

  @column({ serializeAs: 'id' })
  declare roleId: string

  @column({ serializeAs: null })
  declare storeId: string

  @hasMany(() => DashboardRoleMembershipJournal, { serializeAs: null })
  declare journals: HasMany<typeof DashboardRoleMembershipJournal>

  @column({
    prepare: (users: DashboardRoleMembershipUser[]) =>
      // @ts-ignore
      JSON.stringify(users.map((user) => user['prepareForAdapter'](user.$attributes))),
    consume: (users: any[]) => DashboardRoleMembershipUser.$createMultipleFromAdapterResult(users),
  })
  @JournalAttribute()
  declare users: DashboardRoleMembershipUser[]

  @column.dateTime({ autoCreate: true, serializeAs: null })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, serializeAs: null })
  declare updatedAt: DateTime

  @column({ serializeAs: null })
  declare user: string

  static withUsers = scope((query) => {
    query.from(
      DashboardRoleMembership.query()
        .select('id', 'storeId', 'roleId', 'users')
        .select(
          query.client.raw(
            `jsonb_array_elements(users)->>'email' AS ${DashboardRoleMembership.columnName('user')}`
          )
        )
        .as('memberships')
    )
  })

  static selectManyGameInChargedByUser = scope(
    // @ts-expect-error
    (
      query: ModelQueryBuilderContract<typeof DashboardRoleMembership, DashboardRoleMembership>,
      email: string
    ) => {
      query
        .withScopes((s) => s.withUsers())
        .where('user', email)
        .select('storeId')
    }
  )

  static selectManyGameInChargedByTeam = scope(
    // @ts-expect-error
    (
      query: ModelQueryBuilderContract<typeof DashboardRoleMembership, DashboardRoleMembership>,
      team: Team
    ) => {
      query
        .withScopes((s) => s.withUsers())
        .whereIn('user', User.query().select('email').where('teamId', team.id))
        .select('storeId')
    }
  )
}
