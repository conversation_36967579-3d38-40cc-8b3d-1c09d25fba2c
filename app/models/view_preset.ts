import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed } from '@adonisjs/lucid/orm'
import { ModelObject } from '@adonisjs/lucid/types/model'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import ViewPresetConfigMap from '#configmaps/dashboard/view_preset'

import User from './user.js'

export class ViewPresetSchema extends BaseModel {
  @column({
    prepare: (models: ViewPresetAttribute[]) =>
      JSON.stringify(models.map((model) => model.prepareForAdapter(model.$attributes))),
    consume: (rows: string) => {
      return ViewPresetAttribute.$createMultipleFromAdapterResult(JSON.parse(rows))
    },
  })
  declare attributes: ViewPresetAttribute[]
}

const DEFAULT_ID = -1

export class ViewPresetAttribute extends BaseModel {
  @column()
  declare name: string

  @column()
  declare isCohort: boolean

  @column({
    prepare: (values: Array<string | number>) => values.map((e) => Number(e)),
  })
  declare cohortDays: number[]
}

export default class ViewPreset extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: User['id']

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column()
  declare pageId: string

  @column()
  declare name: string

  @column({
    prepare: (model: ViewPresetSchema) => model.prepareForAdapter(model.$attributes),
    consume: (row: ModelObject) => {
      return ViewPresetSchema.$createFromAdapterResult(row)
    },
    serializeAs: null,
  })
  declare schema: ViewPresetSchema

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @computed()
  get attributes() {
    return this.schema.attributes
  }

  static makeDefault(configMap: ViewPresetConfigMap) {
    return new ViewPreset().merge({
      id: DEFAULT_ID,
      pageId: configMap.pageId,
      name: 'Default',
      schema: new ViewPresetSchema().merge({
        attributes: configMap.attributes.map((v) =>
          new ViewPresetAttribute().merge({
            name: v.name,
            isCohort: v.isCohort,
            cohortDays: v.isCohort ? [1, 3, 7] : [],
          })
        ),
      }),
    })
  }

  static async findOrDefault(configMap: ViewPresetConfigMap, user: User, id?: ViewPreset['id']) {
    if (!id || id === DEFAULT_ID) {
      return this.makeDefault(configMap)
    }

    return await ViewPreset.query()
      .where('pageId', configMap.pageId)
      .where('userId', user!.id)
      .where('id', id!)
      .firstOrFail()
  }
}
