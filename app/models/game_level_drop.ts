import { BaseModel, belongsTo, column, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

import { safeDivide } from '#utils/math'

import Game from './game.js'

export default class GameLevelDrop extends compose(BaseModel, DatabaseNaming) {
  static table = 'snp_level_drop'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true, columnName: 'id' })
  declare id: number

  @column({ columnName: 'store_id' })
  declare gameId: string

  @belongsTo(() => Game, { foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column({ columnName: 'version' })
  declare version: string

  @column({ columnName: 'world_id' })
  declare world: number

  @column({ columnName: 'level' })
  declare level: number

  @column({ columnName: 'active_users', consume: (v) => Number(v) })
  declare activeUserCount: number

  @column({ columnName: 'attempt_count', consume: (v) => Number(v) })
  declare attemptCount: number

  @column({ columnName: 'skip_count', consume: (v) => Number(v) })
  declare skipCount: number

  @column({ columnName: 'win_count', consume: (v) => Number(v) })
  declare winCount: number

  @column({ columnName: 'complete_count', consume: (v) => Number(v) })
  declare completeCount: number

  @column({ columnName: 'lose_count', consume: (v) => Number(v) })
  declare loseCount: number

  @column({ columnName: 'total_play_time', consume: (v) => Number(v) })
  declare totalPlaytimeSec: number

  @column({ columnName: 'flag_version' })
  declare isValidSemver: boolean

  static default = scope((q) => {
    q.where('isValidSemver', true)
  })

  get completionRate() {
    return safeDivide(this.completeCount, this.attemptCount)
  }

  get winRate() {
    return safeDivide(this.winCount, this.attemptCount)
  }

  get playtimeSecPerUser() {
    return safeDivide(this.totalPlaytimeSec, this.activeUserCount)
  }

  get attemptCountPerUser() {
    return safeDivide(this.attemptCount, this.activeUserCount)
  }
}
