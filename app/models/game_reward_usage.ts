import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import { DateTime } from 'luxon'

import Game from './game.js'

export default class GameRewardUsage extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'fct_reward_count'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'store_id ' })
  declare gameId: string

  @belongsTo(() => Game, { foreignKey: 'gameId' })
  declare game: BelongsTo<typeof Game>

  @column({ columnName: 'version' })
  declare version: string

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'world_id' })
  declare world: number

  @column({ columnName: 'level' })
  declare level: number

  @column({ columnName: 'location' })
  declare location: string

  @column({ columnName: 'reward_count' })
  declare useCount: number

  @column({ columnName: 'flag_version' })
  declare isValidSemver: boolean
}
