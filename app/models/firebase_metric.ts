import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming, withSerializationContext } from '@mirai-game-studio/adonis-sdk/utils/lucid'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { BigNumber } from 'bignumber.js'
import { uniq } from 'lodash-es'
import format from 'string-template'

import ViewPresetConfigMap, {
  ViewPresetAttributeConfigMap,
} from '#configmaps/dashboard/view_preset'
import { raw } from '#utils/database'

import ViewPreset, { ViewPresetAttribute } from './view_preset.js'
import FirebaseVersionVariant from './firebase_version_variant.js'
import { createFilterScope } from './extensions/filter.js'
import FirebaseAdMetric from './firebase_ad_metric.js'

type SerializationContext = {
  viewPreset: ViewPreset
  viewPresetConfigMap: ViewPresetConfigMap
}

const computedMetricsQuery = 'computed_metrics_query'

export default class FirebaseMetric extends compose(
  BaseModel,
  DatabaseNaming,
  withSerializationContext<SerializationContext>()
) {
  static connection = 'dataWarehouse'

  @column({ isPrimary: true })
  declare id: number

  @column.date()
  declare date: DateTime

  @column()
  declare countryCode: string

  @column()
  declare version: string

  @column()
  declare variantId: FirebaseVersionVariant['id']

  @belongsTo(() => FirebaseVersionVariant, { foreignKey: 'variantId' })
  declare variant: BelongsTo<typeof FirebaseVersionVariant>

  @column({ consume: (v) => Number(v) })
  activeUserCount: number = 0

  @column({ consume: (v) => Number(v) })
  dailyActiveUserCount: number = 0

  @column({ consume: (v) => Number(v) })
  sessionCount: number = 0

  @column({ consume: (v) => Number(v) })
  installCount: number = 0

  @column({
    consume: (v) => BigInt(new BigNumber(v).integerValue().toString()),
    serialize: (v) => v.toString(),
  })
  playtimeMsec: BigInt = BigInt(0)

  @column({ consume: (v) => Number(v) })
  sessionCountPerActiveUser: number = 0

  @column({ consume: (v) => Number(v) })
  retentionRate: number = 0

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as FirebaseMetric),
  })
  activeUserNthDayCounts: Record<string, number> = {}

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as FirebaseMetric),
  })
  sessionNthDayCounts: Record<string, number> = {}

  @column({
    serialize: (values, attribute, model) =>
      serializeCohortAttribute(values, attribute, model as FirebaseMetric),
  })
  playtimeNthDayMsecs: Record<string, number> = {}

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as FirebaseMetric)
    },
  })
  adRevNthDayGrossAmounts: Record<string, number> = {}

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as FirebaseMetric)
    },
  })
  impressionNthDayCounts: Record<string, number> = {}

  @column({
    consume: (v) => Number(v),
    serialize: (values, attribute, model) => {
      return serializeCohortAttribute(values, attribute, model as FirebaseMetric)
    },
  })
  retentionNthDayRates: Record<string, number> = {}

  static filter = createFilterScope(FirebaseMetric)

  static joinSumAdMetric = scope(
    (
      query,
      viewPreset: ViewPreset,
      viewPresetConfigMap: ViewPresetConfigMap,
      gameId: string,
      filters: any
    ) => {
      const adMetricQuery = FirebaseAdMetric.query().select('variantId', 'countryCode', 'date')
      viewPreset.attributes.forEach((vpa) => {
        const name = vpa.name
        switch (name) {
          case 'impressionNthDayCounts':
          case 'adRevNthDayGrossAmounts':
            vpa.cohortDays.forEach((day) => {
              adMetricQuery.select(
                raw`SUM((coalesce(${FirebaseAdMetric.columnFullname(name)}->>'${day}', '0'))::float) AS ${format(viewPresetConfigMap.attributes.get(name).substituteAttributeTemplate, [day])}`
              )
            })
            break
          default:
            break
        }
      })
      adMetricQuery
        .innerJoin(
          FirebaseVersionVariant.table,
          FirebaseVersionVariant.columnName('id', FirebaseVersionVariant.table),
          FirebaseAdMetric.columnName('variantId')
        )
        .where(FirebaseVersionVariant.columnName('gameId', FirebaseVersionVariant.table), gameId)
        .withScopes((s) => {
          s.filter(filters as any, {
            date: FirebaseAdMetric.columnName('date', FirebaseAdMetric.table),
            countryCode: FirebaseAdMetric.columnName('countryCode', FirebaseAdMetric.table),
            variantId: FirebaseAdMetric.columnName('variantId', FirebaseAdMetric.table),
            version: FirebaseAdMetric.columnName('version', FirebaseAdMetric.table),
          })
        })

        .groupBy('variantId', 'countryCode', 'date')
      query.joinRaw(
        raw`
        LEFT JOIN (${adMetricQuery.toQuery()}) AS ${FirebaseAdMetric.table}
          ON ${FirebaseMetric.columnFullname('variantId')} = ${FirebaseAdMetric.columnFullname('variantId')}
          AND ${FirebaseMetric.columnFullname('countryCode')} = ${FirebaseAdMetric.columnFullname('countryCode')}
          AND ${FirebaseMetric.columnFullname('date')} = ${FirebaseAdMetric.columnFullname('date')}
      `
      )
    }
  )

  static selectAggregationPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      if (name.startsWith('ad_') && name.endsWith('ActiveUser')) {
        query.sum(
          FirebaseMetric.columnFullname('activeUserCount'),
          FirebaseMetric.columnName('activeUserCount')
        )
        return
      }

      switch (name) {
        case 'sessionCount':
        case 'installCount':
        case 'activeUserCount':
          query.sum(FirebaseMetric.columnFullname(name), FirebaseMetric.columnName(name))
          break
        case 'playtimeMsec':
          query.select(
            raw`COALESCE(AVG(NULLIF(${FirebaseMetric.columnName(name, computedMetricsQuery)}, 0)), 0) AS ${FirebaseMetric.columnName(name)}`
          )
          break

        case 'dailyActiveUserCount':
          query.select(
            raw`COALESCE(AVG(NULLIF(${FirebaseMetric.columnName(name, computedMetricsQuery)}, 0)), 0) AS ${FirebaseMetric.columnName(name)}`
          )
          break
        case 'sessionCountPerActiveUser':
          query.select(
            this.createSafeDivision(
              `SUM(${FirebaseMetric.columnFullname('sessionCount')})`,
              `COALESCE(AVG(NULLIF(${FirebaseMetric.columnName('dailyActiveUserCount', computedMetricsQuery)}, 0)), 0)`,
              FirebaseMetric.columnName(name)
            )
          )
          break

        case 'retentionRate':
          query.select(
            this.createSafeDivision(
              `COALESCE(AVG(NULLIF(${FirebaseMetric.columnName('dailyActiveUserCount', computedMetricsQuery)}, 0)), 0)`,
              `SUM(${FirebaseMetric.columnFullname('installCount')})`,
              FirebaseMetric.columnName(name)
            )
          )
          break
        case 'sessionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`SUM((coalesce(${FirebaseMetric.quote(name)}->>'${day}', '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case 'activeUserNthDayCounts':
        case 'playtimeNthDayMsecs':
        case 'retentionNthDayRates':
          cohortDays.forEach((day) => {
            query.select(
              raw`COALESCE(AVG(NULLIF(${computedMetricsQuery}.${format(substituteAttributeTemplate, [day])}, 0)), 0) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        default:
          break
        case 'adRevNthDayGrossAmounts':
        case 'impressionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`SUM((coalesce(${format(substituteAttributeTemplate, [day])}, '0'))::float) AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
      }
    }
  )

  static selectPreset = scope(
    (
      query,
      { name, cohortDays }: ViewPresetAttribute,
      { substituteAttributeTemplate }: ViewPresetAttributeConfigMap
    ) => {
      if (name.startsWith('ad_') && name.endsWith('ActiveUser')) {
        query.select(
          raw`${FirebaseMetric.columnFullname('activeUserCount')} AS ${FirebaseMetric.columnName('activeUserCount')}`
        )
        return
      }

      switch (name) {
        case 'sessionCount':
        case 'installCount':
        case 'playtimeMsec':
        case 'dailyActiveUserCount':
        case 'activeUserCount':
          query.select(
            raw`${FirebaseMetric.columnFullname(name)} AS ${FirebaseMetric.columnName(name)}`
          )
          break

        case 'sessionCountPerActiveUser':
          query.select(
            raw`CASE WHEN ${FirebaseMetric.columnFullname('activeUserCount')} = 0 THEN 0 ELSE ${FirebaseMetric.columnFullname('sessionCount')}::float / ${FirebaseMetric.columnFullname('activeUserCount')} END AS ${FirebaseMetric.columnName('sessionCountPerActiveUser')}`
          )
          break

        case 'retentionRate':
          query.select(
            raw`CASE WHEN ${FirebaseMetric.columnFullname('installCount')} = 0 THEN 0 ELSE ${FirebaseMetric.columnFullname('activeUserCount')}::float / ${FirebaseMetric.columnFullname('installCount')} END AS ${FirebaseMetric.columnName('retentionRate')}`
          )
          break
        case 'activeUserNthDayCounts':
        case 'playtimeNthDayMsecs':
        case 'sessionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`(coalesce(${FirebaseMetric.columnFullname(name)}->>'${day}', '0'))::float AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        case 'adRevNthDayGrossAmounts':
        case 'impressionNthDayCounts':
          cohortDays.forEach((day) => {
            query.select(
              raw`(coalesce(${FirebaseMetric.columnFullname(name)}->>'${day}', '0'))::float AS ${format(substituteAttributeTemplate, [day])}`
            )
          })
          break
        default:
          break
      }
    }
  )

  static withComputedMetricsJoin = scope(
    (
      query,
      {
        groupBy,
        viewPreset,
        gameId,
        filters,
        viewPresetConfigMap,
      }: {
        groupBy: string[]
        viewPreset: ViewPreset
        gameId: string
        filters: any
        viewPresetConfigMap: ViewPresetConfigMap
      }
    ) => {
      const groupByFields = uniq(groupBy.concat('date'))
      const subQuery = FirebaseMetric.query()
        .select(groupByFields)
        .select(
          raw`
            SUM(COALESCE(${FirebaseMetric.columnName('activeUserCount')}, 0)) AS ${FirebaseMetric.columnName('dailyActiveUserCount')}
          `
        )
        .select(
          this.createSafeDivision(
            `SUM(${FirebaseMetric.columnName('playtimeMsec')})`,
            `SUM(${FirebaseMetric.columnName('activeUserCount')})`,
            FirebaseMetric.columnName('playtimeMsec')
          )
        )

      viewPreset.attributes.forEach((vpa) => {
        const name = vpa.name
        switch (name) {
          case 'activeUserNthDayCounts':
            vpa.cohortDays.forEach((day) => {
              subQuery.select(
                raw`SUM(coalesce(${FirebaseMetric.quote(name)}->>'${day}', '0')::float) AS activeUserDay${day}`
              )
            })
            break

          case 'playtimeNthDayMsecs':
            vpa.cohortDays.forEach((day) => {
              subQuery.select(
                this.createSafeDivision(
                  `SUM(coalesce(${FirebaseMetric.quote(name)}->>'${day}', '0')::float)`,
                  `SUM(coalesce(${FirebaseMetric.quote('activeUserNthDayCounts')}->>'${day}', '0')::float)`,
                  format(viewPresetConfigMap.attributes.get(name).substituteAttributeTemplate, [
                    day,
                  ])
                )
              )
            })
            break
          case 'retentionNthDayRates':
            vpa.cohortDays.forEach((day) => {
              subQuery.select(
                this.createSafeDivision(
                  `SUM(coalesce(${FirebaseMetric.quote('activeUserNthDayCounts')}->>'${day}', '0')::float)`,
                  `SUM(${FirebaseMetric.quote('installCount')})`,
                  format(viewPresetConfigMap.attributes.get(name).substituteAttributeTemplate, [
                    day,
                  ])
                )
              )
            })
            break
        }
      })
      subQuery
        .innerJoin(
          FirebaseVersionVariant.table,
          FirebaseVersionVariant.columnName('id', FirebaseVersionVariant.table),
          FirebaseMetric.columnName('variantId')
        )
        .where(FirebaseVersionVariant.columnName('gameId', FirebaseVersionVariant.table), gameId)
        .withScopes((s) => {
          s.filter(filters as any, {
            date: FirebaseMetric.columnName('date', FirebaseMetric.table),
            countryCode: FirebaseMetric.columnName('countryCode', FirebaseMetric.table),
            variantId: FirebaseMetric.columnName('variantId', FirebaseMetric.table),
            version: FirebaseMetric.columnName('version', FirebaseMetric.table),
          })
        })
        .groupBy(groupByFields.map((col) => FirebaseMetric.columnFullname(col as any)))

      query.joinRaw(
        raw`
          LEFT JOIN (${subQuery.toQuery()}) AS ${computedMetricsQuery}
            ON ${groupByFields
              .map(
                (col: any) =>
                  `${FirebaseMetric.columnFullname(col)} = ${computedMetricsQuery}.${FirebaseMetric.columnName(col)}`
              )
              .join(' AND ')}
        `
      )
    }
  )

  private static createSafeDivision(
    numerator: string,
    denominator: string,
    columnName: string
  ): any {
    return raw`
        CASE
          WHEN ${denominator} = 0
            THEN 0
          ELSE ${numerator}::float / ${denominator}
        END AS ${columnName}
      `
  }
}

function serializeCohortAttribute(_values: any, name: string, model: FirebaseMetric) {
  if (!model.serializationContext) {
    return {}
  }

  const { viewPreset, viewPresetConfigMap } = model.serializationContext
  const attribute = viewPreset.attributes.find((a) => a.name === name)
  if (!attribute) {
    return {}
  }

  const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.get(name)
  if (!viewPresetAttributeConfigMap.isCohort) {
    throw new Error(`Attribute ${name} is not a cohort attribute`)
  }

  return Object.fromEntries(
    attribute.cohortDays.map((day) => {
      return [
        day.toString(),
        model.$extras[
          // database column name is case-insensitive so we must lowercase it to get the raw value
          format(viewPresetAttributeConfigMap.substituteAttributeTemplate, [day]).toLowerCase()
        ] || 0,
      ]
    })
  )
}
