import { BaseModel, column, scope } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class GameStudio extends compose(BaseModel, DatabaseNaming) {
  static table = 'md_partner'
  static connection = 'dataWarehouse'

  @column({ isPrimary: true, columnName: 'id' })
  declare id: number

  @column({ columnName: 'partner_name' })
  declare name: string

  @column({ columnName: 'is_show' })
  declare isVisible: boolean

  static default = scope((query) => {
    query.where('isVisible', true)
  })
}
