import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

export default class AdGroup extends compose(BaseModel, DatabaseNaming) {
  static connection = 'dataWarehouse'
  static table = 'ad_groups'

  @column({ isPrimary: true })
  declare id: string

  @column({
    // @ts-expect-error lib error
    serialize: (v, attr, model: AdGroup) => model.name || model.id,
  })
  declare name: string
}
