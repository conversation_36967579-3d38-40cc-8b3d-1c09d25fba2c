import { DateTime } from 'luxon'
import { BaseModel, column, computed, scope } from '@adonisjs/lucid/orm'
import { sumBy } from 'lodash-es'
import { Metadata } from '@munkit/main'
import { LucidRow, ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import { compose } from '@adonisjs/core/helpers'
import { DatabaseNaming } from '@mirai-game-studio/adonis-sdk/utils/lucid'

import {
  AclModelName,
  GameRevenueAdType,
  GameRevenueMediationId,
  GameRevenueNetworkId,
} from '#config/enums'
import { avgBy } from '#utils/math'
import { StaticImplements } from '#utils/types'

declare module '@munkit/main' {
  interface MetadataDefinitions {
    'game.rev.field': string[]
  }
}

@Metadata('model.authorize', AclModelName.GameRevenue)
export default class GameRevenue extends compose(BaseModel, DatabaseNaming) {
  static table = 'fct_rev'
  static connection = 'dataWarehouse'

  static adType = GameRevenueAdType
  static mediationId = GameRevenueMediationId
  static networkId = GameRevenueNetworkId

  @column({ isPrimary: true, columnName: 'id' })
  declare id: number

  @column.date({ columnName: 'date' })
  declare date: DateTime

  @column({ columnName: 'store_id' })
  declare storeId: string

  @column({ columnName: 'mediation_id' })
  declare mediationId: number

  @column({ columnName: 'network_id' })
  declare networkId: number

  @column({ columnName: 'ad_type_id' })
  declare adType: number

  @column({ columnName: 'revenue' })
  @Metadata('attr.authorize')
  declare revenue: number

  @column({ columnName: 'impressions' })
  declare impressionCount: number

  @column({ columnName: 'dau' })
  declare dailyActiveUserCount: number

  @column({ columnName: 'tax' })
  declare taxRate: number

  @computed()
  get netRevenue() {
    return this.$extras['net_revenue'] ?? 0
  }

  @computed()
  @Metadata('attr.authorize')
  get impsDau() {
    return this.$extras['imps_dau'] ?? 0
  }

  @computed()
  @Metadata('attr.authorize')
  get ecpm() {
    return Number(this.$extras['ecpm'] ?? 0)
  }

  @computed()
  @Metadata('attr.authorize')
  get arpDau() {
    return Number(this.$extras['arp_dau'] ?? 0)
  }

  static default = scope((query: ModelQueryBuilderContract<typeof GameRevenue, GameRevenue>) => {
    query.andWhere((q) => {
      UnityLPDailyRevenue.default(q)
      q.orWhere((qr) => {
        ApplovinDailyRevenue.default(qr)
      })
      q.orWhere((qr) => {
        AdmobDailyRevenue.default(qr)
      })
      q.orWhere((qr) => {
        NonMediationDailyRevenue.default(qr)
      })
    })
  })

  static selectImpsDau = scope((query, tableName?: string) => {
    return query.select(
      query.client.raw(
        `CASE WHEN coalesce(${GameRevenue.quote('dailyActiveUserCount', tableName)}, 0) = 0 THEN 0 ELSE ${GameRevenue.quote('impressionCount', tableName)}::float / ${GameRevenue.quote('dailyActiveUserCount', tableName)} END as imps_dau`
      )
    )
  })

  static selectEcpm = scope((query, tableName?: string) => {
    return query.select(
      query.client.raw(
        `CASE WHEN ${GameRevenue.quote('impressionCount', tableName)} = 0 THEN 0 ELSE (${GameRevenue.quote('revenue', tableName)} * 1000) / ${GameRevenue.quote('impressionCount', tableName)} END as ecpm`
      )
    )
  })

  static selectArpDau = scope((query, tableName?: string) => {
    return query.select(
      query.client.raw(
        `CASE WHEN ${GameRevenue.quote('dailyActiveUserCount', tableName)} = 0 THEN 0 ELSE (${GameRevenue.quote('revenue', tableName)} * 1000) / ${GameRevenue.quote('dailyActiveUserCount', tableName)} END as arp_dau`
      )
    )
  })

  static selectNetRevenue = scope((query, tableName?: string) => {
    return query.select(
      query.client.raw(
        `${GameRevenue.quote('revenue', tableName)} * (1.0 - ${GameRevenue.quote('taxRate', tableName)}) as net_revenue`
      )
    )
  })

  static date = scope((query, ...args: string[]) => {
    const op = args[0]
    const offset = Number(args[1])
    if (offset > 0) {
      return query.where('date', op, DateTime.now().plus({ days: offset }).toISODate())
    }

    return query.where(
      'date',
      op,
      DateTime.now()
        .minus({ days: Math.abs(offset) })
        .toISODate()
    )
  })

  static async aggregate(storeId: string) {
    const query = GameRevenue.query()

    const dailyActiveUserQuery = this.query()
      .select(
        query.client.raw(
          `avg(${this.quote('dailyActiveUserCount', 'dau_rev')}) as ${this.columnName('dailyActiveUserCount')}`
        )
      )
      .from(
        this.query()
          .withScopes((s) => s.default())
          .groupBy('storeId', 'date')
          .select('date')
          .select(
            query.client.raw(
              `max(${this.quote('dailyActiveUserCount', 'dau_by_date')}) as ${this.columnName('dailyActiveUserCount')}`
            )
          )
          .where('storeId', storeId)
          .from(`${this.table} AS dau_by_date`)
          .as('dau_rev')
      )

    const aggregationQuery = GameRevenue.query()
      .from(
        GameRevenue.query()
          .withScopes((s) => s.default())
          .withScopes((s) => s.selectImpsDau())
          .withScopes((s) => s.selectNetRevenue())
          .select(
            this.columnName('adType'),
            this.columnName('dailyActiveUserCount'),
            this.columnName('date'),
            this.columnName('id'),
            this.columnName('impressionCount'),
            this.columnName('mediationId'),
            this.columnName('revenue'),
            this.columnName('storeId'),
            this.columnName('taxRate')
          )
          .select(
            query.client.raw(
              `CASE WHEN ${this.quote('mediationId')} = ${this.mediationId.NonMediation} THEN ${this.columnName('networkId')} ELSE -1 END as ${this.columnName('networkId')}`
            )
          )
          .where('storeId', storeId)
          .as(this.table)
      )
      .joinRaw(`left join (${dailyActiveUserQuery.toQuery()}) dau on 1 = 1`)
      .withScopes((s) => s.default())
      .groupBy('storeId', 'mediationId', 'networkId', 'adType')
      .where('storeId', storeId)
      .select('storeId', 'mediationId', 'networkId', 'adType')
      .select(
        query.client.raw(
          `avg(${this.quote('dailyActiveUserCount', 'dau')})::float as ${this.columnName('dailyActiveUserCount')}`
        )
      )
      .select(query.client.raw(`avg("imps_dau") as imps_dau`))
      .select(query.client.raw(`sum(${this.quote('revenue')}) as revenue`))
      .select(query.client.raw(`sum("net_revenue") as net_revenue`))
      .as('rev')

    const aggregations = await aggregationQuery

    return aggregations
  }
}

export interface MediationDailyRevenue extends LucidRow {
  mediationId: number
  revenue: number
  netRevenue: number
  dailyActiveUserCount: number
  bannerRevenue: number
  interRevenue: number
  aoaRevenue: number
  rewardRevenue: number
  mrecRevenue: number
  audioRevenue: number
}

interface MediationDailyRevenueModel {
  make(revenues: GameRevenue[]): MediationDailyRevenue
  default(query: ModelQueryBuilderContract<typeof GameRevenue, GameRevenue>): void
}

@StaticImplements<MediationDailyRevenueModel>()
export class UnityLPDailyRevenue extends BaseModel implements MediationDailyRevenue {
  static make(values: GameRevenue[]): UnityLPDailyRevenue {
    const revenues = values.filter((e) => e.mediationId === GameRevenue.mediationId.UnityLP)

    const inters = revenues.filter((r) => r.adType === GameRevenue.adType.Inter)
    const banners = revenues.filter((r) => r.adType === GameRevenue.adType.Banner)
    const rewards = revenues.filter((r) => r.adType === GameRevenue.adType.Reward)
    const mrecs = revenues.filter((r) => r.adType === GameRevenue.adType.MREC)
    const aoas = revenues.filter((r) => r.adType === GameRevenue.adType.AOA)

    return new UnityLPDailyRevenue().merge({
      date: values[0]!.date,
      dailyActiveUserCount: avgBy(values, 'dailyActiveUserCount'),
      revenue: sumBy(revenues, 'revenue'),
      netRevenue: sumBy(revenues, 'netRevenue'),
      bannerRevenue: sumBy(banners, 'revenue'),
      interRevenue: sumBy(inters, 'revenue'),
      aoaRevenue: sumBy(aoas, 'revenue'),
      rewardRevenue: sumBy(rewards, 'revenue'),
      interImpsDau: sumBy(inters, 'impsDau'),
      bannerImpsDau: sumBy(banners, 'impsDau'),
      rewardImpsDau: sumBy(rewards, 'impsDau'),
      mrecRevenue: sumBy(mrecs, 'revenue'),
    })
  }

  static default(query: ModelQueryBuilderContract<typeof GameRevenue, GameRevenue>) {
    query.where('mediationId', GameRevenue.mediationId.UnityLP)
  }

  @column()
  mediationId: number = GameRevenue.mediationId.UnityLP

  @column()
  declare date: DateTime

  @column()
  @Metadata('agg.op', { operator: 'avg' })
  declare dailyActiveUserCount: number

  @column({ columnName: 'Revenue' })
  @Metadata('agg.op', { operator: 'sum' })
  @Metadata('game.rev.field')
  declare revenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare netRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare bannerRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare interRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare aoaRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare rewardRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare mrecRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  audioRevenue: number = 0

  @column({ columnName: 'Banner ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare bannerImpsDau: number

  @column({ columnName: 'Inter ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare interImpsDau: number

  @column({ columnName: 'Reward ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare rewardImpsDau: number
}

@StaticImplements<MediationDailyRevenueModel>()
export class ApplovinDailyRevenue extends BaseModel implements MediationDailyRevenue {
  static make(values: GameRevenue[]): ApplovinDailyRevenue {
    const revenues = values.filter((e) => e.mediationId === GameRevenue.mediationId.Applovin)

    const inters = revenues.filter((r) => r.adType === GameRevenue.adType.Inter)
    const banners = revenues.filter((r) => r.adType === GameRevenue.adType.Banner)
    const rewards = revenues.filter((r) => r.adType === GameRevenue.adType.Reward)
    const aoas = revenues.filter((r) => r.adType === GameRevenue.adType.AOA)
    const mrecs = revenues.filter((r) => r.adType === GameRevenue.adType.MREC)

    return new ApplovinDailyRevenue().merge({
      date: values[0]!.date,
      dailyActiveUserCount: avgBy(values, 'dailyActiveUserCount'),
      revenue: sumBy(revenues, 'revenue'),
      netRevenue: sumBy(revenues, 'netRevenue'),
      bannerImpsDau: sumBy(banners, 'impsDau'),
      interImpsDau: sumBy(inters, 'impsDau'),
      rewardImpsDau: sumBy(rewards, 'impsDau'),
      mrecImpsDau: sumBy(mrecs, 'impsDau'),
      aoaImpsDau: sumBy(aoas, 'impsDau'),
      bannerRevenue: sumBy(banners, 'revenue'),
      interRevenue: sumBy(inters, 'revenue'),
      aoaRevenue: sumBy(aoas, 'revenue'),
      rewardRevenue: sumBy(rewards, 'revenue'),
      mrecRevenue: sumBy(mrecs, 'revenue'),
    })
  }

  static default(query: ModelQueryBuilderContract<typeof GameRevenue, GameRevenue>) {
    query.where('mediationId', GameRevenue.mediationId.Applovin)
  }

  @column()
  mediationId: number = GameRevenue.mediationId.Applovin

  @column()
  declare date: DateTime

  @column()
  @Metadata('agg.op', { operator: 'avg' })
  declare dailyActiveUserCount: number

  @column({ columnName: 'Revenue' })
  @Metadata('agg.op', { operator: 'sum' })
  @Metadata('game.rev.field')
  declare revenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare netRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare bannerRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare interRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare aoaRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare rewardRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare mrecRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  audioRevenue: number = 0

  @column({ columnName: 'Banner ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare bannerImpsDau: number

  @column({ columnName: 'Inter ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare interImpsDau: number

  @column({ columnName: 'Reward ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare rewardImpsDau: number

  @column({ columnName: 'AOA ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare aoaImpsDau: number

  @column({ columnName: 'MREC ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare mrecImpsDau: number
}

@StaticImplements<MediationDailyRevenueModel>()
export class AdmobDailyRevenue extends BaseModel implements MediationDailyRevenue {
  static make(values: GameRevenue[]): AdmobDailyRevenue {
    const revenues = values.filter((e) => e.mediationId === GameRevenue.mediationId.Admob)

    const banners = revenues.filter((r) => r.adType === GameRevenue.adType.AdmobCollapse)
    const aoas = revenues.filter((r) => r.adType === GameRevenue.adType.AdmobAOA)
    const natives = revenues.filter((r) => r.adType === GameRevenue.adType.AdmobNative)
    const adaptives = revenues.filter((r) => r.adType === GameRevenue.adType.AdmobAdaptive)
    const mrecs = revenues.filter((r) => r.adType === GameRevenue.adType.AdmobMREC)

    return new AdmobDailyRevenue().merge({
      date: values[0]!.date,
      dailyActiveUserCount: avgBy(values, 'dailyActiveUserCount'),
      revenue: sumBy(revenues, 'revenue'),
      netRevenue: sumBy(revenues, 'netRevenue'),
      aoaImpsDau: sumBy(aoas, 'impsDau'),
      collapseBannerImpsDau: sumBy(banners, 'impsDau'),
      mrecRevenue: sumBy(mrecs, 'revenue'),
      bannerRevenue: 0,
      interRevenue: 0,
      aoaRevenue: sumBy(aoas, 'revenue'),
      rewardRevenue: 0,
      nativeImpsDau: sumBy(natives, 'impsDau'),
      adaptiveImpsDau: sumBy(adaptives, 'impsDau'),
      mrecImpsDau: sumBy(mrecs, 'impsDau'),
    })
  }

  static default(query: ModelQueryBuilderContract<typeof GameRevenue, GameRevenue>) {
    query.where('mediationId', GameRevenue.mediationId.Admob)
  }

  @column()
  mediationId: number = GameRevenue.mediationId.Admob

  @column()
  declare date: DateTime

  @column()
  @Metadata('agg.op', { operator: 'avg' })
  declare dailyActiveUserCount: number

  @column({ columnName: 'Revenue' })
  @Metadata('agg.op', { operator: 'sum' })
  @Metadata('game.rev.field')
  declare revenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare netRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare bannerRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare interRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare aoaRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare rewardRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare mrecRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  audioRevenue: number = 0

  @column({ columnName: 'AOA Admob ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare aoaImpsDau: number

  @column({ columnName: 'Collapse Admob ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare collapseBannerImpsDau: number

  @column({ columnName: 'Native Admob ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare nativeImpsDau: number

  @column({ columnName: 'Adaptive Admob ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare adaptiveImpsDau: number

  @column({ columnName: 'MREC Admob ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare mrecImpsDau: number
}

@StaticImplements<MediationDailyRevenueModel>()
export class NonMediationDailyRevenue extends BaseModel implements MediationDailyRevenue {
  static make(values: GameRevenue[]): NonMediationDailyRevenue {
    const revenues = values.filter((e) => e.mediationId === GameRevenue.mediationId.NonMediation)
    const odeeos = revenues.filter((e) => e.networkId === GameRevenue.networkId.Odeeo)

    const audios = odeeos.filter((r) => r.adType === GameRevenue.adType.Audio)
    const aoas = revenues.filter((r) => r.adType === GameRevenue.adType.AOA)

    return new NonMediationDailyRevenue().merge({
      date: values[0]!.date,
      dailyActiveUserCount: avgBy(odeeos, 'dailyActiveUserCount'),
      revenue: sumBy(revenues, 'revenue'),
      netRevenue: sumBy(revenues, 'netRevenue'),
      audioRevenue: sumBy(audios, 'revenue'),
      odeeoImpsDau: avgBy(odeeos, 'impsDau'),
      odeeoRevenue: sumBy(odeeos, 'revenue'),
      aoaRevenue: sumBy(aoas, 'revenue'),
    })
  }

  static default(query: ModelQueryBuilderContract<typeof GameRevenue, GameRevenue>) {
    query.where('mediationId', GameRevenue.mediationId.NonMediation)
  }

  @column()
  mediationId: number = GameRevenue.mediationId.NonMediation

  @column()
  declare date: DateTime

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare revenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare netRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'avg' })
  declare dailyActiveUserCount: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  bannerRevenue: number = 0

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  interRevenue: number = 0

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare aoaRevenue: number

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  rewardRevenue: number = 0

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  mrecRevenue: number = 0

  @column()
  @Metadata('agg.op', { operator: 'sum' })
  declare audioRevenue: number

  @column({ columnName: 'Odeoo Revenue' })
  @Metadata('agg.op', { operator: 'sum' })
  @Metadata('game.rev.field')
  declare odeeoRevenue: number

  @column({ columnName: 'Odeeo ImpsDAU' })
  @Metadata('agg.op', { operator: 'avg' })
  @Metadata('game.rev.field')
  declare odeeoImpsDau: number
}
