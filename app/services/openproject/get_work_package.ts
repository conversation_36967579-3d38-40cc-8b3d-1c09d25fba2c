// Generated by https://quicktype.io
//
// To change quicktype's target language, run command:
//
//   "Set quicktype target language"

export interface GetWorkPackage {
  derivedStartDate: null
  derivedDueDate: null
  spentTime: string
  laborCosts: string
  materialCosts: string
  overallCosts: string
  _embedded: GetWorkPackageEmbedded
  _type: string
  id: number
  lockVersion: number
  subject: string
  description: Tion
  scheduleManually: boolean
  startDate: string
  dueDate: string
  estimatedTime: string
  derivedEstimatedTime: string
  remainingTime: null
  derivedRemainingTime: null
  duration: string
  ignoreNonWorkingDays: boolean
  percentageDone: number
  derivedPercentageDone: number
  createdAt: string
  updatedAt: string
  readonly: boolean
  _links: GetWorkPackageLinks
}

export interface GetWorkPackageEmbedded {
  attachments: Attachments
  relations: Attachments
  type: Type
  priority: Priority
  project: Project
  status: Status
  author: AssigneeClass
  assignee: AssigneeClass
  customActions: any[]
  costsByType: Attachments
}

export interface AssigneeClass {
  _type: string
  id: number
  name: string
  createdAt: string
  updatedAt: string
  login: string
  admin: boolean
  firstName: string
  lastName: string
  email: string
  avatar: string
  status: string
  identityUrl: null
  language: string
  _links: AssigneeLinks
}

export interface AssigneeLinks {
  self: PurpleAssignee
  memberships: PurpleAssignee
  showUser: ShowUser
  updateImmediately: AddChild
  lock: AddChild
}

export interface AddChild {
  href: string
  title: string
  method: Method
}

export enum Method {
  Delete = 'delete',
  Patch = 'patch',
  Post = 'post',
}

export interface PurpleAssignee {
  href: null | string
  title: null | string
}

export interface ShowUser {
  href: string
  type: string
}

export interface Attachments {
  _type: string
  total: number
  count: number
  _embedded: AttachmentsEmbedded
  _links: AttachmentsLinks
}

export interface AttachmentsEmbedded {
  elements: any[]
}

export interface AttachmentsLinks {
  self: Activities
}

export interface Activities {
  href: null | string
}

export interface Priority {
  _type: string
  id: number
  name: string
  position: number
  color: string
  isDefault: boolean
  isActive: boolean
  _links: PriorityLinks
}

export interface PriorityLinks {
  self: PurpleAssignee
}

export interface Project {
  _type: string
  id: number
  identifier: string
  name: string
  active: boolean
  public: boolean
  description: Tion
  createdAt: string
  updatedAt: string
  statusExplanation: Tion
  _links: ProjectLinks
}

export interface ProjectLinks {
  self: PurpleAssignee
  createWorkPackage: AddAttachment
  createWorkPackageImmediately: AddAttachment
  workPackages: Activities
  categories: Activities
  versions: Activities
  memberships: Activities
  types: Activities
  update: AddAttachment
  updateImmediately: AddAttachment
  delete: AddAttachment
  schema: Activities
  status: PurpleAssignee
  ancestors: PurpleAssignee[]
  projectStorages: Activities
  parent: PurpleAssignee
}

export interface AddAttachment {
  href: string
  method: Method
}

export interface Tion {
  format: string
  raw: string
  html: string
}

export interface Status {
  _type: string
  id: number
  name: string
  isClosed: boolean
  color: string
  isDefault: boolean
  isReadonly: boolean
  defaultDoneRatio: null
  position: number
  _links: PriorityLinks
}

export interface Type {
  _type: string
  id: number
  name: string
  color: string
  position: number
  isDefault: boolean
  isMilestone: boolean
  createdAt: string
  updatedAt: string
  _links: PriorityLinks
}

export interface GetWorkPackageLinks {
  attachments: Activities
  prepareAttachment: AddAttachment
  addAttachment: AddAttachment
  update: AddAttachment
  schema: Activities
  updateImmediately: AddAttachment
  delete: AddAttachment
  logTime: PurpleAssignee
  move: Atom
  copy: PurpleAssignee
  pdf: Atom
  atom: Atom
  availableRelationCandidates: PurpleAssignee
  customFields: Atom
  configureForm: Atom
  activities: Activities
  availableWatchers: Activities
  relations: Activities
  watchers: Activities
  addWatcher: AddWatcher
  removeWatcher: AddWatcher
  addRelation: AddChild
  addChild: AddChild
  changeParent: AddChild
  addComment: AddChild
  previewMarkup: AddAttachment
  timeEntries: PurpleAssignee
  category: Activities
  type: PurpleAssignee
  priority: PurpleAssignee
  project: PurpleAssignee
  status: PurpleAssignee
  author: PurpleAssignee
  responsible: Activities
  assignee: PurpleAssignee
  version: Activities
  logCosts: Atom
  showCosts: Atom
  costsByType: Activities
  meetings: PurpleAssignee
  github: PurpleAssignee
  github_pull_requests: PurpleAssignee
  self: PurpleAssignee
  watch: AddWatcher
  ancestors: any[]
  parent: PurpleAssignee
  customActions: any[]
}

export interface AddWatcher {
  href: string
  method: Method
  payload?: Payload
  templated?: boolean
}

export interface Payload {
  user: Activities
}

export interface Atom {
  href: string
  type: string
  title: string
}
