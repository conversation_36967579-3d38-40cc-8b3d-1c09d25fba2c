// Generated by https://quicktype.io
//
// To change quicktype's target language, run command:
//
//   "Set quicktype target language"

export interface ListStatus {
  _type: string
  total: number
  count: number
  _embedded: Embedded
  _links: ListStatusLinks
}

export interface Embedded {
  elements: Element[]
}

export interface Element {
  _type: Type
  id: number
  name: string
  isClosed: boolean
  color: string
  isDefault: boolean
  isReadonly: boolean
  defaultDoneRatio: null
  position: number
  _links: ElementLinks
}

export interface ElementLinks {
  self: PurpleSelf
}

export interface PurpleSelf {
  href: string
  title: string
}

export enum Type {
  Status = 'Status',
}

export interface ListStatusLinks {
  self: FluffySelf
}

export interface FluffySelf {
  href: string
}
