import { getMetadata } from '@munkit/main'
import { inject } from '@adonisjs/core'
import { LucidModel } from '@adonisjs/lucid/types/model'

import GameMetric from '#models/game_metric'
import GameMetricV2 from '#models/v2/game_metric'
import User from '#models/user'
import Game from '#models/game'
import GameMetricMetadatum from '#models/game_metric_metadatum'
import { AclModelName, AclSubject, Tool } from '#config/enums'
import { ModelAttributePresenter } from '#controllers/dashboard/game/model_attribute_presenter'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import ACL from '#models/acl'
import { aclAttributeSubject } from '#utils/acl'
import GameRevenue from '#models/game_revenue'

import ToolPermissionService from './tool_permission_service.js'

@inject()
export class AttributeService {
  constructor(private toolPermissionService: ToolPermissionService) {}

  async getAttributes(modelName: string, user?: User, game?: Game) {
    const attributes = this.#getAttributes(modelName)

    if (attributes.length === 0) {
      return []
    }

    let isManager = false
    let readAttributes: string[] = []
    let writeAttributes: string[] = []

    if (user && game) {
      isManager = this.toolPermissionService.canManage(Tool.Dashboard)(user)

      await user.load('ledTeam')

      const memberships = await DashboardRoleMembership.query()
        .withScopes((s) => s.withUsers())
        .where('storeId', game.storeId)
        .where((q) => {
          q.where('user', user.email)

          if (user.ledTeam) {
            q.orWhere('roleId', user.ledTeam.roleId)
          }
        })
        .select('roleId')
        .distinct()

      const roleIds = memberships.map((m) => m.roleId)
      const acls = await ACL.query()
        .whereIn('subject', [
          aclAttributeSubject(AclSubject.AttributeRead, modelName),
          aclAttributeSubject(AclSubject.AttributeWrite, modelName),
        ])
        .whereIn('roleId', roleIds)

      readAttributes = acls
        .filter((acl) => acl.subject === aclAttributeSubject(AclSubject.AttributeRead, modelName))
        .flatMap((acl) => acl.permits)

      writeAttributes = acls
        .filter((acl) => acl.subject === aclAttributeSubject(AclSubject.AttributeWrite, modelName))
        .flatMap((acl) => acl.permits)
    }

    return attributes.map(([key, val]) => {
      const getPermission = () => {
        if (isManager || writeAttributes.includes(key)) {
          return 'rw'
        }

        if (readAttributes.includes(key)) {
          return 'r'
        }

        return 'none'
      }

      return new ModelAttributePresenter().merge({
        name: key,
        displayName: val.columnName || key,
        permission: getPermission(),
      })
    })
  }

  #getAttributes(modelName: string) {
    switch (modelName) {
      case AclModelName.GameMetric: {
        const authorizeAttributes = getMetadata('attr.authorize', GameMetric)
        const metadataAuthorizeAttributes = getMetadata('attr.authorize', GameMetricMetadatum)

        return Array.from(GameMetric.$columnsDefinitions.entries())
          .concat(Array.from(GameMetric.$computedDefinitions.entries()) as any[])
          .filter(([key]) => authorizeAttributes.some((a) => a.name === key))
          .concat(
            Array.from(GameMetricMetadatum.$columnsDefinitions.entries()).filter(([key]) =>
              metadataAuthorizeAttributes.some((a) => a.name === key)
            )
          )
      }

      case AclModelName.GameMetricV2: {
        return this.#getAuthorizedAttributes(GameMetricV2)
      }

      case AclModelName.GameRevenue: {
        return this.#getAuthorizedAttributes(GameRevenue)
      }

      default:
        return []
    }
  }

  #getAuthorizedAttributes(model: LucidModel) {
    const authorizeAttributes = getMetadata('attr.authorize', model)
    return Array.from(model.$columnsDefinitions.entries())
      .concat(Array.from(model.$computedDefinitions.entries()) as any[])
      .filter(([key]) => authorizeAttributes.some((a) => a.name === key))
  }
}
