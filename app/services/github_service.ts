import { Logger } from '@adonisjs/core/logger'
import { ConfigMapRegistry } from '@munkit/main'
import { Octokit } from 'octokit'
import config from 'config'
import pLimit, { LimitFunction } from 'p-limit'
import * as OctokitTypes from '@octokit/types'
import { createAppAuth } from '@octokit/auth-app'
import type { GraphQlQueryResponseData } from '@octokit/graphql'

import { GithubPermission } from '#configmaps/github/github_access_control'

const permissionMap = {
  [GithubPermission.Admin]: 'admin',
  [GithubPermission.Write]: 'push',
  [GithubPermission.Read]: 'pull',
  [GithubPermission.Maintain]: 'maintain',
  [GithubPermission.Minimum]: 'triage',
}

export default class GitHubService {
  #octokit: Octokit
  #limit: LimitFunction
  #org: string

  constructor(
    private logger: Logger,
    private configMapRegistry: ConfigMapRegistry
  ) {
    this.#octokit = new Octokit({
      authStrategy: createAppAuth,
      auth: {
        ...config.get('github.app'),
      },
    })
    this.#limit = pLimit(3)
    this.#org = config.get('github.org') as string
  }

  async updateTeams(teamNames: string[]) {
    const teamConfigMapCollection = this.configMapRegistry.get('cmap.gh.team')
    const memberConfigMapCollection = this.configMapRegistry.get('cmap.gh.member')

    const isAllTeams = teamNames.length === 0

    const teamConfigMaps = isAllTeams
      ? teamConfigMapCollection
      : teamConfigMapCollection.filter((team) => teamNames.includes(team.name))

    const teams = await this.listAll(this.#octokit.rest.teams.list, {
      org: this.#org,
    })

    await Promise.all(
      teamConfigMaps.map((teamConfigMap) =>
        this.#limit(async () => {
          const teamLimit = pLimit(1)

          const memberConfigMaps = teamConfigMap.members.map((member) =>
            memberConfigMapCollection.get(member.fullName)
          )

          const team = teams.find((t) => t.name === teamConfigMap.name)
          let teamSlug = team?.slug ?? ''

          if (!teamSlug) {
            const createGhTeamResponse = await this.#octokit.rest.teams
              .create({
                name: teamConfigMap.name,
                org: this.#org,
                privacy: 'closed',
              })
              .catch((err: any) => {
                err.message = `Cannot create team: ${teamConfigMap.name}, ` + err.message
                return Promise.reject(err)
              })
            teamSlug = createGhTeamResponse.data.slug
          }

          const members = await this.listAll(this.#octokit.rest.teams.listMembersInOrg, {
            org: this.#org,
            team_slug: teamSlug,
          })

          await Promise.all(
            memberConfigMaps.map((memberConfigMap) =>
              teamLimit(async () => {
                const member = members.find((m) => m.login === memberConfigMap.username)
                if (member) {
                  this.logger.debug(
                    `[GitHub] Member already in team: ${memberConfigMap.username}, team: ${teamConfigMap.name}`
                  )
                  return
                }

                await this.#octokit.rest.teams
                  .addOrUpdateMembershipForUserInOrg({
                    org: this.#org,
                    team_slug: teamSlug,
                    username: memberConfigMap.username,
                  })
                  .catch((err: any) => {
                    err.message =
                      `Cannot add user to team, user: ${memberConfigMap.fullName}, team: ${teamConfigMap.name}, ` +
                      err.message
                    return Promise.reject(err)
                  })

                this.logger.info(
                  `[GitHub] Added member: ${memberConfigMap.username}, team: ${teamConfigMap.name}`
                )
              })
            )
          )

          await Promise.all(
            members.map((member) =>
              teamLimit(async () => {
                const memberConfigMap = memberConfigMapCollection.find(
                  (m) => m.username === member.login
                )

                if (memberConfigMap) {
                  return
                }

                await this.#octokit.rest.teams
                  .removeMembershipForUserInOrg({
                    org: this.#org,
                    team_slug: teamSlug,
                    username: member.login,
                  })
                  .catch((err: any) => {
                    err.message =
                      `Cannot remove user from team, user: ${member.login}, team: ${teamConfigMap.name}, ` +
                      err.message
                    return Promise.reject(err)
                  })

                this.logger.info(
                  `[GitHub] Removed member: ${member.login}, team: ${teamConfigMap.name}`
                )
              })
            )
          )
        })
      )
    )

    if (isAllTeams) {
      await Promise.all(
        teams.map((team) =>
          this.#limit(async () => {
            if (teamConfigMapCollection.tryGet(team.name)) {
              this.logger.debug(`[GitHub] Skip team: ${team.name}`)
              return
            }

            await this.#octokit.rest.teams
              .deleteInOrg({
                org: this.#org,
                team_slug: team.slug,
              })
              .catch((err: any) => {
                err.message = `Cannot delete team: ${team.name}, ` + err.message
                return Promise.reject(err)
              })
            this.logger.info(`[GitHub] Deleted team: ${team.name}`)
          })
        )
      )
    }
  }

  async updateAccessControls(aclIds: string[]) {
    const aclConfigMapConllection = this.configMapRegistry.get('cmap.gh.acl')
    const memberConfigMapCollection = this.configMapRegistry.get('cmap.gh.member')

    const isAllAcls = aclIds.length === 0
    const aclConfigMaps = isAllAcls
      ? aclConfigMapConllection
      : aclConfigMapConllection.filter((acl) => aclIds.includes(acl.id))

    const repositories = await this.listAll(this.#octokit.rest.repos.listForOrg, {
      org: this.#org,
    })

    const teams = await this.listAll(this.#octokit.rest.teams.list, {
      org: this.#org,
    })

    await Promise.all(
      aclConfigMaps.map((aclConfigMap) =>
        this.#limit(async () => {
          const aclLimit = pLimit(1)

          await Promise.all(
            aclConfigMap.teams.map((aclTeamConfigMap) =>
              aclLimit(async () => {
                const aclTeamLimit = pLimit(1)

                const team = teams.find((t) => t.name === aclTeamConfigMap.name)

                if (!team) {
                  return this.logger.warn(`[GitHub] Team not found: ${aclTeamConfigMap.name}`)
                }

                const teamRepositories = await this.listAll(
                  this.#octokit.rest.teams.listReposInOrg,
                  {
                    org: this.#org,
                    team_slug: team.slug,
                  }
                )

                await Promise.all(
                  teamRepositories.map((teamRepository) =>
                    aclTeamLimit(async () => {
                      if (aclConfigMap.repositories.tryGet(teamRepository.name)) {
                        this.logger.debug(
                          `[GitHub] Skip add repository: ${teamRepository.name}, team: ${team.name}`
                        )
                        return
                      }

                      await this.#octokit.rest.teams
                        .removeRepoInOrg({
                          org: this.#org,
                          team_slug: team.slug,
                          owner: this.#org,
                          repo: teamRepository.name,
                        })
                        .catch((err: any) => {
                          err.message =
                            `Cannot remove repo from team, repo: ${teamRepository.name}, team: ${team.name}, error: ` +
                            err.message
                          return Promise.reject(err)
                        })

                      this.logger.info(
                        `[GitHub] Removed repository: ${teamRepository.name}, team: ${team.name}`
                      )
                    })
                  )
                )

                await Promise.all(
                  aclConfigMap.repositories.map((aclRepositoryConfigMap) =>
                    aclTeamLimit(async () => {
                      const repository = repositories.find(
                        (r) => r.name === aclRepositoryConfigMap.name
                      )

                      if (!repository) {
                        return this.logger.warn(
                          `[GitHub] Repository not found: ${aclRepositoryConfigMap.name}, team: ${team.name}`
                        )
                      }

                      const teamPermission =
                        aclRepositoryConfigMap.teams.tryGet(team.name)?.permission ??
                        aclTeamConfigMap.defaultPermission

                      await this.#octokit.rest.teams
                        .addOrUpdateRepoPermissionsInOrg({
                          org: this.#org,
                          team_slug: team.slug,
                          owner: this.#org,
                          repo: repository.name,
                          permission: permissionMap[teamPermission],
                        })
                        .catch((err: any) => {
                          err.message =
                            `Cannot update team permission, repo: ${repository.name}, team: ${team.name}, permission: ${teamPermission}, error: ` +
                            err.message
                          return Promise.reject(err)
                        })

                      this.logger.info(
                        `[GitHub] Set permission: repo: ${repository.name}, team: ${team.name}, permission: ${teamPermission}`
                      )
                    })
                  )
                )
              })
            )
          )

          await Promise.all(
            aclConfigMap.repositories.map(async (aclRepositoryConfigMap) => {
              const repository = repositories.find((r) => r.name === aclRepositoryConfigMap.name)

              if (!repository) {
                return this.logger.warn(
                  `[GitHub] Repository not found: ${aclRepositoryConfigMap.name}`
                )
              }

              const collaborators = await this.listAll(this.#octokit.rest.repos.listCollaborators, {
                owner: this.#org,
                repo: repository.name,
                affiliation: 'direct',
              })

              await Promise.all(
                aclRepositoryConfigMap.outsideCollaborators.map((collaboratorConfigMap) =>
                  aclLimit(async () => {
                    const memberConfigMap = memberConfigMapCollection.get(
                      collaboratorConfigMap.fullName
                    )

                    const member = collaborators.find((c) => c.login === memberConfigMap.username)

                    if (member) {
                      return this.logger.debug(
                        `[GitHub] Skip collaborator: ${memberConfigMap.username}, repo: ${repository.name}`
                      )
                    }

                    await this.#octokit.rest.repos
                      .addCollaborator({
                        owner: this.#org,
                        repo: repository.name,
                        username: memberConfigMap.username,
                        permission: permissionMap[collaboratorConfigMap.permission],
                      })
                      .catch((err: any) => {
                        err.message =
                          `Cannot update collaborator permission, repo: ${repository.name}, team: ${memberConfigMap.fullName}, permission: ${collaboratorConfigMap.permission}, error: ` +
                          err.message
                        return Promise.reject(err)
                      })

                    this.logger.info(
                      `[GitHub] Added collaborator: ${memberConfigMap.username}, repo: ${repository.name}, permission: ${collaboratorConfigMap.permission}`
                    )
                  })
                )
              )
            })
          )
        })
      )
    )
  }

  async updateArchives() {
    const repositoryConfigMapCollection = this.configMapRegistry.get('cmap.gh.archive')
    const archivedRepos = repositoryConfigMapCollection.filter((repo) => repo.archived)
    const repositories = await this.listRepos()

    await Promise.all(
      archivedRepos.map((archivedRepo) =>
        this.#limit(async () => {
          const repo = repositories.find((r) => r.name === archivedRepo.name)

          if (!repo || repo.archived) {
            return
          }

          await this.#octokit.rest.repos.update({
            owner: this.#org,
            repo: archivedRepo.name,
            archived: true,
            private: true,
          })

          this.logger.info(`[GitHub] Archived repository: ${archivedRepo.name}`)
        })
      )
    )

    const deleteRepos = repositoryConfigMapCollection.filter((repo) => repo.deleted)
    await Promise.all(
      deleteRepos.map((deletedRepo) => {
        this.#limit(async () => {
          const repo = repositories.find((r) => r.name === deletedRepo.name)
          if (!repo) {
            return
          }

          await this.#octokit.rest.repos.delete({
            owner: this.#org,
            repo: deletedRepo.name,
          })

          this.logger.info(`[GitHub] Deleted repository: ${deletedRepo.name}`)
        })
      })
    )
  }

  async updateProtectedBranch(repoNames: string[]) {
    const isAllRepositories = repoNames.length === 0
    const rulesetConfigMapCollection = this.configMapRegistry.get('cmap.gh.ruleset')

    const protectedBranchConfigMaps = this.configMapRegistry
      .get('cmap.gh.pbranch')
      .filter((protectedBranch) => {
        return isAllRepositories || repoNames.includes(protectedBranch.repository)
      })

    await Promise.all(
      protectedBranchConfigMaps.map((protectedBranchConfigMap) =>
        this.#limit(async () => {
          const ruleset = protectedBranchConfigMap.getRuleset(rulesetConfigMapCollection)

          const isSquashMergeEnabled = ruleset.mergeStrategies.some(
            (strategy) => strategy.name === 'SQUASH'
          )
          const isMergeEnabled = ruleset.mergeStrategies.some(
            (strategy) => strategy.name === 'MERGE'
          )

          await this.#octokit.rest.repos
            .update({
              default_branch: ruleset.defaultBranch,
              owner: this.#org,
              repo: protectedBranchConfigMap.repository,
              allow_auto_merge: false,
              delete_branch_on_merge: true,
              ...(isSquashMergeEnabled
                ? {
                    allow_squash_merge: true,
                    squash_merge_commit_message: 'BLANK',
                    squash_merge_commit_title: 'PR_TITLE',
                  }
                : {
                    allow_squash_merge: false,
                  }),
              ...(isMergeEnabled
                ? {
                    allow_merge_commit: true,
                    merge_commit_message: 'BLANK',
                    merge_commit_title: 'PR_TITLE',
                  }
                : {
                    allow_merge_commit: false,
                  }),
              allow_rebase_merge: ruleset.mergeStrategies.some(
                (strategy) => strategy.name === 'REBASE'
              ),

              allow_update_branch: true,
              has_issues: true,
              has_projects: false,
              has_wiki: true,
            })
            .catch((err: any) => {
              err.message =
                `Cannot update repo config, repo: ${protectedBranchConfigMap.repository}, ruleset id: ${ruleset.id}, error: ` +
                err.message
              return Promise.reject(err)
            })

          this.logger.debug(`[GitHub] Updated repository: ${protectedBranchConfigMap.repository}`)

          const branchLimit = pLimit(1)

          await Promise.all(
            ruleset.environments.map((environment) =>
              branchLimit(async () => {
                await this.#octokit.rest.repos
                  .createOrUpdateEnvironment({
                    environment_name: environment.name,
                    owner: this.#org,
                    repo: protectedBranchConfigMap.repository,
                  })
                  .catch((err: any) => {
                    err.message =
                      `Cannot create environment, repo: ${protectedBranchConfigMap.repository}, environment: ${environment.name}, error: ` +
                      err.message
                    return Promise.reject(err)
                  })

                this.logger.debug(
                  `[GitHub] Updated environment, name: ${environment.name}, repo: ${protectedBranchConfigMap.repository}`
                )
              })
            )
          )
          const branchProtectionsQuery: GraphQlQueryResponseData = await this.#octokit.graphql({
            query: `query($org: String!, $repo: String!) {
              repository(owner: $org, name: $repo) {
                branchProtectionRules(first: 100) {
                  edges {
                    node {
                      id
                      pattern
                      dismissesStaleReviews
                      isAdminEnforced
                      requiredStatusChecks {
                        app {
                          id
                        }
                        context
                      }
                      requiredApprovingReviewCount
                      requiresCodeOwnerReviews
                    }
                  }
                }
              }
            }`,
            org: this.#org,
            repo: protectedBranchConfigMap.repository,
          })

          const repositoryQuery: GraphQlQueryResponseData = await this.#octokit.graphql({
            query: `query($org: String!, $repo: String!) {
              repository(owner: $org, name: $repo) {
                id
              }
            }`,
            org: this.#org,
            repo: protectedBranchConfigMap.repository,
          })

          const repositoryId = repositoryQuery.repository.id

          await Promise.all(
            ruleset.branches.map((branchConfigMap) =>
              branchLimit(async () => {
                const branchProtection =
                  branchProtectionsQuery.repository.branchProtectionRules.edges.find(
                    (edge: any) => edge.node.pattern === branchConfigMap.name
                  )?.node

                const payload = {
                  requiresApprovingReviews: true,
                  requiredApprovingReviewCount:
                    branchConfigMap.rules.find((rule) => rule.rule === 'APPROVAL_REQUIRED')
                      ?.parsedValue ?? 0,
                  isAdminEnforced: !branchConfigMap.rules.find(
                    (rule) => rule.rule === 'ADMIN_BYPASS'
                  )?.parsedValue,
                  requiresCodeOwnerReviews: Boolean(
                    branchConfigMap.rules.find((rule) => rule.rule === 'CODE_OWNERS')?.parsedValue
                  ),
                  dismissesStaleReviews: Boolean(
                    branchConfigMap.rules.find((rule) => rule.rule === 'APPROVAL_DISMISS')
                      ?.parsedValue
                  ),
                }

                if (!branchProtection) {
                  await this.#octokit
                    .graphql({
                      query: `mutation($rule: CreateBranchProtectionRuleInput!) {
                      createBranchProtectionRule(input: $rule) {
                        branchProtectionRule {
                          repository {
                            name
                          }
                        }
                      }
                    }`,
                      rule: {
                        repositoryId,
                        pattern: branchConfigMap.name,
                        ...payload,
                      },
                    })
                    .catch((err: any) => {
                      err.message =
                        `Cannot create protected branch, repo: ${protectedBranchConfigMap.repository}, branch: ${branchConfigMap.name}, error: ` +
                        err.message
                      return Promise.reject(err)
                    })
                } else {
                  await this.#octokit
                    .graphql({
                      query: `mutation($rule: UpdateBranchProtectionRuleInput!) {
                      updateBranchProtectionRule(input: $rule) {
                        branchProtectionRule {
                          repository {
                            name
                          }
                        }
                      }
                    }`,
                      rule: {
                        branchProtectionRuleId: branchProtection.id,
                        ...payload,
                      },
                    })
                    .catch((err: any) => {
                      err.message =
                        `Cannot update protected branch, repo: ${protectedBranchConfigMap.repository}, branch: ${branchConfigMap.name}, error: ` +
                        err.message
                      return Promise.reject(err)
                    })
                }
              })
            )
          )
        })
      )
    )
  }

  async listRepos() {
    const repositories = await this.listAll(this.#octokit.rest.repos.listForOrg, {
      org: this.#org,
    })

    return repositories
  }

  async listAll<R extends OctokitTypes.RequestInterface>(
    request: R,
    parameters?: Parameters<R>[0]
  ) {
    const results = []
    for await (const response of this.#octokit.paginate.iterator(request, {
      ...(parameters as any),
      per_page: 100,
    })) {
      // do whatever you want with each response, break out of the loop, etc.
      results.push(response.data)
    }
    return results.flat() as Awaited<ReturnType<typeof request>>['data']
  }
}
