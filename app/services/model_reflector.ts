import { ApplicationService } from '@adonisjs/core/types'
import { LucidModel } from '@adonisjs/lucid/types/model'

export class ModelReflector {
  #models = new Map<string, LucidModel>()

  constructor(private app: ApplicationService) {}

  async initAsync() {
    const { fsImportAll } = await import('@poppinss/utils')
    const modelsPath = this.app.modelsPath()
    const models: Record<string, LucidModel> = await fsImportAll(modelsPath)
    Object.entries(models).forEach(([_filename, model]) => {
      this.#models.set(model.table, model)
    })
  }

  getModel(table: string) {
    if (!this.#models.has(table)) {
      throw new Error(`Model ${table} not found`)
    }

    return this.#models.get(table)!
  }
}
