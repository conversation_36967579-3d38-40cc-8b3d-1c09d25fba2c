import { inject } from '@adonisjs/core'

import { Permission, Tool } from '#config/enums'
import User from '#models/user'

@inject()
export default class ToolPermissionService {
  can(user: User, tool: Tool, permission: Permission) {
    return user.toolPermissions.some(
      (t) =>
        (t.tool === Tool.All || t.tool === tool) &&
        (t.action === Permission.Manage || t.action === permission)
    )
  }

  canManage(tool: Tool) {
    return (user: User) => {
      return this.can(user, tool, Permission.Manage)
    }
  }

  canView(tool: Tool) {
    return (user: User) => {
      return this.can(user, tool, Permission.View)
    }
  }
}
