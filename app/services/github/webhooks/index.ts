import { PullRequestClosed } from './pull_request_closed.js'
import { PullRequestOpened } from './pull_request_opened.js'
import { PullRequestReviewRequested } from './pull_request_review_requested.js'

export type GithubWebhookEvent =
  | (PullRequestOpened & {
      action: 'opened'
    })
  | (PullRequestReviewRequested & { action: 'review_requested' })
  | (PullRequestClosed & { action: 'closed' })
