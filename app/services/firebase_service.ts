import firebase from '@googleapis/firebase'
import { OAuth2Client } from 'google-auth-library'
import app from '@adonisjs/core/services/app'
import config from 'config'
import { inject } from '@adonisjs/core'

import { VaultKeyClient } from '#services/vaultkey_client'

export interface FirebaseProjectData {
  project: any
  analyticsProperty: any
}

export default class FirebaseService {
  @inject()
  private async createFirebaseClient() {
    const vkClient = await app.container.make(VaultKeyClient)
    const creds = await vkClient.getOAuth2Credentials(config.get('googleapi').vaultkeyId)
    const oauthClient = new OAuth2Client({
      clientId: creds.clientId,
      clientSecret: creds.clientSecret,
      redirectUri: creds.redirectUri,
    })

    oauthClient.setCredentials({
      access_token: creds.accessToken,
      scope: creds.scope,
      token_type: 'Bearer',
    })

    return new firebase.firebase_v1beta1.Firebase({
      auth: oauthClient as any,
    })
  }

  @inject()
  async getProjectData(projectId?: string): Promise<FirebaseProjectData> {
    const client = await this.createFirebaseClient()
    const projects = await client.projects.list({
      pageSize: 99999,
    })
    const isProjectExist = projects.data.results?.find((project) => project.projectId === projectId)
    if (!isProjectExist) {
      return {
        project: null,
        analyticsProperty: null,
      }
    }

    const [project, analyticsDetails] = await Promise.all([
      client.projects.get({
        name: `projects/${projectId}`,
      }),
      client.projects.getAnalyticsDetails({
        name: `projects/${projectId}/analyticsDetails`,
      }),
    ])

    return {
      project: project.data,
      analyticsProperty: analyticsDetails.data.analyticsProperty,
    }
  }

  @inject()
  async getProjectInfo(projectId: string) {
    const client = await this.createFirebaseClient()

    return await client.projects.get({
      name: `projects/${projectId}`,
    })
  }

  @inject()
  async getAnalyticsDetails(projectId: string) {
    const client = await this.createFirebaseClient()

    return await client.projects.getAnalyticsDetails({
      name: `projects/${projectId}/analyticsDetails`,
    })
  }
}
