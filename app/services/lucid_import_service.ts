import { inject } from '@adonisjs/core'
import { Logger } from '@adonisjs/core/logger'
import { Database } from '@adonisjs/lucid/database'
import { LucidModel, ModelAttributes } from '@adonisjs/lucid/types/model'
import { chunk, omit } from 'lodash-es'

@inject()
export class LucidImportService {
  constructor(
    private db: Database,
    private logger: Logger
  ) {}

  async import<TModel extends LucidModel>(
    Model: TModel,
    records: InstanceType<TModel>[],
    {
      chunkSize = 1000,
      connection,
      excludedAttributes = [] as any,
    }: {
      chunkSize?: number
      connection?: string
      excludedAttributes?: Array<keyof ModelAttributes<InstanceType<TModel>>>
    } = {}
  ) {
    const transaction = await this.db.connection(connection || Model.connection).transaction()
    try {
      await Promise.sequence(chunk(records, chunkSize), async (chunked) => {
        await transaction
          .insertQuery()
          .knexQuery.insert(
            chunked.map((r) => {
              const attrs = omit(r.$attributes, excludedAttributes)
              return r.prepareForAdapter(attrs)
            })
          )
          .into(Model.table)
      })
      await transaction.commit()
    } catch (err) {
      this.logger.error(err)
      await transaction.rollback()
    }
  }
}
