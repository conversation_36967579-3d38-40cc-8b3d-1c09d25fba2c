import { inject } from '@adonisjs/core'

import User from '#models/user'
import Game from '#models/game'
import DashboardRoleMembership from '#models/dashboard_role_membership'

@inject()
export class UserService {
  async findManyByEmails(emails: string[]) {
    if (emails.length === 0) {
      return []
    }

    const emailToUser = await User.query()
      .preload('team')
      .whereIn('email', emails)
      .then((us) => new Map(us.map((u) => [u.email, u])))

    const missings = emails
      .filter((email) => !emailToUser.has(email))
      .map((e) =>
        new User().merge({
          email: e,
        })
      )

    const newUsers = await User.createMany(missings)
    return Array.from(emailToUser.values()).concat(newUsers)
  }

  async isInProject(user: User, gameId: Game): Promise<boolean>
  async isInProject(user: User, gameId: Game['id']): Promise<boolean>
  async isInProject(user: User, gameOrGameId: Game['id'] | Game): Promise<boolean> {
    const gameId = typeof gameOrGameId === 'string' ? gameOrGameId : gameOrGameId.id
    await user.load('ledTeam')

    return await DashboardRoleMembership.query()
      .withScopes((s) => s.withUsers())
      .where('storeId', gameId)
      .whereIn(
        'user',
        User.query()
          .select('email')
          .where('id', user.id)
          .orWhere((q) => {
            if (user.ledTeam) {
              q.orWhere('teamId', user.ledTeam.id)
            }
          })
      )
      .count('id')
      .first()
      .then((r) => r?.$extras?.['count'] || 0)
      .then((count) => count > 0)
  }
}
