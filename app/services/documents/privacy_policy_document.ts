import type { HttpContext } from '@adonisjs/core/http'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import Document from '#models/document'

export class PrivacyPolicyDocument {
  name = 'landing-page-privacy-policy'

  async onUpdate(document: Document) {
    document.id = 'landing-page-privacy-policy'
  }

  async onAuthorizeUpdate(bouncer: HttpContext['bouncer']) {
    await bouncer.with('LandingPageDocumentPolicy').authorize('edit')
  }

  async onAuthorizeShow(bouncer: HttpContext['bouncer']) {
    await bouncer.with('LandingPageDocumentPolicy').authorize('edit')
  }

  onQuery(query: ModelQueryBuilderContract<typeof Document, Document>) {
    return query.where('id', 'landing-page-privacy-policy')
  }
}

export default PrivacyPolicyDocument
