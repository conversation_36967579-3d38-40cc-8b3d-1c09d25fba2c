import type { HttpContext } from '@adonisjs/core/http'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import Document from '#models/document'

export class PurrateKingAdsTxtDocument {
  name = 'purrate-king-ads-txt'

  async onUpdate(document: Document) {
    document.id = 'purrate-king-ads-txt'
  }

  async onAuthorizeUpdate(bouncer: HttpContext['bouncer']) {
    await bouncer.with('LandingPageDocumentPolicy').authorize('edit')
  }

  async onAuthorizeShow(bouncer: HttpContext['bouncer']) {
    await bouncer.with('LandingPageDocumentPolicy').authorize('edit')
  }

  onQuery(query: ModelQueryBuilderContract<typeof Document, Document>) {
    return query.where('id', 'purrate-king-ads-txt')
  }
}

export default PurrateKingAdsTxtDocument
