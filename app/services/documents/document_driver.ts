import AdsTxtDocument from './ads_txt_document.js'
import AllDocument from './all_document.js'
import PrivacyPolicyDocument from './privacy_policy_document.js'
import PurrateKingAdsTxtDocument from './purrate_king_ads_txt_document.js'

export class DocumentDriverFactory {
  drivers = [new AdsTxtDocument(), new PrivacyPolicyDocument(), new PurrateKingAdsTxtDocument()]

  getDriver(name: string) {
    return this.drivers.find((driver) => driver.name === name) || new AllDocument()
  }
}
