import type { HttpContext } from '@adonisjs/core/http'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import Document from '#models/document'
import { Tool } from '#config/enums'

export class AllDocument {
  name = '*'

  async onUpdate(_document: Document) {}

  async onAuthorizeUpdate(bouncer: HttpContext['bouncer']) {
    await bouncer.with('ToolPolicy').authorize('manage', Tool.Dashboard)
  }

  async onAuthorizeShow(bouncer: HttpContext['bouncer']) {
    await bouncer.with('ToolPolicy').authorize('view', Tool.Dashboard)
  }

  onQuery(query: ModelQueryBuilderContract<typeof Document, Document>) {
    return query
  }
}

export default AllDocument
