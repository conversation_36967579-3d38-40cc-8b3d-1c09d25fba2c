import type { HttpContext } from '@adonisjs/core/http'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import Document from '#models/document'

export class AdsTxtDocument {
  name = 'landing-page-ads-txt'

  async onUpdate(document: Document) {
    document.id = 'landing-page-ads-txt'
  }

  async onAuthorizeUpdate(bouncer: HttpContext['bouncer']) {
    await bouncer.with('LandingPageDocumentPolicy').authorize('edit')
  }

  async onAuthorizeShow(bouncer: HttpContext['bouncer']) {
    await this.onAuthorizeUpdate(bouncer)
  }

  onQuery(query: ModelQueryBuilderContract<typeof Document, Document>) {
    return query.where('id', 'landing-page-ads-txt')
  }
}

export default AdsTxtDocument
