import { inject } from '@adonisjs/core'
import { Logger } from '@adonisjs/core/logger'
import { ErrorTrackingService } from '@mirai-game-studio/adonis-sdk/error_tracking'
import {
  Client,
  Events,
  GatewayIntentBits,
  MessageCreateOptions,
  MessagePayload,
  TextChannel,
  ThreadChannel,
} from 'discord.js'
import config from 'config'

@inject()
export default class DiscordBotService {
  #client!: Client

  constructor(
    private logger: Logger,
    private errorTrackingService: ErrorTrackingService
  ) {}

  async boot() {
    this.#client = await this.#createClient()
  }

  async shutdown() {
    await this.#client.destroy()
  }

  async useClient(fn: (client: Client) => void) {
    return fn(this.#client)
  }

  async sendMessage(channelId: string, message: MessageCreateOptions | MessagePayload) {
    const channel = (await this.#client.channels.fetch(channelId)) as
      | TextChannel
      | ThreadChannel
      | null
    if (!channel) {
      return this.logger.error(`[Discord] channel not found: ${channelId}`)
    }

    await channel.send(message)
  }

  async #createClient(): Promise<Client> {
    return new Promise((resolve, reject) => {
      const client = new Client({ intents: [GatewayIntentBits.Guilds] })
      client.once(Events.ClientReady, async (readyClient) => {
        this.logger.info(`Logged in as ${readyClient.user.tag}!`)
        return resolve(readyClient)
      })

      client.once(Events.Error, async (error) => {
        this.logger.error(`[Discord] cannot boot client, error: ${error.message}`)
        await this.errorTrackingService.notify(error)
        await client.destroy()
        return reject(error)
      })

      client.login(config.get('discord.bot.token'))
    })
  }
}
