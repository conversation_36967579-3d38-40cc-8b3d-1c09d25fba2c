import { Logger } from '@adonisjs/core/logger'
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import config from 'config'
import { uniq } from 'lodash-es'

import type { GetWorkPackage } from './openproject/get_work_package.js'
import type { Element, ListStatus } from './openproject/list_status.js'

export default class OpenProjectService {
  #client: AxiosInstance
  #statuses: Element[] | undefined

  constructor(private logger: Logger) {
    this.#client = axios.create({
      baseURL: config.get('openproject.endpoint'),
      auth: config.get('openproject.apikey') as { username: string; password: string },
    })
  }

  getWorkPackageIdsFromIntegration(pullRequestBody: string) {
    if (!pullRequestBody) {
      return []
    }

    return uniq(
      Array.from(pullRequestBody.matchAll(/OP#(\d+)|\/work_packages\/(\d+)|\/wp\/(\d+)/gm)).map(
        (e) => Number(e[1] ?? e[2] ?? e[3])
      )
    )
  }

  async updateWorkPackageStatus(workPackage: GetWorkPackage, statusText: string) {
    const { lockVersion, id } = workPackage

    const statuses = await this.listStatus()
    const status = statuses.find((s) => s.name === statusText)

    if (!status) {
      const err = new Error(`status not found, statusText: ${statusText}`)
      this.logger.error(err.message)
      throw err
    }

    await this.#request({
      url: `/v3/work_packages/${id}`,
      method: 'PATCH',
      data: {
        lockVersion: lockVersion,
        _links: {
          status: {
            href: status['_links'].self.href,
          },
        },
      },
    })
  }

  async getWorkPackage(id: number) {
    return await this.#request<GetWorkPackage>({
      url: `/v3/work_packages/${id}`,
      method: 'GET',
    })
  }

  async listStatus() {
    if (!this.#statuses) {
      this.#statuses = await this.#request<ListStatus>({
        url: '/v3/statuses',
        method: 'GET',
      }).then((data) => data['_embedded'].elements)
    }

    return this.#statuses!
  }

  async #request<T>(cfg: AxiosRequestConfig): Promise<T> {
    return await this.#client.request<T>(cfg).then((response) => response.data)
  }
}
