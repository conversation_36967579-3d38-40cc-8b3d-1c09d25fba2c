import fs from 'node:fs'

import { JWT } from 'google-auth-library'
import config from 'config'
import { androidpublisher_v3 } from '@googleapis/androidpublisher'
import { inject } from '@adonisjs/core'
import { Job } from '@mirai-game-studio/adonis-sdk/types/queue'

import { GooglePlayPublisherConfigMap } from '#configmaps/publishing/game_publish'

import DownloaderFactory from './downloader_factory.js'

export type GooglePlayReleaseData = {
  installableDownloadUrl: string
  traceDownloadUrl: string
  versionCode: number
  version: string
  changelog?: string
}

@inject()
export default class GooglePlayPublishService {
  #client: androidpublisher_v3.Androidpublisher

  constructor(private downloaderFactory: DownloaderFactory) {
    const auth = new JWT(config.get('androidpublisher.gservice'))

    this.#client = new androidpublisher_v3.Androidpublisher({
      auth: auth as any,
    })
  }

  async release(
    googlePlayPublisherConfigMap: GooglePlayPublisherConfigMap,
    release: GooglePlayReleaseData,
    job: Job
  ) {
    const { packageName } = googlePlayPublisherConfigMap

    const edit = await this.#client.edits.insert({
      packageName,
      requestBody: {
        id: 'toolkit',
        expiryTimeSeconds: '3600',
      },
    })

    const editId = edit.data.id!

    await job.log(`new edit: ${editId}`)

    try {
      await this.#update(googlePlayPublisherConfigMap, release, editId, job)

      await this.#client.edits.commit({
        editId,
        packageName,
      })
    } catch (error: any) {
      await job.log(`deleting edit: ${editId}`)
      await this.#client.edits.delete({
        editId,
        packageName,
      })

      throw error
    }
  }

  async #update(
    googlePlayPublisherConfigMap: GooglePlayPublisherConfigMap,
    release: GooglePlayReleaseData,
    editId: string,
    job: Job
  ) {
    const { packageName } = googlePlayPublisherConfigMap

    const [aabPath, symbolsPath] = await Promise.all([
      this.downloaderFactory
        .for(release.installableDownloadUrl)
        .downloadAsFile(release.installableDownloadUrl),
      this.downloaderFactory.for(release.traceDownloadUrl).downloadAsFile(release.traceDownloadUrl),
    ])

    await job.log(`downloaded aab: ${aabPath}, symbols: ${symbolsPath}`)

    try {
      const { versionCode, version, changelog } = release
      const track = googlePlayPublisherConfigMap.track.toLowerCase()

      await job.log(`Start upload aab: ${aabPath}`)

      await this.#client.edits.bundles.upload({
        packageName,
        editId,
        media: {
          mimeType: 'application/octet-stream',
          body: fs.createReadStream(aabPath),
        },
      })

      await job.log(`Start upload symbols: ${symbolsPath}`)

      await this.#client.edits.deobfuscationfiles.upload({
        packageName,
        editId,
        apkVersionCode: versionCode,
        deobfuscationFileType: 'nativeCode',
        media: {
          mimeType: 'application/octet-stream',
          body: fs.createReadStream(symbolsPath),
        },
      })

      await job.log(`Updating track: ${track}`)

      await this.#client.edits.tracks.update({
        packageName,
        editId,
        track,
        requestBody: {
          track,
          releases: [
            {
              versionCodes: [`${versionCode}`],
              status: googlePlayPublisherConfigMap.status.toLowerCase(),
              name: version,
              releaseNotes: changelog
                ? [
                    {
                      language: 'en-US',
                      text: changelog,
                    },
                  ]
                : undefined,
            },
          ],
        },
      })
    } finally {
      fs.rmSync(aabPath)
      fs.rmSync(symbolsPath)

      await job.log(`removed aab: ${aabPath}, symbols: ${symbolsPath}`)
    }
  }
}
