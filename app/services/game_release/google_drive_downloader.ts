import fs from 'node:fs'
import path from 'node:path'

import { JWT } from 'google-auth-library'
import app from '@adonisjs/core/services/app'
import { v4 as uuid } from 'uuid'
import config from 'config'
import { drive_v3 } from '@googleapis/drive'
import { Logger } from '@adonisjs/core/logger'

export interface DownloaderContract {
  downloadAsFile(url: string): Promise<string>
}

export default class GoogleDriveDownloader implements DownloaderContract {
  #client: drive_v3.Drive

  constructor(private logger: Logger) {
    const auth = new JWT(config.get('androidpublisher.gservice'))
    this.#client = new drive_v3.Drive({
      auth: auth as any,
    })
  }

  async downloadAsFile(url: string): Promise<string> {
    const filename = uuid()
    const downloadDir = app.tmpPath(`downloads`)
    fs.mkdirSync(downloadDir, { recursive: true })
    const downloadPath = path.join(downloadDir, filename)

    const fileId = url.match(/\/file\/d\/(.*)\//)?.[1]

    return await this.#client.files
      .get(
        {
          fileId,
          alt: 'media',
        },
        { responseType: 'stream' }
      )
      .then((res) => {
        return new Promise((resolve, reject) => {
          const dest = fs.createWriteStream(downloadPath)

          res.data
            .on('end', () => {
              resolve(downloadPath)
            })
            .on('error', (err) => {
              this.logger.error(err.message)
              this.logger.error(err.stack)
              reject(err)
            })
            .pipe(dest as any)
        })
      })
  }
}
