import axios, { AxiosInstance } from 'axios'
import { Logger } from '@adonisjs/core/logger'

export interface VaultkeyClientConfig {
  endpoint: string
  apiKey: string
}

interface VaultKeyGoogleOAuth2Credential {
  access_token: string
  client_id: string
  client_secret: string
  redirect_uri: string
  refresh_token: string
  scope: string
}

export class VaultKeyClient {
  private client: AxiosInstance

  constructor(
    options: VaultkeyClientConfig,
    private logger: Logger
  ) {
    this.client = axios.create({
      baseURL: options.endpoint,
      headers: {
        'X-API-KEY': options.apiKey,
      },
    })
  }

  async getOAuth2Credentials(id: string) {
    try {
      return this.client
        .get<VaultKeyGoogleOAuth2Credential>(`/api/credentials/${id}`)
        .then((response) => response.data)
        .then((data) => ({
          refreshToken: data['refresh_token'],
          clientId: data['client_id'],
          accessToken: data['access_token'],
          clientSecret: data['client_secret'],
          redirectUri: data['redirect_uri'],
          scope: data['scope'],
        }))
    } catch (err) {
      this.logger.error(
        'Cannot get credentials from <PERSON><PERSON><PERSON>, error: %s, status: %s',
        err.message,
        err.response?.status || 'N/A'
      )
      throw err
    }
  }
}
