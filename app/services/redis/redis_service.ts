import { inject } from '@adonisjs/core'
import { LucidModel, LucidRow } from '@adonisjs/lucid/types/model'
import parseDuration from 'parse-duration'
import type { RedisService as AdonisRedisService } from '@adonisjs/redis/types'

class BaseRedisKey {
  constructor(
    public value: string,
    public exp?: number | string
  ) {}
}

export class RedisStringKey extends BaseRedisKey {
  type: 'String' = 'String' as const
}

@inject()
export class RedisService {
  constructor(private redis: AdonisRedisService) {}

  get(key: RedisStringKey): Promise<string | null>
  get<T extends LucidModel>(key: RedisStringKey, cls: T): Promise<InstanceType<T> | null>

  async get<T extends LucidModel>(key: RedisStringKey, cls?: T) {
    const data = await this.redis.get(key.value)
    if (!data) return null

    if (cls) {
      return cls.$createFromAdapterResult(JSON.parse(data))
    } else {
      return data
    }
  }

  async getMany<T extends LucidModel>(key: RedisStringKey, cls: T): Promise<InstanceType<T>[]> {
    const data = await this.redis.get(key.value)
    if (!data) return []

    return JSON.parse(data).map((d: any) => cls.$createFromAdapterResult(d))
  }

  save(key: RedisStringKey, value: string): Promise<string>
  save<T extends LucidRow>(key: RedisStringKey, value: T): Promise<T>
  save<T extends LucidRow>(key: RedisStringKey, value: T[]): Promise<T[]>

  async save<T extends LucidRow>(
    key: RedisStringKey,
    value: T | T[] | string
  ): Promise<T | T[] | string> {
    const exp = typeof key.exp === 'string' ? parseDuration(key.exp) : key.exp
    const setArgs = exp ? ['EX', exp] : []
    if (Array.isArray(value)) {
      await this.redis.set(
        key.value,
        JSON.stringify(value.map((v) => v.prepareForAdapter(v.$attributes))),
        // @ts-expect-error
        ...setArgs
      )
    } else if (typeof value === 'string') {
      await this.redis.set(
        key.value,
        value,
        // @ts-expect-error
        ...setArgs
      )
    } else {
      await this.redis.set(
        key.value,
        JSON.stringify(value.prepareForAdapter(value.$attributes)),
        // @ts-expect-error
        ...setArgs
      )
    }
    return value
  }
}
