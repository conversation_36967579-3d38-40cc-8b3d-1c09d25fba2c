import { BaseModel, column } from '@adonisjs/lucid/orm'
import { chain } from 'lodash-es'

import DashboardRoleMembership from '#models/dashboard_role_membership'
import DashboardRoleMembershipUser from '#models/dashboard_role_membership_user'

export class GameRolePresenter extends BaseModel {
  @column()
  declare id: string

  @column()
  declare roles: Array<{ id: string; users: DashboardRoleMembershipUser[] }>

  static present(memberships: DashboardRoleMembership[]): GameRolePresenter[] {
    return chain(memberships)
      .groupBy('storeId')
      .map((roles, gameId) => {
        return new GameRolePresenter().merge({
          id: gameId,
          roles: roles.map((role) => ({
            id: role.roleId,
            users: role.users,
          })),
        })
      })
      .value()
  }
}
