import { BaseModel, column } from '@adonisjs/lucid/orm'
import { first, groupBy, last, orderBy, toPairs } from 'lodash-es'

import GameLevelDrop from '#models/game_level_drop'
import { safeDivide } from '#utils/math'

export class GameLevelDropPresenter extends BaseModel {
  @column()
  declare version: string

  @column()
  declare world: number

  @column()
  declare level: number

  @column()
  declare activeUserCount: number

  @column()
  declare userDropCount: number

  @column()
  declare userDropRate: number

  @column()
  declare completionRate: number

  @column()
  declare winRate: number

  @column()
  declare playtimeSecPerUser: number

  @column()
  declare overallUserDropRate: number

  @column()
  declare attemptCountPerUser: number

  /**
   * This method support single version only
   */
  static presentList(models: GameLevelDrop[]): GameLevelDropPresenter[] {
    const worldToLevels = groupBy(models, (ld) => ld.world)
    return orderBy(toPairs(worldToLevels), (p) => p[0], 'asc').flatMap(([_world, levels]) => {
      levels = orderBy(levels, (l) => l.level, 'asc')
      const lastLevel = last(levels)!
      const firstLevel = first(levels)!
      let totalDropCount = 0

      return levels.map((l, index) => {
        const nextLevel = levels[index + 1]
        const isLastLevel = l === lastLevel

        const userDropCount = isLastLevel ? 0 : l.activeUserCount - nextLevel!.activeUserCount
        totalDropCount += userDropCount

        const presenter = new GameLevelDropPresenter().merge({
          world: l.world,
          level: l.level,
          version: l.version,
          activeUserCount: l.activeUserCount,
          winRate: l.winRate,
          completionRate: l.completionRate,
          playtimeSecPerUser: l.playtimeSecPerUser,
          attemptCountPerUser: l.attemptCountPerUser,
          userDropCount,
          userDropRate: safeDivide(userDropCount, l.activeUserCount),
          overallUserDropRate: safeDivide(totalDropCount, firstLevel.activeUserCount),
        })

        return presenter
      })
    })
  }
}
