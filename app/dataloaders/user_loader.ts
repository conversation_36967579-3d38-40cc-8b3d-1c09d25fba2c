import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'

import User from '#models/user'

@inject()
export class UserLoader {
  private batch = new DataLoader(
    async (ids: readonly User['id'][]): Promise<Array<User>> => {
      const records = await User.query()
        .whereIn('id', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => records.get(id)!)
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: User['id']) {
    return this.batch.load(id)
  }

  loadById = this.load
}
