import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { ModelObject } from '@adonisjs/lucid/types/model'

import Campaign from '#models/campaign'

@inject()
export class CampaignLoader {
  private batch = new DataLoader(
    async (ids: readonly Campaign['id'][]): Promise<Array<ModelObject>> => {
      const records = await Campaign.query()
        .whereIn('id', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => records.get(id)!.serialize())
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: Campaign['id']) {
    return this.batch.load(id)
  }
}
