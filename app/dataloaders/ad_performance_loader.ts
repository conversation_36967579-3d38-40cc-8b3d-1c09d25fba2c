import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { DateTime } from 'luxon'
import { CherryPick } from '@adonisjs/lucid/types/model'

import GameMetric from '#models/v2/game_metric'
import GameRevenue from '#models/game_revenue'
import GameRevenuePolicy from '#policies/dashboard/game/game_revenue_policy'
import User from '#models/user'
import Game from '#models/game'
import { DataSource } from '#graphql/main'

@inject()
export class AdPerformanceLoader {
  private gameIdToCherryPick: Map<string, CherryPick> = new Map()
  declare user: User
  declare source: DataSource

  constructor(private gameRevenuePolicy: GameRevenuePolicy) {}

  private batch = new DataLoader(
    async (keys: readonly [string, string][]) => {
      const revsQuery = this.query
        .from(
          this.query
            .groupBy('storeId', 'date', 'adType')
            .select('storeId', 'date', 'adType')
            .max(
              GameRevenue.columnName('dailyActiveUserCount'),
              GameRevenue.columnName('dailyActiveUserCount')
            )
            .sum(
              GameRevenue.columnName('impressionCount'),
              GameRevenue.columnName('impressionCount')
            )
            .sum(GameRevenue.columnName('revenue'), GameRevenue.columnName('revenue'))
            .as(GameRevenue.table)
        )
        .select('storeId', 'date', 'adType', 'revenue', 'impressionCount', 'dailyActiveUserCount')
        .withScopes((s) => {
          s.selectArpDau()
          s.selectEcpm()
          s.selectImpsDau()
        })
        .whereIn(
          ['storeId', 'date'],
          Array.from(keys).map(([gameId, date]) => [
            gameId,
            DateTime.fromFormat(date, 'yyyy-MM-dd').toJSDate(),
          ])
        )

      const revs = await revsQuery

      return Promise.sequence(keys, async (key) => {
        const [gameId, date] = key

        if (!this.gameIdToCherryPick.has(gameId)) {
          this.gameIdToCherryPick.set(
            gameId,
            await this.gameRevenuePolicy.cherryPick(
              this.user,
              await Game.findOrFail(gameId),
              this.source
            )
          )
        }

        const cherryPick = this.gameIdToCherryPick.get(gameId)!

        return revs
          .filter(
            (rev) =>
              rev.storeId === gameId &&
              rev.date.hasSame(DateTime.fromFormat(date, 'yyyy-MM-dd'), 'day')
          )
          .map((rev) => rev.serialize(cherryPick))
      })
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  private get query() {
    return GameRevenue.query({ connection: GameRevenue.connectionInContext(this.source) })
  }

  async load({
    user,
    gameId,
    date,
    source,
    isAggregation,
  }: {
    user: User
    gameId: GameMetric['gameId']
    date: GameMetric['date']
    source?: DataSource
    isAggregation: boolean
  }) {
    this.user = user
    this.source = source || DataSource.Origin

    if (isAggregation) {
      const revs = await this.query
        .from(
          this.query
            .from(
              this.query
                .groupBy('storeId', 'date', 'adType')
                .select('storeId', 'date', 'adType')
                .max(
                  GameRevenue.columnName('dailyActiveUserCount'),
                  GameRevenue.columnName('dailyActiveUserCount')
                )
                .sum(
                  GameRevenue.columnName('impressionCount'),
                  GameRevenue.columnName('impressionCount')
                )
                .where('storeId', gameId)
                .sum(GameRevenue.columnName('revenue'), GameRevenue.columnName('revenue'))
                .as(GameRevenue.table)
            )
            .select('storeId', 'adType')
            .sum(
              GameRevenue.columnName('dailyActiveUserCount'),
              GameRevenue.columnName('dailyActiveUserCount')
            )
            .sum(
              GameRevenue.columnName('impressionCount'),
              GameRevenue.columnName('impressionCount')
            )
            .sum(GameRevenue.columnName('revenue'), GameRevenue.columnName('revenue'))
            .groupBy('storeId', 'adType')
            .where('storeId', gameId)
            .as(GameRevenue.table)
        )
        .select('adType', 'revenue', 'dailyActiveUserCount', 'impressionCount')
        .withScopes((s) => {
          s.selectArpDau()
          s.selectEcpm()
          s.selectImpsDau()
        })

      return revs
    }

    return this.batch.load([gameId, date.toString()])
  }
}
