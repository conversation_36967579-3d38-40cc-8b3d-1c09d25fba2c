import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'

import GameMetricMetadatum from '#models/game_metric_metadatum'
import Game from '#models/game'

@inject()
export class GameMetricMetadataLoader {
  declare gameId: Game['id']

  private batch = new DataLoader(
    async (dates: readonly string[]): Promise<Array<GameMetricMetadatum | null>> => {
      const metadata = await GameMetricMetadatum.query()
        .where('gameId', this.gameId)
        .whereIn('date', Array.from(dates))
        .then((rows) => new Map(rows.map((row) => [row.date.toISODate(), row])))

      return dates.map((date) => metadata.get(date) || null)
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(date: any) {
    return this.batch.load(date)
  }
}
