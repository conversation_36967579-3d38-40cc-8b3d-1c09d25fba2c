import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { uniq } from 'lodash-es'

import Workflow from '#models/workflow'

@inject()
export class WorkflowLoader {
  private readonly batch = new DataLoader(
    async (workflowIds: readonly Workflow['id'][]) => {
      const records = await Workflow.query()
        .whereIn('id', uniq(workflowIds))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return workflowIds.map((workflowId) => records.get(workflowId))
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 100),
    }
  )

  async load(workflowId: Workflow['id']) {
    return this.batch.load(workflowId)
  }
}
