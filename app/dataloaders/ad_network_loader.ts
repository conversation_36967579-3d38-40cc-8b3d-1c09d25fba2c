import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'

import AdNetwork from '#models/ad_network'

@inject()
export class AdNetworkLoader {
  private batch = new DataLoader(
    async (ids: readonly number[]): Promise<Array<AdNetwork>> => {
      const networks = await AdNetwork.query()
        .whereIn('id', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => networks.get(id)!)
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: AdNetwork['id']) {
    return this.batch.load(id)
  }
}
