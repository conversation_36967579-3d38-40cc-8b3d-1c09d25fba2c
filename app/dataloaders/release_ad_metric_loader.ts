import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import { ConfigMapRegistry } from '@munkit/main'
import DataLoader from 'dataloader'

import { QueryReleaseMetricsArgs } from '#graphql/main'
import ReleaseAdMetric from '#models/release_ad_metric'
import ReleaseMetric from '#models/release_metric'
import AdType from '#models/v2/ad_type'
import ViewPreset from '#models/view_preset'
import { adMetricGroupingMatcher } from '#utils/ad_metric'

@inject()
export class ReleaseAdMetricLoader {
  constructor(
    private configMapRegistry: ConfigMapRegistry,
    private context: HttpContext
  ) {}

  declare args: QueryReleaseMetricsArgs

  private batch = new DataLoader(
    async (metrics: readonly ReleaseMetric[]): Promise<any> => {
      if (metrics.length === 0) {
        return []
      }
      const {
        where: { gameId, ...filters },
        group,
      } = this.args

      const pageId = 'dashboard.games.release_metrics.index'
      const viewPresetConfigMap = this.configMapRegistry.get('cmap.viewpreset').get(pageId)
      const preset = await ViewPreset.findOrDefault(
        this.configMapRegistry.get('cmap.viewpreset').get('dashboard.games.release_metrics.index'),
        this.context.auth.user!,
        this.args.preset?.viewPresetId || undefined
      )
      const ads = await ReleaseAdMetric.query()
        .innerJoin(
          AdType.table,
          AdType.columnName('id', AdType.table),
          ReleaseAdMetric.columnName('adTypeId', ReleaseAdMetric.table)
        )
        .where((q) => {
          if (group?.fields?.length) {
            const values = metrics.map((m) => group.fields.map((f) => m[f as keyof typeof m]))
            q.whereIn(group.fields, values as any)
          }
        })
        .groupBy(AdType.columnName('category'), ...(group?.fields || []))
        .select(AdType.columnName('category'), ...(group?.fields || []))
        .withScopes((s) => {
          s.filter(filters as any, {
            date: ReleaseAdMetric.columnFullname('date'),
            version: ReleaseAdMetric.columnFullname('version'),
            countryCode: ReleaseAdMetric.columnFullname('countryCode'),
          })
          preset.attributes.forEach((vpa) => {
            const attributeConfig = viewPresetConfigMap.attributes.get(vpa.name)
            if (attributeConfig) {
              s.selectAggregationPreset(vpa, attributeConfig)
            }
          })
        })
        .where(ReleaseAdMetric.columnFullname('gameId'), gameId)

      return metrics.map((m) => {
        const matchedAds = ads.filter((a) => adMetricGroupingMatcher(m, a, group?.fields))

        return matchedAds.map((e) => {
          return e
            .withSerializationContext({
              parent: m,
              viewPreset: preset,
              viewPresetConfigMap,
            })
            .serialize()
        })
      })
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(metric: ReleaseMetric) {
    return this.batch.load(metric)
  }
}
