import DataLoader from 'dataloader'
import { groupBy } from 'lodash-es'

import User from '#models/user'
import DashboardRoleMembership from '#models/dashboard_role_membership'

export class UserGameMembershipLoader {
  private batch = new DataLoader(
    async (emails: readonly User['email'][]): Promise<Array<DashboardRoleMembership[]>> => {
      const memberships = await DashboardRoleMembership.query()
        .withScopes((s) => s.withUsers())
        .whereIn('user', emails as string[])

      const emailToMemberships = groupBy(memberships, (m) => m.user)

      return emails.map((email) => emailToMemberships[email] || [])
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(email: User['email']) {
    return this.batch.load(email)
  }
}
