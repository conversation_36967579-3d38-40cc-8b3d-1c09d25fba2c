import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'

import AdAgency from '#models/ad_agency'

@inject()
export class AdAgencyLoader {
  private batch = new DataLoader(
    async (ids: readonly number[]): Promise<Array<AdAgency>> => {
      const records = await AdAgency.query()
        .whereIn('id', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => records.get(id)!)
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: AdAgency['id']) {
    return this.batch.load(id)
  }
}
