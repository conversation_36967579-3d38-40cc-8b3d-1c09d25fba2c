import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { ModelObject } from '@adonisjs/lucid/types/model'

import AdGroup from '#models/ad_group'

@inject()
export class AdGroupLoader {
  private batch = new DataLoader(
    async (ids: readonly AdGroup['id'][]): Promise<Array<ModelObject>> => {
      const records = await AdGroup.query()
        .whereIn('id', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => records.get(id)!.serialize())
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: AdGroup['id']) {
    return this.batch.load(id)
  }
}
