import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'

import Game from '#models/game'

@inject()
export class GameLoader {
  private batch = new DataLoader(
    async (ids: readonly Game['id'][]): Promise<Array<Game>> => {
      const records = await Game.query()
        .whereIn('storeId', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => records.get(id)!)
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: Game['id']) {
    return this.batch.load(id)
  }
}
