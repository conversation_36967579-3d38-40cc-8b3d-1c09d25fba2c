import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { isNil } from 'lodash-es'

import Team from '#models/team'

@inject()
export class TeamLoader {
  private batch = new DataLoader(
    async (ids: readonly Team['id'][]): Promise<Array<Team>> => {
      const records = await Team.query()
        .whereIn('id', Array.from(ids))
        .then((rows) => new Map(rows.map((row) => [row.id, row])))

      return ids.map((id) => records.get(id)!)
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(id: Team['id'] | null | undefined) {
    if (isNil(id)) {
      return null
    }

    return this.batch.load(id)
  }
}
