import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { uniq } from 'lodash-es'
import { DateTime } from 'luxon'

import GameMetric from '#models/v2/game_metric'

@inject()
export class GameMetricLoader {
  declare gameId: string

  private batch = new DataLoader(
    async (dates: readonly string[]): Promise<Array<GameMetric>> => {
      const records = await GameMetric.query()
        .withScopes((s) => s.default(this.gameId))
        .where('gameId', this.gameId)
        .whereIn(
          'date',
          uniq(dates).map((d) => DateTime.fromISO(d).toJSDate())
        )
        .then((rows) => new Map(rows.map((row) => [row.date.toISODate(), row])))

      return dates.map(
        (date) =>
          records.get(date) || new GameMetric().merge({ paidInstalls: 0, cost: 0, revenue: 0 })
      )
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 100),
    }
  )

  async load(date: any) {
    return this.batch.load(date)
  }
}
