import { inject } from '@adonisjs/core'
import DataLoader from 'dataloader'
import { ModelObject } from '@adonisjs/lucid/types/model'
import { ConfigMapRegistry } from '@munkit/main'
import { HttpContext } from '@adonisjs/core/http'

import FirebaseAdMetric from '#models/firebase_ad_metric'
import FirebaseMetric from '#models/firebase_metric'
import { QueryFirebaseMetricsArgs } from '#graphql/main'
import FirebaseVersionVariant from '#models/firebase_version_variant'
import AdType from '#models/v2/ad_type'
import ViewPreset from '#models/view_preset'
import { adMetricGroupingMatcher } from '#utils/ad_metric'

@inject()
export class FirebaseAdMetricLoader {
  constructor(
    private configMapRegistry: ConfigMapRegistry,
    private context: HttpContext
  ) {}

  declare args: QueryFirebaseMetricsArgs

  private batch = new DataLoader(
    async (metrics: readonly FirebaseMetric[]): Promise<Array<ModelObject[]>> => {
      if (metrics.length === 0) {
        return []
      }

      const where = this.args.where
      const group = this.args.group

      const preset = await ViewPreset.findOrDefault(
        this.configMapRegistry
          .get('cmap.viewpreset')
          .get('dashboard.games.firebase_experiments.index'),
        this.context.auth.user!,
        this.args.preset?.viewPresetId || undefined
      )
      const viewPresetConfigMap = this.configMapRegistry
        .get('cmap.viewpreset')
        .get('dashboard.games.firebase_experiments.index')

      const ads = await FirebaseAdMetric.query()
        .innerJoin(
          FirebaseVersionVariant.table,
          FirebaseVersionVariant.columnName('id', FirebaseVersionVariant.table),
          FirebaseAdMetric.columnName('variantId', FirebaseAdMetric.table)
        )
        .innerJoin(
          AdType.table,
          AdType.columnName('id', AdType.table),
          FirebaseAdMetric.columnName('adTypeId', FirebaseAdMetric.table)
        )
        .where(FirebaseVersionVariant.columnName('gameId'), where.gameId)
        .where((q) => {
          if (where.networkId) {
            q.where('networkId', where.networkId.values[0])
          }

          if (where.mediationId) {
            q.where('mediationId', where.mediationId.values[0])
          }

          if (group?.fields?.length) {
            const values = metrics.map((m) => group.fields.map((f) => m[f as keyof typeof m]))

            q.whereIn(group.fields, values as any)
          }
        })
        .groupBy(AdType.columnName('category'), ...(group?.fields || []))
        .select(AdType.columnName('category'), ...(group?.fields || []))
        .withScopes((s) => {
          s.filter({ date: where.date })

          preset.attributes.forEach((vpa) => {
            const attributeConfig = viewPresetConfigMap.attributes.get(vpa.name)
            if (attributeConfig) {
              s.selectAggregationPreset(vpa, attributeConfig)
            }
          })
        })

      return metrics.map((m) =>
        ads
          .filter((a) => adMetricGroupingMatcher(m, a, group?.fields))
          .map((e) =>
            e
              .withSerializationContext({
                parent: m,
                viewPreset: preset,
                viewPresetConfigMap,
              })
              .serialize()
          )
      )
    },
    {
      maxBatchSize: 200,
      batchScheduleFn: (callback) => setTimeout(callback, 20),
    }
  )

  async load(metric: FirebaseMetric) {
    return this.batch.load(metric)
  }
}
