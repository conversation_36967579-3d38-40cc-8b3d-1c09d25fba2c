import type { HttpContext } from '@adonisjs/core/http'
import router from '@adonisjs/core/services/router'

import { UserKind } from '#graphql/main'

export default class HomeController {
  async handle({ response, auth }: HttpContext) {
    if (auth.isAuthenticated) {
      if (auth.user!.kind === UserKind.Partner) {
        return response.redirect(router.builder().make('partner.games.index'))
      } else {
        return response.redirect(router.builder().make('dashboard.games.index'))
      }
    }

    return response.redirect(router.builder().make('sessions.create'))
  }
}
