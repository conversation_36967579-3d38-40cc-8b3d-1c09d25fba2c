import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main'
import { z } from 'zod'

import GitHubService from '#services/github_service'

const protectedBranchUpdateValidator = z.object({
  repos: z.array(z.string().trim()),
})

export default class ProtectedBranchesController {
  @inject()
  async update({ request, response, bouncer }: HttpContext, githubService: GitHubService) {
    await bouncer.with('GitHubProtectedBranchPolicy').authorize('update')
    const { repos } = await protectedBranchUpdateValidator.parseAsync(request.body())

    return request.format({
      json: async () => {
        await githubService.updateProtectedBranch(repos)

        response.success({})
      },
      default: 'json',
    })
  }

  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer }: HttpContext) {
    await bouncer.with('GitHubProtectedBranchPolicy').authorize('index')
    return inertia.render('github/protected_branches/index')
  }
}
