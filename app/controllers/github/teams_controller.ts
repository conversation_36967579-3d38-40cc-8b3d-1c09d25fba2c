import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { isEmpty } from 'lodash-es'
import vinejs from '@vinejs/vine'

import GitHubService from '#services/github_service'

const teamUpdateValidator = vinejs.compile(
  vinejs.object({
    names: vinejs.array(vinejs.string().trim()),
  })
)

export default class TeamsController {
  @inject()
  async update({ response, request, bouncer }: HttpContext, githubService: GitHubService) {
    await bouncer.with('GitHubTeamPolicy').authorize('update')

    const { names } = await request.validateUsing(teamUpdateValidator)

    return request.format({
      json: async () => {
        const teams = isEmpty(names) ? [] : names
        await githubService.updateTeams(teams)
        response.success({})
      },
      default: 'json',
    })
  }
}
