import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { isEmpty } from 'lodash-es'
import { Metadata } from '@munkit/main'
import { z } from 'zod'

import GitHubService from '#services/github_service'

const aclUpdateValidator = z.object({
  ids: z.array(z.string().trim()),
})

export default class AccessControlsController {
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer }: HttpContext) {
    await bouncer.with('GitHubAclPolicy').authorize('index')
    return inertia.render('github/access_controls/index')
  }

  @inject()
  async update({ response, request, bouncer }: HttpContext, githubService: GitHubService) {
    await bouncer.with('GitHubAclPolicy').authorize('update')
    const { ids } = await aclUpdateValidator.parseAsync(request.body())

    return request.format({
      json: async () => {
        const aclIds = isEmpty(ids) ? [] : ids
        await githubService.updateAccessControls(aclIds)
        response.success({})
      },
      default: 'json',
    })
  }
}
