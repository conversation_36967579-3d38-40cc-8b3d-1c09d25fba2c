import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'

import GitHubService from '#services/github_service'

export default class StatusesController {
  @inject()
  async index({ view, bouncer }: HttpContext, githubService: GitHubService) {
    await bouncer.with('GitHubStatusPolicy').authorize('index')

    const repos = await githubService.listRepos()
    return view.render('github/statuses/index', {
      repositories: repos.map((repo) => ({ name: repo.name, archived: repo.archived })),
    })
  }
}
