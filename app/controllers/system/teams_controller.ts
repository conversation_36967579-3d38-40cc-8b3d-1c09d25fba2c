import type { HttpContext } from '@adonisjs/core/http'
import vine, { SimpleMessagesProvider } from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { Database } from '@adonisjs/lucid/database'
import { Infer } from '@vinejs/vine/types'
import { Metadata } from '@munkit/main'

import Team from '#models/team'
import { UserService } from '#services/user_service'
import User from '#models/user'
import { configMapRule } from '#validators/rules/config_map_rule'
import { teamMemberRule } from '#validators/team/team_member_rule'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import { update } from '#utils/pure'
import TeamPolicy from '#policies/team_policy'

const teamUpdateValidator = vine.compile(
  vine.object({
    name: vine.string().trim(),
    memberEmails: vine.array(vine.string().email()),
    leaderEmail: vine.string().trim().email().use(teamMemberRule()).optional(),
  })
)

export type TeamUpdateRequestData = Infer<typeof teamUpdateValidator>

const teamDestroyValidator = vine.compile(
  vine.object({
    canDestroy: vine.accepted(),
  })
)

teamDestroyValidator.messagesProvider = new SimpleMessagesProvider({
  'canDestroy.accepted': 'Team cannot be destroyed because it has members in charge of games.',
})

const teamCreateValidator = vine.compile(
  vine.object({
    name: vine.string().trim(),
    memberEmails: vine.array(vine.string().email()),
    roleId: vine.string().use(
      configMapRule({
        id: 'cmap.dash.role',
      })
    ),
    leaderEmail: vine.string().trim().email().use(teamMemberRule()).optional(),
  })
)

export type TeamCreateRequestData = Infer<typeof teamCreateValidator>

export default class TeamsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ request, inertia, response, bouncer, auth }: HttpContext, teamPolicy: TeamPolicy) {
    await bouncer.with('TeamPolicy').authorize('index')

    return request.format<'html' | 'json'>({
      default: 'html',
      html: () => inertia.render('system/teams/index'),
      json: async () => {
        const teamsQuery = Team.query()
        await teamPolicy.scope(auth.user!, teamsQuery)
        const teams = await teamsQuery.preload('leader').orderBy('id', 'desc')
        response.success(teams)
      },
    })
  }

  async show({ request, response, bouncer }: HttpContext) {
    return request.format<'json'>({
      json: async () => {
        const team = await this.#getTeam(request.param('id'))
        await bouncer.with('TeamPolicy').authorize('show', team)

        response.success(team)
      },
      default: 'json',
    })
  }

  async edit({ request, view, bouncer }: HttpContext) {
    await bouncer.with('TeamPolicy').authorize('edit')
    const team = await this.#getTeam(request.param('id'))
    return view.render('system/teams/edit', { team })
  }

  @inject()
  async update(
    { request, response, bouncer }: HttpContext,
    userService: UserService,
    db: Database
  ) {
    await bouncer.with('TeamPolicy').authorize('update')

    return request.format<'json'>({
      json: async () => {
        const { name, memberEmails, leaderEmail } = await request.validateUsing(teamUpdateValidator)
        const users = await userService.findManyByEmails(memberEmails)
        const leader = users.find((u) => u.email === leaderEmail)

        await db.transaction(async (tx) => {
          const team = await Team.findOrFail(request.param('id'), {
            client: tx,
          })
          team.merge({ name, leaderId: leader?.id || null })
          await team.save()

          await team.load('members')

          const { removes, adds } = update(team.members, users, (u) => u.id)

          for (const userRemoved of removes) {
            const memberships = await DashboardRoleMembership.query()
              .withScopes((s) => s.withUsers())
              .where('user', userRemoved.email)

            for (const membership of memberships) {
              membership.users = membership.users.filter((u) => u.email !== userRemoved.email)
              await membership.save()
            }
          }

          await User.query({ client: tx })
            .whereIn(
              'id',
              removes.map((u) => u.id)
            )
            .update({ teamId: null })

          await User.query({ client: tx })
            .whereIn(
              'id',
              adds.map((u) => u.id)
            )
            .update({ teamId: team.id })

          return team
        })

        const team = await this.#getTeam(request.param('id'))

        response.success(team)
      },
      default: 'json',
    })
  }

  async create({ view, bouncer }: HttpContext) {
    await bouncer.with('TeamPolicy').authorize('create')

    return view.render('system/teams/create')
  }

  @inject()
  async store({ bouncer, request, response }: HttpContext, userService: UserService, db: Database) {
    await bouncer.with('TeamPolicy').authorize('store')

    return request.format<'json'>({
      json: async () => {
        const { name, memberEmails, roleId, leaderEmail } =
          await request.validateUsing(teamCreateValidator)
        const users = await userService.findManyByEmails(memberEmails)
        const leader = users.find((u) => u.email === leaderEmail)

        const teamId = await db.transaction(async (tx) => {
          const team = await Team.create(
            {
              name,
              roleId,
              leaderId: leader?.id || null,
            },
            {
              client: tx,
            }
          )

          await User.query({ client: tx })
            .whereIn(
              'id',
              users.map((u) => u.id)
            )
            .update({ teamId: team.id })

          return team.id
        })

        const team = await this.#getTeam(teamId)

        return response.success(team)
      },
      default: 'json',
    })
  }

  async destroy({ request, response, bouncer }: HttpContext) {
    await bouncer.with('TeamPolicy').authorize('destroy')

    return request.format({
      default: 'json',
      json: async () => {
        const team = await Team.findOrFail(request.param('id'))
        const memberships = await DashboardRoleMembership.query().withScopes((s) =>
          s.selectManyGameInChargedByTeam(team)
        )
        await teamDestroyValidator.validate({ canDestroy: memberships.length === 0 })
        await team.delete()
        response.noContent()
      },
    })
  }

  #getTeam(id: number) {
    return Team.query().preload('members').preload('leader').where('id', id).firstOrFail()
  }
}
