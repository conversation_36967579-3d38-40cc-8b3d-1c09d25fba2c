import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { Metadata } from '@munkit/main'

import User from '#models/user'

export default class UsersController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer }: HttpContext) {
    await bouncer.with('UserPolicy').authorize('index')

    return inertia.render('system/users/index')
  }

  async simulate({ bouncer, request, response, auth }: HttpContext) {
    const user = await User.findOrFail(request.param('id'))

    await bouncer.with('UserPolicy').authorize('simulate', user)

    await auth.use('web').login(user)
    return response.redirect().toRoute('home')
  }
}
