import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import User from '#models/user'

import { MePermissionPresenter, MePresenter } from './me_presenter.js'

export default class MeController {
  @inject()
  async index(
    { response, request, auth }: HttpContext,
    toolPermissionService: ToolPermissionService
  ) {
    return request.format({
      json: async () => {
        const user = auth.user! as User
        await user.load('teamLead')

        return response.success(
          new MePresenter().merge({
            profile: user,
            permission: new MePermissionPresenter().merge({
              isManager: toolPermissionService.canManage(Tool.Dashboard)(user),
              isTeamLeader: <PERSON><PERSON><PERSON>(user.teamLead),
              isMember: <PERSON><PERSON><PERSON>(user.teamId),
            }),
          })
        )
      },

      default: 'json',
    })
  }
}
