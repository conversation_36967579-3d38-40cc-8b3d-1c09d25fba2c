import type { HttpContext } from '@adonisjs/core/http'

import Game from '#models/game'

export default class GameMetricsController {
  async index({ inertia, params, bouncer }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('partner.game_metric').authorize('index', game)
    return inertia.render('partner/game_metrics/index', {
      game: { id: game.id, name: game.name },
      canViewGameMetricV2: await bouncer.with('dash.game.metric_v2').allows('index', game),
    })
  }
}
