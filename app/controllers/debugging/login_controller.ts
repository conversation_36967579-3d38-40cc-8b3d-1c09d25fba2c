import type { HttpContext } from '@adonisjs/core/http'
import TokenSerializer from 'app/presenters/api/token_serializer.js'

import User from '#models/user'

export default class LoginController {
  async handle({ request, response }: HttpContext) {
    const userId = request.body()['id']
    const user = await User.findOrFail(userId)
    response.ok({
      data: new TokenSerializer(
        await User.accessTokens.create(user),
        await User.refreshToken.create(user)
      ),
    })
  }
}
