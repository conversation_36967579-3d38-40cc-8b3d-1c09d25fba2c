import type { HttpContext } from '@adonisjs/core/http'

import { getDocumentValidator } from '#validators/document/get_document'
import { DocumentDriverFactory } from '#services/documents/document_driver'
import Document from '#models/document'

export default class DocumentsController {
  driverFactory = new DocumentDriverFactory()

  async show({ bouncer, request, response }: HttpContext) {
    const { id } = await getDocumentValidator.validate({
      id: request.param('id'),
    })

    const docDriver = this.driverFactory.getDriver(id)
    await docDriver.onAuthorizeShow(bouncer)

    const document = await Document.findOrFail(id)

    response.ok({ data: document, meta: {} })
  }
}
