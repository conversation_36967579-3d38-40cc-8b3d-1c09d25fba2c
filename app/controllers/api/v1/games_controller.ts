import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { compact, flatten, values, pick, uniq, camelCase } from 'lodash-es'
import app from '@adonisjs/core/services/app'
import { ConfigMapRegistry } from '@munkit/main'
import { inject } from '@adonisjs/core'

import User from '#models/user'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import DashboardRoleMembershipUser from '#models/dashboard_role_membership_user'
import Game from '#models/game'
import { GamePlatform } from '#config/enums'
import FirebaseService from '#services/firebase_service'
import GameStudio from '#models/game_studio'

export const createGameValidator = vine.compile(
  vine.object({
    partner: vine.string().trim().optional(),
    storeId: vine.string().trim(),
    packageName: vine.string().trim().optional(),
    platform: vine.enum(GamePlatform).optional(),
    projectId: vine.string().trim().optional(),
    mktManager: vine.array(vine.string().email()).optional(),
    ua: vine.array(vine.string().email()).optional(),
    monet: vine.array(vine.string().email()).optional(),
    creative: vine.array(vine.string().email()).optional(),
    miraiPo: vine.array(vine.string().email()).optional(),
    miraiGd: vine.array(vine.string().email()).optional(),
    miraiDev: vine.array(vine.string().email()).optional(),
    outsideManager: vine.array(vine.string().email()).optional(),
    outsideDev: vine.array(vine.string().email()).optional(),
  })
)

export default class GamesController {
  @inject()
  public async create(
    { request, response, bouncer, auth }: HttpContext,
    configMapRegistry: ConfigMapRegistry
  ) {
    await bouncer.with('DashboardGamePolicy').authorize('update')
    const validatedData = await createGameValidator.validate(request.body())
    const firebaseService = await app.container.make(FirebaseService)
    const data = await firebaseService.getProjectData(validatedData.projectId)
    if (validatedData.projectId && data.project === null) {
      return response.badRequest({
        success: false,
        message: 'Project ID not found',
      })
    }
    const roleConfigMap = configMapRegistry.get('cmap.dash.role')
    const emailFields = roleConfigMap.map((role) => camelCase(role.id))
    const emailFieldsMapping = Object.fromEntries(
      roleConfigMap.map((role) => [camelCase(role.id), role.id])
    )

    const allEmails: string[] = compact(flatten(values(pick(validatedData, emailFields)))).map(
      (email) => email.trim()
    )
    const users = await User.query().whereIn('email', allEmails).whereNull('deletedAt')
    if (users.length !== allEmails.length) {
      return response.badRequest({
        success: false,
        message: 'Some emails do not exist in the system',
      })
    }

    if (validatedData.projectId) {
      const studio = await GameStudio.firstOrCreate({
        name: validatedData.partner,
        isVisible: true,
      })
      await Game.firstOrCreate(
        { storeId: validatedData.storeId },
        {
          name: data.project.displayName,
          platform: validatedData.platform,
          packageName: validatedData.packageName,
          isActive: true,
          isInhouse: validatedData.partner === 'Mirai',
          googleCloudProjectId: data.project.projectId,
          googleAnalyticsPropertyId: data.analyticsProperty.id,
          studioId: studio.id,
        }
      )
    }

    for (const camelCaseField of emailFields) {
      const roleEmails = (validatedData as any)[camelCaseField] || []
      if (roleEmails.length > 0) {
        const originalRoleId = emailFieldsMapping[camelCaseField] || camelCaseField

        const membership = await DashboardRoleMembership.firstOrNew({
          storeId: validatedData.storeId,
          roleId: originalRoleId,
        })
        const journal = membership.makeJournal()

        const existingEmails = membership.users?.map((u) => u.email) || []
        const newEmails = roleEmails.filter((email: string) => {
          const foundUser = users.find((user) => user.email === email)
          return foundUser?.teamId
        })
        const mergedEmails = uniq([...existingEmails, ...newEmails])

        membership.merge({
          users: mergedEmails.map((email: string) =>
            new DashboardRoleMembershipUser().merge({ email })
          ),
        })
        if (membership.$isPersisted) {
          await journal.save()
        }
        await membership.save()
      }
    }

    return response.created({
      success: true,
      message: 'Game created successfully',
      data: {
        id: data.project?.projectId,
        game: data.project?.displayName,
      },
      user: auth.user?.email,
    })
  }
}
