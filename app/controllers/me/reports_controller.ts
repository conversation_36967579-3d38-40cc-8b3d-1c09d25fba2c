import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'

import GameMetric from '#models/game_metric'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import Game from '#models/game'
import User from '#models/user'

const reportIndexValidator = vine.compile(
  vine.object({
    from: vine
      .date({
        formats: ['YYYY-MM-DD'],
      })
      .optional(),
    to: vine
      .date({
        formats: ['YYYY-MM-DD'],
      })
      .optional(),
  })
)

export default class ReportsController {
  @inject()
  async index(
    { request, response, auth, bouncer }: HttpContext,
    toolPermissionService: ToolPermissionService
  ) {
    return request.format({
      default: 'json',
      json: async () => {
        const requestData = await request.validateUsing(reportIndexValidator)
        const to = requestData.to
          ? DateTime.fromJSDate(requestData.to)
          : DateTime.now().startOf('day')
        const from = requestData.from
          ? DateTime.fromJSDate(requestData.from)
          : to.minus({ days: 30 })

        const user = auth.user! as User
        await user.load('team')

        const isManager = toolPermissionService.canManage(Tool.Dashboard)(user)

        const selectGameIds = async () => {
          if (isManager) {
            const gamesQuery = Game.query()
            await bouncer.with('GameQueryPolicy').authorize('scope', gamesQuery)
            return gamesQuery.select('storeId')
          }

          if (user.team && user.isLeader(user.team)) {
            return DashboardRoleMembership.query()
              .withScopes((s) => s.selectManyGameInChargedByTeam(user.team))
              .distinctOn('storeId')
              .select('storeId')
          }

          return DashboardRoleMembership.query()
            .withScopes((s) => s.selectManyGameInChargedByUser(user.email))
            .distinctOn('storeId')
            .select('storeId')
        }

        const storeIds = await selectGameIds().then((games) => games.map((g) => g.storeId))

        const aggregation = await GameMetric.query()
          .from(
            GameMetric.query()
              .withScopes((s) => s.default())
              .as('revenue_list')
          )
          .whereIn('storeId', storeIds)
          .whereBetween('date', [from.toJSDate(), to.toJSDate()])
          .groupBy('date')
          .select('date')
          .sum(`revenue_list.${GameMetric.columnName('profit')}`, GameMetric.columnName('profit'))
          .orderBy('date', 'desc')

        response.success({
          profits: aggregation,
        })
      },
    })
  }
}
