import type { HttpContext } from '@adonisjs/core/http'

import { healthChecks } from '#start/health'

export default class HealthChecksController {
  async readiness({ response, auth }: HttpContext) {
    const report = await healthChecks.run()
    const isAuthenticated = await auth
      .use('web')
      .authenticate()
      .catch(() => auth.use('basic').check())

    if (isAuthenticated) {
      return report.isHealthy ? response.ok(report) : response.serviceUnavailable(report)
    }

    return report.isHealthy
      ? response.ok({ isHealthy: false })
      : response.serviceUnavailable({ isHealthy: false })
  }

  async liveness({ response }: HttpContext) {
    return response.ok({ isHealthy: true })
  }
}
