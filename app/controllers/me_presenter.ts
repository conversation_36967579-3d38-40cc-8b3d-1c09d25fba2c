import { BaseModel, column } from '@adonisjs/lucid/orm'

import User from '#models/user'

export class MePermissionPresenter extends BaseModel {
  @column()
  declare isTeamLeader: boolean

  @column()
  declare isManager: boolean

  @column()
  declare isMember: boolean
}

export class MePresenter extends BaseModel {
  @column()
  declare profile: User

  @column()
  declare permission: MePermissionPresenter
}
