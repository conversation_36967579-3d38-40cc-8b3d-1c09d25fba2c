import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'

import User from '#models/user'
import UnauthorizedException from '#exceptions/unauthorized_exception'

export default class GoogleController {
  async redirect({ ally }: HttpContext) {
    return ally.use('google').redirect()
  }

  @inject()
  async callback({ ally, auth, response, session }: HttpContext) {
    const googleUser = await ally.use('google').user()

    const user = await User.findBy({
      email: googleUser.email,
    })

    if (!user || user.isDeleted) {
      throw new UnauthorizedException()
    }

    await user.save()
    await auth.use('web').login(user)

    const redirectPath = session.get('redirectTo', '/')
    session.forget('redirectTo')
    await session.commit()

    return response.redirect(redirectPath)
  }
}
