import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { Metadata } from '@munkit/main'
import { RuntimeException } from '@adonisjs/core/exceptions'

import User from '#models/user'
import UnauthorizedException from '#exceptions/unauthorized_exception'

const sessionCreateValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim(),
    password: vine.string().trim(),
    remember: vine.boolean().optional(),
  })
)

export default class SessionsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async create({ inertia }: HttpContext) {
    return inertia.render('sessions/create')
  }

  @inject()
  async store({ request, response, auth, session }: HttpContext) {
    const { password, email } = await request.validateUsing(sessionCreateValidator)

    try {
      const user = await User.verifyCredentials(email, password)

      if (!user || user.isDeleted) {
        throw new UnauthorizedException()
      }

      await auth.use('web').login(user)

      const redirectPath = session.get('redirectTo', '/')
      session.forget('redirectTo')
      await session.commit()

      return response.redirect(redirectPath)
    } catch (error) {
      if (error instanceof RuntimeException) {
        throw new UnauthorizedException()
      }

      throw error
    }
  }

  async destroy({ response, auth }: HttpContext) {
    await auth.use('web').logout()
    return response.redirect().toRoute('home')
  }
}
