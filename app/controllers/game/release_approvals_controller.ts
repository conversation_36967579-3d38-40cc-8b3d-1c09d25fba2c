import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { Queue } from '@mirai-game-studio/adonis-sdk/queue'
import parseDuration from 'parse-duration'

import { GameReleaseStatus } from '#config/enums'
import GameReleaseApproval from '#models/game_release_approval'
import GameReleaseProposal from '#models/game_release_proposal'

export default class GameReleaseApprovalsController {
  async update({ request, bouncer, response, params }: HttpContext) {
    await bouncer.with('GameReleaseApprovalPolicy').authorize('update')

    const approval = await GameReleaseApproval.findOrFail(params['id'])
    const form = request.only(['releaseStatus'])
    approval.merge(form)
    await approval.save()

    response.redirect().back()
  }

  async index({ view, bouncer, params, request, response }: HttpContext) {
    await bouncer.with('GameReleaseApprovalPolicy').authorize('index')

    return request.format<'html' | 'json'>({
      default: 'html',
      html: () => view.render('game/release_approvals/index'),
      json: async () => {
        const proposalId = params['release_proposal_id']

        const proposal = await GameReleaseProposal.findOrFail(proposalId)
        const approvals = await GameReleaseApproval.query()
          .preload('reviewer')
          .where('proposalId', proposalId)
          .orderBy('id', 'desc')
          .paginate(params['page'] || 1, 50)

        const { data, meta } = approvals.toJSON()

        return response.success(data, {
          ...meta,
          proposal,
        })
      },
    })
  }

  @inject()
  async store({ request, auth, response, bouncer }: HttpContext, queue: Queue) {
    await bouncer.with('GameReleaseApprovalPolicy').authorize('store')

    return request.format({
      json: async () => {
        const form = request.only(['proposalId', 'publisherId', 'status'])

        const pendingApproval = await GameReleaseApproval.query()
          .where('proposalId', form.proposalId)
          .where('publisherId', form.publisherId)
          .whereIn('releaseStatus', [GameReleaseStatus.Pending, GameReleaseStatus.InProgress])

        if (pendingApproval.length > 0) {
          response.error(
            'Game upload for this store is in progress. Please wait until it finished then try again'
          )
          return
        }

        const proposal = await GameReleaseProposal.findOrFail(form.proposalId)

        const approval = await GameReleaseApproval.create({
          reviewerId: auth.user!.id,
          proposalId: proposal.id,
          publisherId: form.publisherId,
          releaseStatus: GameReleaseStatus.Pending,
        })

        const job = await queue.dispatch(
          '#jobs/release_game_job',
          {
            approvalId: approval.id,
          },
          {
            queueName: 'default',
            delay: parseDuration('5s')!,
          }
        )

        proposal.status = form.status
        approval.jobId = job!.id

        await Promise.all([proposal.save(), approval.save()])

        response.success(approval, {
          proposal,
        })
      },
      default: 'json',
    })
  }
}
