import type { HttpContext } from '@adonisjs/core/http'
import { last } from 'lodash-es'

import { GameReleaseProposalStatus, GameStore } from '#config/enums'
import GameReleaseProposal, { AndroidExtra } from '#models/game_release_proposal'
import {
  createAndroidReleaseProposalValidator,
  createReleaseProposalValidator,
} from '#validators/game/release_proposal'

export default class GameReleaseProposalsController {
  async create({ request, view }: HttpContext) {
    const form = request.only([
      'repository',
      'platform',
      'semver',
      'revision',
      'installableDownloadUrl',
      'symbolsDownloadUrl',
      'versionCode',
    ])

    return view.render('game/release_proposals/create', {
      form: {
        ...form,
        repository: last(form.repository.split('/')),
      },
      GameStore,
    })
  }

  async store({ request, view }: HttpContext) {
    const form = await createReleaseProposalValidator.validate(request.all())
    const extraForm = await createAndroidReleaseProposalValidator.validate(request.all())

    const proposal = await GameReleaseProposal.updateOrCreate(
      {
        repository: form.repository,
      },
      {
        ...form,
        status: GameReleaseProposalStatus.Pending,
        extra: new AndroidExtra().merge(extraForm),
      }
    )

    return view.render('game/release_proposals/store', { proposal })
  }

  async index({ request, view, response, bouncer }: HttpContext) {
    await bouncer.with('GameReleaseProposalPolicy').authorize('index')

    return await request.format<'json' | 'html'>({
      json: async () => {
        const page = Number(request.input('page', '1'))
        const statuses = request.input('status', [GameReleaseProposalStatus.Pending])

        const proposals = await GameReleaseProposal.query()
          .orderBy('updated_at', 'desc')
          .whereIn('status', statuses)
          .paginate(page)
        const json = proposals.toJSON()

        response.success(json.data, json.meta)
      },
      html: () => {
        return view.render('game/release_proposals/index')
      },
      default: 'html',
    })
  }
}
