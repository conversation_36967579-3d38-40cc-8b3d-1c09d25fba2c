import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { Metadata } from '@munkit/main'

import Document from '#models/document'
import { DocumentDriverFactory } from '#services/documents/document_driver'
import { getDocumentValidator } from '#validators/document/get_document'
import { configMapRule } from '#validators/rules/config_map_rule'

const updateDocumentValidator = vine.compile(
  vine.object({
    content: vine.string(),
    id: vine.string().use(
      configMapRule({
        id: 'cmap.document',
      })
    ),
  })
)

export default class DocumentsController {
  driverFactory = new DocumentDriverFactory()

  async update({ bouncer, request, response }: HttpContext) {
    const { content, id } = await updateDocumentValidator.validate({
      content: request.input('content'),
      id: request.param('id'),
    })

    const docDriver = this.driverFactory.getDriver(id)
    await docDriver.onAuthorizeUpdate(bouncer)

    const document = await Document.findOrFail(id)
    document.merge({ content })
    await docDriver.onUpdate(document)
    await document.save()

    return response.ok({ data: document, meta: {} })
  }

  @Metadata('route', { 'ui.next': 'ondemand' })
  async edit({ inertia, bouncer, request, response }: HttpContext) {
    const { id } = await getDocumentValidator.validate({
      id: request.param('id'),
    })

    const docDriver = this.driverFactory.getDriver(id)
    await docDriver.onAuthorizeShow(bouncer)

    return request.format<'html' | 'json'>({
      html: async () => {
        return inertia.render('documents/edit')
      },
      json: async () => {
        let document = await Document.firstOrCreate({ id }, { content: '' })

        return response.ok({ data: document, meta: {} })
      },
      default: 'html',
    })
  }

  @Metadata('route', { 'ui.next': 'ondemand' })
  async index({ inertia, bouncer, request, response }: HttpContext) {
    await bouncer.with('LandingPageDocumentPolicy').authorize('index')

    return request.format<'html' | 'json'>({
      html: async () => {
        return inertia.render('documents/index')
      },
      json: async () => {
        response.ok({ data: [], meta: {} })
      },
      default: 'html',
    })
  }
}
