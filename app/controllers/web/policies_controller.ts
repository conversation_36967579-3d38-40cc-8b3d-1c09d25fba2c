import { HttpContext } from '@adonisjs/core/http'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import pLimit from 'p-limit'

const authorizeLimit = pLimit(5)

export class Authorization extends BaseModel {
  @column()
  declare policy: string

  @column()
  declare allowed: boolean
}

export default class PoliciesController {
  async index({ request, response, bouncer }: HttpContext) {
    return request.format({
      json: async () => {
        const policies = request.body()['policies'] as {
          name: string
          action: string
          args: any[]
        }[]

        const authorizations = await Promise.all(
          policies.map((policy) =>
            authorizeLimit(async () => {
              return new Authorization().merge({
                policy: `${policy.name}.${policy.action}`,
                allowed: await bouncer
                  .with(policy.name as any)
                  .allows(policy.action as any, ...policy.args)
                  .catch(() => false),
              })
            })
          )
        )

        return response.json({ data: authorizations })
      },
      default: 'json',
    })
  }
}
