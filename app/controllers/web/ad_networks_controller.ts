import type { HttpContext } from '@adonisjs/core/http'

import AdNetwork from '#models/ad_agency'

export default class AdNetworksController {
  async index({ request, response }: HttpContext) {
    return request.format({
      json: async () => {
        const adNetworks = await AdNetwork.query().withScopes((s) => s.default())
        response.ok({ data: adNetworks, meta: {} })
      },
      default: 'json',
    })
  }
}
