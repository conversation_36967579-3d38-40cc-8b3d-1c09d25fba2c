import type { HttpContext } from '@adonisjs/core/http'
import app from '@adonisjs/core/services/app'
import { Metadata } from '@munkit/main'

export default class GraphiqlsController {
  @Metadata('route', { 'ui.next': 'always' })
  async handle({ inertia }: HttpContext) {
    if (app.inProduction) {
      return inertia.render('errors/index', {
        status: 404,
        title: 'Not Found',
        explanation: 'The requested page could not be found.',
      })
    }

    return inertia.render('graphiqls/index', {
      title: 'GraphiQL',
      description: 'GraphiQL is an in-browser IDE for exploring GraphQL.',
    })
  }
}
