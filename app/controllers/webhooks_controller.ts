import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { ConfigMapRegistry } from '@munkit/main'
import pLimit from 'p-limit'
import { Queue } from '@mirai-game-studio/adonis-sdk/queue'
import { Logger } from '@adonisjs/core/logger'
import { uniq } from 'lodash-es'

import { GithubWebhookActionType } from '#configmaps/github/github_webhook'
import { GithubWebhookEvent } from '#services/github/webhooks/index'
import PullRequest from '#models/pull_request'

const webhookLimit = pLimit(5)

export default class WebhooksController {
  @inject()
  async github(
    { request, response }: HttpContext,
    configMapRegistry: ConfigMapRegistry,
    queueService: Queue,
    logger: Logger
  ) {
    const event = request.body() as GithubWebhookEvent

    const repository = event.repository.name

    if (!repository) {
      logger.debug('ping event')
      return response.ok({ message: 'ping' })
    }

    switch (event.action) {
      case 'review_requested': {
        const pullRequestUrl = event.pull_request.html_url
        await PullRequest.updateOrCreate(
          { id: pullRequestUrl },
          {
            reviewers: event.pull_request.requested_reviewers.map((r) => r.login),
          }
        )
        break
      }

      case 'opened': {
        const pullRequestUrl = event.pull_request.html_url
        await PullRequest.updateOrCreate(
          { id: pullRequestUrl },
          {
            reviewers: event.pull_request.requested_reviewers.map((r) => r.login),
            requestedReviewers: event.pull_request.requested_reviewers.map((r) => r.login),
          }
        )
        break
      }

      default:
        break
    }

    const webhookConfigMap = configMapRegistry
      .get('cmap.gh.webhook')
      .find((w) => w.repositories.some((r) => r.name === repository))

    if (!webhookConfigMap) {
      return response.ok({ message: 'success' })
    }

    const repositoryConfigMap = webhookConfigMap.repositories.get(repository)
    const repositoryActionIds = uniq(
      repositoryConfigMap.actions.map((a) => a.id).concat(webhookConfigMap.actions.map((a) => a.id))
    )

    await Promise.all(
      repositoryActionIds.map((actionId) =>
        webhookLimit(async () => {
          const action =
            repositoryConfigMap.actions.tryGet(actionId) ?? webhookConfigMap.actions.get(actionId)
          switch (action.type) {
            case GithubWebhookActionType.OpenProjectTask: {
              await queueService.dispatch(
                '#jobs/webhook_open_project_task_move_job',
                {
                  event,
                  actionId,
                  webhookId: webhookConfigMap.id,
                },
                {
                  queueName: 'default',
                }
              )
              break
            }

            default:
              break
          }
        })
      )
    )

    return response.ok({ message: 'success' })
  }
}
