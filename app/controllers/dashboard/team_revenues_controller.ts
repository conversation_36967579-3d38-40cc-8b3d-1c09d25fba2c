import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { Database } from '@adonisjs/lucid/database'
import { inject } from '@adonisjs/core'

import DashboardRoleMembership from '#models/dashboard_role_membership'
import { configMapRule } from '#validators/rules/config_map_rule'
import User from '#models/user'
import GameMetric, { SubjectMetric } from '#models/game_metric'
import Team from '#models/team'

import { SubjectPresenter } from './team_revenue_presenter.js'

const teamRevenueIndexValidator = vine.compile(
  vine.object({
    roleId: vine.string().use(
      configMapRule({
        id: 'cmap.dash.role',
      })
    ),
    groupBy: vine.enum(['team', 'user']),
    teamId: vine.number().optional(),
    page: vine
      .number()
      .parse((v) => v || 1)
      .optional(),
    perPage: vine
      .number()
      .parse((v) => v || 30)
      .optional(),
    from: vine.date({
      formats: ['YYYY-MM-DD'],
    }),
    to: vine.date({
      formats: ['YYYY-MM-DD'],
    }),
    orderDirection: vine.enum(['asc', 'desc']),
  })
)

export default class TeamRevenuesController {
  @inject()
  async index({ bouncer, request, response, view }: HttpContext, db: Database) {
    await bouncer.with('DashboardTeamRevenuePolicy').authorize('index')

    return request.format<'json' | 'html'>({
      html: () => view.render('dashboard/team_revenues/index'),
      json: async () => {
        const requestData = await request.validateUsing(teamRevenueIndexValidator)

        const makeSubjectTableQuery = async () => {
          let query: string
          let subjects: SubjectPresenter[] = []

          if (requestData.groupBy === 'team') {
            const userQuery = User.query()
              .withScopes((s) => s.withLeaderTeamId())
              .select('email')

            const memberships = await DashboardRoleMembership.query()
              .withScopes((s) => s.withUsers())
              .distinctOn('storeId', User.columnName('teamId', User.table))
              .joinRaw(
                `INNER JOIN (${userQuery.toQuery()}) users ON ${User.quote('email')} = "user"`
              )
              .whereNotNull(db.raw(User.quote('teamId')))
              .select(db.raw(User.quote('teamId')))
              .select('storeId', 'user')
              .where(
                DashboardRoleMembership.columnName('roleId', 'memberships'),
                requestData.roleId
              )
              .where((q) => {
                if (requestData.teamId) {
                  q.where(User.columnName('teamId', User.table), requestData.teamId)
                }
              })

            query = db
              .rawQuery(
                `SELECT * FROM jsonb_to_recordset('${JSON.stringify(
                  memberships.map((m) => ({
                    subject: m.$extras['team_id'],
                    store_id: m.storeId,
                  }))
                )}') AS record(subject INT, store_id TEXT)`
              )
              .toQuery()

            subjects = await Team.query()
              .whereIn(
                'id',
                memberships.map((m) => m.$extras['team_id'])
              )
              .then((ts) =>
                ts.map((t) => new SubjectPresenter().merge({ id: t.id.toString(), name: t.name }))
              )
          } else if (requestData.groupBy === 'user') {
            const userQuery = User.query()
              .from(
                User.query()
                  .select('email')
                  .withScopes((s) => s.withLeaderTeamId())
                  .as('users')
              )
              .whereIn(
                'teamId',
                Team.query()
                  .where('roleId', requestData.roleId)
                  .select('id')
                  .where((q) => {
                    if (requestData.teamId) {
                      q.where('id', requestData.teamId)
                    }
                  })
              )

            const memberships = await DashboardRoleMembership.query()
              .withScopes((s) => s.withUsers())
              .whereIn('user', userQuery.select('email'))
              .distinctOn('storeId', 'user')

            query = db
              .rawQuery(
                `SELECT * FROM jsonb_to_recordset('${JSON.stringify(
                  memberships.map((m) => ({
                    subject: m.user,
                    store_id: m.storeId,
                  }))
                )}') AS record(subject TEXT, store_id TEXT)`
              )
              .toQuery()

            subjects = await userQuery
              .clone()
              .then((ts) =>
                ts.map((t) =>
                  new SubjectPresenter().merge({ id: t.email, name: t.fullName || t.email })
                )
              )
          } else {
            throw new Error("Invalid groupBy value. It should be either 'team' or 'user'")
          }

          return { query, subjects }
        }

        const { query: subjectQuery, subjects: subjectPresenters } = await makeSubjectTableQuery()

        const baseQuery = SubjectMetric.query()
          .from(
            GameMetric.query()
              .withScopes((s) => s.default())
              .as('revenue_list')
          )
          .joinRaw(
            db.raw(
              `INNER JOIN (${subjectQuery}) subject_games ON subject_games.store_id = ${GameMetric.quote('storeId', 'revenue_list')}`
            )
          )
          .whereNotNull('subject_games.subject')

        const revenues = await baseQuery
          .clone()
          .groupBy('subject_games.subject')
          .select('subject_games.subject')
          .sum(GameMetric.columnName('profit', 'revenue_list'), GameMetric.columnName('profit'))
          .sum(`revenue_list.internal_profit`, 'internal_profit')
          .sum(`revenue_list.external_profit`, 'external_profit')
          .orderByRaw(`"${GameMetric.columnName('profit')}" DESC NULLS LAST`)
          .paginate(requestData.page!, requestData.perPage!)

        const { data, meta } = revenues.toJSON()

        const subjects = data.map((d) => d.subject)

        const metricsQuery = baseQuery
          .clone()
          .whereBetween('date', [requestData.from, requestData.to])
          .sum(GameMetric.columnName('profit'), GameMetric.columnName('profit'))
          .sum(GameMetric.columnName('cost'), GameMetric.columnName('cost'))
          .sum(GameMetric.columnName('revenue'), GameMetric.columnName('revenue'))
          .sum(`external_profit`, `external_profit`)
          .sum(`internal_profit`, `internal_profit`)

        const breakdowns = await metricsQuery
          .clone()
          .whereIn('subject_games.subject', subjects)
          .groupBy('date', 'subject_games.subject')
          .select('date', 'subject')
          .orderBy('date', requestData.orderDirection)

        const aggregationQuery = metricsQuery.clone()

        const aggregation = await aggregationQuery.clone().firstOrFail()

        const aggregationBreakdown = await aggregationQuery
          .clone()
          .groupBy('date')
          .select('date')
          .orderBy('date', requestData.orderDirection)

        response.success(data, {
          ...meta,
          breakdowns,
          subjects: subjectPresenters,
          aggregation: {
            data: aggregation,
            breakdown: aggregationBreakdown,
          },
        })
      },
      default: 'html',
    })
  }
}
