import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'

import DashboardNotification from '#models/dashboard_notification'

const storeValidatior = vine.compile(
  vine.object({
    message: vine.string(),
  })
)

const updateValidator = vine.compile(
  vine.object({
    message: vine.string().optional(),
    isVisible: vine.boolean().optional(),
    isPinned: vine.boolean().optional(),
  })
)

export default class NotificationsController {
  async store({ request, bouncer }: HttpContext) {
    await bouncer.with('dash.notification').authorize('store')

    const { message } = await request.validateUsing(storeValidatior)

    const notification = await DashboardNotification.create({
      isPinned: false,
      isVisible: true,
      message,
    })

    return request.format({
      json: async () => {
        return { data: notification }
      },
      default: 'json',
    })
  }

  async update({ request, bouncer, params }: HttpContext) {
    await bouncer.with('dash.notification').authorize('update')

    const notification = await DashboardNotification.findOrFail(params.id)
    const attributes = await request.validateUsing(updateValidator)
    await notification.merge(attributes).save()

    return request.format({
      json: async () => {
        return {
          data: notification,
        }
      },
      default: 'json',
    })
  }
}
