import type { HttpContext } from '@adonisjs/core/http'

import GameStudio from '#models/game_studio'

export default class GameStudioMetricsController {
  async index({ bouncer, inertia }: HttpContext) {
    await bouncer.with('dash.game_studio_metric').authorize('index')
    const defaultStudio = await GameStudio.query()
      .withScopes((s) => s.default())
      .orderBy('id', 'asc')
      .firstOrFail()
    return inertia.render('dashboard/game_studio_metrics/index', {
      title: 'Studio Metrics',
      defaultStudio: { name: defaultStudio.name, id: defaultStudio.id },
    })
  }
}
