import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'

import GameReview from '#models/game_review'
import Game from '#models/game'

const gameReviewUpdateValidator = vine.compile(
  vine.object({
    marketingNote: vine.string().optional(),
    productNote: vine.string().optional(),
  })
)

export default class GameReviewsController {
  async index({ view, bouncer, request }: HttpContext) {
    const game = await Game.findOrFail(request.param('game_id'))
    await bouncer.with('dash.game.review').authorize('index', game)

    return view.render('dashboard/game/game_reviews/index')
  }

  async update({ request, response, bouncer }: HttpContext) {
    const gameId = request.param('game_id')
    const game = await Game.findOrFail(request.param('game_id'))
    await bouncer.with('dash.game.review').authorize('update', game)

    return request.format<'json'>({
      json: async () => {
        const date = request.param('date')
        const attrs = await request.validateUsing(gameReviewUpdateValidator)
        const gameReview = await GameReview.updateOrCreate(
          {
            gameId,
            date,
          },
          attrs
        )
        response.success(gameReview)
      },
      default: 'json',
    })
  }
}
