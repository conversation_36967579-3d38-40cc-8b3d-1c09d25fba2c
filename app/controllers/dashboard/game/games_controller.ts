import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { Metadata } from '@munkit/main'

export default class GamesController {
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer }: HttpContext) {
    await bouncer.with('DashboardGamePolicy').authorize('index')
    return inertia.render('dashboard/games/index', {
      canUpdateRole: await bouncer.with('DashboardRolePolicy').allows('update'),
    })
  }

  @inject()
  async show({ request, response, bouncer }: HttpContext) {
    await bouncer.with('DashboardGamePolicy').authorize('index')
    return response.redirect().toRoute('dashboard.games.metrics.index', {
      game_id: request.param('id'),
    })
  }
}
