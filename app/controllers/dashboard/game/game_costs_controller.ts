import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { Metadata } from '@munkit/main'

import Game from '#models/game'

export default class GameCostsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, params, bouncer }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('dash.game.cost').authorize('index', game)

    return inertia.render('dashboard/games/game_costs/index', {
      game: { id: game.id, name: game.name },
    })
  }
}
