import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'

import Game from '#models/game'
import GamePerformanceSetting, { GamePerformanceCriteria } from '#models/game_performance_setting'
import { GamePerformanceCriteriaConclusion } from '#graphql/main'

const gamePerformanceSettingUpdateValidator = vine.compile(
  vine.object({
    criterias: vine.array(
      vine.object({
        conclusion: vine.enum(Object.values(GamePerformanceCriteriaConclusion)),
        // @ts-ignore
        metrics: vine.object({
          roas: vine.number(),
          playtime: vine.number(),
          retentionRateDay1: vine.number(),
          cpi: vine.number(),
          monetImpsDau: vine.number(),
        }),
      })
    ),
  })
)

export default class GamePerformanceSettingsController {
  async update({ bouncer, request, response }: HttpContext) {
    return request.format({
      json: async () => {
        const game = await Game.findOrFail(request.param('storeId'))
        await bouncer.with('dash.game.perf_setting').authorize('update', game)
        const { criterias } = await request.validateUsing(gamePerformanceSettingUpdateValidator)

        const performanceSetting = await GamePerformanceSetting.updateOrCreate(
          {
            gameId: game.id,
          },
          {
            criterias: criterias.map((c) => new GamePerformanceCriteria().merge(c)),
          }
        )

        return response.success(performanceSetting)
      },
      default: 'json',
    })
  }
}
