import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { groupBy, toPairs } from 'lodash-es'

import GameLevelDrop from '#models/game_level_drop'
import { compareSemver } from '#utils/semver'
import Game from '#models/game'

import { GameLevelDropPresenter } from './game_level_drop_presenter.js'

const indexValidator = vine.compile(
  vine.object({
    versions: vine.array(vine.string().trim()).optional(),
  })
)

export default class GameLevelDropsController {
  async index({ view, bouncer, params, request, response }: HttpContext) {
    const storeId = params['game_id']
    const game = await Game.findOrFail(storeId)
    await bouncer.with('dash.game.lv_drop').authorize('index', game)

    return request.format<'html' | 'json'>({
      json: async () => {
        const versions = await GameLevelDrop.query()
          .withScopes((s) => s.default())
          .select('version')
          .distinct()
          .where('storeId', storeId)
          .then((r) =>
            r
              .map((e) => e.version)
              .sort(compareSemver)
              .reverse()
          )

        const requestData = await indexValidator.validate(request.qs())
        const requestedVersions = requestData.versions ?? []
        const levelDrops = await GameLevelDrop.query()
          .where('storeId', storeId)
          .whereIn(
            'version',
            requestedVersions.length > 0 ? requestedVersions : versions.slice(0, 1)
          )
          .orderBy('world', 'asc')
          .orderBy('level', 'asc')

        const versionToLevelDrops = groupBy(levelDrops, 'version')
        const presenters = toPairs(versionToLevelDrops).flatMap(([_, lds]) => {
          return GameLevelDropPresenter.presentList(lds)
        })

        return response.json({
          data: presenters,
          meta: {
            versions: versions,
          },
        })
      },
      html: async () => {
        return view.render('dashboard/game/game_level_drops/show')
      },
      default: 'html',
    })
  }
}
