import { BaseModel, column } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

import { MediationDailyRevenue } from '#models/game_revenue'

import { AggregationAttributePresenter } from './model_attribute_presenter.js'

export class MediationPresenter extends BaseModel {
  @column()
  declare id: number

  @column({
    serialize: (values: ColumnPresenter[]) => values.map((e) => e.serialize()),
  })
  columns: ColumnPresenter[] = []
}

export class ColumnPresenter extends BaseModel {
  @column()
  declare name: string

  @column()
  declare header: string
}

export class MediationDailyRevenueGroup extends BaseModel {
  @column.date()
  declare date: DateTime

  @column({
    serialize: (values: MediationDailyRevenue[]) => values.map((e) => e.serialize()),
  })
  declare revenues: MediationDailyRevenue[]
}

export class MediationAggregationPresenter extends BaseModel {
  @column()
  declare mediationId: number

  @column({
    serialize: (values: MediationDailyRevenueGroup[]) => values.map((e) => e.serialize()),
  })
  declare attributes: AggregationAttributePresenter[]
}
