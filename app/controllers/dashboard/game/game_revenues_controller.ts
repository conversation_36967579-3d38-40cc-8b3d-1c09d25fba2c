import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { first, groupBy, last, orderBy, toPairs } from 'lodash-es'
import { DateTime } from 'luxon'
import { getAllMetadata } from '@munkit/main'

import GameRevenue, {
  AdmobDailyRevenue,
  ApplovinDailyRevenue,
  NonMediationDailyRevenue,
  UnityLPDailyRevenue,
} from '#models/game_revenue'
import { orderDirectionRule } from '#validators/rules/order_direction_rule'
import Game from '#models/game'

import {
  ColumnPresenter,
  MediationAggregationPresenter,
  MediationDailyRevenueGroup,
  MediationPresenter,
} from './mediation_presenter.js'
import { AggregationAttributePresenter } from './model_attribute_presenter.js'

const gameRevenueIndexValidator = vine.compile(
  vine.object({
    from: vine
      .date({
        formats: ['YYYY-MM-DD'],
      })
      .optional(),
    to: vine
      .date({
        formats: ['YYYY-MM-DD'],
      })
      .optional(),
    page: vine
      .number()
      .parse((v) => v || 1)
      .optional(),
    perPage: vine
      .number()
      .parse((v) => v || 200)
      .optional(),
    direction: vine.string().use(orderDirectionRule()),
  })
)

export default class GameRevenuesController {
  async index({ request, response, view, params, bouncer }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('dash.game.revenue').authorize('index', game)

    return request.format<'html' | 'json'>({
      html: async () => {
        return view.render('dashboard/game/game_revenues/index')
      },
      json: async () => {
        const { direction, from, page, perPage, to } =
          await request.validateUsing(gameRevenueIndexValidator)
        const dates = await GameRevenue.query()
          .where((q) => {
            if (from) q.where('date', '>=', from)
            if (to) q.where('date', '<=', to)
          })
          .where('store_id', game.id)
          .select('date')
          .groupBy('date')
          .orderBy('date', direction as any)
          .paginate(page!, perPage)

        const bounds = orderBy(
          [first(dates)?.date, last(dates)?.date].filter(Boolean) as DateTime[],
          (e) => e,
          'asc'
        )
        const upperbound = last(bounds)
        const lowerbound = first(bounds)

        const revenues = await GameRevenue.query()
          .withScopes((s) => s.default())
          .withScopes((s) => s.selectImpsDau())
          .withScopes((s) => s.selectNetRevenue())
          .select('*')
          .where('store_id', game.id)
          .where((q) => {
            if (lowerbound) {
              q.where('date', '>=', lowerbound.toJSDate())
            }

            if (upperbound) {
              q.where('date', '<=', upperbound.toJSDate())
            }
          })

        const dailyRevenues = toPairs(groupBy(revenues, (r) => r.date.toFormat('yyyy-MM-dd'))).map(
          ([dateStr, values]) => {
            return new MediationDailyRevenueGroup().merge({
              date: DateTime.fromFormat(dateStr, 'yyyy-MM-dd')!,
              revenues: [
                UnityLPDailyRevenue.make(values),
                ApplovinDailyRevenue.make(values),
                AdmobDailyRevenue.make(values),
                NonMediationDailyRevenue.make(values),
              ],
            })
          }
        )

        const aggregations = await GameRevenue.aggregate(game.id)
        const aggregatedRevenues = new MediationDailyRevenueGroup().merge({
          date: DateTime.now(),
          revenues: [
            UnityLPDailyRevenue.make(aggregations),
            ApplovinDailyRevenue.make(aggregations),
            AdmobDailyRevenue.make(aggregations),
            NonMediationDailyRevenue.make(aggregations),
          ],
        })

        response.ok({
          data: orderBy(dailyRevenues, (e) => e.date, direction as any),
          meta: {
            ...dates.getMeta(),
            mediations: [
              [GameRevenue.mediationId.UnityLP, UnityLPDailyRevenue] as const,
              [GameRevenue.mediationId.Applovin, ApplovinDailyRevenue] as const,
              [GameRevenue.mediationId.Admob, AdmobDailyRevenue] as const,
              [GameRevenue.mediationId.NonMediation, NonMediationDailyRevenue] as const,
            ].map(([mediationId, RevenueClass]) => {
              return new MediationPresenter().merge({
                id: mediationId,
                columns: getAllMetadata('game.rev.field', RevenueClass).map((field) => {
                  const col = RevenueClass.$columnsDefinitions.get(field.name)!
                  return new ColumnPresenter().merge({ name: field.name, header: col.columnName })
                }),
              })
            }),
            aggregation: aggregatedRevenues,
            aggregationAttributes: [
              [GameRevenue.mediationId.UnityLP, UnityLPDailyRevenue] as const,
              [GameRevenue.mediationId.Applovin, ApplovinDailyRevenue] as const,
              [GameRevenue.mediationId.Admob, AdmobDailyRevenue] as const,
              [GameRevenue.mediationId.NonMediation, NonMediationDailyRevenue] as const,
            ].map(([mediationId, RevenueClass]) => {
              return new MediationAggregationPresenter().merge({
                mediationId,
                attributes: getAllMetadata('agg.op', RevenueClass).map((field) => {
                  return new AggregationAttributePresenter().merge({
                    name: field.name,
                    operator: field.metadata.operator,
                  })
                }),
              })
            }),
          },
        })
      },
      default: 'html',
    })
  }
}
