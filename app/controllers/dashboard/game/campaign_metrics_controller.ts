import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { Metadata } from '@munkit/main'

import Game from '#models/game'

export default class CampaignMetricsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  public async index({ inertia, bouncer, params }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('dash.game.campaign_metric').authorize('index', game)
    return inertia.render('dashboard/games/campaign_metrics/index', {
      game: { id: game.id, name: game.name },
    })
  }
}
