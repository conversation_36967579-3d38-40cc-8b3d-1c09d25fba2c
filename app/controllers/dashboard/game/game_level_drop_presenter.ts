import { BaseModel, column, computed } from '@adonisjs/lucid/orm'
import { fromPairs, groupBy, toPairs, orderBy, thru, max } from 'lodash-es'

import GameLevelDrop from '#models/game_level_drop'
import { safeAvg, safeDivide } from '#utils/math'

export class GameLevelDropPresenter extends BaseModel {
  @column()
  declare id: number

  @column()
  declare isFirstLevel: boolean

  @column()
  declare version: string

  @column()
  declare level: number

  @column()
  declare world: number

  @column({
    // serialize: (value: number) => formatter.round(value),
  })
  declare activeUserCount: number

  @column()
  declare playCount: number

  @column({
    // serialize: (value: number) => formatter.round(value),
  })
  declare eventCountPerUser: number

  @column({
    // serialize: (value: number) => formatter.round(value),
  })
  declare userDropCount: number

  @column({
    // serialize: (value: number) => formatter.percentage(value),
  })
  declare userDropPercentage: number

  @column({
    // serialize: (value: number) => formatter.percentage(value),
  })
  declare userDropRatio: number

  @computed()
  get avgPlaytimeSec() {
    return safeDivide(this.totalPlaytimeSec, this.activeUserCount)
  }

  @column()
  declare totalPlaytimeSec: number

  static presentList(models: GameLevelDrop[]): GameLevelDropPresenter[] {
    const gameLevelDrops = orderBy(models, ['world', 'level'], ['asc', 'asc'])
    const levelsByWorld = groupBy(gameLevelDrops, 'world')
    const worldToFirstLevel = fromPairs(
      toPairs(levelsByWorld).map(([world, levels]) => [world, levels[0].level])
    )

    let totalUserDropCount = 0

    const maxActiveUserCount = max(models.map((m) => m.activeUserCount)) ?? 0

    return gameLevelDrops.map((gameLevelDrop, index) => {
      const isFirstLevel = worldToFirstLevel[gameLevelDrop.world] === gameLevelDrop.level
      const getUserDropCount = () => {
        if (isFirstLevel) {
          return 0
        }

        return thru(gameLevelDrops[index - 1]?.activeUserCount, (prevActiveUserCount) =>
          prevActiveUserCount ? prevActiveUserCount - gameLevelDrop.activeUserCount : 0
        )
      }

      const userDropCount = getUserDropCount()

      totalUserDropCount += userDropCount

      return new GameLevelDropPresenter().merge({
        activeUserCount: gameLevelDrop.activeUserCount,
        eventCountPerUser: gameLevelDrop.attemptCount / gameLevelDrop.activeUserCount,
        playCount: gameLevelDrop.attemptCount,
        userDropCount: userDropCount,
        userDropPercentage: safeAvg(userDropCount, gameLevelDrop.activeUserCount + userDropCount),
        userDropRatio: safeDivide(totalUserDropCount, maxActiveUserCount),
        level: gameLevelDrop.level,
        version: gameLevelDrop.version,
        world: gameLevelDrop.world,
        id: gameLevelDrop.id,
        isFirstLevel,
        totalPlaytimeSec: gameLevelDrop.totalPlaytimeSec,
      })
    })
  }
}
