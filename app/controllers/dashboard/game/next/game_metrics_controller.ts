import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main'
import { inject } from '@adonisjs/core'

import Game from '#models/game'

export default class GameMetricsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, params, bouncer }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('dash.game.metric_v2').authorize('index', game)

    return inertia.render('dashboard/games/game_metrics_next/index', {
      game: game.serialize() as Game,
    })
  }
}
