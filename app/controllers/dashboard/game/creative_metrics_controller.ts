import type { HttpContext } from '@adonisjs/core/http'

import Game from '#models/game'

export default class GameCreativeMetricsController {
  async index({ bouncer, inertia, params }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('dash.game.creative_metric').authorize('index', game)
    return inertia.render('dashboard/game/creative_metrics/index', {
      game: { id: game.id, name: game.name },
    })
  }
}
