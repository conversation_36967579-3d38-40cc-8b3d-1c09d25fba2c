import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main/decorator'

import Game from '#models/game'

export default class GameMetricsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer, params }: HttpContext) {
    const game = await Game.query().where('storeId', params['game_id']).firstOrFail()
    await bouncer.with('dash.game.metric').authorize('index', game)

    return inertia.render('dashboard/games/game_metrics/index', {
      game: {
        id: game.id,
        name: game.name,
      },
    })
  }
}
