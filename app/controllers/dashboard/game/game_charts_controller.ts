import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main'
import z from 'zod'

import Game from '#models/game'

const showValidator = z.object({
  gameId: z.string(),
  chartId: z.string(),
})

export default class GameChartsController {
  @Metadata('route', { 'ui.next': 'always' })
  public async show({ inertia, params, bouncer }: HttpContext) {
    const { gameId, chartId } = await showValidator.parseAsync({
      gameId: params['game_id'],
      chartId: params['id'],
    })

    const game = await Game.findOrFail(gameId)
    await bouncer.with('dash.game.chart').authorize('show', game, chartId)

    return inertia.render('dashboard/games/charts/show', {
      game,
      chartId,
    })
  }
}
