import { BaseModel, column } from '@adonisjs/lucid/orm'

import type { AggregationOperator } from '#models/extensions/aggregation'

export class ModelAttributePresenter extends BaseModel {
  @column()
  declare name: string

  @column()
  declare displayName: string

  @column()
  declare aggregate: string

  @column()
  declare permission: 'r' | 'rw' | 'none'
}

export class AggregationAttributePresenter extends BaseModel {
  @column()
  declare name: string

  @column()
  declare operator: AggregationOperator

  @column({
    serialize: (values: AggregationAttributePresenter[]) => values?.map((e) => e.serialize()),
  })
  declare depends?: AggregationAttributePresenter[]
}
