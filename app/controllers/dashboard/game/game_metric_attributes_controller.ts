import type { HttpContext } from '@adonisjs/core/http'

import GameMetric from '#models/game_metric'
import GameMetricMetadatum from '#models/game_metric_metadatum'

import { ModelAttributePresenter } from './model_attribute_presenter.js'

export default class GameMetricAttributesController {
  async index({ request, response }: HttpContext) {
    return request.format({
      json: async () => {
        response.json({
          data: Array.from(GameMetric.$columnsDefinitions.entries())
            .concat(Array.from(GameMetric.$computedDefinitions.entries()) as any[])
            .concat(Array.from(GameMetricMetadatum.$columnsDefinitions.entries()))
            .filter(([attr]) => {
              return !['id', 'createdAt', 'updatedAt', 'storeId', 'date', 'metricId'].includes(attr)
            })
            .map(([attr, column]) =>
              new ModelAttributePresenter().merge({
                displayName: column.columnName,
                name: attr,
              })
            ),
        })
      },
      default: 'json',
    })
  }
}
