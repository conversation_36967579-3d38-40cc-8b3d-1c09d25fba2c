import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { Metadata } from '@munkit/main'

import Game from '#models/game'

export default class FirebaseExperimentsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer, params }: HttpContext) {
    const game = await Game.findOrFail(params['game_id'])
    await bouncer.with('dash.game.fb_experiment').authorize('index', game)
    return inertia.render('dashboard/games/firebase_experiments/index', {
      game: { id: game.id, name: game.name },
    })
  }
}
