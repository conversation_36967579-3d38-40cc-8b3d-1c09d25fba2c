import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main'

import Game from '#models/game'

export default class ReleaseMetricsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  public async index({ inertia, params }: HttpContext) {
    const gameId = params['game_id']
    const game = await Game.findOrFail(gameId)
    return inertia.render('dashboard/games/release_metrics/index', { game: game.serialize() })
  }
}
