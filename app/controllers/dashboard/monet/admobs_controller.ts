import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main'

export default class AdmobsController {
  @inject()
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer }: HttpContext) {
    await bouncer.with('dash.monet.admob').authorize('index')

    return inertia.render('dashboard/monet/admobs/index')
  }
}
