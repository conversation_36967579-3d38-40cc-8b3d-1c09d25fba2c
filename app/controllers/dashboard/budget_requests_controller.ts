import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import { Metadata } from '@munkit/main'

@inject()
export default class BudgetRequestsController {
  @Metadata('route', { 'ui.next': 'always' })
  async index({ inertia, bouncer }: HttpContext) {
    await bouncer.with('dash.budget_request').authorize('index')
    return inertia.render('dashboard/budget_requests/index')
  }
}
