import type { HttpContext } from '@adonisjs/core/http'
import { ConfigMapRegistry, Metadata } from '@munkit/main'
import pLimit from 'p-limit'
import { inject } from '@adonisjs/core'
import { column, computed } from '@adonisjs/lucid/orm'
import vine from '@vinejs/vine'

import { updateGameMetricAclValidator } from '#validators/dashboard/game_metric_acl'
import ACL from '#models/acl'
import { AclSubject } from '#config/enums'

const updateLimit = pLimit(3)

const aclShowValidator = vine.compile(
  vine.object({
    subject: vine.string(),
  })
)

@inject()
export default class AccessControlsController {
  constructor(private configMapRegistry: ConfigMapRegistry) {}

  @Metadata('route', { 'ui.next': 'always' })
  async show({ inertia, request, bouncer, response, params }: HttpContext) {
    await bouncer.with('AclPolicy').authorize('show')
    const { subject } = await request.validateUsing(aclShowValidator, { data: params })

    return request.format<'json' | 'html'>({
      default: 'html',
      html: () => {
        if (subject === AclSubject.Route) {
          return inertia.render(`dashboard/acls/route`)
        } else {
          return inertia.render(`dashboard/acls/attribute`)
        }
      },
      json: async () => {
        response.json({
          data: await this.#getAccessControls(subject),
        })
      },
    })
  }

  async update({ request, response, bouncer, params }: HttpContext) {
    await bouncer.with('AclPolicy').authorize('update')

    const subject = params['subject']

    const { roles } = await updateGameMetricAclValidator.validate(request.all())

    return request.format({
      json: async () => {
        await Promise.all(
          roles.map((role) =>
            updateLimit(async () => {
              await ACL.updateOrCreate({ roleId: role.id, subject }, { permits: role.permits })
            })
          )
        )

        response.json({
          data: await this.#getAccessControls(subject),
        })
      },
      default: 'json',
    })
  }

  async #getAccessControls(subject: string) {
    const roleConfigMap = this.configMapRegistry.get('cmap.dash.role')
    const accessControls = await ACL.query().where('subject', subject)

    return roleConfigMap.map((role) => {
      const acl = accessControls.find((a) => a.roleId === role.id)
      return new ACLPresenter().merge(
        acl || {
          roleId: role.id,
          data: [],
        }
      )
    })
  }
}

export class ACLPresenter extends ACL {
  @column({ serializeAs: null })
  declare data: string[]

  @computed()
  get attrs(): string[] {
    return this.data
  }
}
