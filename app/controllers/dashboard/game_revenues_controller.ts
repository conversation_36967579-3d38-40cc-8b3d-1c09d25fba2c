import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { uniq } from 'lodash-es'
import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import { Database } from '@adonisjs/lucid/database'

import GameMetric from '#models/game_metric'
import Game from '#models/game'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import Team from '#models/team'
import { Tool } from '#config/enums'
import ToolPermissionService from '#services/tool_permission_service'
import User from '#models/user'

const gameRevenueIndexValidator = vine.compile(
  vine.object({
    from: vine
      .date({
        formats: ['YYYY-MM-DD'],
      })
      .optional(),
    to: vine
      .date({
        formats: ['YYYY-MM-DD'],
      })
      .optional(),
    page: vine
      .number()
      .parse((v) => v || 1)
      .optional(),
    perPage: vine
      .number()
      .parse((v) => v || 200)
      .optional(),
    direction: vine
      .enum(['asc', 'desc'])
      .parse((v) => v || 'desc')
      .optional(),
    teamId: vine.string().trim().optional(),
    memberEmail: vine.string().trim().optional(),
    date: vine.date({
      formats: ['YYYY-MM-DD'],
    }),
    sort: vine.enum(['profit', 'daily_cost', 'daily_profit', 'weekly_cost', 'weekly_profit']),
  })
)

export default class GameRevenuesController {
  @inject()
  async index(
    { request, response, view, bouncer, auth }: HttpContext,
    toolPermissionService: ToolPermissionService,
    db: Database
  ) {
    await bouncer.with('DashboardGameRevenuePolicy').authorize('index')

    return request.format<'html' | 'json'>({
      html: async () => {
        return view.render('dashboard/game_revenue/index')
      },
      json: async () => {
        const requestData = await request.validateUsing(gameRevenueIndexValidator)

        const user = auth.user! as User
        await user.load('teamLead')

        const isManager = toolPermissionService.canManage(Tool.Dashboard)(auth.user!)

        // TODO: Refactor
        const getStoreIdsInScope = async () => {
          if (isManager) {
            if (requestData.teamId) {
              const team = await Team.findOrFail(requestData.teamId!)

              return DashboardRoleMembership.query()
                .withScopes((s) => s.selectManyGameInChargedByTeam(team))
                .then((rs) => uniq(rs.map((r) => r.storeId)))
            }
          }

          if (isManager || auth.user!.teamLead) {
            if (requestData.memberEmail) {
              return DashboardRoleMembership.query()
                .withScopes((s) => s.selectManyGameInChargedByUser(requestData.memberEmail))
                .then((rs) => uniq(rs.map((r) => r.storeId)))
            }
          }

          const gamesQuery = Game.query()
          await bouncer.with('GameQueryPolicy').authorize('scope', gamesQuery)
          const games = await gamesQuery

          return games.map((g) => g.storeId)
        }

        const baseQuery = GameMetric.query().from(
          GameMetric.query()
            .withScopes((s) => s.default())
            .as(GameMetric.table)
        )

        const scopedStoreIds = await getStoreIdsInScope()

        const sortField =
          requestData.sort.startsWith('daily_') || requestData.sort.startsWith('weekly_')
            ? requestData.sort
            : GameMetric.columnName(requestData.sort as any, 'revenue_list')

        const revenuesQuery = GameMetric.query()
          .preload('game')
          .from(
            baseQuery
              .clone()
              .preload('game')
              .sum(
                GameMetric.columnName('profit', GameMetric.table),
                GameMetric.columnName('profit')
              )
              .sum(`${GameMetric.table}.internal_profit`, 'internal_profit')
              .sum(`${GameMetric.table}.external_profit`, 'external_profit')
              .sum(
                GameMetric.columnName('totalInstalls', GameMetric.table),
                GameMetric.columnName('totalInstalls')
              )
              .whereIn(GameMetric.columnName('storeId', GameMetric.table), scopedStoreIds)
              .select(GameMetric.columnName('storeId', GameMetric.table))
              .groupBy(GameMetric.columnName('storeId', GameMetric.table))
              .as('revenue_list')
          )
          .joinRaw(
            `LEFT JOIN (${GameMetric.query()
              .from(
                GameMetric.query()
                  .withScopes((s) => s.default())
                  .as(GameMetric.table)
              )
              .select('storeId', GameMetric.columnName('cost'), GameMetric.columnName('profit'))
              .where('date', requestData.date)
              .toQuery()}) daily_metrics ON daily_metrics.store_id = revenue_list.store_id`
          )
          .joinRaw(
            `LEFT JOIN (${GameMetric.query()
              .from(
                GameMetric.query()
                  .withScopes((s) => s.default())
                  .as(GameMetric.table)
              )
              .select('storeId')
              .sum(GameMetric.columnName('cost'), GameMetric.columnName('cost'))
              .sum(GameMetric.columnName('profit'), GameMetric.columnName('profit'))
              .groupBy('storeId')
              .whereBetween('date', [
                DateTime.fromJSDate(requestData.date).minus({ days: 6 }).toJSDate(),
                requestData.date,
              ])
              .toQuery()}) weekly_metrics ON weekly_metrics.store_id = revenue_list.store_id`
          )
          .select('revenue_list.*')
          .select(db.raw(`${GameMetric.quote('cost', 'daily_metrics')} AS daily_cost`))
          .select(db.raw(`${GameMetric.quote('profit', 'daily_metrics')} AS daily_profit`))
          .select(db.raw(`${GameMetric.quote('cost', 'weekly_metrics')} AS weekly_cost`))
          .select(db.raw(`${GameMetric.quote('profit', 'weekly_metrics')} AS weekly_profit`))
          .orderByRaw(`${sortField} ${requestData.direction} NULLS LAST`)

        const revenues = await revenuesQuery.paginate(requestData.page!, requestData.perPage!)

        const storeIds = Array.from(revenues.map((r) => r.storeId))

        const breakdownQuery = baseQuery.clone().where((q) => {
          if (requestData.from) {
            q.where('date', '>=', requestData.from)
          }

          if (requestData.to) {
            q.where('date', '<=', requestData.to)
          }
        })

        const breakdowns = await breakdownQuery
          .clone()
          .select(
            'storeId',
            'date',
            'revenue',
            'profit',
            'cost',
            'external_profit',
            'internal_profit',
            'totalInstalls'
          )
          .whereIn('storeId', storeIds)

        const dailyAggregationsQuery = breakdownQuery
          .clone()
          .whereIn('storeId', scopedStoreIds)
          .sum(GameMetric.columnName('profit'), GameMetric.columnName('profit'))
          .sum(GameMetric.columnName('cost'), GameMetric.columnName('cost'))
          .sum(GameMetric.columnName('revenue'), GameMetric.columnName('revenue'))
          .sum(`internal_profit`, 'internal_profit')
          .sum(`external_profit`, 'external_profit')
          .sum(GameMetric.columnName('totalInstalls'), GameMetric.columnName('totalInstalls'))

        const aggregation = await dailyAggregationsQuery.clone().firstOrFail()

        const aggregationBreakdown = await dailyAggregationsQuery
          .clone()
          .groupBy('date')
          .select('date')

        const { data, meta } = revenues.toJSON()

        response.json({
          data,
          meta: {
            ...meta,
            breakdowns,
            aggregation: {
              data: aggregation,
              breakdown: aggregationBreakdown,
            },
          },
        })
      },
      default: 'html',
    })
  }
}
