import type { HttpContext } from '@adonisjs/core/http'

import GameMetric from '#models/game_metric'

export default class RevenueReportsController {
  async index({ view, request, response, bouncer }: HttpContext) {
    await bouncer.with('DashboardRevenueReportPolicy').authorize('index')

    return request.format<'html' | 'json'>({
      json: async () => {
        const rawsql = GameMetric.query().client.raw
        const results = await GameMetric.query()
          .from(
            GameMetric.query()
              .withScopes((s) => s.default())
              .as('metrics')
          )
          .select(
            rawsql(
              `DATE_TRUNC('month', ${GameMetric.quote('date', 'metrics')}) AS ${GameMetric.quote('date', '')}`
            )
          )
          .sum(GameMetric.columnName('cost', 'metrics'), GameMetric.columnName('cost'))
          .sum(GameMetric.columnName('profit', 'metrics'), GameMetric.columnName('profit'))
          .sum(GameMetric.columnName('revenue', 'metrics'), GameMetric.columnName('revenue'))
          .sum('metrics.external_profit', 'external_profit')
          .sum('metrics.internal_profit', 'internal_profit')
          .groupByRaw(rawsql(`DATE_TRUNC('month', ${GameMetric.quote('date', 'metrics')})`))
          .orderBy(GameMetric.columnName('date'), 'desc')

        response.json({ data: results })
      },
      html: () => {
        return view.render('dashboard/revenue_reports/index')
      },
      default: 'html',
    })
  }
}
