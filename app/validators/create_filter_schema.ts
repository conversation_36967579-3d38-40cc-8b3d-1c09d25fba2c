import vine, { BaseLiteralType } from '@vinejs/vine'

import { FilterOperator } from '#graphql/main'

const allOps = Object.values(FilterOperator)

export function createFilterSchema({
  ops = allOps,
  valueSchema,
  required = false,
}: {
  ops?: FilterOperator[]
  valueSchema?: BaseLiteralType<any, any, any>
  required?: boolean
}) {
  valueSchema ??= vine.string()

  const schema = vine.object({
    values: vine.array(valueSchema),
    operator: vine.string().in(ops),
  })

  if (required) {
    return schema
  }

  return schema.optional()
}
