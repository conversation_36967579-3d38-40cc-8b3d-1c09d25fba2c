import vine from '@vinejs/vine'
import { FieldContext } from '@vinejs/vine/types'

async function validateTeamMember(value: unknown, _options: any, field: FieldContext) {
  if (typeof value !== 'string' || !Array.isArray(field.parent.memberEmails)) {
    return
  }

  if (!(field.parent.memberEmails as string[]).includes(value)) {
    field.report(`Leader must be a member of team`, 'conflict', field)
  }
}

export const teamMemberRule = vine.createRule(validateTeamMember)
