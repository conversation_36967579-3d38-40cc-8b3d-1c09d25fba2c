import app from '@adonisjs/core/services/app'
import vine from '@vinejs/vine'
import { FieldContext } from '@vinejs/vine/types'
import { ConfigMapBindings } from '@munkit/main'

type Options<T extends keyof ConfigMapBindings> = {
  id: T
  lookup?: (
    collection: ConfigMapBindings[T],
    value: unknown
  ) => ReturnType<ConfigMapBindings[T]['get']>
}

async function configMap<T extends keyof ConfigMapBindings>(
  value: unknown,
  options: Options<T>,
  field: FieldContext
) {
  if (typeof value !== 'string') {
    return
  }

  // @ts-expect-error
  options.lookup ??= (collection, v) => collection.get(v)

  const configMapRegistry = await app.container.make('cmap.registry')
  const configMapCollection = configMapRegistry.get(options.id)

  // @ts-expect-error
  const record = options.lookup(configMapCollection, value)

  if (!record) {
    field.report(`The {{ field }} field is a valid ${options.id}`, 'configMap', field)
  }
}

export const configMapRule = vine.createRule(configMap)
