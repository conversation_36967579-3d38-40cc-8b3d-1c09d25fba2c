import vine from '@vinejs/vine'
import { FieldContext } from '@vinejs/vine/types'

import { DataSource } from '#graphql/main'

async function dataSource(value: unknown, _options: unknown, field: FieldContext) {
  if (!value) {
    field.mutate(DataSource.Origin, field)
    return
  }

  if (typeof value !== 'string') {
    return field.report(`The {{ field }} field must be a string`, 'dataSource', field)
  }

  const isValid = Object.values(DataSource).includes(value as any)
  if (!isValid) {
    return field.report(`The {{ field }} field is not a valid data source`, 'dataSource', field)
  }
}

export const dataSourceRule = vine.createRule(dataSource)
