import vine from '@vinejs/vine'
import { FieldContext } from '@vinejs/vine/types'

import { OrderDirection } from '#graphql/main'

async function orderDirection(value: unknown, _options: unknown, field: FieldContext) {
  if (!value) {
    field.mutate(OrderDirection.Desc.toLowerCase(), field)
    return
  }

  if (typeof value !== 'string') {
    return
  }

  const allowedValues = new Set([
    ...Object.values(OrderDirection),
    ...Object.values(OrderDirection).map((v) => v.toLowerCase()),
  ])

  if (!allowedValues.has(value)) {
    field.report(`The {{ field }} field must be one of: ASC / DESC`, 'orderDirection', field)
    return
  }

  field.mutate(value.toLowerCase(), field)
}

export const orderDirectionRule = vine.createRule(orderDirection)
