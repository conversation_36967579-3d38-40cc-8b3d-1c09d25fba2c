import vine from '@vinejs/vine'

import { configMapRule } from '#validators/rules/config_map_rule'

export const updateDashboardRolesValidator = vine.compile(
  vine.object({
    storeId: vine.string().trim(),
    roles: vine.array(
      vine.object({
        id: vine
          .string()
          .trim()
          .use(
            configMapRule({
              id: 'cmap.dash.role',
            })
          ),
        users: vine.array(vine.string().trim()).optional(),
      })
    ),
  })
)
