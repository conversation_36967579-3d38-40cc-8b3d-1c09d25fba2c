import vine from '@vinejs/vine'

import { configMapRule } from '#validators/rules/config_map_rule'

export const updateGameMetricAclValidator = vine.compile(
  vine.object({
    roles: vine.array(
      vine.object({
        id: vine
          .string()
          .trim()
          .use(
            configMapRule({
              id: 'cmap.dash.role',
            })
          ),
        permits: vine.array(vine.string().trim()).optional(),
      })
    ),
  })
)
