import vine from '@vinejs/vine'

import { GamePlatform } from '#config/enums'

export const createReleaseProposalValidator = vine.compile(
  vine.object({
    repository: vine.string().trim(),
    platform: vine.enum(GamePlatform),
    semver: vine.string().trim(),
    revision: vine.string().trim(),
    installableDownloadUrl: vine.string().trim(),
    changelog: vine.string().trim(),
  })
)

export const createAndroidReleaseProposalValidator = vine.compile(
  vine.object({
    versionCode: vine.number(),
    symbolsDownloadUrl: vine.string().trim(),
  })
)
