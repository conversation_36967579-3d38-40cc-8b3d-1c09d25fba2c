import escapeStringRegexp from 'escape-string-regexp'

export function formatString(template: string, ...args: any[]) {
  return template.replace(/{(\d+)}/g, (match, index) => {
    return typeof args[index] !== 'undefined' ? args[index] : match
  })
}

export function isUrlMatchPattern(url: string, pattern: string) {
  const regex = new RegExp(
    `^${escapeStringRegexp(pattern).replaceAll(/\\\{\d+\\\}/g, '([^\\\\]+)')}$`
  )
  const result = regex.exec(url)
  if (!result) {
    return {
      match: false,
      placeholders: [],
    }
  }

  const [, ...placeholders] = result
  return {
    match: true,
    placeholders,
  }
}
