import db from '@adonisjs/lucid/services/db'

export function toRaw(strings: TemplateStringsArray, ...values: any[]) {
  return (bindings?: any) =>
    db.raw(
      strings
        .reduce((acc, str, i) => {
          return acc + str + (values[i] ?? '')
        }, '')
        .trim()
        .replaceAll(/\s+/g, ' '),
      bindings
    )
}

export function raw(strings: TemplateStringsArray, ...values: any[]) {
  return toRaw(strings, ...values)()
}
