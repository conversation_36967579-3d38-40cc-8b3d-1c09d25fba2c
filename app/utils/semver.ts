import semver, { SemVer } from 'semver'

export function compareSemver(a: string, b: string) {
  const standardize = (v: string) => {
    if (v.split('.').length === 2) {
      return `${v}.0`
    }

    return v
  }

  if (semver.lt(standardize(a), standardize(b))) {
    return -1
  }

  if (semver.gt(standardize(a), standardize(b))) {
    return 1
  }

  return 0
}

export function semverComparator(a?: string | null, b?: string | null) {
  if (!a || a === 'unknown' || !semver.valid(a)) {
    return -1
  }

  if (!b || b === 'unknown' || !semver.valid(b)) {
    return 1
  }

  const aVersion = new SemVer(a)
  const bVersion = new SemVer(b)

  return aVersion.compare(bVersion)
}
