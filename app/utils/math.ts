import { sumBy, maxBy as lmaxBy, minBy as lminBy } from 'lodash-es'

export function safeAvg(sum: number, count: number) {
  return safeDivide(sum, count)
}

export function safeDivide(a: number, b: number) {
  if (b === 0) {
    return 0
  }

  return a / b
}

export function avgBy<T>(collection: T[], iteratee: string): number {
  return safeDivide(sumBy(collection, iteratee), collection.length)
}

export function maxBy<T>(collection: T[], iteratee: string) {
  return (lmaxBy(collection, iteratee) as any)?.[iteratee]
}

export function minBy<T>(collection: T[], iteratee: string) {
  return (lminBy(collection, iteratee) as any)?.[iteratee]
}
