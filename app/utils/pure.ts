import pLimit from 'p-limit'

export function enumValues<T extends {}>(Enum: T) {
  // @ts-ignore
  return Object.values(Enum).filter((e): e is InstanceType<T> => Number.isInteger(e))
}

export function update<T>(oldCollection: T[], newCollection: T[], getId: (item: T) => any) {
  const oldMap = new Map(oldCollection.map((item) => [getId(item), item]))
  const newMap = new Map(newCollection.map((item) => [getId(item), item]))

  const toAdd = newCollection.filter((item) => !oldMap.has(getId(item)))
  const toRemove = oldCollection.filter((item) => !newMap.has(getId(item)))
  const toUpdate = newCollection.filter((item) => oldMap.has(getId(item)))

  return { adds: toAdd, removes: toRemove, updates: toUpdate }
}

export function autoFill<T, U>(
  collection: T[],
  iteratee: (item: T) => U,
  generate: (index: number) => T | null
) {
  const iteratorToItem = new Map(collection.map((item) => [iteratee(item), item]))

  const results = []
  let count = 0
  let item = generate(count)

  while (item) {
    const iterator = iteratee(item)

    if (iteratorToItem.has(iterator)) {
      results.push(iteratorToItem.get(iterator)!)
    } else {
      results.push(item)
    }

    count += 1
    item = generate(count)
  }

  return results
}
class EnumHelpers<T extends string, U> {
  keyToValue: Map<T, U>
  valueToKey: Map<U, T>

  constructor(enums: Record<T, U>) {
    this.keyToValue = new Map(Object.keys(enums).map((key) => [key as T, enums[key as T]]))
    this.valueToKey = new Map(Object.keys(enums).map((key) => [enums[key as T], key as T]))
  }

  getValue(key: T) {
    return this.keyToValue.get(key)
  }

  getKey(value: U) {
    return this.valueToKey.get(value)
  }
}

export function createEnum<T extends string, U>(enums: Record<T, U>) {
  return new EnumHelpers<T, U>(enums)
}

export function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

Promise.sequence = async function <T, U>(
  iterable: Array<T> | readonly T[],
  fn: (value: T, index: number) => Promise<U>,
  concurrency = 1
) {
  const sequentially = pLimit(concurrency)
  return await Promise.all(iterable.map((e, index) => sequentially(async () => await fn(e, index))))
}

declare global {
  interface PromiseConstructor {
    sequence: <T, U>(
      iterable: Array<T> | readonly T[],
      fn: (value: T, index: number) => Promise<U>,
      concurrency?: number
    ) => Promise<U[]>
  }
}

export const mergeIf = <T extends Record<string, any>>(
  source: T,
  entries: { value: Record<string, any>; check: boolean }[]
) => {
  return entries.reduce((acc, entry) => {
    if (entry.check) {
      return {
        ...acc,
        ...entry.value,
      }
    }

    return acc
  }, source)
}

export const inRange = (
  value: number,
  {
    upper,
    lower,
    upperNotion = ']',
    lowerNotion = '[',
  }: { upper: number; lower: number; lowerNotion?: '[' | '('; upperNotion?: ']' | ')' }
) => {
  const lowerBound = lowerNotion === '[' ? value >= lower : value > lower
  const upperBound = upperNotion === ']' ? value <= upper : value < upper

  return lowerBound && upperBound
}
