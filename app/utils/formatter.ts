import { DateTime } from 'luxon'

export const formatter = {
  round: (value: string | number, decimals = 2) => {
    if (!value && value !== 0) {
      return undefined
    }

    return (Math.round(Number(value) * 10 ** decimals) / 10 ** decimals).toLocaleString()
  },
  time: (fromUnit: string, toUnit: string) => (value: string | number) => {
    const units = [
      { unit: 'ms', multiplier: 1000 },
      { unit: 's', multiplier: 60 },
      { unit: 'm', multiplier: 60 },
      { unit: 'h', multiplier: 60 },
      { unit: 'd', multiplier: 24 },
    ]

    const fromUnitIndex = units.findIndex((e) => e.unit === fromUnit)
    const toUnitIndex = units.findIndex((e) => e.unit === toUnit)
    const exponent = fromUnitIndex < toUnitIndex ? -1 : 1

    const toValue = units
      .slice(fromUnitIndex, toUnitIndex)
      .reduce((acc, e) => acc * e.multiplier ** exponent, Number(value))

    if (toUnitIndex === 0) {
      return {
        [toUnit]: toValue,
      }
    }

    return {
      [toUnit]: Math.floor(toValue),
      [units[toUnitIndex - 1].unit]: Math.floor(
        (toValue - Math.floor(toValue)) * units[toUnitIndex - 1].multiplier
      ),
    }
  },
  percentage: (value: string | number, decimals: number = 2) => {
    return `${formatter.round(Number(value) * 100, decimals)}%`
  },
  day: (value: any) => {
    return DateTime.fromISO(value as any).weekdayLong?.slice(0, 3)
  },
  note: (value: any, maxLength: number = 100) => {
    if (!value) {
      return ''
    }

    if (value.length <= maxLength) {
      return value
    }

    return `${value.substring(0, maxLength)}...`
  },
}
