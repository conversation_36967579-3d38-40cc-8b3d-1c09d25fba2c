import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { promisify } from 'node:util'
import crypto from 'node:crypto'

export const readdir = promisify(fs.readdir)
export const readFile = promisify(fs.readFile)
export const writeFile = promisify(fs.writeFile)
export const stat = promisify(fs.stat)
export const mkdir = promisify(fs.mkdir)

export const join = (url: string, ...paths: string[]) =>
  path.join(path.dirname(fileURLToPath(url)), ...paths)

export const md5 = (data: string) => crypto.createHash('md5').update(data).digest('hex')
