import { LucidRow } from '@adonisjs/lucid/types/model'
import { DateTime } from 'luxon'

export function adMetricGroupingMatcher<TMetric extends LucidRow, TAdMetric extends LucidRow>(
  metric: TMetric,
  adMetric: TAdMetric,
  groupBy: string[] | null | undefined
) {
  if (groupBy?.length) {
    return groupBy.every((f) => {
      const adMetricAttributeValue = adMetric[f as keyof typeof adMetric]
      const metricAttributeValue = metric[f as keyof typeof metric]

      if (adMetricAttributeValue instanceof DateTime) {
        return adMetricAttributeValue.toFormat('yyyy-MM-dd') === metricAttributeValue
      }

      return (adMetricAttributeValue as any) === metricAttributeValue
    })
  }
}
