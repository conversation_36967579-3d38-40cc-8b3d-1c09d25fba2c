import { ErrorTrackingService } from '@mirai-game-studio/adonis-sdk/error_tracking'
import type { JobHandlerContract, Job } from '@mirai-game-studio/adonis-sdk/types/queue'
import { ConfigMapRegistry } from '@munkit/main'
import { inject } from '@adonisjs/core'

import { ConfigMapScope, GameReleaseStatus, GameStore } from '#config/enums'
import { GooglePlayPublisherConfigMap } from '#configmaps/publishing/game_publish'
import GameReleaseApproval from '#models/game_release_approval'
import GameReleaseProposal, { AndroidExtra } from '#models/game_release_proposal'
import GooglePlayPublishService from '#services/game_release/google_play_publish_service'
import { ConfigMapReloader } from '#configmaps/reloader'

export type ReleaseGameJobPayload = {
  approvalId: GameReleaseProposal['id']
}

@inject()
export default class implements JobHandlerContract<ReleaseGameJobPayload> {
  #approval!: GameReleaseApproval

  constructor(
    public job: Job<ReleaseGameJobPayload>,
    private configMapRegistry: ConfigMapRegistry,
    private googlePlayPublishService: GooglePlayPublishService,
    private errorTrackingService: ErrorTrackingService,
    private configMapReloader: ConfigMapReloader
  ) {
    this.job = job
  }

  async handle(payload: ReleaseGameJobPayload) {
    await this.configMapReloader.perform(ConfigMapScope.Publishing)

    try {
      await this.#onJob(payload)
    } catch (err) {
      await this.#onError()
      throw err
    }
  }

  async failed(error: Error) {
    await this.errorTrackingService.notify(error)
  }

  async #onError() {
    this.#approval.releaseStatus = GameReleaseStatus.Error
    await this.#approval.save()
  }

  async #onJob(payload: ReleaseGameJobPayload) {
    const approval = await GameReleaseApproval.findOrFail(payload.approvalId)
    this.#approval = approval
    await approval.load('proposal')

    const publisherConfigMapCollection = this.configMapRegistry.get('cmap.pl.publisher')
    const gamePublishConfigMap = this.configMapRegistry
      .get('cmap.pl.game')
      .tryGet(approval.proposal.repository)

    if (!gamePublishConfigMap) {
      await this.job.log(`Config not found for ${approval.proposal.repository}`)
      return
    }

    const publisherConfigMap = publisherConfigMapCollection.get(approval.publisherId)
    const gamePublisherConfigMap = gamePublishConfigMap.publishers.get(approval.publisherId)

    approval.releaseStatus = GameReleaseStatus.InProgress
    await approval.save()

    const getPublishStoreConfigMap = async () => {
      switch (publisherConfigMap.store) {
        case GameStore.GooglePlay:
        default: {
          await this.job.log('Google Play Publisher')
          return new GooglePlayPublisherConfigMap(gamePublisherConfigMap.properties)
        }
      }
    }

    const publishStoreConfigMap = await getPublishStoreConfigMap()

    switch (publishStoreConfigMap.type) {
      case GameStore.GooglePlay:
      default: {
        const platformExtra = approval.proposal.extra as AndroidExtra
        await this.googlePlayPublishService.release(
          publishStoreConfigMap,
          {
            installableDownloadUrl: approval.proposal.installableDownloadUrl,
            traceDownloadUrl: platformExtra.symbolsDownloadUrl,
            version: approval.proposal.semver,
            changelog: approval.proposal.changelog,
            versionCode: platformExtra.versionCode,
          },
          this.job
        )
      }
    }

    approval.releaseStatus = GameReleaseStatus.Completed
    await approval.save()
  }
}

declare module '@mirai-game-studio/adonis-sdk/types/queue' {
  export interface JobList {
    '#jobs/release_game_job': ReleaseGameJobPayload
  }
}
