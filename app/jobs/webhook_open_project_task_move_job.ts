import { inject } from '@adonisjs/core'
import { ErrorTrackingService } from '@mirai-game-studio/adonis-sdk/error_tracking'
import type { JobHandlerContract, Job } from '@mirai-game-studio/adonis-sdk/types/queue'
import { ConfigMapRegistry } from '@munkit/main'
import pLimit from 'p-limit'

import { ConfigMapScope } from '#config/enums'
import { GithubPullRequestState, GithubWebhookActionType } from '#configmaps/github/github_webhook'
import { ConfigMapReloader } from '#configmaps/reloader'
import { GithubWebhookEvent } from '#services/github/webhooks/index'
import OpenProjectService from '#services/open_project_service'

export type WebhookOpenProjectTaskMoveJobPayload = {
  event: GithubWebhookEvent
  actionId: string
  webhookId: string
}

const limit = pLimit(5)

@inject()
export default class implements JobHandlerContract<WebhookOpenProjectTaskMoveJobPayload> {
  constructor(
    public job: Job,
    private configMapRegistry: ConfigMapRegistry,
    private openProjectService: OpenProjectService,
    private errorTrackingService: ErrorTrackingService,
    private configMapReloader: ConfigMapReloader
  ) {
    this.job = job
  }

  async handle(payload: WebhookOpenProjectTaskMoveJobPayload) {
    await this.configMapReloader.perform(ConfigMapScope.GitHub)

    const { event, actionId, webhookId } = payload

    const webhookConfigMap = this.configMapRegistry.get('cmap.gh.webhook').get(webhookId)
    const repository = event.repository.name
    const repositoryConfigMap = webhookConfigMap.repositories.get(repository)
    const repositoryAction =
      repositoryConfigMap.actions.tryGet(actionId) ?? webhookConfigMap.actions.get(actionId)
    const actionValue = repositoryAction.value

    if (actionValue.type !== GithubWebhookActionType.OpenProjectTask) {
      await this.job.log('action type not match')
      return
    }

    const pullRequestState = event.pull_request.merged
      ? GithubPullRequestState.Merged
      : event.pull_request.state

    const pullRequestStateToTaskStatus = actionValue.pullRequestStateToTaskStatus
    const nextStatus = pullRequestStateToTaskStatus[pullRequestState]

    if (!nextStatus) {
      await this.job.log('status not match')
      return
    }

    const workPackageIds = this.openProjectService.getWorkPackageIdsFromIntegration(
      event.pull_request.body
    )

    await Promise.all(
      workPackageIds.map((workPackageId) =>
        limit(async () => {
          const workPackage = await this.openProjectService.getWorkPackage(workPackageId)
          const workPackageType = workPackage['_embedded'].type.name.toLowerCase()
          if (workPackageType !== actionValue.group.toLowerCase()) {
            await this.job.log(
              `Work package type not match, expect: ${actionValue.group}, got: ${workPackageType}`
            )
            return
          }

          await this.openProjectService.updateWorkPackageStatus(workPackage, nextStatus)

          const fromStatus = workPackage['_embedded'].status.name
          await this.job.log(
            `Updated work package, id: ${workPackage.id}, from status: ${fromStatus}, to status: ${nextStatus}, webhook: ${webhookId}, action: ${actionId}`
          )
        })
      )
    )

    return workPackageIds
  }

  async failed(error: Error) {
    await this.errorTrackingService.notify(error)
  }
}

declare module '@mirai-game-studio/adonis-sdk/types/queue' {
  export interface JobList {
    '#jobs/webhook_open_project_task_move_job': WebhookOpenProjectTaskMoveJobPayload
  }
}
