import { inject } from '@adonisjs/core'
import { Logger } from '@adonisjs/core/logger'
import { Database } from '@adonisjs/lucid/database'
import type { JobHandlerContract, Job } from '@mirai-game-studio/adonis-sdk/types/queue'
import { ConfigMapRegistry } from '@munkit/main'
import { last } from 'lodash-es'
import { DateTime } from 'luxon'

import SnapshotCursor from '#models/snapshot_cursor'
import { ModelReflector } from '#services/model_reflector'

export type SnapshotDataJobPayload = {
  force: boolean
  filters: Filter[]
}

export type Filter = {
  selector: string
  scope: string
  args: string[]
}

@inject()
export default class implements JobHandlerContract<SnapshotDataJobPayload> {
  constructor(
    public job: Job<SnapshotDataJobPayload>,
    private configMapRegsitry: ConfigMapRegistry,
    private database: Database,
    private logger: Logger,
    private modelReflector: ModelReflector
  ) {
    this.job = job
  }

  /**
   * Base Entry point
   */
  async handle({ force, filters }: SnapshotDataJobPayload) {
    const tableSnapshotSettingConfigMapCollection = this.configMapRegsitry.get(
      'cmap.general.tablesnapshotsetting'
    )

    // this service is only used in this job, if it is used in other places, the initialization logic should be moved to app_provider
    await this.modelReflector.initAsync()

    await Promise.sequence(
      tableSnapshotSettingConfigMapCollection,
      async ({ cursorColumnName, name, columns, connection, keys }) => {
        try {
          this.logger.info(`Snapshot connection=${connection} table=${name}`)
          let cursor = await SnapshotCursor.query()
            .where('table', name)
            .where('connection', connection)
            .first()

          const cols = columns.map((c) => c.name)
          if (!force && cursor && cursor.finishedAt >= DateTime.now().minus({ hours: 12 })) {
            this.logger.info('Skip snapshot because last snapshot is within time threshold')
            return
          }

          cursor ||= new SnapshotCursor().merge({ table: name, connection, current: null })
          let done = false

          while (!done) {
            const records = await this.database
              .connection(connection)
              .query()
              .select(cols)
              .from(name)
              .where((q) => {
                if (cursor?.current) {
                  q.where(cursorColumnName, '<', cursor.current)
                }
              })
              .where((q) => {
                const Model = this.modelReflector.getModel(name) as any

                filters
                  .filter((f) => f.selector === name || f.selector === 'all')
                  .forEach(({ scope, args }) => {
                    if (!Model[scope]) {
                      this.logger.info(`Skip filter ${scope}`)
                      return
                    }

                    this.logger.info(`Apply filter ${scope}`)
                    Model[scope](q, ...args)
                  })
              })
              .orderBy(cursorColumnName, 'desc')
              .limit(200)

            if (records.length === 0) {
              cursor.merge({ finishedAt: DateTime.now(), current: null })
              done = true
              await cursor.save()
              break
            }

            const snapshotConnection = this.database.connection(`${connection}Snapshot`)
            const snapshotTransaction = await snapshotConnection.transaction()

            const keyColumnNames = keys.map((k) => k.columnName)

            try {
              await snapshotTransaction
                .insertQuery()
                .knexQuery.insert(records)
                .into(name)
                .onConflict(keyColumnNames)
                .merge()

              await snapshotTransaction
                .query()
                .from(name)
                .whereNotIn(
                  keyColumnNames,
                  records.map((r) => keyColumnNames.map((k) => r[k]))
                )
                .where((q) => {
                  if (cursor.current) {
                    q.where(cursorColumnName, '<', cursor.current)
                  }
                })
                .where(cursorColumnName, '>=', last(records)[cursorColumnName])
                .delete()

              await snapshotTransaction.commit()
            } catch (err) {
              this.logger.error(err)
              await snapshotTransaction.rollback()
              break
            }

            cursor.merge({ current: last(records)[cursorColumnName] })
            await cursor.save()
          }
        } catch (err) {
          this.logger.error(err)
        }
      }
    )
  }

  /**
   * This is an optional method that gets called if it exists when the retries has exceeded and is marked failed.
   */
  async failed() {}
}

declare module '@mirai-game-studio/adonis-sdk/types/queue' {
  export interface JobList {
    '#jobs/snapshot_data_job': SnapshotDataJobPayload
  }
}
