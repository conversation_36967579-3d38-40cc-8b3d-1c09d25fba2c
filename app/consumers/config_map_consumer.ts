import { inject } from '@adonisjs/core'
import { Logger } from '@adonisjs/core/logger'
import { Application } from '@adonisjs/core/app'
import { ContainerBindings } from '@adonisjs/core/types'
import { BaseConsumer } from '@mirai-game-studio/adonis-sdk/pubsub'

import { ConfigMapScope, NotificationCode } from '#config/enums'
import { ConfigMapReloader } from '#configmaps/reloader'

export type ConfigMapConsumerData = {
  scopes: ConfigMapScope[]
  requestId: string
}

@inject()
export class ConfigMapConsumer extends BaseConsumer<ConfigMapConsumerData> {
  static pattern = 'configmap:update'

  constructor(
    protected logger: Logger,
    private app: Application<ContainerBindings>,
    private configMapReloader: ConfigMapReloader
  ) {
    super(logger)
  }

  override async handle(
    messageOrData: string | ConfigMapConsumerData,
    _dataOrOptional?: ConfigMapConsumerData
  ) {
    const data = messageOrData as ConfigMapConsumerData
    this.logger.info('Update config maps: %s', data.scopes.join(', '))
    for (const scope of data.scopes) {
      try {
        await this.configMapReloader.perform(scope)
      } catch (error) {
        this.logger.error('Cannot update config map %s due to error %s', scope, error.message)
      }
    }
    this.logger.info('Config maps updated: %s', data.scopes.join(', '))

    const transmit = await this.app.container.make('transmit')
    const redis = await this.app.container.make('redis')

    const updateCount = await redis.incr(`configmap:update:${data.requestId}`)
    await redis.expire(`configmap:update:${data.requestId}`, 120)
    const [, subCount = 0] = await redis.pubsub('NUMSUB', ConfigMapConsumer.pattern)

    if (updateCount === subCount) {
      transmit.broadcast('global/notifications', [
        {
          message: 'New config map available, please refresh the page',
          type: 'info',
          code: NotificationCode['configMap.updated'],
        },
      ])
    }
  }
}
