import { HttpContext } from '@adonisjs/core/http'

declare module '@adonisjs/core/http' {
  interface HttpContext {
    react: {
      render: (component: string, options?: any) => void
    }
  }
}

HttpContext.getter('react', function (this: HttpContext) {
  return {
    render: (component: string, options: any = {}) =>
      this.view.render('pages/react', {
        component,
        ...options,
        title: options.title ?? 'Mirai Studio Toolkit',
      }),
  }
})
