import { Response } from '@adonisjs/core/http'

declare module '@adonisjs/core/http' {
  interface Response {
    success<TData, TMeta = {}>(data: TData, meta?: TMeta): void
    error(...messages: string[]): void
  }
}

Response.macro('success', function <
  TData extends unknown,
  TMeta extends unknown = {},
>(this: Response, data: TData, meta?: TMeta) {
  this.ok({ data, meta: meta || {} })
})

Response.macro('error', function (this: Response, ...messages: string[]) {
  this.badRequest({ errors: messages.map((m) => ({ message: m })) })
})
