import { Vine } from '@vinejs/vine'

import { createViewPresetSchema } from '#validators/view_preset'
import { createGroupSchema } from '#validators/group'

declare module '@vinejs/vine' {
  interface Vine {
    viewPreset(): ReturnType<typeof createViewPresetSchema>
    group(...args: Parameters<typeof createGroupSchema>): ReturnType<typeof createGroupSchema>
  }
}

Vine.macro('viewPreset', () => {
  return createViewPresetSchema()
})

Vine.macro('group', (attributes: string[]) => {
  return createGroupSchema(attributes)
})
