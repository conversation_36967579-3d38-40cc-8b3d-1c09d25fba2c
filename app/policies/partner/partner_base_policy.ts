import { inject } from '@adonisjs/core'

import { Tool } from '#config/enums'
import User from '#models/user'
import BasePolicy from '#policies/base_policy'

@inject()
export class PartnerBasePolicy extends BasePolicy {
  async before(user: User): Promise<boolean | undefined> {
    const isManager = this.manage(user, Tool.Dashboard)

    if (isManager) {
      return true
    }

    if (!this.partner(user) && !isManager) {
      return false
    }
  }
}
