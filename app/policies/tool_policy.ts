import { AuthorizerResponse } from '@adonisjs/bouncer/types'
import { inject } from '@adonisjs/core'

import { Tool } from '#config/enums'
import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'

import BasePolicy from './base_policy.js'

@inject()
export default class ToolPolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  view(user: User, tool: Tool): AuthorizerResponse {
    return this.toolPermissionService.canView(tool)(user)
  }

  manage(user: User, tool: Tool): AuthorizerResponse {
    return this.toolPermissionService.canManage(tool)(user)
  }
}
