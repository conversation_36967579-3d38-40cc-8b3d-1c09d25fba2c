import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'

import BasePolicy from './base_policy.js'

@inject()
export default class AclPolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  show(user: User) {
    return this.toolPermissionService.canManage(Tool.Dashboard)(user)
  }

  update(user: User) {
    return this.toolPermissionService.canManage(Tool.Dashboard)(user)
  }
}
