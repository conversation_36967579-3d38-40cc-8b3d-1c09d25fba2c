import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import BasePolicy from '#policies/base_policy'

@inject()
export default class ReleaseProposalPolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  index(user: User) {
    return this.toolPermissionService.canManage(Tool.GitHub)(user)
  }
}
