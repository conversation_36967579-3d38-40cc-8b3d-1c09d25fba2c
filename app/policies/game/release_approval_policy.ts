import { inject } from '@adonisjs/core'

import User from '#models/user'
import { Tool } from '#config/enums'
import ToolPermissionService from '#services/tool_permission_service'
import BasePolicy from '#policies/base_policy'

@inject()
export default class ReleaseApprovalPolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  store(user: User) {
    return this.toolPermissionService.canManage(Tool.GitHub)(user)
  }

  index(user: User) {
    return this.toolPermissionService.canManage(Tool.GitHub)(user)
  }

  update(user: User) {
    return this.toolPermissionService.canManage(Tool.GitHub)(user)
  }
}
