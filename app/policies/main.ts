export const policies = {
  'metabase.chart': () => import('#policies/dashboard/metabase_chart_policy'),
  'dash.game.release_metric': () => import('#policies/dashboard/game/release_metric_policy'),
  'GameQueryPolicy': () => import('#policies/query/game_query_policy'),
  'ToolPermissionPolicy': () => import('#policies/tool_permission_policy'),
  'SessionPolicy': () => import('#policies/session_policy'),
  'dash.monet.admob': () => import('#policies/dashboard/monet/admob_policy'),
  'dash.budget_request': () => import('#policies/dashboard/budget_request_policy'),
  'workflow': () => import('#policies/workflow_policy'),
  'dash.game_studio_metric': () => import('#policies/dashboard/game_studio_metric_policy'),
  'dash.game.campaign_metric': () => import('#policies/dashboard/game/campaign_metric_policy'),
  'dash.game.creative_metric': () => import('#policies/dashboard/game/game_creative_metric_policy'),
  'dash.game.fb_experiment': () => import('#policies/dashboard/game/firebase_experiment_policy'),
  'dash.game.chart': () => import('#policies/dashboard/game/chart_policy'),
  'partner.game': () => import('#policies/partner/game_policy'),
  'partner.game_metric': () => import('#policies/partner/game_metric_policy'),
  'dash.game.route': () => import('#policies/dashboard/game/route_policy'),
  'dash.route': () => import('#policies/dashboard/route_policy'),
  'ChangelogPolicy': () => import('#policies/changelog_policy'),
  'dash.notification': () => import('#policies/dashboard/notification_policy'),
  'dash.game.product_metric': () => import('#policies/dashboard/game/product_metric_policy'),
  'cmap': () => import('#policies/config_map_policy'),
  'dash.game.lv_drop': () => import('#policies/dashboard/game/game_level_drop_policy'),
  'dash.game.review': () => import('#policies/dashboard/game/game_review_policy'),
  'dash.game.perf_setting': () =>
    import('#policies/dashboard/game/game_performance_setting_policy'),
  'dash.game.cost': () => import('#policies/dashboard/game/game_spend_policy'),
  'dash.game.metric': () => import('#policies/dashboard/game/game_metric_policy'),
  'dash.game.metric_v2': () => import('#policies/dashboard/game/game_metric_v2_policy'),
  'dash.game.revenue': () => import('#policies/dashboard/game/game_revenue_policy'),

  'DashboardTeamRevenuePolicy': () => import('#policies/dashboard/team_revenue_policy'),
  'TeamPolicy': () => import('#policies/team_policy'),
  'LandingPageDocumentPolicy': () => import('#policies/document/document_policy'),
  'UserPolicy': () => import('#policies/user_policy'),
  'DashboardRevenueReportPolicy': () => import('#policies/dashboard/revenue_report_policy'),
  'DashboardGameRevenuePolicy': () => import('#policies/dashboard/game_revenue_policy'),
  'AclPolicy': () => import('#policies/acl_policy'),
  'DashboardRolePolicy': () => import('#policies/dashboard/role_policy'),
  'DashboardGamePolicy': () => import('#policies/dashboard/game_policy'),
  'ToolPolicy': () => import('#policies/tool_policy'),
  'GameReleaseApprovalPolicy': () => import('#policies/game/release_approval_policy'),
  'GameReleaseProposalPolicy': () => import('#policies/game/release_proposal_policy'),
  'GitHubAclPolicy': () => import('#policies/github/acl_policy'),
  'GitHubProtectedBranchPolicy': () => import('#policies/github/protected_branch_policy'),
  'GitHubStatusPolicy': () => import('#policies/github/status_policy'),
  'GitHubTeamPolicy': () => import('#policies/github/team_policy'),
}

export type Policies = typeof policies
