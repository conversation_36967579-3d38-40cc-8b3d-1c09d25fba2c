import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import { Tool } from '#config/enums'
import Team from '#models/team'
import User from '#models/user'

import DashboardBasePolicy from './dashboard/dashboard_base_policy.js'

export default class TeamPolicy extends DashboardBasePolicy {
  async index(user: User) {
    await user.load('teamLead')
    return <PERSON><PERSON><PERSON>(user.teamLead)
  }

  async show(user: User, team: Team) {
    return user.id === team.leaderId
  }

  async update(user: User) {
    return this.edit(user)
  }

  async edit(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async create(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async store(user: User) {
    return this.create(user)
  }

  async destroy(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async scope(user: User, query: ModelQueryBuilderContract<typeof Team, Team>) {
    await user.load('teamLead')

    const isManager = await this.manage(user, Tool.Dashboard)

    if (isManager) {
      // query all
    } else if (user.teamLead) {
      query.where('id', user.teamLead.id)
    }

    return true
  }
}
