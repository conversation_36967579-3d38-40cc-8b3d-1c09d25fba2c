import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import { BasePolicy } from '@adonisjs/bouncer'
import { inject } from '@adonisjs/core'

import Game from '#models/game'
import User from '#models/user'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import { GamesWhere } from '#graphql/main'

@inject()
export default class GameQueryPolicy extends BasePolicy {
  constructor(private toolPermissionService: ToolPermissionService) {
    super()
  }

  async scope(user: User, query: ModelQueryBuilderContract<typeof Game, Game>, where?: GamesWhere) {
    if (this.toolPermissionService.canManage(Tool.Dashboard)(user)) {
      if (where?.userIds && where.userIds.length > 0) {
        const gameStoreIds = await DashboardRoleMembership.query()
          .withScopes((s) => s.withUsers())
          .whereIn('user', User.query().whereIn('id', where?.userIds).select('email'))
          .distinct('storeId')
          .then((results) => results.map((r) => r.storeId))

        query.whereIn('storeId', gameStoreIds)
      }
    }

    if (!this.toolPermissionService.canManage(Tool.Dashboard)(user)) {
      await user.load('teamLead')
      const storeIds = await DashboardRoleMembership.query()
        .withScopes((s) => s.withUsers())
        .where((mq) => {
          if (user.teamLead) {
            mq.orWhereIn(
              'user',
              User.query().select('email').where('teamId', user.teamLead.id).orWhere('id', user.id)
            )
          } else {
            mq.where('user', user.email)
          }
        })
        .then((ms) => ms.map((m) => m.storeId))

      query.whereIn('storeId', storeIds)
    }

    return true
  }
}
