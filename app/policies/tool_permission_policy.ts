import { inject } from '@adonisjs/core'

import BasePolicy from '#policies/base_policy'
import User, { ToolPermission } from '#models/user'

@inject()
export default class ToolPermissionPolicy extends BasePolicy {
  async store(user: User, tool: ToolPermission) {
    return this.toolPermissionService.canManage(tool.tool)(user)
  }

  async update(user: User, tool: ToolPermission) {
    return this.toolPermissionService.canManage(tool.tool)(user)
  }
}
