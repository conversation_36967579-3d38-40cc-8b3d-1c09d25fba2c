import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

import User from '#models/user'
import { Tool } from '#config/enums'
import ToolPermissionService from '#services/tool_permission_service'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class DashboardGameRevenuePolicy extends DashboardBasePolicy {
  constructor(
    private context: HttpContext,
    protected toolPermissionService: ToolPermissionService
  ) {
    super(toolPermissionService)
  }

  async index(user: User) {
    return (
      this.toolPermissionService.canView(Tool.Dashboard)(user) &&
      (await this.context.bouncer
        .with('dash.route')
        .allows('access', 'dashboard.game_revenues.index'))
    )
  }
}
