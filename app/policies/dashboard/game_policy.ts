import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import Game from '#models/game'
import { UserService } from '#services/user_service'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class GamePolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService
  ) {
    super(toolPermissionService)
  }

  index(user: User) {
    return this.toolPermissionService.canView(Tool.Dashboard)(user)
  }

  async update(user: User) {
    return this.inhouse(user) && this.toolPermissionService.canManage(Tool.Bot)(user)
  }

  show(user: User, game: Game) {
    return this.userService.isInProject(user, game)
  }
}
