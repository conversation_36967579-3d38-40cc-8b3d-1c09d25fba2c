import { inject } from '@adonisjs/core'
import { ConfigMapRegistry } from '@munkit/main'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import ACL from '#models/acl'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class RoutePolicy extends DashboardBasePolicy {
  constructor(
    private configMapRegistry: ConfigMapRegistry,
    protected toolPermissionService: ToolPermissionService
  ) {
    super(toolPermissionService)
  }

  async access(user: User, route: string) {
    const settingConfigMap = this.configMapRegistry.get('cmap.dash.setting').sole()
    const authorizedRoute = settingConfigMap.aclAccessibleRoutes.tryGet(route)

    if (!authorizedRoute) {
      return true
    }

    await user.load('team')

    if (!user.team) {
      return false
    }

    const authorized = await ACL.query()
      .withScopes((s) => s.route())
      .where('route', route)
      .groupBy('roleId')
      .select('roleId')
      .where('roleId', user.team.roleId)
      .count(ACL.columnName('roleId'))
      .first()
      .then((r) => (r?.$extras?.['count'] || 0) > 0)

    return authorized
  }
}
