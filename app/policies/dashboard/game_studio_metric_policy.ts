import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class GameStudioMetricPolicy extends DashboardBasePolicy {
  constructor(
    private httpContext: HttpContext,
    protected toolPermissionService: ToolPermissionService
  ) {
    super(toolPermissionService)
  }

  async index(_user: User) {
    return this.httpContext.bouncer
      .with('dash.route')
      .allows('access', 'dashboard.game_studio_metrics.index')
  }
}
