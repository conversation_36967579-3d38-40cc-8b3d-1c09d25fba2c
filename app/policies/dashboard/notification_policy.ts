import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import { Tool } from '#config/enums'
import DashboardNotification from '#models/dashboard_notification'
import User from '#models/user'
import BasePolicy from '#policies/base_policy'

export default class NotificationPolicy extends BasePolicy {
  async store(user: User) {
    return this.inhouse(user) && this.toolPermissionService.canManage(Tool.Dashboard)(user)
  }

  async update(user: User) {
    return this.inhouse(user) && this.toolPermissionService.canManage(Tool.Dashboard)(user)
  }

  async index(user: User) {
    return this.inhouse(user)
  }

  async scope(user: User, query: ModelQueryBuilderContract<typeof DashboardNotification>) {
    if (!this.toolPermissionService.canManage(Tool.Dashboard)(user)) {
      query.where('isVisible', true)
    }

    return true
  }
}
