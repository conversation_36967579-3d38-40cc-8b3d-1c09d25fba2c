import { inject } from '@adonisjs/core'

import Game from '#models/game'
import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'
import DashboardBasePolicy from '../dashboard_base_policy.js'
import { DataSource } from '#graphql/main'
import { Tool } from '#config/enums'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class GameMetricV2Policy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async before(user: User) {
    if (this.manage(user, Tool.Dashboard)) {
      return true
    }
  }

  async index(user: User, game: Game, source: DataSource = DataSource.Origin) {
    if (this.partner(user)) {
      return source === DataSource.Snapshot
    }

    return (
      (await this.userService.isInProject(user, game)) &&
      this.gameRoutePolicy.access(user, game, 'v2.dashboard.games.metrics.index')
    )
  }
}
