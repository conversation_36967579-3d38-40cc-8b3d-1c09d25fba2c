import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

import User from '#models/user'
import { Tool } from '#config/enums'
import ToolPermissionService from '#services/tool_permission_service'
import Game from '#models/game'
import DashboardBasePolicy from '../dashboard_base_policy.js'

@inject()
export default class CampaignMetricPolicy extends DashboardBasePolicy {
  constructor(
    private context: HttpContext,
    protected toolPermissionService: ToolPermissionService
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      this.toolPermissionService.canView(Tool.Dashboard)(user) &&
      (await this.context.bouncer
        .with('dash.game.route')
        .allows('access', game, 'dashboard.games.campaign_metrics.index'))
    )
  }
}
