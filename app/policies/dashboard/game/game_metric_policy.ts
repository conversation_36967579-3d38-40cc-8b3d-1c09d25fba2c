import { inject } from '@adonisjs/core'
import { CherryPick } from '@adonisjs/lucid/types/model'

import { AclModelName } from '#config/enums'
import Game from '#models/game'
import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'
import DashboardBasePolicy from '../dashboard_base_policy.js'
import { AttributeService } from '#services/attribute_service'
import { DataSource } from '#graphql/main'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class GameMetricPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private attributeService: AttributeService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      (await this.userService.isInProject(user, game)) &&
      this.gameRoutePolicy.access(user, game, 'dashboard.games.metrics.index')
    )
  }

  update(user: User, game: Game) {
    return this.index(user, game)
  }

  async cherryPick(user: User, game: Game): Promise<CherryPick> {
    const attributes = await this.attributeService.getAttributes(
      AclModelName.GameMetric,
      user,
      game
    )

    const allowedAttributes = attributes.filter((a) => a.permission !== 'none').map((a) => a.name)

    return {
      fields: ['id', 'date', 'storeId', ...allowedAttributes],
      relations: {
        metadata: {
          fields: ['id', ...allowedAttributes],
        },
      },
    }
  }

  async cherryPickV2(
    user: User,
    game: Game,
    dataSource: DataSource = DataSource.Origin
  ): Promise<CherryPick> {
    const baseColumns = ['id', 'isAggregation', 'date', 'gameId']

    if (dataSource === DataSource.Snapshot) {
      return {
        fields: [
          ...baseColumns,
          'totalInstalls',
          'cost',
          'roas',
          'revenue',
          'profit',
          'retentionRateDay1',
          'retentionRateDay3',
          'retentionRateDay7',
          'playtime',
          'sessions',
        ],
      }
    }
    const attributes = await this.attributeService.getAttributes(
      AclModelName.GameMetricV2,
      user,
      game
    )

    const allowedAttributes = attributes.filter((a) => a.permission !== 'none').map((a) => a.name)

    return {
      fields: [...baseColumns, ...allowedAttributes],
    }
  }
}
