import { inject } from '@adonisjs/core'

import User from '#models/user'
import Game from '#models/game'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'
import DashboardBasePolicy from '../dashboard_base_policy.js'

@inject()
export default class GamePerformanceSettingPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService
  ) {
    super(toolPermissionService)
  }

  async update(user: User, game: Game) {
    return this.userService.isInProject(user, game)
  }
}
