import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'
import Game from '#models/game'
import DashboardBasePolicy from '../dashboard_base_policy.js'

@inject()
export default class GameReviewPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private context: HttpContext
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      (await this.userService.isInProject(user, game)) &&
      (await this.context.bouncer
        .with('dash.game.route')
        .allows('access', game, 'dashboard.games.reviews.index'))
    )
  }

  async update(user: User, game: Game) {
    return this.index(user, game)
  }
}
