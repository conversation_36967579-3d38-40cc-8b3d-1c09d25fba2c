import { inject } from '@adonisjs/core'
import { Cherry<PERSON>ick } from '@adonisjs/lucid/types/model'

import DashboardBasePolicy from '../dashboard_base_policy.js'
import ToolPermissionService from '#services/tool_permission_service'
import User from '#models/user'
import Game from '#models/game'
import { UserService } from '#services/user_service'
import { AttributeService } from '#services/attribute_service'
import { AclModelName } from '#config/enums'
import { canRead } from '#utils/acl'
import { DataSource } from '#graphql/main'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class GameRevenuePolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private attributeService: AttributeService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      (await this.userService.isInProject(user, game)) &&
      this.gameRoutePolicy.access(user, game, 'dashboard.games.revenues.index')
    )
  }

  async update(user: User, game: Game) {
    return this.index(user, game)
  }

  async cherryPick(
    user: User,
    game: Game,
    source: DataSource = DataSource.Origin
  ): Promise<CherryPick> {
    if (source === DataSource.Snapshot) {
      return { fields: ['adType', 'impressionCount', 'dailyActiveUserCount'] }
    }

    const attributes = await this.attributeService.getAttributes(
      AclModelName.GameRevenue,
      user,
      game
    )

    return {
      fields: [
        'adType',
        'impressionCount',
        'dailyActiveUserCount',
        ...attributes.filter((a) => canRead(a.permission)).map((a) => a.name),
      ],
    }
  }
}
