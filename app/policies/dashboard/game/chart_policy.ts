import { inject } from '@adonisjs/core'
import { ConfigMapRegistry } from '@munkit/main'

import Game from '#models/game'
import User from '#models/user'
import DashboardBasePolicy from '../dashboard_base_policy.js'
import ToolPermissionService from '#services/tool_permission_service'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class ChartPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private gameRoutePolicy: GameRoutePolicy,
    private configMapRegistry: ConfigMapRegistry
  ) {
    super(toolPermissionService)
  }

  public async show(user: User, game: Game, chartId: string) {
    await user.load('team')
    const dashboardSettingConfigMap = this.configMapRegistry.get('cmap.dash.setting').sole()
    const acl = dashboardSettingConfigMap.gameOverviewChartAcls.tryGet(user.team?.roleId)
    if (!acl) {
      return false
    }

    if (!acl.charts.tryGet(chartId)) {
      return false
    }

    return await this.gameRoutePolicy.access(user, game, 'dashboard.games.overview.show')
  }
}
