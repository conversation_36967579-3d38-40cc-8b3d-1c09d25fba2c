import { inject } from '@adonisjs/core'

import DashboardBasePolicy from '../dashboard_base_policy.js'
import { UserService } from '#services/user_service'
import ToolPermissionService from '#services/tool_permission_service'
import User from '#models/user'
import Game from '#models/game'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class GameLevelDropPolicy extends DashboardBasePolicy {
  constructor(
    private userService: UserService,
    protected toolPermissionService: ToolPermissionService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      (await this.userService.isInProject(user, game)) &&
      this.gameRoutePolicy.access(user, game, 'dashboard.games.level_drops.index')
    )
  }
}
