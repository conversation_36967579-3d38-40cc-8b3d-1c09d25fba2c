import { inject } from '@adonisjs/core'

import Game from '#models/game'
import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'
import DashboardBasePolicy from '../dashboard_base_policy.js'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class ProductMetricPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      (await this.userService.isInProject(user, game)) &&
      this.gameRoutePolicy.access(user, game, 'dashboard.games.product_metrics.index')
    )
  }
}
