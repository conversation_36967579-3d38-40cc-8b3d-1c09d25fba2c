import { inject } from '@adonisjs/core'
import { ConfigMapRegistry } from '@munkit/main'

import User from '#models/user'
import DashboardBasePolicy from '../dashboard_base_policy.js'
import ToolPermissionService from '#services/tool_permission_service'
import Game from '#models/game'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import ACL from '#models/acl'
import Team from '#models/team'

@inject()
export default class GameRoutePolicy extends DashboardBasePolicy {
  constructor(
    private configMapRegistry: ConfigMapRegistry,
    protected toolPermissionService: ToolPermissionService
  ) {
    super(toolPermissionService)
  }

  async access(user: User, game: Game, route: string) {
    const settingConfigMap = this.configMapRegistry.get('cmap.dash.setting').sole()
    const authorizedRoute = settingConfigMap.aclAccessibleRoutes.tryGet(route)

    if (!authorizedRoute) {
      return true
    }

    const membership = await DashboardRoleMembership.query()
      .withScopes((s) => s.withUsers())
      .where('storeId', game.storeId)
      .whereIn(
        'roleId',
        ACL.query()
          .withScopes((s) => s.route())
          .where('route', route)
          .select('roleId')
      )
      .where((q) => {
        q.where('user', user.email).orWhereIn(
          'user',
          User.query()
            .select('email')
            .whereIn('teamId', Team.query().select('id').where('leaderId', user.id))
        )
      })
      .select('id')

    return membership.length > 0
  }
}
