import { inject } from '@adonisjs/core'

import Game from '#models/game'
import User from '#models/user'
import { AdNetworkInputType } from '#config/enums'
import ToolPermissionService from '#services/tool_permission_service'
import AdNetwork from '#models/ad_agency'
import DashboardBasePolicy from '#policies/dashboard/dashboard_base_policy'
import { UserService } from '#services/user_service'

import GameRoutePolicy from './route_policy.js'

@inject()
export default class GameCostPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async update(user: User, game: Game, networkId: number) {
    const adNetwork = await AdNetwork.find(networkId)
    return (
      (await this.index(user, game)) &&
      !!adNetwork &&
      adNetwork.inputType === AdNetworkInputType.Manual
    )
  }

  async index(user: User, game: Game) {
    return (
      (await this.userService.isInProject(user, game)) &&
      this.gameRoutePolicy.access(user, game, 'dashboard.games.costs.index')
    )
  }
}
