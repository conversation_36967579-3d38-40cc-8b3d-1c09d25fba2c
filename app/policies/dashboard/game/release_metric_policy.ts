import { inject } from '@adonisjs/core'

import Game from '#models/game'
import User from '#models/user'
import DashboardBasePolicy from '#policies/dashboard/dashboard_base_policy'
import GameRoutePolicy from '#policies/dashboard/game/route_policy'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'

@inject()
export default class ReleaseMetricPolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private userService: UserService,
    private gameRoutePolicy: GameRoutePolicy
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    const isInProject = await this.userService.isInProject(user, game)
    const isAuthorized = await this.gameRoutePolicy.access(
      user,
      game,
      'dashboard.games.release_metrics.index'
    )
    return isInProject && isAuthorized
  }
}
