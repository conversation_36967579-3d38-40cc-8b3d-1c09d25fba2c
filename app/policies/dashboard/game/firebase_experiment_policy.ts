import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

import DashboardBasePolicy from '../dashboard_base_policy.js'
import ToolPermissionService from '#services/tool_permission_service'
import User from '#models/user'
import Game from '#models/game'
import { Tool } from '#config/enums'

@inject()
export default class FirebaseExperimentPolicy extends DashboardBasePolicy {
  constructor(
    private context: HttpContext,
    protected toolPermissionService: ToolPermissionService
  ) {
    super(toolPermissionService)
  }

  async index(user: User, game: Game) {
    return (
      this.toolPermissionService.canView(Tool.Dashboard)(user) &&
      (await this.context.bouncer
        .with('dash.game.route')
        .allows('access', game, 'dashboard.games.firebase_experiments.index'))
    )
  }
}
