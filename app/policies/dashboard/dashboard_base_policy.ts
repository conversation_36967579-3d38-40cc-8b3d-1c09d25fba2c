import { inject } from '@adonisjs/core'

import { Tool } from '#config/enums'
import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import BasePolicy from '#policies/base_policy'

@inject()
export default class DashboardBasePolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  async before(user: User) {
    return super.withBefore(user, async () => {
      if (this.toolPermissionService.canManage(Tool.Dashboard)(user)) {
        return true
      }
    })
  }
}
