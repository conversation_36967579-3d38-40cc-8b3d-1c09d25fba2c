import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class TeamRevenuePolicy extends DashboardBasePolicy {
  constructor(
    protected toolPermissionService: ToolPermissionService,
    private context: HttpContext
  ) {
    super(toolPermissionService)
  }

  async index(user: User) {
    await user.load('teamLead')
    return (
      <PERSON><PERSON>an(user.teamLead) &&
      (await this.context.bouncer
        .with('dash.route')
        .allows('access', 'dashboard.game_revenues.index'))
    )
  }
}
