import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class RolePolicy extends DashboardBasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  index(user: User) {
    return this.toolPermissionService.canManage(Tool.Dashboard)(user)
  }

  update(user: User) {
    return this.index(user)
  }
}
