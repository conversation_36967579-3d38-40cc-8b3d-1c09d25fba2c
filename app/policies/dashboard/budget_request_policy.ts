import { inject } from '@adonisjs/core'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import User from '#models/user'
import BudgetRequest from '#models/budget_request'
import { Tool } from '#config/enums'
import Workflow from '#models/workflow'

import DashboardBasePolicy from './dashboard_base_policy.js'

@inject()
export default class BudgetRequestPolicy extends DashboardBasePolicy {
  async before(user: User) {
    if (!this.inhouse(user)) {
      return false
    }
  }

  async index(_user: User) {
    return true
  }

  async store(user: User, workflow: Workflow) {
    if (this.manage(user, Tool.Dashboard)) {
      return true
    }

    await user.load('team')
    return <PERSON><PERSON><PERSON>(user.team && user.team.roleId === workflow.roleId)
  }

  async update(user: User, request: BudgetRequest) {
    return this.#ownerUpdatable(user, request) || request.step.assigneeId === user.id
  }

  async updateState(user: User, request: BudgetRequest) {
    return request.step.assigneeId === user.id
  }

  async delete(user: User, request: BudgetRequest) {
    return this.manage(user, Tool.Dashboard) || this.#ownerUpdatable(user, request)
  }

  async scope(
    _user: User,
    _query: ModelQueryBuilderContract<typeof BudgetRequest, BudgetRequest>
  ) {}

  #ownerUpdatable(user: User, request: BudgetRequest) {
    return request.createdById === user.id && request.stepId === 0
  }
}
