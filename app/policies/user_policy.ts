import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import { Tool } from '#config/enums'
import User from '#models/user'
import BasePolicy from '#policies/base_policy'

export default class UserPolicy extends BasePolicy {
  async index(user: User) {
    return this.view(user, Tool.Dashboard)
  }

  async update(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async destroy(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async simulate(user: User, resource: User) {
    return resource.toolPermissions.every((tool) => {
      return this.manage(user, tool.tool)
    })
  }

  async store(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async note(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async team(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async inchargedGames(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async scope(user: User, query: ModelQueryBuilderContract<typeof User, User>) {
    const isManager = this.manage(user, Tool.Dashboard)

    if (isManager) {
      // query all
    } else if (user.teamId) {
      query.where('team_id', user.teamId)
    } else {
      query.where('id', user.id)
    }

    return true
  }
}
