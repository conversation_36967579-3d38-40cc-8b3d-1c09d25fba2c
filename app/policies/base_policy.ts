import { BasePolicy as BaseBouncerPolicy } from '@adonisjs/bouncer'
import { AuthorizerResponse } from '@adonisjs/bouncer/types'
import { inject } from '@adonisjs/core'
import { isNil } from 'lodash-es'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import { UserKind } from '#graphql/main'

@inject()
export default class BasePolicy extends BaseBouncerPolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super()
  }
  async before(user: User): Promise<boolean | undefined> {
    return this.#before(user)
  }

  async #before(user: User): Promise<boolean | undefined> {
    if (!this.inhouse(user)) {
      return false
    }

    if (this.toolPermissionService.canManage(Tool.All)(user)) {
      return true
    }
  }

  protected async withBefore(
    user: User,
    authorize: (user: User) => Promise<boolean | undefined>
  ): Promise<boolean | undefined> {
    const authorized = await this.#before(user)
    if (!isNil(authorized)) {
      return authorized
    }

    return authorize(user)
  }

  protected view(user: User, tool: Tool): AuthorizerResponse {
    return this.toolPermissionService.canView(tool)(user)
  }

  protected manage(user: User, tool: Tool): AuthorizerResponse {
    return this.toolPermissionService.canManage(tool)(user)
  }

  protected inhouse(user: User) {
    return user.kind === UserKind.Inhouse
  }

  protected partner(user: User) {
    return user.kind === UserKind.Partner
  }
}

export function bindRoute(policyClass: any, route: string) {
  Reflect.defineMetadata('policy:route', route, policyClass)
}

type Action<TPolicy> = keyof TPolicy

export function getRoute<T>(policyInstance: T, action: Action<T>) {
  return (
    Reflect.getMetadata('policy:route', (policyInstance as any).constructor) + (action as string)
  )
}
