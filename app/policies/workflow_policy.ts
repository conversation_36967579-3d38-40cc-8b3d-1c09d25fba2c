import { inject } from '@adonisjs/core'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

import { Tool } from '#config/enums'
import User from '#models/user'
import Workflow from '#models/workflow'

import DashboardBasePolicy from './dashboard/dashboard_base_policy.js'

@inject()
export default class WorkflowPolicy extends DashboardBasePolicy {
  async index(_user: User) {
    return true
  }

  async update(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async store(user: User) {
    return this.manage(user, Tool.Dashboard)
  }

  async scope(user: User, query: ModelQueryBuilderContract<typeof Workflow, Workflow>) {
    if (!this.manage(user, Tool.Dashboard)) {
      await user.load('team')
      query.where('roleId', user.team.roleId)
    }

    return true
  }
}
