import { inject } from '@adonisjs/core'

import User from '#models/user'
import ToolPermissionService from '#services/tool_permission_service'
import { Tool } from '#config/enums'
import BasePolicy from '#policies/base_policy'

@inject()
export default class DocumentPolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  async before(user: User) {
    return super.withBefore(user, async () => {
      if (this.toolPermissionService.canManage(Tool.LandingPage)(user)) {
        return true
      }
    })
  }

  async edit(user: User) {
    return this.index(user)
  }

  async update(user: User) {
    return this.edit(user)
  }

  async index(_user: User) {
    return false
  }
}
