import { inject } from '@adonisjs/core'

import { ConfigMapScope, Tool } from '#config/enums'
import User from '#models/user'

import BasePolicy from './base_policy.js'

@inject()
export default class ConfigMapPolicy extends BasePolicy {
  async before(user: User) {
    if (this.toolPermissionService.canManage(Tool.All)(user)) {
      return true
    }
  }

  async show(user: User) {
    return Object.values(Tool).some((tool) => this.toolPermissionService.canView(tool)(user))
  }

  async index(user: User) {
    return Object.values(Tool).some((tool) => this.toolPermissionService.canView(tool)(user))
  }

  async update(user: User, scope: ConfigMapScope) {
    let tool = Tool.All

    switch (scope) {
      case ConfigMapScope.Dashboard:
        tool = Tool.Dashboard
        break

      case ConfigMapScope.GitHub:
      case ConfigMapScope.General:
      case ConfigMapScope.Publishing:
        tool = Tool.GitHub
        break

      default:
        break
    }

    return this.toolPermissionService.canManage(tool)(user)
  }
}
