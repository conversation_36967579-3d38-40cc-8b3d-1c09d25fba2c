import { inject } from '@adonisjs/core'

import { Tool } from '#config/enums'
import User from '#models/user'
import BasePolicy from '#policies/base_policy'
import ToolPermissionService from '#services/tool_permission_service'

@inject()
export default class ProtectedBranchPolicy extends BasePolicy {
  constructor(protected toolPermissionService: ToolPermissionService) {
    super(toolPermissionService)
  }

  index(user: User) {
    return this.toolPermissionService.canView(Tool.GitHub)(user)
  }

  update(user: User) {
    return this.toolPermissionService.canManage(Tool.GitHub)(user)
  }
}
