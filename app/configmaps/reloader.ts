import { inject } from '@adonisjs/core'
import { ConfigMapManager } from '@mirai-game-studio/adonis-sdk/config_map'

@inject()
export class ConfigMapReloader {
  constructor(private readonly configMapManager: ConfigMapManager) {}

  async perform(scope: string, driver?: 'redis' | 'local') {
    const googleDriveDataSource = await this.configMapManager.getDataSource('gdrive')
    const destDataSource = await this.configMapManager.getDataSource(driver)

    const nameToCsv = await googleDriveDataSource.getMany(scope)
    await destDataSource.setMany(scope, nameToCsv)

    await this.configMapManager.boot(scope)
  }
}
