import { ConfigMapCollection, Metadata, schema, ConfigMapRecord } from '@munkit/main'
import { array, string } from 'yup'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.dash.membership': ConfigMapCollection<DashboardProjectMembershipConfigMap>
  }
}

export class DashboardProjectRoleConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', {
    name: 'Role Id',
    schema: schema.string.required.resolve(),
  })
  declare roleId: string

  @Metadata('configmap.attribute', {
    name: 'Members',
    schema: ({ value }) => {
      return array().of(string().required().trim()).compact().cast(value.split('\n'))
    },
  })
  declare memberEmails: string[]
}

@Metadata('configmap.bind', { id: 'cmap.dash.membership', file: 'dashboard:ProjectMembership' })
export default class DashboardProjectMembershipConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Game Store Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare storeId: string

  @Metadata('configmap.collection', DashboardProjectRoleConfigMap)
  roles = new ConfigMapCollection(DashboardProjectRoleConfigMap)
}
