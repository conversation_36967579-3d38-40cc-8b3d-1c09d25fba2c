import { ConfigMapCollection, ConfigMapRecord, schema, Metadata } from '@munkit/main'
import { boolean } from 'yup'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.archive': ConfigMapCollection<GithubArchiveConfigMap>
  }
}

@Metadata('configmap.bind', {
  file: 'github:Archive',
  id: 'cmap.gh.archive',
})
export default class GithubArchiveConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', { name: 'Repository', schema: schema.string.required.resolve() })
  @Metadata('configmap.key')
  declare name: string

  @Metadata('configmap.attribute', { name: 'Archived', schema: schema.boolean.required.resolve() })
  declare archived: boolean

  @Metadata('configmap.attribute', {
    name: 'Deleted',
    schema: ({ value }) => (value ? boolean().cast(value) : false),
  })
  declare deleted: boolean
}
