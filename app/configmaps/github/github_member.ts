import { ConfigMapCollection, ConfigMapRecord, schema, Metadata } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.member': ConfigMapCollection<GithubMemberConfigMap>
  }
}

@Metadata('configmap.bind', {
  file: 'github:Member',
  id: 'cmap.gh.member',
})
export default class GithubMemberConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', { name: 'Username', schema: schema.string.required.resolve() })
  declare username: string

  @Metadata('configmap.attribute', { name: 'Full Name', schema: schema.string.required.resolve() })
  @Metadata('configmap.key')
  declare fullName: string

  merge(attributes: Partial<GithubMemberConfigMap>) {
    Object.assign(this, attributes)
    return this
  }
}
