import { ConfigMapCollection, ConfigMapRecord, schema, Metadata } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.acl': ConfigMapCollection<GithubAccessControlConfigMap>
  }
}

export enum GithubPermission {
  Minimum = 'Minimum',
  Read = 'Read',
  Write = 'Write',
  Maintain = 'Maintain',
  Admin = 'Admin',
}

export class GithubAclTeamConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', { name: 'Team', schema: schema.string.required.resolve() })
  declare name: string

  @Metadata('configmap.attribute', {
    name: 'Team Default Permission',
    schema: schema.enum(GithubPermission).required.resolve(),
  })
  declare defaultPermission: GithubPermission
}

export class GithubAclRepositoryOutsideCollaboratorConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', {
    name: 'Repository Outside Collaborator',
    schema: schema.string.required.resolve(),
  })
  declare fullName: string

  @Metadata('configmap.attribute', {
    name: 'Repository Outside Collaborator Permission',
    schema: schema.enum(GithubPermission).required.resolve(),
  })
  declare permission: GithubPermission
}

export class GithubAclRepositoryTeamConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', {
    name: 'Repository Team',
    schema: schema.string.required.resolve(),
  })
  declare name: string

  @Metadata('configmap.attribute', {
    name: 'Repository Team Permission Override',
    schema: schema.enum(GithubPermission).required.resolve(),
  })
  declare permission: GithubPermission
}

export class GithubAclRepositoryConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', { name: 'Repository', schema: schema.string.required.resolve() })
  declare name: string

  @Metadata('configmap.collection', GithubAclRepositoryOutsideCollaboratorConfigMap)
  outsideCollaborators = new ConfigMapCollection(GithubAclRepositoryOutsideCollaboratorConfigMap)

  @Metadata('configmap.collection', GithubAclRepositoryTeamConfigMap)
  teams = new ConfigMapCollection(GithubAclRepositoryTeamConfigMap)
}

@Metadata('configmap.bind', {
  file: 'github:AccessControl',
  id: 'cmap.gh.acl',
})
export default class GithubAccessControlConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', { name: 'Id', schema: schema.string.required.resolve() })
  declare id: string

  @Metadata('configmap.collection', GithubAclTeamConfigMap)
  teams = new ConfigMapCollection(GithubAclTeamConfigMap)

  @Metadata('configmap.collection', GithubAclRepositoryConfigMap)
  repositories = new ConfigMapCollection(GithubAclRepositoryConfigMap)
}
