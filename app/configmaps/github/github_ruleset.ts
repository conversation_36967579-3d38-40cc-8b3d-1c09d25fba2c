import { column, computed } from '@adonisjs/lucid/orm'
import { ConfigMapCollection, schema, Metadata } from '@munkit/main'
import { boolean, number } from 'yup'

import { ConfigMapRecord } from '#configmaps/record'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.ruleset': ConfigMapCollection<GithubRulesetConfigMap>
  }
}

export class GithubMergeStrategyConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Available Merge Strategy',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare name: 'SQUASH' | 'MERGE' | 'REBASE'
}

export class GithubRulesetEnvironmentConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Environment',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare name: string
}

export class GithubRulesetRuleConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Rule',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare rule: 'APPROVAL_REQUIRED' | 'APPROVAL_DISMISS' | 'CODE_OWNERS' | 'ADMIN_BYPASS'

  @Metadata('configmap.attribute', {
    name: 'Rule Value',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare value: string

  @computed()
  get parsedValue() {
    switch (this.rule) {
      case 'APPROVAL_REQUIRED': {
        return number().integer().required().cast(this.value)
      }

      case 'ADMIN_BYPASS':
      case 'APPROVAL_DISMISS':
      case 'CODE_OWNERS': {
        return boolean().required().cast(this.value)
      }

      default: {
        return this.value
      }
    }
  }
}

export class GithubRulesetBranchConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Branch',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare name: string

  @Metadata('configmap.collection', GithubRulesetRuleConfigMap)
  @column()
  rules = new ConfigMapCollection(GithubRulesetRuleConfigMap)
}

@Metadata('configmap.bind', {
  file: 'github:Ruleset',
  id: 'cmap.gh.ruleset',
})
export default class GithubRulesetConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', { name: 'Ruleset Id', schema: schema.string.required.resolve() })
  @Metadata('configmap.key')
  @column()
  declare id: string

  @Metadata('configmap.attribute', {
    name: 'Default Branch',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare defaultBranch: string

  @Metadata('configmap.collection', GithubMergeStrategyConfigMap)
  @column()
  mergeStrategies = new ConfigMapCollection(GithubMergeStrategyConfigMap)

  @Metadata('configmap.collection', GithubRulesetEnvironmentConfigMap)
  @column()
  environments = new ConfigMapCollection(GithubRulesetEnvironmentConfigMap)

  @Metadata('configmap.collection', GithubRulesetBranchConfigMap)
  @column()
  branches = new ConfigMapCollection(GithubRulesetBranchConfigMap)
}
