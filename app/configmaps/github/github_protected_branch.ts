import { ConfigMapCollection, schema, Metadata } from '@munkit/main'
import { column } from '@adonisjs/lucid/orm'

import { ConfigMapRecord } from '#configmaps/record'

import GithubRulesetConfigMap from './github_ruleset.js'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.pbranch': ConfigMapCollection<GithubProtectedBranchConfigMap>
  }
}

export class GithubInlineRulesetConfigMap extends GithubRulesetConfigMap {
  @Metadata('configmap.key')
  @column()
  id = 'sole'
}

@Metadata('configmap.bind', {
  file: 'github:ProtectedBranch',
  id: 'cmap.gh.pbranch',
})
export default class GithubProtectedBranchConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', { name: 'Repository', schema: schema.string.required.resolve() })
  @Metadata('configmap.key')
  @column()
  declare repository: string

  @Metadata('configmap.attribute', { name: 'Ruleset Id', schema: schema.string.resolve() })
  @column()
  declare rulesetId?: string

  @Metadata('configmap.collection', GithubInlineRulesetConfigMap)
  @column()
  inlineRulesets = new ConfigMapCollection(GithubInlineRulesetConfigMap)

  getRuleset(rulesetConfigMapCollection: ConfigMapCollection<GithubRulesetConfigMap>) {
    if (!this.rulesetId) {
      return this.inlineRulesets.sole()
    }

    return rulesetConfigMapCollection.get(this.rulesetId)
  }
}
