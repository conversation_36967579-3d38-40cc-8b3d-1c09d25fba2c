import { Metadata, ConfigMapRecord, schema, ConfigMapCollection } from '@munkit/main'
import { fromPairs } from 'lodash-es'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.webhook': ConfigMapCollection<GithubWebhookConfigMap>
  }
}

export enum GithubWebhookActionType {
  DiscordNotify = 'DISCORD_NOTIFY',
  OpenProjectTask = 'OP_TASK_MOVE',
}

export enum GithubPullRequestState {
  Merged = 'merged',
  Closed = 'closed',
  Open = 'open',
}

export class GithubWebhookActionValueConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Action Value',
    schema: schema.string.required.resolve(),
  })
  declare value: string
}

export class GithubWebhookActionConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Action Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare id: string

  @Metadata('configmap.attribute', {
    name: 'Action Type',
    schema: schema.enum(GithubWebhookActionType).required.resolve(),
  })
  declare type: GithubWebhookActionType

  @Metadata('configmap.collection', GithubWebhookActionValueConfigMap)
  values = new ConfigMapCollection(GithubWebhookActionValueConfigMap)

  #value:
    | GithubWebhookActionDiscordNotifyValue
    | GithubWebhookActionOpenProjectTaskValue
    | undefined

  get value() {
    if (!this.#value) {
      switch (this.type) {
        case GithubWebhookActionType.DiscordNotify:
          this.#value = new GithubWebhookActionDiscordNotifyValue(this.values)
          break

        default:
          this.#value = new GithubWebhookActionOpenProjectTaskValue(this.values)
          break
      }
    }

    return this.#value
  }
}

export class GithubWebhookRepositoryActionValueOverrideConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Repository Action Value Override',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare value: string
}

export class GithubWebhookActionDiscordNotifyValue {
  constructor(
    private valueConfigMapCollection: ConfigMapCollection<GithubWebhookActionValueConfigMap>
  ) {}

  type: GithubWebhookActionType.DiscordNotify = GithubWebhookActionType.DiscordNotify

  get channelId(): string {
    return this.valueConfigMapCollection[0].value
  }
}

export class GithubWebhookActionOpenProjectTaskValue {
  #pullRequestStateToTaskStatus: Record<string, string> | undefined

  constructor(
    private valueConfigMapCollection: ConfigMapCollection<GithubWebhookActionValueConfigMap>
  ) {}

  type: GithubWebhookActionType.OpenProjectTask = GithubWebhookActionType.OpenProjectTask

  get group(): string {
    return this.valueConfigMapCollection[0]?.value
  }

  get pullRequestStateToTaskStatus(): Record<string, string> {
    if (!this.#pullRequestStateToTaskStatus) {
      const values = this.valueConfigMapCollection.filter((_, index) => index > 0)
      this.#pullRequestStateToTaskStatus = fromPairs(values.map((value) => value.value.split(':')))
    }

    return this.#pullRequestStateToTaskStatus
  }
}

export class GithubWebhookRepositoryActionOverrideConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Repository Action Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare id: string

  @Metadata('configmap.attribute', {
    name: 'Repository Action Type Override',
    schema: schema.enum(GithubWebhookActionType).required.resolve(),
  })
  declare type: GithubWebhookActionType

  @Metadata('configmap.collection', GithubWebhookRepositoryActionValueOverrideConfigMap)
  values = new ConfigMapCollection(GithubWebhookRepositoryActionValueOverrideConfigMap)

  #value:
    | GithubWebhookActionDiscordNotifyValue
    | GithubWebhookActionOpenProjectTaskValue
    | undefined

  get value() {
    if (!this.#value) {
      switch (this.type) {
        case GithubWebhookActionType.DiscordNotify:
          this.#value = new GithubWebhookActionDiscordNotifyValue(this.values)
          break

        default:
          this.#value = new GithubWebhookActionOpenProjectTaskValue(this.values)
          break
      }
    }

    return this.#value
  }
}

export class GithubWebhookRepositoryConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Repository',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare name: string

  @Metadata('configmap.collection', GithubWebhookRepositoryActionOverrideConfigMap)
  actions = new ConfigMapCollection(GithubWebhookRepositoryActionOverrideConfigMap)
}

@Metadata('configmap.bind', {
  file: 'github:Webhook',
  id: 'cmap.gh.webhook',
})
export default class GithubWebhookConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare id: string

  @Metadata('configmap.collection', GithubWebhookActionConfigMap)
  actions = new ConfigMapCollection(GithubWebhookActionConfigMap)

  @Metadata('configmap.collection', GithubWebhookRepositoryConfigMap)
  repositories = new ConfigMapCollection(GithubWebhookRepositoryConfigMap)
}
