import { ConfigMapCollection, ConfigMapRecord, schema, Metadata } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.gh.team': ConfigMapCollection<GithubTeamConfigMap>
  }
}

export class GithubTeamMemberConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', { name: 'Member', schema: schema.string.required.resolve() })
  declare fullName: string
}

@Metadata('configmap.bind', {
  file: 'github:Team',
  id: 'cmap.gh.team',
})
export default class GithubTeamConfigMap extends ConfigMapRecord {
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', { name: 'Name', schema: schema.string.required.resolve() })
  declare name: string

  @Metadata('configmap.collection', GithubTeamMemberConfigMap)
  members = new ConfigMapCollection(GithubTeamMemberConfigMap)
}
