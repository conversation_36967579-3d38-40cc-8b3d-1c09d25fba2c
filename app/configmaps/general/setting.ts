import { ConfigMapCollection, ConfigMapRecord, Metadata, schema } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.general.setting': ConfigMapCollection<SettingConfigMap>
  }
}

@Metadata('configmap.bind', {
  file: 'general:Setting',
  id: 'cmap.general.setting',
})
export class SettingConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  id: string = 'sole'

  @Metadata('configmap.attribute', {
    name: 'Change Log Document Id',
    schema: schema.string.required.resolve(),
  })
  declare changeLogDocId: string
}

export default SettingConfigMap
