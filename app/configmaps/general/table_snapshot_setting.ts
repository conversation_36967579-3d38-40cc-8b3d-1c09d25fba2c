import { ConfigMapCollection, ConfigMapRecord, Metadata, schema } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.general.tablesnapshotsetting': ConfigMapCollection<TableSnapshotSettingConfigMap>
  }
}

export class TableSnapshotKeySettingConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Key Column Name',
    schema: schema.string.required.resolve(),
  })
  declare columnName: string
}

export class TableSnapshotColumnSettingConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Column Name',
    schema: schema.string.required.resolve(),
  })
  declare name: string
}

@Metadata('configmap.bind', {
  file: 'general:TableSnapshotSetting',
  id: 'cmap.general.tablesnapshotsetting',
})
export class TableSnapshotSettingConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Name',
    schema: schema.string.required.resolve(),
  })
  declare name: string

  @Metadata('configmap.attribute', {
    name: 'Connection',
    schema: schema.string.required.resolve(),
  })
  declare connection: string

  @Metadata('configmap.attribute', {
    name: 'Cursor Column Name',
    schema: schema.string.required.resolve(),
  })
  declare cursorColumnName: string

  @Metadata('configmap.collection', TableSnapshotKeySettingConfigMap)
  keys = new ConfigMapCollection(TableSnapshotKeySettingConfigMap)

  @Metadata('configmap.collection', TableSnapshotColumnSettingConfigMap)
  columns = new ConfigMapCollection(TableSnapshotColumnSettingConfigMap)
}

export default TableSnapshotSettingConfigMap
