import { ConfigMapCollection, ConfigMapRecord, Metadata, schema } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.document': ConfigMapCollection<DocumentConfigMap>
  }
}

@Metadata('configmap.bind', {
  file: 'general:Document',
  id: 'cmap.document',
})
export class DocumentConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare id: string

  @Metadata('configmap.attribute', {
    name: 'Name',
    schema: schema.string.required.resolve(),
  })
  declare name: string
}

export default DocumentConfigMap
