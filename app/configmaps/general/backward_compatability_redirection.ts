import { ConfigMapCollection, ConfigMapRecord, Metadata, schema } from '@munkit/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.backwardredirect': ConfigMapCollection<BackwardCompatibilityRedirectionConfigMap>
  }
}

@Metadata('configmap.bind', {
  file: 'general:BackwardRedirection',
  id: 'cmap.backwardredirect',
})
export class BackwardCompatibilityRedirectionConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Pattern',
    schema: schema.string.required.resolve(),
  })
  declare pattern: string

  @Metadata('configmap.attribute', {
    name: 'Redirect Pattern',
    schema: schema.string.required.resolve(),
  })
  declare redirectPattern: string
}

export default BackwardCompatibilityRedirectionConfigMap
