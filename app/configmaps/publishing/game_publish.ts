import { ConfigMapCollection, ConfigMapRecord, Metadata, schema } from '@munkit/main'

import { GameStore } from '#config/enums'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.pl.game': ConfigMapCollection<GamePublishConfigMap>
  }
}

export class GamePublisherPropertyConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Property',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare name: string

  @Metadata('configmap.attribute', {
    name: 'Property Value',
    schema: schema.string.required.resolve(),
  })
  declare value: string
}

export class GooglePlayPublisherConfigMap {
  constructor(collection: ConfigMapCollection<GamePublisherPropertyConfigMap>) {
    this.packageName = collection.get('Package Name').value
    this.track = (collection.tryGet('Track')?.value ?? 'Internal') as 'Internal' | 'Closed'
    this.status = (collection.tryGet('Status')?.value ?? 'Draft') as 'Draft'
  }

  type: GameStore = GameStore.GooglePlay

  declare packageName: string
  declare track: 'Internal' | 'Closed'
  declare status: 'Draft'
}

export class GamePublisherConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Publisher',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare id: string

  @Metadata('configmap.collection', GamePublisherPropertyConfigMap)
  properties = new ConfigMapCollection(GamePublisherPropertyConfigMap)
}

@Metadata('configmap.bind', {
  file: 'publishing:GamePublish',
  id: 'cmap.pl.game',
})
export default class GamePublishConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Repository',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare repository: string

  @Metadata('configmap.collection', GamePublisherConfigMap)
  publishers = new ConfigMapCollection(GamePublisherConfigMap)
}
