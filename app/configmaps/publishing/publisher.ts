import { ConfigMapCollection, Metadata, schema } from '@munkit/main'
import { column } from '@adonisjs/lucid/orm'

import { GameStore } from '#config/enums'
import { ConfigMapRecord } from '#configmaps/record'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.pl.publisher': ConfigMapCollection<PublisherConfigMap>
  }
}

@Metadata('configmap.bind', {
  file: 'publishing:Publisher',
  id: 'cmap.pl.publisher',
})
export default class PublisherConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare id: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Store',
    schema: schema.enum(GameStore).required.resolve(),
  })
  declare store: GameStore

  @column()
  @Metadata('configmap.attribute', {
    name: 'Name',
    schema: schema.string.required.resolve(),
  })
  declare name: string
}
