import path from 'node:path'

import { Initializable } from '@munkit/main'
import { inject } from '@adonisjs/core'
import pLimit from 'p-limit'
import { ConfigMapInferrerContract } from '@mirai-game-studio/adonis-sdk/types/config_map'

import { ConfigMapScope } from '#config/enums'
import { join, readdir } from '#utils/node'

import { ConfigMapRecord } from './record.js'

const limit = pLimit(1)

@inject()
export class ConfigMapInferrer implements ConfigMapInferrerContract {
  #scopeToConfigMapList = new Map<string, Initializable<ConfigMapRecord>[]>()

  async onInit() {
    for (const scope of Object.values(ConfigMapScope)) {
      this.#scopeToConfigMapList.set(scope, [])

      const files = await readdir(join(import.meta.url, scope))
      const configMapFiles = files.filter((file) => {
        const { ext } = path.parse(file)
        return ['.ts', '.js'].includes(ext)
      })

      const Classes = await Promise.all(
        configMapFiles.map((file) =>
          limit(async () => {
            return import(`./${scope}/${file}`).then(
              (m) => m.default as Initializable<ConfigMapRecord>
            )
          })
        )
      )

      this.#scopeToConfigMapList.set(scope, Classes)
    }
  }

  getConfigMaps(scope: string): Initializable<ConfigMapRecord>[]
  getConfigMaps(scope: ConfigMapScope): Initializable<ConfigMapRecord>[] {
    return this.#scopeToConfigMapList.get(scope)!
  }
}
