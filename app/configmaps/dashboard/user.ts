import { string } from 'yup'
import { ConfigMapCollection, ConfigMapRecord, Metadata, schema } from '@munkit/main'

import { Permission, Tool } from '#config/enums'
import { UserKind } from '#graphql/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.user': ConfigMapCollection<UserConfigMap>
  }
}

export class ToolPermissionConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Tool',
    schema: schema.enum(Tool).required.resolve(),
  })
  declare name: Tool

  @Metadata('configmap.attribute', {
    name: 'Tool Permission',
    schema: schema.enum(Permission).required.resolve(),
  })
  declare permission: Permission
}

@Metadata('configmap.bind', { id: 'cmap.user', file: 'dashboard:Whitelist' })
export default class UserConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Email',
    schema: ({ value }) => string().email().required().trim().cast(value),
  })
  @Metadata('configmap.key')
  declare email: string

  @Metadata('configmap.attribute', {
    name: 'Full Name',
    schema: schema.string.required.resolve(),
  })
  declare fullName: string

  @Metadata('configmap.attribute', {
    name: 'User Kind',
    schema: schema.enum(UserKind).required.resolve(),
  })
  declare kind: UserKind

  @Metadata('configmap.collection', ToolPermissionConfigMap)
  tools = new ConfigMapCollection(ToolPermissionConfigMap)
}
