import { column } from '@adonisjs/lucid/orm'
import { ConfigMapCollection, Metadata, schema } from '@munkit/main'

import { ConfigMapRecord } from '#configmaps/record'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.dash.setting': ConfigMapCollection<DashboardSettingConfigMap>
  }
}

export class ACLAccessibleRouteConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'ACL Accessible Route Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare id: string

  @Metadata('configmap.attribute', {
    name: 'ACL Accessible Route Name',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare name: string
}

export class GameOverviewChartConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Game Overview Chart Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare chartId: string
}

export class GameOverviewChartAclConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Game Overview Role Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare roleId: string

  @Metadata('configmap.collection', GameOverviewChartConfigMap)
  @column()
  charts = new ConfigMapCollection(GameOverviewChartConfigMap)
}

@Metadata('configmap.bind', { id: 'cmap.dash.setting', file: 'dashboard:Setting' })
export default class DashboardSettingConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare id: string

  @Metadata('configmap.collection', ACLAccessibleRouteConfigMap)
  @column()
  aclAccessibleRoutes = new ConfigMapCollection(ACLAccessibleRouteConfigMap)

  @Metadata('configmap.collection', GameOverviewChartAclConfigMap)
  @column()
  gameOverviewChartAcls = new ConfigMapCollection(GameOverviewChartAclConfigMap)
}
