import { ConfigMapCollection, Metadata, schema } from '@munkit/main'
import { column } from '@adonisjs/lucid/orm'

import { ConfigMapRecord } from '#configmaps/record'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.dash.metabasechart': ConfigMapCollection<DashboardMetabaseChartConfigMap>
  }
}

export class DashboardMetabaseChartParamConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.attribute', {
    name: 'Param Name',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  declare name: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Param Type',
    schema: schema.string.required.resolve(),
  })
  declare type: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Param Default Value',
    schema: schema.string.resolve(),
  })
  declare defaultValue: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Param Value',
    schema: schema.string.resolve(),
  })
  declare value: string
}

@Metadata('configmap.bind', { id: 'cmap.dash.metabasechart', file: 'dashboard:MetabaseChart' })
export default class DashboardMetabaseChartConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  declare id: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'No',
    schema: schema.integer.required.resolve(),
  })
  declare no: number

  @column()
  @Metadata('configmap.attribute', {
    name: 'Feature Flags',
    schema: schema.string.resolve(),
  })
  declare featureFlags: string

  @column()
  @Metadata('configmap.collection', DashboardMetabaseChartParamConfigMap)
  params = new ConfigMapCollection(DashboardMetabaseChartParamConfigMap)
}
