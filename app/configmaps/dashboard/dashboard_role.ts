import { column } from '@adonisjs/lucid/orm'
import { ConfigMapCollection, Metadata, schema } from '@munkit/main'

import { ConfigMapRecord } from '#configmaps/record'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.dash.role': ConfigMapCollection<DashboardRoleConfigMap>
  }
}

@Metadata('configmap.bind', { id: 'cmap.dash.role', file: 'dashboard:Role' })
export default class DashboardRoleConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare id: string

  @Metadata('configmap.attribute', {
    name: 'Name',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare name: string

  @Metadata('configmap.attribute', {
    name: 'Group',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare group: string
}
