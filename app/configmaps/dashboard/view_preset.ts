import { ConfigMapCollection, Metadata, schema } from '@munkit/main'
import { column } from '@adonisjs/lucid/orm'

import { ConfigMapRecord } from '#configmaps/record'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.viewpreset': ConfigMapCollection<ViewPresetConfigMap>
  }
}

export class ViewPresetAttributeConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Attribute',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare name: string

  @Metadata('configmap.attribute', {
    name: 'Cohort',
    schema: schema.boolean.required.resolve(),
  })
  @column()
  declare isCohort: boolean

  @Metadata('configmap.attribute', {
    name: 'Substitute Attribute Template',
    schema: schema.string.resolve(),
  })
  @column()
  declare substituteAttributeTemplate: string

  @Metadata('configmap.attribute', {
    name: 'Cohort Day',
    schema: schema.string.resolve(),
  })
  @column()
  declare cohortDay: string

  @Metadata('configmap.attribute', {
    name: 'Section',
    schema: schema.string.required.resolve(),
  })
  @column()
  declare section: string
}

@Metadata('configmap.bind', { id: 'cmap.viewpreset', file: 'dashboard:ViewPreset' })
export default class ViewPresetConfigMap extends ConfigMapRecord {
  @Metadata('configmap.attribute', {
    name: 'Page Id',
    schema: schema.string.required.resolve(),
  })
  @Metadata('configmap.key')
  @column()
  declare pageId: string

  @Metadata('configmap.collection', ViewPresetAttributeConfigMap)
  @column()
  attributes = new ConfigMapCollection(ViewPresetAttributeConfigMap)
}
