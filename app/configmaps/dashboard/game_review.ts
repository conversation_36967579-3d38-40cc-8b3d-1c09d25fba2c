import { ConfigMapCollection, Metadata, schema } from '@munkit/main'
import { column } from '@adonisjs/lucid/orm'

import { ConfigMapRecord } from '#configmaps/record'
import { GamePerformanceCriteriaConclusion, Operator } from '#graphql/main'

declare module '@munkit/main' {
  interface ConfigMapBindings {
    'cmap.dash.review': ConfigMapCollection<GameReviewConfigMap>
  }
}

export class GamePerformanceCriteriaComparisonConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.attribute', {
    name: 'Criteria Conclusion',
    schema: schema.enum(GamePerformanceCriteriaConclusion).required.resolve(),
  })
  declare conclusion: GamePerformanceCriteriaConclusion

  @column()
  @Metadata('configmap.attribute', {
    name: 'Criteria Operator',
    schema: schema.enum(Operator).required.resolve(),
  })
  declare operator: Operator
}

export class GamePerformanceCriteriaConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', {
    name: 'Criteria Metric',
    schema: schema.string.required.resolve(),
  })
  declare metricId: string

  @column()
  @Metadata('configmap.collection', GamePerformanceCriteriaComparisonConfigMap)
  comparisons = new ConfigMapCollection(GamePerformanceCriteriaComparisonConfigMap)
}

export class GamePerformanceSuggestionConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.attribute', {
    name: 'Suggestion Id',
    schema: schema.string.required.resolve(),
  })
  declare id: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'ROAS',
    schema: schema.enum(GamePerformanceCriteriaConclusion).required.resolve(),
  })
  declare roas: GamePerformanceCriteriaConclusion

  @column()
  @Metadata('configmap.attribute', {
    name: 'Timeplay',
    schema: schema.enum(GamePerformanceCriteriaConclusion).required.resolve(),
  })
  declare playtime: GamePerformanceCriteriaConclusion

  @column()
  @Metadata('configmap.attribute', {
    name: 'RR',
    schema: schema.enum(GamePerformanceCriteriaConclusion).required.resolve(),
  })
  declare retentionRateDay1: GamePerformanceCriteriaConclusion

  @column()
  @Metadata('configmap.attribute', {
    name: 'UA',
    schema: schema.enum(GamePerformanceCriteriaConclusion).required.resolve(),
  })
  declare cpi: GamePerformanceCriteriaConclusion

  @column()
  @Metadata('configmap.attribute', {
    name: 'Monet',
    schema: schema.enum(GamePerformanceCriteriaConclusion).required.resolve(),
  })
  declare monetImpsDau: GamePerformanceCriteriaConclusion

  @column()
  @Metadata('configmap.attribute', {
    name: 'Optimise Time',
    schema: schema.string.resolve(),
  })
  declare optimizeTimeAdvice?: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Conclusion',
    schema: schema.string.resolve(),
  })
  declare conclusion?: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Product',
    schema: schema.string.resolve(),
  })
  declare productTeamAdvice?: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Marketing',
    schema: schema.string.resolve(),
  })
  declare marketingTeamAdvice?: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Large Scale Plan',
    schema: schema.string.resolve(),
  })
  declare largeScalePlan?: string

  @column()
  @Metadata('configmap.attribute', {
    name: 'Small Scale Plan',
    schema: schema.string.resolve(),
  })
  declare smallScalePlan?: string
}

@Metadata('configmap.bind', { file: 'dashboard:GameReview', id: 'cmap.dash.review' })
export default class GameReviewConfigMap extends ConfigMapRecord {
  @column()
  @Metadata('configmap.key')
  @Metadata('configmap.attribute', {
    name: 'Id',
    schema: schema.string.required.resolve(),
  })
  declare id: string

  @column()
  @Metadata('configmap.collection', GamePerformanceCriteriaConfigMap)
  performanceCriterias = new ConfigMapCollection(GamePerformanceCriteriaConfigMap)

  @column()
  @Metadata('configmap.collection', GamePerformanceSuggestionConfigMap)
  performanceSuggestions = new ConfigMapCollection(GamePerformanceSuggestionConfigMap)
}
