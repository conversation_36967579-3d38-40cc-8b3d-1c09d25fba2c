import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

import { Tool } from '#config/enums'

export default class ToolManagerMiddleware {
  async handle({ bouncer, view, logger }: HttpContext, next: NextFn, options: { tool: Tool }) {
    if (await bouncer.with('ToolPolicy').allows('view', options.tool)) {
      return await next()
    }

    logger.debug(`Access denied to tool ${options.tool}`)

    return view.render('pages/errors/forbidden')
  }
}
