import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

import { GameMetricMetadataLoader } from '#dataloaders/game_metric_metadata_loader'
import { AdPerformanceLoader } from '#dataloaders/ad_performance_loader'
import { AdNetworkLoader } from '#dataloaders/ad_network_loader'
import { AdAgencyLoader } from '#dataloaders/ad_agency_loader'
import { GameMetricLoader } from '#dataloaders/game_metric_loader'
import { GameLoader } from '#dataloaders/game_loader'
import { UserLoader } from '#dataloaders/user_loader'
import { WorkflowLoader } from '#dataloaders/workflow_loader'
import { AdGroupLoader } from '#dataloaders/ad_group_loader'
import { CampaignLoader } from '#dataloaders/campaign_loader'
import { FirebaseAdMetricLoader } from '#dataloaders/firebase_ad_metric_loader'
import { TeamLoader } from '#dataloaders/team_data_loader'
import { UserGameMembershipLoader } from '#dataloaders/user_game_membership_loader'
import { ReleaseAdMetricLoader } from '#dataloaders/release_ad_metric_loader'

@inject()
export default class GraphqlContextMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    ctx.graphql.dataloaders = {
      gameMetricMetadata: await ctx.containerResolver.make(GameMetricMetadataLoader),
      adPerformance: await ctx.containerResolver.make(AdPerformanceLoader),
      adNetwork: await ctx.containerResolver.make(AdNetworkLoader),
      adAgency: await ctx.containerResolver.make(AdAgencyLoader),
      gameMetric: await ctx.containerResolver.make(GameMetricLoader),
      game: await ctx.containerResolver.make(GameLoader),
      user: await ctx.containerResolver.make(UserLoader),
      workflow: await ctx.containerResolver.make(WorkflowLoader),
      adGroup: await ctx.containerResolver.make(AdGroupLoader),
      campaign: await ctx.containerResolver.make(CampaignLoader),
      firebaseAdMetric: await ctx.containerResolver.make(FirebaseAdMetricLoader),
      releaseAdMetric: await ctx.containerResolver.make(ReleaseAdMetricLoader),
      team: await ctx.containerResolver.make(TeamLoader),
      userGameMembership: await ctx.containerResolver.make(UserGameMembershipLoader),
    }

    const output = await next()
    return output
  }
}
