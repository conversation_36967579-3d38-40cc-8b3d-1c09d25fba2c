import type { HttpContext } from '@adonisjs/core/http'
import type { Constructor, LazyImport, NextFn } from '@adonisjs/core/types/http'

export default class RouteMetadataMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const handler = ctx.route?.handler

    if (handler && 'reference' in handler) {
      const reference = handler.reference
      if (Array.isArray(reference)) {
        const [getController, action = 'handle'] = reference

        if (typeof getController === 'function') {
          const { default: Controller } = await (
            getController as LazyImport<Constructor<unknown>>
          )()
          const metadataFields = (Reflect.getMetadata('route', Controller) || []) as Array<{
            name: string
            metadata: Metadata
          }>
          const actionMetadata = metadataFields.find((field) => field.name === action)
          ctx.routeMetadata = actionMetadata?.metadata || {}
        }
      }
    }

    ctx.routeMetadata ||= {}

    return await next()
  }
}

type Metadata = {
  'ui.next'?: 'ondemand' | 'always' | 'reverse'
}

declare module '@adonisjs/core/http' {
  export interface RouteMetadata extends Metadata {}

  interface HttpContext {
    routeMetadata: RouteMetadata
  }
}

declare module '@munkit/main' {
  interface MetadataDefinitions {
    route: Metadata
  }
}
