import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import type { Authenticators } from '@adonisjs/auth/types'
import router from '@adonisjs/core/services/router'
import queryString from 'query-string'

/**
 * Auth middleware is used authenticate HTTP requests and deny
 * access to unauthenticated users.
 */
export default class AuthMiddleware {
  redirectTo = router.builder().make('sessions.create')

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    ctx.session.put(
      'redirectTo',
      queryString.stringifyUrl({
        url: ctx.request.url(),
        query: ctx.request.qs(),
      })
    )
    await ctx.session.commit()

    await ctx.auth.authenticateUsing(options.guards, {
      loginRoute: this.redirectTo,
    })
    return next()
  }
}
