import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import config from 'config'

export default class RequestLogMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const start = process.hrtime.bigint()

    if (config.get('logger').pretty) {
      return next()
    }

    const { logger, request, response } = ctx

    const output = await next()

    const end = process.hrtime.bigint()
    const durationMs = Number((end - start) / BigInt(1e6))

    logger.info({
      method: request.method(),
      path: request.url(),
      status: response.response.statusCode,
      duration: `${durationMs}ms`,
      ip: request.ip(),
      userAgent: request.header('user-agent'),
    })

    return output
  }
}
