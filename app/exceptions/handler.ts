import app from '@adonisjs/core/services/app'
import { HttpContext, ExceptionHandler } from '@adonisjs/core/http'
import { errors, inject } from '@adonisjs/core'
import { ErrorTrackingService } from '@mirai-game-studio/adonis-sdk/error_tracking'
import { SentryCaptureContext } from '@mirai-game-studio/adonis-sdk/error_tracking/sentry'

import { NotFoundExceptionAdapter } from './adapters/not_found_adapter.js'
import UnauthorizedException from './unauthorized_exception.js'
import { UnauthorizedAdapter } from './adapters/unauthorized_adapter.js'

@inject()
export default class HttpExceptionHandler extends ExceptionHandler {
  protected debug = !app.inProduction

  constructor(
    private errorTrackingService: ErrorTrackingService,
    private notFoundAdapter: NotFoundExceptionAdapter,
    private unauthorizedAdapter: UnauthorizedAdapter
  ) {
    super()
  }

  async handle(error: unknown, ctx: HttpContext) {
    const adapter = this.getAdapter(error)

    await adapter.handle(error as any, ctx)

    if (adapter.isHandled) {
      return
    }

    return super.handle(error, ctx)
  }

  async report(error: unknown, ctx: HttpContext) {
    return await this.errorTrackingService.notify(
      error as any,
      {
        user: ctx.auth?.user,
        level: 'error',
        tags: {
          revision: process.env['REPO_COMMIT_ID'],
          stage: process.env['STAGE'],
        },
      } as SentryCaptureContext
    )
  }

  getAdapter(error: unknown) {
    if (error instanceof errors.E_ROUTE_NOT_FOUND) {
      return this.notFoundAdapter
    }

    if (error instanceof UnauthorizedException) {
      return this.unauthorizedAdapter
    }

    return {
      handle: async (_ex: unknown, _ctx: HttpContext) => {},
      isHandled: false,
    }
  }
}
