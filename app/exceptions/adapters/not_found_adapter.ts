import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'
import { ConfigMapRegistry } from '@munkit/main'

import { formatString, isUrlMatchPattern } from '#utils/route'

function* map<T, U>(a: Iterable<T>, fn: (x: T) => U) {
  for (let x of a) yield fn(x)
}

function find<T>(a: Iterable<T>, fn: (x: T) => boolean) {
  for (let x of a) if (fn(x)) return x
}

@inject()
export class NotFoundExceptionAdapter {
  isHandled: boolean = false

  constructor(private configMapRegistry: ConfigMapRegistry) {}

  async handle(_exception: Exception, { request, response }: HttpContext) {
    const backwardCompatibilityRedirectionConfigMapCollection =
      this.configMapRegistry.get('cmap.backwardredirect')

    const result = find(
      map(backwardCompatibilityRedirectionConfigMapCollection, (record) => ({
        configMapRecord: record,
        match: isUrlMatchPattern(request.url(), record.pattern),
      })),
      (x) => x.match.match
    )

    if (!result) {
      return
    }

    const { match, configMapRecord } = result

    this.isHandled = true

    response.redirect(formatString(configMapRecord.redirectPattern, ...match.placeholders))
  }
}
