{"extends": "@mirai-game-studio/typescript-config/adonis.app.json", "compilerOptions": {"baseUrl": "./", "rootDir": "./", "outDir": "./build", "paths": {"#controllers/*": ["./app/controllers/*.js"], "#exceptions/*": ["./app/exceptions/*.js"], "#jobs/*": ["./app/jobs/*.js"], "#models/*": ["./app/models/*.js"], "#mails/*": ["./app/mails/*.js"], "#presenters/*": ["app/presenters/*.js"], "#services/*": ["./app/services/*.js"], "#listeners/*": ["./app/listeners/*.js"], "#events/*": ["./app/events/*.js"], "#middleware/*": ["./app/middleware/*.js"], "#validators/*": ["./app/validators/*.js"], "#resolvers/*": ["./app/resolvers/*.js"], "#utils/*": ["./app/utils/*.js"], "#providers/*": ["./providers/*.js"], "#policies/*": ["./app/policies/*.js"], "#abilities/*": ["./app/abilities/*.js"], "#database/*": ["./database/*.js"], "#tests/*": ["./tests/*.js"], "#start/*": ["./start/*.js"], "#config/*": ["./config/*.js"], "#openapi/*": ["./openapi/src/*.ts"], "#configmaps/*": ["./app/configmaps/*.js"], "#fixtures": ["./tests/fixtures/*"], "#types": ["resources/js/next/types/*.js"], "#graphql/*": ["./graphql/*.js"], "#dataloaders/*": ["./app/dataloaders/*.js"], "#consumers/*": ["./app/consumers/*.js"], "@/next/*": ["./resources/js/next/*"]}, "resolveJsonModule": true}, "exclude": ["./lib/sdk", "./resources", "./tmp"]}