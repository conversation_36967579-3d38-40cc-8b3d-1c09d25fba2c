---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/refs/heads/main/charts/other/app-template/values.schema.json

secrets:
  env:
    enabled: true
    stringData:
      NODE_ENV: production
      NODE_CONFIG_ENV: production
      TZ: Asia/Bangkok
      PORT: '3333'
      HOST: '0.0.0.0'
      LOG_LEVEL: debug
      QUEUE_DB: '1'
      SESSION_DRIVER: cookie
      DB_PRIMARY_HOST: toolkit-web-postgresql-rw
      DB_USER: '{{ .Values.db.self.user }}'
      DB_PASSWORD: '{{ .Values.db.self.password }}'
      DB_DATABASE: toolkit_web_production
      DB_DW_PRIMARY_HOST: data-warehouse-postgresql-rw
      DB_DW_SNAPSHOT_PRIMARY_HOST: toolkit-web-snapshot-postgresql-rw
      DWS_DB_USER: '{{ .Values.db.snapshot.user }}'
      DWS_DB_PASSWORD: '{{ .Values.db.snapshot.password }}'
      DWS_DB_DATABASE: data_warehouse_production_snapshot
      REDIS_TYPE: standalone
      REDIS_HOST: toolkit-web-redis
      REDIS_PORT: '6379'
      REDIS_DB: '0'
      REDIS_PASSWORD: '{{ .Values.redis.password }}'
      GOOGLE_CLIENT_ID: '{{ .Values.server.google.clientId }}'
      GOOGLE_CLIENT_SECRET: '{{ .Values.server.google.clientSecret }}'
      OAUTH_GOOGLE_CALLBACK_URL: https://toolkit.miraistudio.games/google/callback
      CONFIG_MAP_GSERVICE_KEY: |
        {{- .Values.server.google.configMapGserviceKey | nindent 2 }}
      ANDROIDPUBLISHER_GSERVICE_KEY: |
        {{- .Values.server.google.androidpublisherGserviceKey | nindent 2 }}
      GITHUB_APPS_CLIENT_SECRET: '{{ .Values.server.github.clientSecret }}'
      GITHUB_APPS_PRIVKEY: |
        {{- .Values.server.github.privateKey | nindent 2 }}
      VAULTKEY_API_KEY: '{{ .Values.server.vaultKey.apiKey }}'
      CDN_URL: https://s3-std.miraistudio.games/toolkit-web-static
      CDN_S3_ENDPOINT: https://s3-std.miraistudio.games
      CDN_S3_BUCKET: toolkit-web-static
  redis-creds:
    enabled: true
    stringData:
      password: '{{ .Values.redis.password }}'
  postgresql-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.self.user }}'
      password: '{{ .Values.db.self.password }}'
  postgresql-superuser-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.self.rootUser }}'
      password: '{{ .Values.db.self.rootPassword }}'
  snapshot-postgresql-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.snapshot.user }}'
      password: '{{ .Values.db.snapshot.password }}'
  snapshot-postgresql-superuser-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.snapshot.rootUser }}'
      password: '{{ .Values.db.snapshot.rootPassword }}'
