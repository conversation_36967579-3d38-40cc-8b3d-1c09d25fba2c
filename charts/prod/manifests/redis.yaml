---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/redis.redis.opstreelabs.in/redis-v1beta2.json

apiVersion: redis.redis.opstreelabs.in/v1beta2
kind: Redis
metadata:
  name: toolkit-web-redis
  namespace: inhouse-prod
spec:
  podSecurityContext:
    runAsUser: 1000
    fsGroup: 1000
  redisExporter:
    enabled: true
    # renovate: datasource=docker depName=quay.io/opstree/redis-exporter
    image: quay.io/opstree/redis-exporter:v1.76.0
    resources:
      requests:
        cpu: 20m
        memory: 32Mi
      limits:
        cpu: 100m
        memory: 128Mi
  kubernetesConfig:
    # renovate: datasource=docker depName=quay.io/opstree/redis
    image: quay.io/opstree/redis:v8.2.1
    imagePullPolicy: IfNotPresent
    redisSecret:
      name: toolkit-web-secret-redis-creds
      key: password
    resources:
      requests:
        cpu: 50m
        memory: 64Mi
      limits:
        cpu: 200m
        memory: 1Gi
  storage:
    volumeClaimTemplate:
      spec:
        accessModes:
          - ReadWriteOnce
        storageClassName: openebs-mayastor-single
        resources:
          requests:
            storage: 1Gi
