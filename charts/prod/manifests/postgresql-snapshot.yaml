---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/postgresql.cnpg.io/cluster-v1.json

apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: toolkit-web-snapshot-postgresql
  namespace: inhouse-prod
spec:
  instances: 1
  imageCatalogRef:
    apiGroup: postgresql.cnpg.io
    kind: ClusterImageCatalog
    name: postgresql
    major: 17
  superuserSecret:
    name: toolkit-web-secret-snapshot-postgresql-superuser-creds
  enableSuperuserAccess: true
  bootstrap:
    initdb:
      database: data_warehouse_production_snapshot
      owner: toolkit_web
      secret:
        name: toolkit-web-secret-snapshot-postgresql-creds
  postgresql:
    parameters:
      wal_keep_size: 96MB
  resources:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      cpu: 500m
      memory: 1Gi
  storage:
    storageClass: openebs-mayastor-single
    size: 10Gi
