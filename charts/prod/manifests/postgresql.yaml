---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/postgresql.cnpg.io/cluster-v1.json

apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: toolkit-web-postgresql
  namespace: inhouse-prod
spec:
  instances: 1
  imageCatalogRef:
    apiGroup: postgresql.cnpg.io
    kind: ClusterImageCatalog
    name: postgresql
    major: 17
  superuserSecret:
    name: toolkit-web-secret-postgresql-superuser-creds
  enableSuperuserAccess: true
  bootstrap:
    initdb:
      database: toolkit_web_production
      owner: toolkit_web
      secret:
        name: toolkit-web-secret-postgresql-creds
  resources:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      memory: 1Gi
  postgresql:
    parameters:
      wal_keep_size: 96MB
  storage:
    storageClass: openebs-mayastor-replicated
    size: 2Gi
