---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/collectors.grafana.com/alloy-v1alpha1.json

apiVersion: collectors.grafana.com/v1alpha1
kind: Alloy
metadata:
  name: toolkit-web
  namespace: data-warehouse
spec:
  alloy:
    configMap:
      content: |-
        discovery.kubernetes "pod" {
          role = "pod"

          selectors {
            role = "pod"
            label = "app.kubernetes.io/name=toolkit-web-api"
          }
        }

        discovery.relabel "pod_logs" {
          targets = discovery.kubernetes.pod.targets

          rule {
            source_labels = ["__meta_kubernetes_namespace"]
            target_label = "namespace"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_name"]
            target_label = "pod"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_container_name"]
            target_label = "container"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
            target_label = "app"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_instance"]
            target_label = "instance"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_pod_index"]
            target_label = "replica"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_controller"]
            target_label = "controller"
          }

          rule {
            source_labels = ["__meta_kubernetes_pod_label_miraistudio_environment"]
            target_label = "env"
          }
        }

        loki.source.kubernetes "pod_logs" {
          targets    = discovery.relabel.pod_logs.output
          forward_to = [loki.process.parse.receiver]
        }

        loki.process "parse" {
          forward_to = [otelcol.receiver.loki.pod_logs.receiver]

          stage.logfmt {
            mapping = {
              level      = "level",
              time       = "time",
              method     = "method",
              request_id = "request_id",
              path       = "path",
              status     = "status",
              msg        = "msg",
              duration   = "duration",
            }
          }

          stage.labels {
            values = {
              level  = "level",
              http_method = "method",
              http_status = "status",
              http_path = "path",
              duration = "duration",
            }
          }

          stage.drop {
            source = "path"
            expression = ".*__transmit.*"
          }

          stage.drop {
            source = "path"
            expression = ".*health.*"
          }

          stage.timestamp {
            source = "time"
            format = "UnixMs"
          }

          stage.template {
            source = "msg"
            template = "{{ "{{" }} .Value | default (ternary .Entry \"nolog\" (eq .status nil)) {{ "}}" }}"
          }

          stage.output {
            source = "msg"
          }
        }

        otelcol.receiver.loki "pod_logs" {
          output {
            logs = [otelcol.processor.attributes.signoz.input]
          }
        }

        otelcol.processor.attributes "signoz" {
          action {
            key = "k8s.namespace.name"
            from_attribute = "namespace"
            action = "upsert"
          }

          action {
            key = "k8s.pod.name"
            from_attribute = "pod"
            action = "upsert"
          }

          action {
            key = "deployment.environment"
            from_attribute = "env"
            action = "upsert"
          }

          output {
            logs = [otelcol.exporter.otlp.otlp_collector.input]
          }
        }

        otelcol.exporter.otlp "otlp_collector" {
          client {
            endpoint = "signoz-otel-collector.monitoring.svc:4317"
            tls {
              insecure = true
            }
          }
        }

    resources:
      requests:
        cpu: 10m
        memory: 50Mi
      limits:
        cpu: 50m
        memory: 200Mi

  serviceMonitor:
    enabled: true
