---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/refs/heads/main/charts/other/app-template/values.schema.json

server:
  image:
    repository: &repository 963933760463.dkr.ecr.us-east-1.amazonaws.com/mirai-studio/toolkit-web
    tag: &tag latest
  secret:
    env: &secretEnv toolkit-web-secret-env
  reloader: &reloader
    reloader.stakater.com/auto: 'true'
  hostname: &hostname toolkit.miraistudio.games
defaultPodOptions:
  imagePullSecrets:
    - name: k8s-ecr-login-renew-docker-secret
controllers:
  main:
    replicas: 3
    annotations: *reloader
    strategy: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
    pod:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app.kubernetes.io/name
                    operator: In
                    values:
                      - toolkit-web-api
                  - key: app.kubernetes.io/controller
                    operator: In
                    values:
                      - main
              topologyKey: kubernetes.io/hostname
              matchLabelKeys:
                - pod-template-hash
    containers:
      main:
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - node
        args:
          - bin/server.js
        envFrom:
          - secret: *secretEnv
        ports:
          - name: http
            containerPort: &port 3333
        probes:
          liveness:
            enabled: true
            port: *port
            path: /health/liveness
          readiness:
            enabled: true
            port: *port
            path: /health/readiness
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            memory: 1Gi
  assets:
    type: job
    job:
      ttlSecondsAfterFinished: 60
      backoffLimit: 3
    annotations:
      argocd.argoproj.io/hook: PreSync
    initContainers:
      prepare:
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - cp
          - -a
          - /app/build/public/assets/.
          - /assets/
        envFrom:
          - secret: *secretEnv
        resources:
          requests:
            cpu: 10m
            memory: 32Mi
          limits:
            cpu: 50m
            memory: 100Mi
    containers:
      main:
        image:
          repository: minio/mc
          tag: latest
          pullPolicy: IfNotPresent
        command:
          - /bin/sh
        args:
          - -c
          - |
            ls -al /assets
            mc alias set cdn $CDN_S3_ENDPOINT $CDN_S3_ACCESS_KEY_ID $CDN_S3_SECRET_ACCESS_KEY
            mc cp -r /assets/* cdn/$CDN_S3_BUCKET
        envFrom:
          - secret: *secretEnv
        resources:
          requests:
            cpu: 10m
            memory: 32Mi
          limits:
            memory: 100Mi
  config:
    type: job
    job:
      ttlSecondsAfterFinished: 60
      backoffLimit: 3
    annotations:
      argocd.argoproj.io/hook: PostSync
    containers:
      main:
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - node
        args:
          - ace
          - sync:gdrive-configmap
          - -d
          - redis
        envFrom:
          - secret: *secretEnv
        env:
          - name: CONFIG_MAP_DRIVER
            value: local
        resources:
          requests:
            cpu: 50m
            memory: 100Mi
          limits:
            memory: 512Mi
  migrate:
    type: job
    annotations:
      argocd.argoproj.io/hook: PostSync
    job:
      ttlSecondsAfterFinished: 60
      backoffLimit: 3
    containers:
      main:
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - node
          - ace
          - migration:run
          - --force
        envFrom:
          - secret: *secretEnv
        env:
          - name: CONFIG_MAP_DRIVER
            value: local
        resources:
          requests:
            cpu: 50m
            memory: 100Mi
          limits:
            memory: 512Mi
      data-warehouse:
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - node
          - ace
          - migration:run
          - --force
          - --connection
          - dataWarehouse
        envFrom:
          - secret: *secretEnv
        env:
          - name: CONFIG_MAP_DRIVER
            value: local
        resources:
          requests:
            cpu: 50m
            memory: 100Mi
          limits:
            memory: 512Mi
      snapshot:
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - node
          - ace
          - migration:run
          - --force
          - --connection
          - dataWarehouseSnapshot
        envFrom:
          - secret: *secretEnv
        env:
          - name: CONFIG_MAP_DRIVER
            value: local
        resources:
          requests:
            cpu: 50m
            memory: 100Mi
          limits:
            memory: 512Mi
  snapshot-d1:
    type: cronjob
    cronjob: &snapshotCronjob
      timeZone: Asia/Bangkok
      schedule: '0 15 * * *'
      backoffLimit: 3
      ttlSecondsAfterFinished: 300
      failedJobsHistory: 1
      successfulJobsHistory: 1
    containers:
      main: &snapshotMain
        image:
          repository: *repository
          tag: *tag
          pullPolicy: IfNotPresent
        command:
          - node
          - ace
          - snapshot:data
          - --filters
          - 'all:date:<=,-1'
        envFrom:
          - secret: *secretEnv
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            memory: 2Gi
  snapshot-d0:
    type: cronjob
    cronjob:
      <<: *snapshotCronjob
      schedule: '0 1 * * *'
    containers:
      main:
        <<: *snapshotMain
        command:
          - node
          - ace
          - snapshot:data
          - --filters
          - 'all:date:<=,-2'
persistence:
  assets:
    enabled: true
    type: emptyDir
    sizeLimit: 100Mi
    advancedMounts:
      assets:
        prepare:
          - path: /assets
            readOnly: false
        main:
          - path: /assets
            readOnly: true
service:
  main:
    controller: main
    primary: true
    ports:
      http:
        enabled: true
        port: *port
        protocol: HTTP
        targetPort: *port

route:
  main:
    hostnames:
      - *hostname
    parentRefs:
      - name: envoy-external
        namespace: envoy-gateway-system
    rules:
      - backendRefs:
          - identifier: main
            kind: Service
            port: *port
        matches:
          - path:
              type: PathPrefix
              value: /
