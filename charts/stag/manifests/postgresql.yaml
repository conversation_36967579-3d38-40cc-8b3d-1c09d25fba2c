---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/postgresql.cnpg.io/cluster-v1.json

apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: toolkit-web-postgresql
  namespace: data-warehouse-staging
spec:
  instances: 2
  imageCatalogRef:
    apiGroup: postgresql.cnpg.io
    kind: ClusterImageCatalog
    name: postgresql
    major: 17
  superuserSecret:
    name: toolkit-web-secret-postgresql-superuser-creds
  bootstrap:
    recovery:
      source: toolkit-web-postgresql
  externalClusters:
    - name: toolkit-web-postgresql
      plugin:
        name: barman-cloud.cloudnative-pg.io
        parameters:
          barmanObjectName: s3-cloudflare
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      memory: 1Gi
  storage:
    storageClass: openebs-mayastor
    size: 1Gi
  affinity:
    nodeSelector:
      node-role.kubernetes.io/openebs: openebs
    tolerations:
      - key: miraistudio/storage
        operator: Equal
        value: openebs
        effect: NoSchedule
