---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/postgresql.cnpg.io/cluster-v1.json

apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: data-warehouse-postgresql
  namespace: data-warehouse-staging
spec:
  instances: 1
  imageCatalogRef:
    apiGroup: postgresql.cnpg.io
    kind: ClusterImageCatalog
    name: postgresql
    major: 17
  superuserSecret:
    name: toolkit-web-secret-data-warehouse-postgresql-superuser-creds
  bootstrap:
    recovery:
      source: data-warehouse-postgresql
  externalClusters:
    - name: data-warehouse-postgresql
      plugin:
        name: barman-cloud.cloudnative-pg.io
        parameters:
          barmanObjectName: s3-cloudflare
  resources:
    requests:
      cpu: 200m
      memory: 500Mi
    limits:
      memory: 2Gi
  storage:
    storageClass: ceph-block
    size: 50Gi
