---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/postgresql.cnpg.io/cluster-v1.json

apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: toolkit-web-snapshot-postgresql
  namespace: data-warehouse-staging
spec:
  instances: 2
  imageCatalogRef:
    apiGroup: postgresql.cnpg.io
    kind: ClusterImageCatalog
    name: postgresql
    major: 17
  superuserSecret:
    name: toolkit-web-secret-snapshot-postgresql-superuser-creds
  bootstrap:
    initdb:
      database: data_warehouse_staging_snapshot
      owner: toolkit_web
      secret:
        name: toolkit-web-secret-snapshot-postgresql-creds
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      memory: 1Gi
  storage:
    storageClass: openebs-mayastor
    size: 5Gi
  affinity:
    nodeSelector:
      node-role.kubernetes.io/openebs: openebs
    tolerations:
      - key: miraistudio/storage
        operator: Equal
        value: openebs
        effect: NoSchedule
