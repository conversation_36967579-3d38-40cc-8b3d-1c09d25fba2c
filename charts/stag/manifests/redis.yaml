---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/redis.redis.opstreelabs.in/redisreplication-v1beta2.json

apiVersion: redis.redis.opstreelabs.in/v1beta2
kind: RedisReplication
metadata:
  name: toolkit-web-redis
  namespace: data-warehouse-staging
spec:
  clusterSize: 2
  podSecurityContext:
    runAsUser: 1000
    fsGroup: 1000
  redisExporter:
    enabled: true
    # renovate: datasource=docker depName=quay.io/opstree/redis-exporter
    image: quay.io/opstree/redis-exporter:v1.48.0
  kubernetesConfig:
    # renovate: datasource=docker depName=quay.io/opstree/redis
    image: quay.io/opstree/redis:v8.0.3
    imagePullPolicy: IfNotPresent
    redisSecret:
      name: toolkit-web-secret-redis-creds
      key: password
    resources:
      requests:
        cpu: 50m
        memory: 64Mi
      limits:
        cpu: 200m
        memory: 512Mi
  nodeSelector:
    node-role.kubernetes.io/openebs: openebs
  tolerations:
    - key: miraistudio/storage
      operator: Equal
      value: openebs
      effect: NoSchedule
  storage:
    volumeClaimTemplate:
      spec:
        accessModes:
          - ReadWriteOnce
        storageClassName: openebs-mayastor
        resources:
          requests:
            storage: 1Gi
---
# yaml-language-server: $schema=https://s3.miraistudio.games/schemastore/redis.redis.opstreelabs.in/redissentinel-v1beta2.json

apiVersion: redis.redis.opstreelabs.in/v1beta2
kind: RedisSentinel
metadata:
  name: toolkit-web-redis
  namespace: data-warehouse-staging
spec:
  clusterSize: 3
  podSecurityContext:
    runAsUser: 1000
    fsGroup: 1000
  pdb:
    enabled: false
  redisSentinelConfig:
    redisReplicationName: toolkit-web-redis
    redisReplicationPassword:
      secretKeyRef:
        name: toolkit-web-secret-redis-creds
        key: password
  kubernetesConfig:
    # renovate: datasource=docker depName=quay.io/opstree/redis-sentinel
    image: quay.io/opstree/redis-sentinel:v8.0.3
    imagePullPolicy: IfNotPresent
    redisSecret:
      name: toolkit-web-secret-redis-creds
      key: password
    resources:
      requests:
        cpu: 20m
        memory: 32Mi
      limits:
        memory: 128Mi
