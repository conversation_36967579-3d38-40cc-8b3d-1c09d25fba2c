---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/refs/heads/main/charts/other/app-template/values.schema.json

redis:
  type: sentinel
  name: myMaster
  host: toolkit-web-redis-sentinel
  port: '26379'

server:
  cdn:
    s3:
      endpoint: https://s3-std.miraistudio.games
      bucket: toolkit-web-static

secrets:
  env:
    enabled: true
    stringData:
      NODE_ENV: production
      TZ: Asia/Bangkok
      PORT: '3333'
      HOST: '0.0.0.0'
      LOG_LEVEL: debug
      QUEUE_DB: '1'
      SESSION_DRIVER: cookie
      APP_KEY: '{{ .Values.server.appKey }}'
      DB_PRIMARY_HOST: toolkit-web-postgresql-rw
      DB_DATABASE: toolkit_web_production
      DB_REPLICAS: '[{"host": "toolkit-web-postgresql-ro"}]'
      DB_USER: '{{ .Values.db.self.user }}'
      DB_PASSWORD: '{{ .Values.db.self.password }}'
      DB_DW_PRIMARY_HOST: data-warehouse-postgresql-rw
      DB_DW_REPLICAS: '[{"host": "data-warehouse-postgresql-r"}]'
      DW_DB_USER: '{{ .Values.db.dataWarehouse.user }}'
      DW_DB_PASSWORD: '{{ .Values.db.dataWarehouse.password }}'
      DB_DW_SNAPSHOT_PRIMARY_HOST: toolkit-web-snapshot-postgresql-rw
      DB_DW_SNAPSHOT_REPLICAS: '[{"host": "toolkit-web-snapshot-postgresql-ro"}]'
      DWS_DB_USER: '{{ .Values.db.snapshot.user }}'
      DWS_DB_PASSWORD: '{{ .Values.db.snapshot.password }}'
      DWS_DB_DATABASE: data_warehouse_staging_snapshot
      REDIS_TYPE: '{{ .Values.redis.type }}'
      REDIS_NAME: '{{ .Values.redis.name }}'
      REDIS_HOST: '{{ .Values.redis.host }}'
      REDIS_PORT: '{{ .Values.redis.port }}'
      REDIS_DB: '0'
      REDIS_PASSWORD: '{{ .Values.redis.password }}'
      GOOGLE_CLIENT_ID: '{{ .Values.server.google.clientId }}'
      GOOGLE_CLIENT_SECRET: '{{ .Values.server.google.clientSecret }}'
      OAUTH_GOOGLE_CALLBACK_URL: https://toolkit-stag-10-241-90-160.local.miraistudio.games/google/callback
      CONFIG_MAP_GSERVICE_KEY: |
        {{- .Values.server.google.configMapGserviceKey | nindent 2 }}
      ANDROIDPUBLISHER_GSERVICE_KEY: |
        {{- .Values.server.google.androidpublisherGserviceKey | nindent 2 }}
      GITHUB_APPS_CLIENT_SECRET: '{{ .Values.server.github.clientSecret }}'
      GITHUB_APPS_PRIVKEY: |
        {{- .Values.server.github.privateKey | nindent 2 }}
      VAULTKEY_API_KEY: '{{ .Values.server.vaultKey.apiKey }}'
      ADMOB_VAULTKEY_ID: '{{ .Values.server.vaultKey.admob }}'
      GOOGLEAPI_VAULTKEY_ID: '{{ .Values.server.vaultKey.googleApi }}'
      METABASE_SECRET_KEY: '{{ .Values.server.metabase.secretKey }}'
      CDN_URL: '{{ .Values.server.cdn.s3.endpoint }}/{{ .Values.server.cdn.s3.bucket }}'
      CDN_S3_BUCKET: '{{ .Values.server.cdn.s3.bucket }}'
      CDN_S3_ACCESS_KEY_ID: '{{ .Values.server.cdn.s3.accessKeyId }}'
      CDN_S3_SECRET_ACCESS_KEY: '{{ .Values.server.cdn.s3.secretAccessKey }}'
      CDN_S3_ENDPOINT: '{{ .Values.server.cdn.s3.endpoint }}'
  redis-creds:
    enabled: true
    stringData:
      password: '{{ .Values.redis.password }}'
  postgresql-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.self.user }}'
      password: '{{ .Values.db.self.password }}'
  postgresql-superuser-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.self.rootUser }}'
      password: '{{ .Values.db.self.rootPassword }}'
  snapshot-postgresql-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.snapshot.user }}'
      password: '{{ .Values.db.snapshot.password }}'
  snapshot-postgresql-superuser-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.snapshot.user }}'
      password: '{{ .Values.db.snapshot.password }}'
  data-warehouse-postgresql-superuser-creds:
    enabled: true
    stringData:
      username: '{{ .Values.db.dataWarehouse.rootUser }}'
      password: '{{ .Values.db.dataWarehouse.rootPassword }}'
  data-warehouse-postgresql-creds:
    enabled: true
    stringData:
      password: '{{ .Values.db.dataWarehouse.password }}'
      postgres-password: '{{ .Values.db.dataWarehouse.rootPassword }}'
      replication-password: '{{ .Values.db.dataWarehouse.password }}'
