---
# yaml-language-server: $schema=https://www.schemastore.org/kustomization.json

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

helmCharts:
  - releaseName: toolkit-web-api
    namespace: data-warehouse-staging
    repo: oci://ghcr.io/bjw-s-labs/helm/
    name: app-template
    version: 4.2.0
    valuesFile: values.yaml

labels:
  - includeTemplates: true
    pairs:
      miraistudio/environment: stag
