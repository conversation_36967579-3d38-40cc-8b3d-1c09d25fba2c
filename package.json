{"name": "adonis-ts", "version": "0.0.0", "private": true, "license": "UNLICENSED", "type": "module", "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#jobs/*": "./app/jobs/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#presenters/*": "./app/presenters/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#resolvers/*": "./app/resolvers/*.js", "#utils/*": "./app/utils/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js", "#configmaps/*": "./app/configmaps/*.js", "#fixtures/*": "./tests/fixtures/*", "#types/*": "./types/*.js", "#graphql/*": "./graphql/*.js", "#dataloaders/*": "./app/dataloaders/*.js", "#consumers/*": "./app/consumers/*.js"}, "scripts": {"build": "node ace build", "build-storybook": "storybook build", "dev": "node ace serve --hmr", "format": "prettier --write .", "graphgen": "graphql-codegen --config graphql/codegen.ts", "lint": "eslint .", "prepare": "husky", "queue:dashboard": "node ace queue:dashboard", "queue:start": "node ace queue:listen", "start": "node bin/server.js", "storybook": "storybook dev -p 6006", "test": "node ace test", "typecheck": "tsc --noEmit"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix", "*.{json,md,yml,css}": "prettier --write"}, "prettier": "@mirai-game-studio/prettier-config", "overrides": {"google-spreadsheet": {"google-auth-library": "^10.1.0"}}, "dependencies": {"@adonisjs/ally": "^5.1.0", "@adonisjs/auth": "^9.4.0", "@adonisjs/bouncer": "^3.1.5", "@adonisjs/core": "^6.17.2", "@adonisjs/cors": "^2.2.1", "@adonisjs/i18n": "^2.2.0", "@adonisjs/inertia": "^3.1.1", "@adonisjs/lucid": "^21.6.1", "@adonisjs/redis": "^9.2.0", "@adonisjs/session": "^7.5.1", "@adonisjs/shield": "^8.2.0", "@adonisjs/static": "^1.1.1", "@adonisjs/transmit": "^2.0.2", "@adonisjs/transmit-client": "^1.0.0", "@adonisjs/vite": "^4.0.0", "@apollo/server": "^5.0.0", "@faker-js/faker": "^9.6.0", "@googleapis/admob": "^3.0.1", "@googleapis/androidpublisher": "^28.0.1", "@googleapis/drive": "^13.0.1", "@googleapis/firebase": "^9.0.1", "@inertiajs/react": "^2.0.6", "@mirai-game-studio/adonis-sdk": "^1.9.0", "@munkit/main": "^1.19.0", "@octokit/auth-app": "^8.0.0", "@octokit/core": "^7.0.0", "@octokit/graphql": "^9.0.0", "@octokit/plugin-paginate-rest": "^13.0.0", "@poppinss/utils": "^6.9.2", "@sentry/node": "^10.0.0", "@vinejs/vine": "^3.0.1", "axios": "^1.8.4", "bignumber.js": "^9.3.0", "case-anything": "^3.1.2", "config": "^4.0.0", "csv-parse": "^6.0.0", "csv-stringify": "^6.5.2", "dataloader": "^2.2.3", "discord.js": "^14.18.0", "edge.js": "^6.2.1", "escape-string-regexp": "^5.0.0", "fast-cartesian": "^9.0.1", "google-auth-library": "^10.1.0", "google-spreadsheet": "^5.0.0", "graphql": "^16.10.0", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "luxon": "^3.6.1", "octokit": "^5.0.0", "p-limit": "^6.2.0", "parse-duration": "^2.1.4", "pg": "^8.14.1", "pino-logfmt": "^1.0.0", "query-string": "^9.1.1", "reflect-metadata": "^0.2.2", "semver": "^7.7.1", "string-template": "^1.0.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "yup": "^1.6.1", "zod": "^4.0.0"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/tsconfig": "^1.4.1", "@apollo/client": "^3.13.6", "@apollo/server": "^5.0.0", "@chromatic-com/storybook": "^4.0.0", "@ckeditor/ckeditor5-react": "^11.0.0", "@commitlint/config-conventional": "^19.8.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-brands-svg-icons": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^0.2.2", "@graphiql/toolkit": "^0.11.3", "@graphql-codegen/cli": "5.0.7", "@graphql-codegen/introspection": "4.0.3", "@graphql-codegen/typescript": "4.1.6", "@graphql-codegen/typescript-resolvers": "4.5.1", "@graphql-typed-document-node/core": "^3.2.0", "@hookform/resolvers": "^5.0.1", "@japa/runner": "^4.2.0", "@mirai-game-studio/adonis-testing-sdk": "^1.3.0", "@mirai-game-studio/eslint-config": "^1.0.4", "@mirai-game-studio/prettier-config": "^1.0.2", "@mirai-game-studio/typescript-config": "^1.0.5", "@mui/icons-material": "^7.0.1", "@mui/lab": "7.0.0-beta.14", "@mui/material": "^7.0.1", "@mui/x-charts": "^7.29.1", "@mui/x-data-grid-generator": "^7.29.1", "@mui/x-data-grid-premium": "^7.29.1", "@mui/x-data-grid-pro": "^7.29.1", "@mui/x-date-pickers-pro": "^7.29.1", "@mui/x-license": "^7.29.1", "@octokit/types": "^14.0.0", "@playwright/test": "^1.52.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.0", "@redocly/cli": "^2.0.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^9.0.0", "@storybook/addon-onboarding": "^9.0.0", "@storybook/blocks": "^8.6.12", "@storybook/react": "^9.0.0", "@storybook/react-vite": "^9.0.0", "@storybook/test": "^8.6.12", "@swc/core": "^1.11.18", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.72.0", "@tanstack/react-query-devtools": "^5.72.0", "@tanstack/react-table": "^8.21.3", "@tanstack/table-core": "^8.21.3", "@types/config": "3.3.4", "@types/jest": "^30.0.0", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.10", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.6.2", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/semver": "^7.7.0", "@types/string-template": "^1.0.6", "@types/uuid": "^10.0.0", "@uidotdev/usehooks": "^2.4.1", "@vitejs/plugin-react": "^5.0.0", "ckeditor5": "^46.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "commitlint": "^19.8.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "eslint": "^9.24.0", "glob": "^11.0.1", "graphiql": "^5.0.0", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "graphql-scalars": "^1.24.2", "graphql-type-json": "^0.3.2", "hot-hook": "^0.4.0", "husky": "^9.1.7", "i18next": "^25.0.0", "js-cookie": "^3.0.5", "lint-staged": "^16.0.0", "lucide-react": "^0.536.0", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "react": "^19.1.0", "react-country-flag": "^3.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "rimraf": "^6.0.1", "sonner": "^2.0.3", "storybook": "^9.0.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.5", "type-fest": "^4.39.1", "typescript": "~5.7.3", "vh-sticky-table-header": "^1.8.2", "vite": "^6.3.5", "zustand": "^5.0.3"}}