import type { ApplicationService } from '@adonisjs/core/types'
import { ErrorTrackingService } from '@mirai-game-studio/adonis-sdk/error_tracking'

import DiscordBotService from '#services/discord/discord_bot_service'

declare module '@adonisjs/core/types' {
  interface ContainerBindings {
    'discord.bot': DiscordBotService
  }
}

export default class DiscordBotProvider {
  constructor(protected app: ApplicationService) {}

  register() {
    this.app.container.singleton(DiscordBotService, async (resolver) => {
      return new DiscordBotService(
        await resolver.make('logger'),
        (await resolver.make('err.track')) as ErrorTrackingService
      )
    })

    this.app.container.alias('discord.bot', DiscordBotService)
  }

  async boot() {
    // const bot = await this.app.container.make('discord.bot')
    // try {
    //   await bot.boot()
    // } catch (err) {
    //   const logger = await this.app.container.make('logger')
    //   logger.error(err)
    //   process.exit(1)
    // }
  }

  async start() {}

  async ready() {}

  async shutdown() {
    // const bot = await this.app.container.make('discord.bot')
    // await bot.shutdown()
  }
}
