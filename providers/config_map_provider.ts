import { ConfigMapRegistry } from '@munkit/main'
import type { ApplicationService } from '@adonisjs/core/types'

import { ConfigMapInferrer } from '#configmaps/inferrer'
import { ConfigMapScope } from '#config/enums'

declare module '@adonisjs/core/types' {
  interface ContainerBindings {
    'cmap.registry': ConfigMapRegistry
  }
}

export default class ConfigMapProvider {
  constructor(protected app: ApplicationService) {}

  register() {
    this.app.container.singleton(ConfigMapInferrer, () => new ConfigMapInferrer())
    this.app.container.alias('configmap.inferrer', ConfigMapInferrer)
    this.app.container.alias('cmap.registry', ConfigMapRegistry)
  }

  async boot() {
    const configMapInferrer = await this.app.container.make(ConfigMapInferrer)
    await configMapInferrer.onInit()

    const configMapManager = await this.app.container.make('configmap.manager')
    const logger = await this.app.container.make('logger')

    for (const scope of Object.values(ConfigMapScope)) {
      await configMapManager.boot(scope)
      logger.info(`Config map ${scope} booted`)
    }
  }

  async start() {}

  async ready() {}

  async shutdown() {}
}
