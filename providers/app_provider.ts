import type { ApplicationService } from '@adonisjs/core/types'
import { BaseModel } from '@adonisjs/lucid/orm'

import DownloaderFactory from '#services/game_release/downloader_factory'
import GoogleDriveDownloader from '#services/game_release/google_drive_downloader'
import GitHubService from '#services/github_service'
import OpenProjectService from '#services/open_project_service'
import { RedisService } from '#services/redis/redis_service'
import ToolPermissionService from '#services/tool_permission_service'
import { UserService } from '#services/user_service'
import '#utils/pure'
import { DataSource } from '#graphql/main'
import { ModelReflector } from '#services/model_reflector'

declare module '@adonisjs/core/types' {
  interface ContainerBindings {
    github: GitHubService
    openproject: OpenProjectService
    modelReflector: ModelReflector
  }
}

declare module '@adonisjs/lucid/types/model' {
  interface LucidModel {
    connectionInContext(source: DataSource): string
  }
}

export default class AppProvider {
  constructor(protected app: ApplicationService) {}

  register() {
    this.app.container.singleton(
      GitHubService,
      async (resolver) =>
        new GitHubService(await resolver.make('logger'), await resolver.make('cmap.registry'))
    )
    this.app.container.alias('github', GitHubService)

    this.app.container.singleton(
      OpenProjectService,
      async (resolver) => new OpenProjectService(await resolver.make('logger'))
    )
    this.app.container.alias('openproject', OpenProjectService)

    this.app.container.singleton(ToolPermissionService, async () => new ToolPermissionService())

    this.app.container.singleton(
      GoogleDriveDownloader,
      async (resolver) => new GoogleDriveDownloader(await resolver.make('logger'))
    )
    this.app.container.singleton(
      DownloaderFactory,
      async (resolver) => new DownloaderFactory(await resolver.make(GoogleDriveDownloader))
    )

    this.app.container.singleton(
      RedisService,
      async (resolver) => new RedisService(await resolver.make('redis'))
    )

    this.app.container.singleton(UserService, async () => new UserService())
    this.app.container.singleton(ModelReflector, async () => new ModelReflector(this.app))
  }

  async boot() {
    await import('../app/extensions/response.js')
    await import('../app/extensions/http_context.js')
    await import('../app/extensions/validator.js')

    BaseModel.connectionInContext = function (source: DataSource) {
      switch (source) {
        case DataSource.Snapshot:
          return `${this.connection}Snapshot`

        case DataSource.Origin:
        default:
          return this.connection!
      }
    }
  }

  async start() {}

  async ready() {}

  async shutdown() {}
}
