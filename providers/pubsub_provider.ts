import type { ApplicationService } from '@adonisjs/core/types'

export default class PubsubProvider {
  constructor(protected app: ApplicationService) {}

  register() {}

  async boot() {
    const publisher = await this.app.container.make('pubsub.publisher')
    const { ConfigMapConsumer } = await import('#consumers/config_map_consumer')
    await publisher.register(ConfigMapConsumer)
  }

  async start() {}

  async ready() {}

  async shutdown() {}
}
