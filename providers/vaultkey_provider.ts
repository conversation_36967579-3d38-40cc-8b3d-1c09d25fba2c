import type { ApplicationService } from '@adonisjs/core/types'

import { VaultKeyClient } from '#services/vaultkey_client'

export default class VaultkeyProvider {
  constructor(protected app: ApplicationService) {}

  register() {
    this.app.container.singleton(VaultKeyClient, async (resolver) => {
      const logger = await resolver.make('logger')
      const config = await resolver.make('config')
      return new VaultKeyClient(config.get('vaultkey'), logger)
    })
  }

  async boot() {}

  async start() {}

  async ready() {}

  async shutdown() {}
}
