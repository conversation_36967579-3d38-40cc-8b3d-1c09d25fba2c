import { defineConfig, makeResolver } from '@mirai-game-studio/adonis-sdk/graphql'
import { Exception } from '@adonisjs/core/exceptions'
import { unwrapResolverError } from '@apollo/server/errors'
import app from '@adonisjs/core/services/app'
import logger from '@adonisjs/core/services/logger'

import {
  AdNetworkCategory as GraphAdNetworkCategory,
  AdTypeCategory as GraphAdTypeCategory,
} from '#graphql/main'

import { AdNetworkCategory, AdTypeCategory } from './enums.js'

export default defineConfig({
  resolvers: {
    /**
     * Query
     */
    Query: {
      v2: () => {
        return {} as any
      },

      users: makeResolver([() => import('#resolvers/user_resolver'), 'index']),
      team: makeResolver([() => import('#resolvers/team_resolver'), 'show']),
      teams: makeResolver([() => import('#resolvers/team_resolver'), 'index']),
      game: makeResolver([() => import('#resolvers/game_resolver'), 'show']),
      games: makeResolver([() => import('#resolvers/game_resolver'), 'index']),
      gamePerformanceSetting: makeResolver([
        () => import('#resolvers/game_performance_setting_resolver'),
        'show',
      ]),
      gameMetrics: makeResolver([() => import('#resolvers/game_metric_resolver'), 'index']),
      attributes: makeResolver([() => import('#resolvers/attribute_resolver'), 'index']),
      aggregateGameMetric: makeResolver([
        () => import('#resolvers/game_metric_resolver'),
        'aggregate',
      ]),
      gameReview: makeResolver([() => import('#resolvers/game_review_resolver'), 'show']),
      configMapCollections: makeResolver([() => import('#resolvers/config_map_resolver'), 'index']),
      configMaps: makeResolver([() => import('#resolvers/config_map_resolver'), 'show']),
      profile: makeResolver([() => import('#resolvers/profile_resolver'), 'show']),
      aggregatePlaytime: makeResolver([
        () => import('#resolvers/game_playtime_resolver'),
        'aggregate',
      ]),
      gameRewardUsages: makeResolver([
        () => import('#resolvers/game_reward_usage_resolver'),
        'index',
      ]),
      gameRewardUsageVersions: makeResolver([
        () => import('#resolvers/game_reward_usage_resolver'),
        'versions',
      ]),
      gameDailyLevelDropVersions: makeResolver([
        () => import('#resolvers/game_daily_level_drop_resolver'),
        'versions',
      ]),
      gameLevelDrops: makeResolver([() => import('#resolvers/game_level_drop_resolver'), 'index']),
      gameLevelDropVersions: makeResolver([
        () => import('#resolvers/game_level_drop_resolver'),
        'versions',
      ]),
      gamePlaytimeVersions: makeResolver([
        () => import('#resolvers/game_playtime_resolver'),
        'versions',
      ]),
      accessControl: makeResolver([() => import('#resolvers/access_control_resolver'), 'show']),
      adTypes: makeResolver([() => import('#resolvers/ad_type_resolver'), 'index']),
      dashboardNotifications: makeResolver([
        () => import('#resolvers/dashboard_notification_resolver'),
        'index',
      ]),
      gameRetentionRateVersions: makeResolver([
        () => import('#resolvers/game_retention_rate_resolver'),
        'versions',
      ]),
      gameRetentionRates: makeResolver([
        () => import('#resolvers/game_retention_rate_resolver'),
        'index',
      ]),
      gameNetworkRevenues: makeResolver([
        () => import('#resolvers/game_network_revenue_resolver'),
        'index',
      ]),
      aggregateGameNetworkRevenue: makeResolver([
        () => import('#resolvers/game_network_revenue_resolver'),
        'aggregate',
      ]),
      gameAgencyCosts: makeResolver([
        () => import('#resolvers/game_agency_cost_resolver'),
        'index',
      ]),
      aggregateGameAgencyCost: makeResolver([
        () => import('#resolvers/game_agency_cost_resolver'),
        'aggregate',
      ]),
      gameCreativeMetrics: makeResolver([
        () => import('#resolvers/game_creative_metric_resolver'),
        'index',
      ]),
      sideMenus: makeResolver([() => import('#resolvers/side_menu_resolver'), 'index']),
      gameStudios: makeResolver([() => import('#resolvers/game_studio_resolver'), 'index']),
      gameStudioMetrics: makeResolver([
        () => import('#resolvers/game_studio_metric_resolver'),
        'index',
      ]),
      workflows: makeResolver([() => import('#resolvers/workflow_resolver'), 'index']),
      workflowStepActions: makeResolver([
        () => import('#resolvers/workflow_resolver'),
        'stepActionIndex',
      ]),
      budgetRequests: makeResolver([() => import('#resolvers/budget_request_resolver'), 'index']),
      adMetrics: makeResolver([() => import('#resolvers/ad_metric_resolver'), 'index']),
      viewPresets: makeResolver([() => import('#resolvers/view_preset_resolver'), 'index']),

      admobGames: makeResolver([() => import('#resolvers/admob_resolver'), 'games']),
      admobMetrics: makeResolver([() => import('#resolvers/admob_resolver'), 'index']),

      networks: makeResolver([() => import('#resolvers/network_resolver'), 'index']),
      adNetworks: makeResolver([() => import('#resolvers/ad_networks_resolver'), 'index']),
      mediations: makeResolver([() => import('#resolvers/mediation_resolver'), 'index']),
      firebaseExperiments: makeResolver([
        () => import('#resolvers/firebase_experiment_resolver'),
        'index',
      ]),
      firebaseMetrics: makeResolver([() => import('#resolvers/firebase_metric_resolver'), 'index']),
      firebaseVersionVariants: makeResolver([
        () => import('#resolvers/firebase_version_varitans_resolver'),
        'index',
      ]),
      gameRoles: makeResolver([() => import('#resolvers/game_role_resolver'), 'index']),

      releaseMetrics: makeResolver([() => import('#resolvers/release_metric_resolver'), 'index']),
      releaseVersions: makeResolver([() => import('#resolvers/release_version_resolver'), 'index']),
      metabaseChart: makeResolver([() => import('#resolvers/metabase_chart_resolver'), 'show']),

      gameCosts: makeResolver([() => import('#resolvers/game_cost_resolver'), 'index']),
    },

    /**
     * Mutations
     */
    Mutation: {
      createWorkflow: makeResolver([() => import('#resolvers/workflow_resolver'), 'store']),
      updateWorkflow: makeResolver([() => import('#resolvers/workflow_resolver'), 'update']),
      createBudgetRequest: makeResolver([
        () => import('#resolvers/budget_request_resolver'),
        'store',
      ]),
      updateBudgetRequests: makeResolver([
        () => import('#resolvers/budget_request_resolver'),
        'update',
      ]),
      updateBudgetRequestsState: makeResolver([
        () => import('#resolvers/budget_request_resolver'),
        'updateState',
      ]),
      deleteBudgetRequests: makeResolver([
        () => import('#resolvers/budget_request_resolver'),
        'destroy',
      ]),

      createViewPreset: makeResolver([() => import('#resolvers/view_preset_resolver'), 'store']),
      deleteViewPresets: makeResolver([() => import('#resolvers/view_preset_resolver'), 'destroy']),

      updateProfile: makeResolver([() => import('#resolvers/profile_resolver'), 'update']),

      updateUser: makeResolver([() => import('#resolvers/user_resolver'), 'update']),
      createUser: makeResolver([() => import('#resolvers/user_resolver'), 'store']),
      deleteUsers: makeResolver([() => import('#resolvers/user_resolver'), 'destroy']),

      updateGameRoles: makeResolver([() => import('#resolvers/game_role_resolver'), 'update']),

      deleteTeam: makeResolver([() => import('#resolvers/team_resolver'), 'destroy']),
      updateTeam: makeResolver([() => import('#resolvers/team_resolver'), 'update']),
      createTeam: makeResolver([() => import('#resolvers/team_resolver'), 'store']),

      updateConfigMaps: makeResolver([() => import('#resolvers/config_map_resolver'), 'update']),

      updateAccessControl: makeResolver([
        () => import('#resolvers/access_control_resolver'),
        'update',
      ]),

      updateGameMetric: makeResolver([() => import('#resolvers/game_metric_resolver'), 'update']),

      updateGameCost: makeResolver([() => import('#resolvers/game_cost_resolver'), 'update']),
    },

    /**
     * Relationship
     */
    FirebaseMetric: {
      ads: makeResolver([() => import('#resolvers/firebase_metric_resolver'), 'ads']),
    },
    ReleaseMetric: {
      ads: makeResolver([() => import('#resolvers/release_metric_resolver'), 'ads']),
    },
    Ad: {
      group: makeResolver([() => import('#resolvers/ad_resolver'), 'group']),
      campaign: makeResolver([() => import('#resolvers/ad_resolver'), 'campaign']),
    },
    BudgetRequest: {
      game: makeResolver([() => import('#resolvers/budget_request_resolver'), 'game']),
      createdBy: makeResolver([() => import('#resolvers/budget_request_resolver'), 'createdBy']),
      workflow: makeResolver([() => import('#resolvers/budget_request_resolver'), 'workflow']),
    },
    WorkflowStep: {
      assignee: makeResolver([() => import('#resolvers/workflow_resolver'), 'assginee']),
    },
    GameStudioMetric: {
      game: makeResolver([() => import('#resolvers/game_studio_metric_resolver'), 'game']),
    },
    GameCreativeMetric: {
      agency: makeResolver([() => import('#resolvers/game_creative_metric_resolver'), 'agency']),
    },
    GameAgencyCost: {
      agency: makeResolver([() => import('#resolvers/game_agency_cost_resolver'), 'agency']),
      metric: makeResolver([() => import('#resolvers/game_agency_cost_resolver'), 'metric']),
    },
    GameNetworkRevenue: {
      network: makeResolver([() => import('#resolvers/game_network_revenue_resolver'), 'network']),
      metric: makeResolver([() => import('#resolvers/game_network_revenue_resolver'), 'metric']),
    },
    GameMetric: {
      metadata: makeResolver([() => import('#resolvers/game_metric_resolver'), 'metadata']),
    },
    User: {
      team: makeResolver([() => import('#resolvers/user_resolver'), 'team']),
      inchargedGames: makeResolver([() => import('#resolvers/user_resolver'), 'inchargedGames']),
    },
    V2Query: {
      gameMetrics: makeResolver([() => import('#resolvers/v2/game_metric_resolver'), 'index']),
      aggregateGameMetrics: makeResolver([
        () => import('#resolvers/v2/game_metric_resolver'),
        'aggregate',
      ]),
    },
    V2GameMetric: {
      adPerformances: makeResolver([
        () => import('#resolvers/v2/game_metric_resolver'),
        'adPerformances',
      ]),
    },
    Team: {
      leader: makeResolver([() => import('#resolvers/team_resolver'), 'leader']),
      members: makeResolver([() => import('#resolvers/team_resolver'), 'members']),
    },
    GameCost: {
      network: makeResolver([() => import('#resolvers/game_cost_resolver'), 'network']),
    },

    /**
     * Enum
     */
    AdTypeCategory: {
      [GraphAdTypeCategory.AppOpen]: AdTypeCategory.AppOpen,
      [GraphAdTypeCategory.Audio]: AdTypeCategory.Audio,
      [GraphAdTypeCategory.Banner]: AdTypeCategory.Banner,
      [GraphAdTypeCategory.CollapsibleBanner]: AdTypeCategory.CollapsibleBanner,
      [GraphAdTypeCategory.Interstitial]: AdTypeCategory.Interstitial,
      [GraphAdTypeCategory.Mrec]: AdTypeCategory.Mrec,
      [GraphAdTypeCategory.Native]: AdTypeCategory.Native,
      [GraphAdTypeCategory.Reward]: AdTypeCategory.Reward,
      [GraphAdTypeCategory.Unknown]: AdTypeCategory.Unknown,
    },
    AdNetworkCategory: {
      [GraphAdNetworkCategory.Firebase]: AdNetworkCategory.Firebase,
      [GraphAdNetworkCategory.Appsflyer]: AdNetworkCategory.Appsflyer,
    },
  },
  schemaPath: 'graphql/schema.graphql',
  formatError: (formattedError, resolverError: any) => {
    const error = unwrapResolverError(resolverError) as any

    if (!app.inProduction) {
      logger.error(error.message)
      logger.error(error.stack)
    }

    if (error instanceof Exception) {
      return {
        message: error.message,
        extensions: {
          code: error.code || error.status,
        },
      }
    }

    if (app.inProduction) {
      logger.error(error.message)
      logger.error(error.stack)

      return {
        message: 'Something went wrong',
      }
    }

    return formattedError
  },
})
