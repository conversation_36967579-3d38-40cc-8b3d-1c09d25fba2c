import { defineConfig } from '@mirai-game-studio/adonis-sdk/queue'
import config from 'config'

import redisConfig from '#config/redis'

const queueConfig = defineConfig({
  connection: {
    ...redisConfig.connections.main,
    db: config.get('queue').db,
  },
  queues: {
    default: {},
  },
  jobs: {},
  queue: {} as any,
  worker: {} as any,
})

export default queueConfig

declare module '@mirai-game-studio/adonis-sdk/types/queue' {
  type Queues = typeof queueConfig.queues
  interface QueueList extends Queues {}
}

declare module 'config' {
  interface QueueConfig {
    baseUrl: string
    db: number
  }

  interface ConfigBindings {
    queue: QueueConfig
  }
}
