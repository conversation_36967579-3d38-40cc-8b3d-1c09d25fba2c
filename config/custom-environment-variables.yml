config-map:
  driver: <PERSON><PERSON><PERSON>_MAP_DRIVER
  gdrive:
    gservice:
      key: CO<PERSON><PERSON>_MAP_GSERVICE_KEY
  local:
    path: CONFIG_MAP_LOCAL_DIR
github:
  app:
    clientSecret: GITHUB_APPS_CLIENT_SECRET
    privateKey: GITHUB_APPS_PRIVKEY
openproject:
  apikey:
    password: OPENPROJECT_APIKEY_PASSWORD
sentry:
  dsn: SENTRY_DSN
discord:
  bot:
    token: DISCORD_BOT_TOKEN
androidpublisher:
  gservice:
    key: ANDROIDPUBLISHER_GSERVICE_KEY
db:
  primary:
    host: DB_PRIMARY_HOST
    port: DB_PRIMARY_PORT
  replicas:
    __name: DB_REPLICAS
    __format: json
  username: DB_USER
  password: DB_PASSWORD
  database: DB_DATABASE
redis:
  type: REDIS_TYPE
  name: REDIS_NAME
  host: REDIS_HOST
  port: REDIS_PORT
  password: REDIS_PASSWORD
  db: REDIS_DB
dataWarehouse:
  primary:
    host: DB_DW_PRIMARY_HOST
    port: DB_DW_PRIMARY_PORT
  replicas:
    __name: DB_DW_REPLICAS
    __format: json
  username: DW_DB_USER
  password: DW_DB_PASSWORD
  database: DW_DB_DATABASE
dataWarehouseSnapshot:
  primary:
    host: DB_DW_SNAPSHOT_PRIMARY_HOST
    port: DB_DW_SNAPSHOT_PRIMARY_PORT
  replicas:
    __name: DB_DW_SNAPSHOT_REPLICAS
    __format: json
  username: DWS_DB_USER
  password: DWS_DB_PASSWORD
  database: DWS_DB_DATABASE
logger:
  level: LOG_LEVEL
oauth:
  google:
    clientId: GOOGLE_CLIENT_ID
    clientSecret: GOOGLE_CLIENT_SECRET
    callbackUrl: OAUTH_GOOGLE_CALLBACK_URL
queue:
  db: QUEUE_DB
admob:
  vaultkeyId: ADMOB_VAULTKEY_ID
vaultkey:
  apiKey: VAULTKEY_API_KEY
metabase:
  secretKey: METABASE_SECRET_KEY
googleapi:
  vaultkeyId: GOOGLEAPI_VAULTKEY_ID
cdn:
  url: CDN_URL
