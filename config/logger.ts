import app from '@adonisjs/core/services/app'
import { defineConfig, targets } from '@adonisjs/core/logger'
import config from 'config'

import env from '#start/env'
import { mergeIf } from '#utils/pure'

const { level } = config.get('logger')

export const isPrettyLogEnabled = app.inProduction ? false : config.get('logger').pretty

const loggerConfig = defineConfig({
  default: 'app',

  /**
   * The loggers object can be used to define multiple loggers.
   * By default, we configure only one logger (named "app").
   */
  loggers: {
    app: {
      enabled: true,
      name: env.get('APP_NAME'),
      level,
      transport: mergeIf(targets.pretty(), [
        {
          value: {
            target: 'pino-logfmt',
            level,
            options: {
              flattenNestedObjects: true,
              convertToSnakeCase: true,
            },
          },
          check: !isPrettyLogEnabled,
        },
      ]),
      formatters: {
        bindings: () => {
          return {}
        },
        level: (label) => {
          return { level: label }
        },
      },
    },
  },
})

export default loggerConfig

/**
 * Inferring types for the list of loggers you have configured
 * in your application.
 */
declare module '@adonisjs/core/types' {
  export interface LoggersList extends InferLoggers<typeof loggerConfig> {}
}

declare module 'config' {
  interface ConfigBindings {
    logger: {
      level: string
      pretty: boolean
    }
  }
}
