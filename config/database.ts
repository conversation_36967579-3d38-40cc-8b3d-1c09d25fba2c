import { defineConfig } from '@adonisjs/lucid'
import config from 'config'

import { isPrettyLogEnabled } from './logger.js'
import { ConnectionConfig, PostgreConfig } from '@adonisjs/lucid/types/database'

const db = config.get('db')
const dw = config.get('dataWarehouse')
const dwSnapshot = config.get('dataWarehouseSnapshot')

function getConnection(cfg: DatabaseConfig): PostgreConfig {
  const connectionConfig: Partial<ConnectionConfig['connection']> = {
    user: cfg.username,
    password: cfg.password,
    database: cfg.database,
    ...(cfg.tls
      ? {
          ssl: {
            checkServerIdentity: () => undefined,
            rejectUnauthorized: false,
          },
        }
      : {}),
  }

  const commonConfig: PostgreConfig = {
    client: 'pg',
    debug: cfg.log.enabled,
  }

  if (cfg.replicas.length > 0) {
    return {
      ...commonConfig,
      connection: {
        ...connectionConfig,
      },
      replicas: {
        read: {
          connection: cfg.replicas,
        },
        write: {
          connection: {
            host: cfg.primary.host,
          },
        },
      },
    }
  }

  return {
    ...commonConfig,
    connection: {
      ...connectionConfig,
      host: cfg.primary.host,
      port: cfg.primary.port,
    },
  }
}

const dbConfig = defineConfig({
  prettyPrintDebugQueries: isPrettyLogEnabled,
  connection: 'postgres',
  connections: {
    postgres: {
      ...getConnection(db),
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
    },
    dataWarehouse: {
      ...getConnection(dw),
      migrations: {
        naturalSort: true,
        paths: (dw.migration ? ['database/mocks'] : []).concat('database/data_warehouse'),
      },
    },
    dataWarehouseSnapshot: {
      ...getConnection(dwSnapshot),
      migrations: {
        naturalSort: true,
        paths: ['database/mocks', 'database/data_warehouse'],
      },
    },
  },
})

export default dbConfig

interface DatabaseConnection {
  host: string
  port: number
}

interface DatabaseConfig {
  primary: DatabaseConnection
  replicas: DatabaseConnection[]
  database: string
  username: string
  password: string
  tls: boolean
  log: {
    enabled: boolean
  }
  migration?: boolean
}

declare module 'config' {
  interface ConfigBindings {
    db: DatabaseConfig
    dataWarehouse: DatabaseConfig
    dataWarehouseSnapshot: DatabaseConfig
  }
}
