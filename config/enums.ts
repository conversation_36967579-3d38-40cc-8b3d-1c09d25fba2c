export enum Tool {
  Dashboard = 'dashboard',
  GitHub = 'github',
  System = 'system',
  All = 'all',
  LandingPage = 'landing-page',
  Document = 'document',
  Bot = 'bot',
}

export enum Permission {
  View = 'view',
  Manage = 'manage',
}

export enum ConfigMapScope {
  GitHub = 'github',
  Dashboard = 'dashboard',
  Publishing = 'publishing',
  General = 'general',
}

export enum GameReleaseProposalStatus {
  Pending = -1,
  Rejected = 0,
  Approved = 1,
}

export enum GamePlatform {
  Android = 'android',
  iOS = 'ios',
}

export enum GameStore {
  GooglePlay = 'google_play',
  AppStore = 'app_store',
}

export enum GameReleaseStatus {
  Pending = -1,
  Error = 0,
  InProgress = 1,
  Completed = 2,
}

export enum AdNetworkInputType {
  Auto = 'Auto',
  Manual = 'Manual',
}

export enum GameSpendType {
  Calculated = 'C',
  Estimated = 'E',
  Manual = 'Manual',
}

export const GameRevenueAdType = {
  Reward: 1,
  AOA: 2,
  Banner: 3,
  Inter: 4,
  MREC: 5,
  AdmobCollapse: 6,
  AdmobAOA: 7,
  AdmobNative: 8,
  AdmobAdaptive: 9,
  AdmobMREC: 10,
  Audio: 11,
}

export const GameRevenueMediationId = {
  UnityLP: 3,
  Applovin: 1,
  Admob: 2,
  NonMediation: 0,
}

export const GameRevenueNetworkId = {
  Odeeo: 23,
}

export const TeamUserRole = {
  Member: 'member',
  Leader: 'leader',
}

export enum AclSubject {
  Route = 'route',
  AttributeRead = 'attribute:read',
  AttributeWrite = 'attribute:write',
}

export enum AclModelName {
  GameMetric = 'GameMetric',
  GameMetricV2 = 'GameMetricV2',
  AdMetric = 'AdMetric',
  GameRevenue = 'GameRevenue',
}

export const NotificationCode = {
  'configMap.updated': 'configMap.updated',
}

export enum AdTypeCategory {
  Banner = 'banner',
  Interstitial = 'interstitial',
  Reward = 'reward',
  Native = 'native',
  Audio = 'audio',
  AppOpen = 'app_open',
  CollapsibleBanner = 'collapsible_banner',
  Mrec = 'mrec',
  Unknown = 'unknown',
}

export enum AdNetworkCategory {
  Firebase = 'firebase',
  Appsflyer = 'appsflyer',
}
