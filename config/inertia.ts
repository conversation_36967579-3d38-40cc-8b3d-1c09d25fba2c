import { defineConfig } from '@adonisjs/inertia'
import type { InferSharedProps } from '@adonisjs/inertia/types'

import type User from '#models/user'

import { Tool } from './enums.js'

const inertiaConfig = defineConfig({
  rootView: ({ request, routeMetadata }) => {
    const isNextUIAvailable = routeMetadata['ui.next']
    const isNextUIRequested =
      (request.input('ui_next') || request.cookiesList()['ui_next']) === 'true'

    return isNextUIAvailable === 'always' || (isNextUIAvailable === 'ondemand' && isNextUIRequested)
      ? 'layouts/inertia_next_layout'
      : 'layouts/inertia_layout'
  },

  /**
   * Data that should be shared with all rendered pages
   */
  sharedData: {
    pageId: (ctx) => ctx.inertia.always(() => ctx.route?.name!),
    user: (ctx) =>
      ctx.inertia.always(() => ctx.auth.user) as unknown as Pick<
        User,
        'id' | 'fullName' | 'email' | 'kind' | 'teamId' | 'toolPermissions' | 'hasPassword'
      >,
    abilities: ({ bouncer, inertia, auth }) => {
      return inertia.always(async () => ({
        canViewGitHub: Boolean(
          auth.user && (await bouncer.with('ToolPolicy').allows('view', Tool.GitHub))
        ),
        canManageDashboard: Boolean(
          auth.user && (await bouncer.with('ToolPolicy').allows('manage', Tool.Dashboard))
        ),
        canManageGitHub: Boolean(
          auth.user && (await bouncer.with('ToolPolicy').allows('manage', Tool.GitHub))
        ),
        canManageAll: Boolean(
          auth.user && (await bouncer.with('ToolPolicy').allows('manage', Tool.All))
        ),
      }))
    },
    csrfToken: (ctx) => ctx.inertia.always(() => ctx.request.csrfToken),
  },

  /**
   * Options for the server-side rendering
   */
  ssr: {
    enabled: false,
    entrypoint: 'resources/js/ssr.tsx',
  },
})

export default inertiaConfig

declare module '@adonisjs/inertia/types' {
  export interface SharedProps extends InferSharedProps<typeof inertiaConfig> {}
}
