config-map:
  driver: redis
static:
  enabled: false
sentry:
  enabled: false
queue:
  baseUrl: https://stg-toolkit-queue.miraistudio.games
db:
  primary:
    host: toolkit-web-postgresql-rw
    port: 5432
  replicas:
    - host: toolkit-web-postgresql-ro
      port: 5432
  database: toolkit_web_staging
  tls: false
  log:
    enabled: false
dataWarehouse:
  primary:
    host: data-warehouse-postgresql
    port: 5432
  replicas: []
  database: data_warehouse_production
  tls: false
  migration: false
  log:
    enabled: false
dataWarehouseSnapshot:
  primary:
    host: toolkit-web-snapshot-postgresql-rw
    port: 5432
  replicas:
    - host: toolkit-web-snapshot-postgresql-ro
      port: 5432
  database: data_warehouse_snapshot_staging
  tls: false
  log:
    enabled: false
redis:
  type: sentinel
  name: myMaster
  tls: false
  port: 6379
  host: toolkit-web-redis-sentinel
  db: 0
vaultkey:
  endpoint: https://vaultkey-10-241-90-160.local.miraistudio.games
