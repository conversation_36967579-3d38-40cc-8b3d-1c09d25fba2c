import { defineConfig } from '@adonisjs/redis'
import {
  InferConnections,
  RedisClusterConnectionConfig,
  RedisConnectionConfig,
} from '@adonisjs/redis/types'
import config from 'config'

const conf = config.get('redis')

const getRedisConnection = (): RedisConnectionConfig | RedisClusterConnectionConfig => {
  const commonConfig: Partial<RedisConnectionConfig> = {
    db: conf.db || 0,
    password: conf.password,
    keyPrefix: '',
    retryStrategy(times) {
      return times > 10 ? null : times * 50
    },
    ...(conf.tls
      ? {
          tls: {
            checkServerIdentity: () => undefined,
            rejectUnauthorized: false,
            servername: conf.host,
          },
          slotsRefreshTimeout: 3000,
        }
      : {}),
  }

  if (conf.type === 'standalone') {
    return {
      host: conf.host,
      port: Number(conf.port || '6379'),
      ...commonConfig,
    }
  }

  if (conf.type === 'sentinel') {
    return {
      sentinels: [
        {
          host: conf.host,
          port: Number(conf.port || '26379'),
        },
      ],
      sentinelPassword: conf.password,
      name: conf.name,
      ...commonConfig,
    }
  }

  return {
    ...commonConfig,
    host: conf.host,
    port: Number(conf.port || '6379'),
  }
}

const redisConfig = defineConfig({
  connection: 'main',

  connections: {
    main: getRedisConnection(),
    configmap: getRedisConnection(),
  },
})

export default redisConfig

declare module '@adonisjs/redis/types' {
  export interface RedisConnections extends InferConnections<typeof redisConfig> {}
}

declare module 'config' {
  interface DatabaseConnection {
    host: string
    port: number
  }

  interface ConfigBindings {
    redis: {
      type: 'standalone' | 'sentinel' | 'cluster'
      name: string
      password?: string
      tls?: boolean
      host: string
      port?: number
      db?: number
    }
  }
}
