config-map:
  driver: local
  gdrive:
    gservice:
      email: <EMAIL>
      key: FIXME
      scopes:
        - https://www.googleapis.com/auth/spreadsheets
    docs:
      github: 1ydr4YIF-cpua13YVv5k8XHUaYyddUAfef6vTP5usXdU
      dashboard: 1YqmfdYEvj5u0M37TSpOPhv64ex0M-pN_bOTKVJkTcvU
      publishing: 1vKAfjeVh9D2zWhA7EDqMRMkbWRB9hFZhL7ZqzfVfvW4
      import: 1YqmfdYEvj5u0M37TSpOPhv64ex0M-pN_bOTKVJkTcvU
      general: 1gn_h_W7c4YQCORWJ3yCQ7OPDdyoIjG5ktAYDv32_L5M
  local:
    path: tests/fixtures/configmaps
  redis:
    connection: configmap
    prefix: 'configmap:data'
github:
  org: mirai-game-studio
  app:
    appId: 864461
    clientId: Iv1.03102a2187fa2084
    clientSecret: FIXME
    privateKey: FIXME
    installationId: 48973120
oauth:
  google:
    clientId: 292520005952-ledm0d521v0cpvoapkdod1ab0jm53mjq.apps.googleusercontent.com
    callbackUrl: https://toolkit.miraistudio.games/google/callback
static:
  enabled: false
  cdn:
    url: https://s3-std.miraistudio.games/toolkit-web-static/
openproject:
  endpoint: https://task.miraistudio.games/api
  apikey:
    username: apikey
    password: FIXME
sentry:
  enabled: false
  dsn: ''
discord:
  bot:
    token: FIXME
androidpublisher:
  gservice:
    email: <EMAIL>
    key: FIXME
    scopes:
      - https://www.googleapis.com/auth/androidpublisher
      - https://www.googleapis.com/auth/drive.readonly
queue:
  db: 0
  baseUrl: http://localhost:9999
session:
  age: 72h
dataWarehouse:
  migration: false
  username: SECRET
  password: SECRET
  database: mirai_data_warehouse_development
  tls: false
  primary:
    host: localhost
    port: 5432
  replicas:
    - host: localhost
      port: 5433
    - host: localhost
      port: 5434
  log:
    enabled: true
dataWarehouseSnapshot:
  migration: true
  username: SECRET
  password: SECRET
  database: mirai_data_warehouse_snapshot_development
  tls: false
  primary:
    host: localhost
    port: 5432
  replicas:
    - host: localhost
      port: 5433
    - host: localhost
      port: 5434
  log:
    enabled: true
db:
  username: SECRET
  password: SECRET
  database: mirai_studio_toolkit_development
  tls: false
  primary:
    host: localhost
    port: 5432
  replicas:
    - host: localhost
      port: 5433
    - host: localhost
      port: 5434
  log:
    enabled: true
redis:
  type: standalone
  name: ''
  host: localhost
  port: 6379
  password: ''
  db: 0
  tls: false
logger:
  level: info
  pretty: false
admob:
  publisherId: pub-9561563625318152
  vaultkeyId: SECRET
vaultkey:
  endpoint: https://vaultkey-10-241-90-160.local.miraistudio.games
  apiKey: SECRET
metabase:
  baseUrl: https://metabase.miraistudio.games
  secretKey: SECRET
  expiresIn: 10m
googleapi:
  vaultkeyId: SECRET
