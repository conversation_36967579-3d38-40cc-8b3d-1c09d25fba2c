config-map:
  driver: redis
static:
  enabled: false
sentry:
  enabled: false
queue:
  baseUrl: https://toolkit-queue.miraistudio.games
db:
  primary:
    host: studio-toolkit-postgresql.production.svc.miraistudio.local
    port: 5432
  replicas: []
  database: studio_toolkit_production
  tls: false
  log:
    enabled: false
dataWarehouse:
  primary:
    host: data-warehouse-postgresql.production.svc.miraistudio.local
    port: 5432
  replicas: []
  database: data_warehouse_production
  tls: false
  log:
    enabled: false
dataWarehouseSnapshot:
  primary:
    host: studio-toolkit-snapshot-postgresql.production.svc.miraistudio.local
    port: 5432
  replicas: []
  database: data_warehouse_snapshot_production
  tls: false
  log:
    enabled: false
redis:
  cluster: false
  tls: false
  port: 6379
  host: studio-toolkit-redis-master.production.svc.miraistudio.local
  db: 0
vaultkey:
  endpoint: https://vaultkey-10-241-90-160.local.miraistudio.games
