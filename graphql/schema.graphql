#
#
# Query
#
#

type Query {
  users(where: UsersWhere!, offset: Offset): Users!
  team(id: Int!): Team!
  game(id: ID!): Game!
  games(where: GamesWhere!, offset: Offset): Games!
  gamePerformanceSetting(gameId: ID!): GamePerformanceSetting!
  gameMetrics(where: GameMetricWhere!, offset: Offset, order: Order): GameMetrics!
  attributes(where: AttributesWhere!): Attributes!
  aggregateGameMetric(where: GameMetricWhere!): GameMetric!
  gameCosts(where: GameCostsWhere!, offset: Offset): GameCosts!
  aggregateGameCost(id: ID!, where: AggregateGameCostWhere!): GameCost!
  gameReview(gameId: ID!, date: Date!): GameReview!
  configMapCollections(ids: [ID!]!): [ConfigMapCollection!]!
  configMaps(ids: [ID!]!): [ConfigMap!]!
  profile: User!
  aggregatePlaytime(where: AggregatePlaytimeWhere!): PlaytimeAggregation!
  gamePlaytimeVersions(where: ProductMetricVersionsWhere!): Versions!
  gameRewardUsages(where: GameRewardUsagesWhere!, group: Group!, order: Order!): GameRewardUsages!
  gameRewardUsageVersions(where: ProductMetricVersionsWhere!): Versions!
  gameLevelDrops(where: GameLevelDropsWhere!): GameLevelDrops!
  gameDailyLevelDropVersions(where: ProductMetricVersionsWhere!): Versions!
  gameLevelDropVersions(where: GameLevelDropVersionsWhere!): Versions!
  gameRetentionRateVersions(where: ProductMetricVersionsWhere!): Versions!
  gameRetentionRates(where: GameRetentionRatesWhere!): GameRetentionRates!
  accessControl(where: AccessControlWhere!): AccessControl!
  adTypes: AdTypes!
  dashboardNotifications: DashboardNotifications!
  gameNetworkRevenues(
    where: GameNetworkRevenuesWhere!
    offset: Offset
    order: Order!
  ): GameNetworkRevenues!
  aggregateGameNetworkRevenue(where: AggregateGameNetworkRevenueWhere!): GameNetworkRevenues!
  gameAgencyCosts(where: GameAgencyCostsWhere!, offset: Offset, order: Order!): GameAgencyCosts!
  aggregateGameAgencyCost(where: AggregateGameAgencyCostWhere!): GameAgencyCosts!
  gameCreativeMetrics(
    where: GameCreativeMetricsWhere!
    offset: Offset
    order: Order!
  ): GameCreativeMetrics!
  sideMenus: SideMenus!
  gameStudios: GameStudios!
  gameStudioMetrics(where: GameStudioMetricsWhere!): GameStudioMetrics!
  workflows: Workflows!
  workflowStepActions: WorkflowStepActions!
  budgetRequests(where: BudgetRequestsWhere!, offset: Offset): BudgetRequests!

  viewPresets(where: ViewPresetsWhere!): ViewPresets!

  adMetrics(where: AdMetricsWhere!, group: Group!, preset: Preset): AdMetrics!

  admobGames: AdmobGames!
  admobMetrics(where: AdmobMetricsWhere!): AdmobMetrics!

  networks(where: NetworksWhere!): Networks!
  adNetworks: AdNetworks!
  mediations: Mediations!
  firebaseExperiments(where: FirebaseExperimentsWhere!): FirebaseExperiments!
  firebaseMetrics(where: FirebaseMetricsWhere!, group: Group, preset: Preset): FirebaseMetrics!
  firebaseVersionVariants(where: FirebaseVersionVariantsWhere!): FirebaseVersionVariants!

  v2: V2Query!

  gameRoles(gameIds: [ID!]!): GamesRoles!

  teams: [Team!]!

  releaseMetrics(where: ReleaseMetricsWhere!, group: Group, preset: Preset): ReleaseMetrics!

  releaseVersions(where: ReleaseVersionsWhere!): ReleaseVersions!

  metabaseChart(where: MetabaseChartWhere!): MetabaseChart!
}

type V2Query {
  gameMetrics(
    where: V2GameMetricWhere!
    offset: Offset
    order: Order
    source: DataSource
  ): V2GameMetrics!

  aggregateGameMetrics(id: String!, where: V2GameMetricWhere!, source: DataSource): V2GameMetric!
}

type Mutation {
  createWorkflow(form: CreateWorkflowForm!): Workflow!
  updateWorkflow(form: UpdateWorkflowForm!): Workflow!
  createBudgetRequest(form: CreateBudgetRequestForm!): BudgetRequest!
  updateBudgetRequests(forms: [UpdateBudgetRequestForm!]!): [BudgetRequest!]!
  updateBudgetRequestsState(forms: [UpdateBudgetRequestStateForm!]!): [BudgetRequest!]!
  deleteBudgetRequests(where: DeleteBudgetRequestsWhere!): Void

  createUser(form: CreateUserForm!): User!
  updateUser(where: UpdateUserWhere!, form: UpdateUserForm!): User!
  deleteUsers(where: DeleteUsersWhere!): Void

  createViewPreset(form: CreateViewPresetForm!): ViewPreset!
  deleteViewPresets(where: DeleteViewPresetsWhere!): Void

  updateProfile(form: UpdateProfileForm!): User!

  updateGameRoles(forms: [UpdateGameRolesGame!]!): GamesRoles!

  createTeam(form: CreateTeamForm!): Team!
  updateTeam(where: UpdateTeamWhere!, form: UpdateTeamForm!): Team!
  deleteTeam(where: DeleteTeamWhere!): Void

  updateConfigMaps(where: UpdateConfigMapsWhere!): Void

  updateAccessControl(where: AccessControlWhere!, form: AccessControlUpdateForm!): AccessControl!

  updateGameMetric(where: UpdateGameMetricWhere!, form: UpdateGameMetricForm!): GameMetric!

  updateGameCost(where: UpdateGameCostWhere!, form: UpdateGameCostForm!): Void
}

#
#
# Scalars
#
#

scalar JSON
scalar Date
scalar Void
scalar UUID
scalar BigInt

#
#
# Enums
#
#

enum DataSource {
  ORIGIN
  SNAPSHOT
}

enum OrderDirection {
  ASC
  DESC
}

enum UserKind {
  PARTNER
  INHOUSE
}

enum AdNetworkCategory {
  FIREBASE
  APPSFLYER
}

enum FilterOperator {
  EQ
  NE
  GT
  LT
  LTE
  GTE
  BETWEEN
  IN
}

#
#
# Inputs and Responses
#
#

input Order {
  field: String
  direction: OrderDirection
}

input Offset {
  page: Int
  perPage: Int
}

input Group {
  fields: [String!]!
}

type PageInfo {
  total: Int!
  perPage: Int!
  currentPage: Int!
  lastPage: Int!
  firstPage: Int
  # firstPageUrl: String!
  # lastPageUrl: String!
  # nextPageUrl: String!
  # previousPageUrl: String!
}

input UsersWhere {
  email: String
}

type Users {
  collection: [User!]!
  pageInfo: PageInfo!
}

input GameMetricWhere {
  gameId: ID!
  dateGte: Date
  dateLte: Date
}

type GameMetrics {
  collection: [GameMetric!]!
  pageInfo: PageInfo!
}

type User {
  id: ID!
  fullName: String!
  email: String!
  toolPermissions: [ToolPermission!]!
  inchargedGames: [UserInchargedGame!]!
  team: Team
  teamLead: Team
  ledTeam: Team
  kind: UserKind!
  hasPassword: Boolean!
  isDeleted: Boolean!
  note: String
}

type ToolPermission {
  tool: String!
  action: String!
}

type UserInchargedGame {
  storeId: ID!
  roleId: String!
}

type Team {
  id: Int!
  name: String!
  roleId: String!
  leaderId: ID
  leader: User
  members: [User!]!
}

type Game {
  id: ID!
  name: String!
  packageName: String!
  platform: String!
  isInhouse: Boolean!
  isActive: Boolean!
}

input GamesWhere {
  keyword: String
  userIds: [String!]
}

type Games {
  collection: [Game!]!
  pageInfo: PageInfo!
}

type GamePerformanceSetting {
  gameId: ID!
  criterias: [GamePerformanceCriteria!]!
}

enum GamePerformanceCriteriaConclusion {
  Drop
  Fail
  Pass
}

type GamePerformanceCriteria {
  conclusion: GamePerformanceCriteriaConclusion!
  metrics: JSON!
}

type GameMetric {
  id: Int!
  date: Date
  gameId: ID!

  paidInstalls: Int!
  organicInstalls: Int!
  organicPercentage: Float!
  totalInstalls: Int!

  cost: Float!
  cpi: Float!
  roas: Float!
  revenue: Float!
  profit: Float!

  retentionRateDay1: Float!
  retentionRateDay3: Float!
  retentionRateDay7: Float!

  bannerImpsDau: Float!
  interImpsDau: Float!
  rewardImpsDau: Float!
  aoaImpsDau: Float!
  mrecImpsDau: Float!
  audioImpsDau: Float!
  aoaAdmobImpsDau: Float!
  collapseAdmobImpsDau: Float!
  nativeAdmobImpsDau: Float!
  adaptiveAdmobImpsDau: Float!
  mrecAdmobImpsDau: Float!

  averageSession: Float!
  playtime: Float!

  note: String
  uaNote: String
  monetNote: String
  productNote: String
  versionNote: String

  metadata: GameMetricMetadata
}

type GameMetricMetadata {
  id: Int!

  note: String
  uaNote: String
  monetNote: String
  productNote: String
  versionNote: String
}

enum Operator {
  eq
  ne
  gt
  gte
  lt
  lte
  in
  nin
}

type GameReview {
  gameId: ID!
  date: Date!
  marketingNote: String
  productNote: String
}

type ConfigMapCollection {
  id: ID!
  items: [JSON!]!
}

type ConfigMap {
  id: ID!
  sole: JSON!
}

input AggregatePlaytimeWhere {
  activeDateFrom: Date!
  activeDateTo: Date!
  installDateFrom: Date
  installDateTo: Date
  gameId: ID!
  versions: [String!]!
}

type PlaytimeAggregation {
  engagementSessionCount: Float!
  playtimeSec: Float!
}

input GameRewardUsagesWhere {
  gameId: ID!
  versions: [String!]!
  dateFrom: Date!
  dateTo: Date!
}

type GameRewardUsages {
  collection: [GameRewardUsage!]!
  versions: [String!]!
}

type GameRewardUsage {
  world: Int!
  level: Int!
  location: String!
  useCount: Int!
}

input ProductMetricVersionsWhere {
  gameId: ID!
  dateFrom: Date!
  dateTo: Date!
}

type Versions {
  collection: [String!]!
}

input GameLevelDropsWhere {
  gameId: ID!
  versions: [String!]!
  dateFrom: Date
  dateTo: Date
  installDateFrom: Date
  installDateTo: Date
}

type GameLevelDrops {
  collection: [GameLevelDrop!]!
}

type GameLevelDrop {
  version: String!
  world: Int!
  level: Int!
  activeUserCount: Int!
  userDropCount: Int!
  userDropRate: Float!
  completionRate: Float!
  winRate: Float!
  playtimeSecPerUser: Float!
  overallUserDropRate: Float!
  attemptCountPerUser: Float!
}

input GameLevelDropVersionsWhere {
  gameId: ID!
}

type AccessControl {
  roles: [RoleAccessControl!]!
}

type RoleAccessControl {
  roleId: String!
  subject: String!
  permits: [String!]!
}

input AccessControlWhere {
  subject: String!
}

input AccessControlUpdateForm {
  roles: [AccessControlRoleInput!]!
}

input AccessControlRoleInput {
  id: ID!
  permits: [String!]
}

input AttributesWhere {
  gameId: ID
  modelName: String!
}

type Attributes {
  collection: [Attribute!]!
}

type Attribute {
  name: String!
  displayName: String!
  aggregate: String
  permission: String!
}

input V2GameMetricWhere {
  gameId: ID!
  dateFrom: Date
  dateTo: Date
}

type V2GameMetrics {
  collection: [V2GameMetric!]!
  pageInfo: PageInfo!
}

type V2GameMetric {
  id: ID!
  date: Date
  gameId: ID!

  paidInstalls: Int!
  organicInstalls: Int!
  organicPercentage: Float!
  totalInstalls: Int!

  cost: Float!
  cpi: Float!
  roas: Float!
  revenue: Float!
  profit: Float!
  dailyActiveUsers: Int!

  retentionRateDay1: Float!
  retentionRateDay3: Float!
  retentionRateDay7: Float!

  sessions: Float!
  playtime: Float!

  uaNote: String
  monetNote: String
  productNote: String
  versionNote: String
  note: String

  adPerformances: [AdPerformance!]!
}

type AdPerformance {
  impsDau: Float!
  ecpm: Float!
  arpDau: Float!
  adType: Int!
  revenue: Float!
  impressionCount: Int!
  dailyActiveUserCount: Int!
}

type AdTypes {
  collection: [AdType!]!
}

type AdType {
  id: Int!
  name: String!
  order: Int!
}

type DashboardNotifications {
  collection: [DashboardNotification!]!
}

type DashboardNotification {
  id: Int!
  message: String!
  isVisible: Boolean!
  isPinned: Boolean!
}

input GameRetentionRatesWhere {
  gameId: ID!
  dateFrom: Date!
  dateTo: Date!
  versions: [String!]!
}

type GameRetentionRates {
  collection: [GameRetentionRate!]!
}

type GameRetentionRate {
  date: Date!
  gameId: ID!
  newUsers: Int!
  day1: Float!
  day2: Float!
  day3: Float!
  day4: Float!
  day5: Float!
  day6: Float!
  day7: Float!
}

input GameNetworkRevenuesWhere {
  gameId: ID!
  dateFrom: Date!
  dateTo: Date!
}

type GameNetworkRevenues {
  collection: [GameNetworkRevenue!]!
  pageInfo: PageInfo!
}

type GameNetworkRevenue {
  date: Date!
  networkId: Int!
  revenue: Float!
  mediationRevenue: Float!
  varianceRate: Float!

  network: AdNetwork!
  metric: AgencyMetric!
}

input AggregateGameNetworkRevenueWhere {
  gameId: ID!
  dateFrom: Date
  dateTo: Date
}

type AdNetwork {
  id: Int!
  name: String!
  inputType: String!
}

type AdNetworks {
  collection: [AdNetwork!]!
}

input GameAgencyCostsWhere {
  gameId: ID!
  dateFrom: Date!
  dateTo: Date!
}

input AggregateGameAgencyCostWhere {
  gameId: ID!
  dateFrom: Date
  dateTo: Date
}

type GameAgencyCosts {
  collection: [GameAgencyCost!]!
  pageInfo: PageInfo!
}

type GameAgencyCost {
  date: Date!
  totalCost: Float!
  mediationCost: Float!
  varianceRate: Float!

  agencyId: Int!
  agency: AdAgency!

  metric: AgencyMetric!
}

# TODO: Rename to use for both agency and rev
type AgencyMetric {
  paidInstalls: Int!
  cost: Float!
  revenue: Float!
}

type AdAgency {
  id: Int!
  name: String!
}

input GameCreativeMetricsWhere {
  dateFrom: Date!
  dateTo: Date!
  gameId: ID!
}

type GameCreativeMetrics {
  collection: [GameCreativeMetric!]!
  pageInfo: PageInfo!
}

type GameCreativeMetric {
  id: Int!
  date: Date!

  campaign: String!
  adGroup: String!
  adGroupStartDate: Date!
  adSet: String!
  editor: String!
  isPlayable: Boolean!
  targetRoasRate: Float!
  preTaxCostAmount: Float!
  grossRevenueAmount: Float!
  clickCount: Int!
  impressionCount: Int!
  installCount: Int!

  roasRate: Float!
  cpi: Float!
  conversionRate: Float!
  clickthroughRate: Float!

  agency: AdAgency!
}

type SideMenus {
  collection: [SideMenu!]!
}

type SideMenu {
  name: String!
  icon: String!
  path: String!
  group: String!
}

type GameStudios {
  collection: [GameStudio!]!
}

type GameStudio {
  id: Int!
  name: String!
}

input GameStudioMetricsWhere {
  dateFrom: Date!
  dateTo: Date!
  studioId: Int!
}

type GameStudioMetrics {
  collection: [GameStudioMetric!]!
}

type GameStudioMetric {
  game: Game!
  gameId: ID!

  totalInstalls: Int!
  mmpCostAmount: Float!
  totalAgencyCost: Float!
  revenue: Float!
  profit: Float!
}

type Workflows {
  collection: [Workflow!]!
}

type UserPublicInfo {
  id: ID!
  fullName: String!
}

type WorkflowStep {
  assigneeId: ID!
  assignee: UserPublicInfo!
  action: String!
  name: String!
  alternateAction: String!
}

type Workflow {
  id: Int!
  name: String!
  roleId: String!
  steps: [WorkflowStep!]!
}

input WorkflowStepForm {
  assigneeId: ID!
  action: String!
  name: String!
  alternateAction: String!
}

input CreateWorkflowForm {
  name: String!
  roleId: String!
  steps: [WorkflowStepForm!]!
}

input UpdateWorkflowForm {
  id: Int!
  name: String
  steps: [WorkflowStepForm!]
}

type BudgetRequests {
  _debug: JSON!
  collection: [BudgetRequest!]!
  pageInfo: PageInfo!
}

input BudgetRequestsWhere {
  assigneeId: ID

  createdAt: Filter!
  stepId: Filter
  workflowId: Filter
  gameId: Filter
  lastAction: Filter
}

type BudgetRequest {
  id: Int!
  step: WorkflowStep!
  stepId: Int!
  workflowId: Int!
  workflow: Workflow!
  expirationDate: Date!
  createdAt: Date!
  amount: Float!
  gameId: ID!
  game: Game!
  createdById: ID!
  createdBy: UserPublicInfo!
  lastAction: String
  description: String
}

input CreateBudgetRequestForm {
  workflowId: Int!
  amount: Float!
  gameId: ID!
  expirationDate: Date!
  description: String
}

input UpdateBudgetRequestForm {
  id: Int!
  amount: Float!
  expirationDate: Date!
  description: String
}

input UpdateBudgetRequestStateForm {
  id: Int!
  lastAction: String
  stepId: Int!
}

input DeleteBudgetRequestsWhere {
  ids: [Int!]!
}

type WorkflowStepActions {
  collection: [WorkflowStepAction!]!
}

type WorkflowStepAction {
  action: String!
}

input AdMetricsWhere {
  gameId: ID!
  date: Filter!
  adRevGrossAmount: Filter
  adCostNonTaxAmount: Filter
  impressionCount: Filter
  clickCount: Filter
  installCount: Filter
  campaignId: Filter
  groupId: Filter
  adId: Filter
  agencyId: Filter
  countryCode: Filter
  cpi: Filter
  ctr: Filter
  cvr: Filter
  cpc: Filter
  ipm: Filter
  roas: Filter
}

type AdMetrics {
  collection: [AdMetric!]!

  ads: [Ad!]!
  adGroups: [AdGroup!]!
  campaigns: [Campaign!]!
  agencies: [AdAgency!]!
  viewPreset: ViewPreset!
}

type Ad {
  id: ID!
  name: String!

  campaignId: ID!
  campaign: Campaign!
  groupId: ID!
  group: AdGroup!
}

type AdGroup {
  id: ID!
  name: String!
}

type Campaign {
  id: ID!
  name: String!

  agencyId: Int!
  agency: AdAgency
}

type AdMetric {
  date: Date
  adId: String
  groupId: String
  campaignId: String
  countryCode: String
  agencyId: Int

  adRevGrossAmount: Float!
  adRevNthDayGrossAmounts: JSON!
  adCostNonTaxAmount: Float!
  impressionCount: Float!
  clickCount: Float!
  installCount: Float!
  cpi: Float!
  ctr: Float!
  cvr: Float!
  cpc: Float!
  ipm: Float!
  roas: Float!
  roasNthDayRates: JSON!
  sessionCount: Int!
  sessionNthDayCounts: JSON!
  dailyActiveUserCount: Float!
  activeUserNthDayCounts: JSON!
  retentionRate: Float!
  retentionNthDayRates: JSON!
}

scalar FilterValue

input Filter {
  operator: FilterOperator!
  values: [FilterValue!]!
}

input ViewPresetsWhere {
  pageId: String!
}

type ViewPresets {
  collection: [ViewPreset!]!
}

type ViewPreset {
  id: Int!
  name: String!
  attributes: [ViewPresetAttribute!]!
}

type ViewPresetAttribute {
  name: String!
  isCohort: Boolean!
  cohortDays: [Int!]!
}

input ViewPresetAttributeForm {
  name: String!
  cohortDays: [Int!]!
}

input CreateViewPresetForm {
  name: String!
  pageId: String!
  attributes: [ViewPresetAttributeForm!]!
}

input DeleteViewPresetsWhere {
  ids: [Int!]!
}

input UpdateProfileForm {
  password: String!
  passwordConfirmation: String!
}

input Preset {
  viewPresetId: Int
}

type AdmobGames {
  collection: [JSON!]!
}

input AdmobMetricsWhere {
  gameId: String!
  dateFrom: Date!
  dateTo: Date!
  formats: [String!]!
}

type AdmobMetrics {
  mediation: [JSON!]!
  network: [JSON!]!
}

input FirebaseExperimentsWhere {
  gameId: ID!
}

type FirebaseExperiments {
  collection: [FirebaseExperiment!]!
}

type FirebaseExperiment {
  name: String!
}

input FirebaseMetricsWhere {
  gameId: ID!
  experiment: Filter!
  date: Filter!
  countryCode: Filter
  variantId: Filter
  networkId: Filter
  mediationId: Filter
}

type FirebaseMetrics {
  collection: [FirebaseMetric!]!
  viewPreset: ViewPreset!
  variants: [FirebaseVersionVariant!]!
}

type FirebaseVersionVariant {
  id: String!
  experiment: String!
  name: String!
}

type FirebaseMetric {
  date: Date
  variantId: String
  version: String
  countryCode: String

  sessionCount: Int!
  sessionNthDayCounts: JSON!
  dailyActiveUserCount: Float!
  activeUserNthDayCounts: JSON!
  playtimeMsec: BigInt!
  playtimeNthDayMsecs: JSON!
  retentionNthDayRates: JSON!
  installCount: Int!
  sessionCountPerActiveUser: Float!
  retentionRate: Float!
  impressionNthDayCounts: JSON!
  adRevNthDayGrossAmounts: JSON!
  ads: [FirebaseAdMetric!]!
}

type FirebaseAdMetric {
  adTypeCategory: AdTypeCategory!

  adRevGrossAmount: Float!
  impressionCount: Float!
  impressionCountPerActiveUser: Float!
  adRevGrossAmountPerActiveUser: Float!
}

type Mediations {
  collection: [Mediation!]!
}

type Mediation {
  id: String!
  name: String!
}

input NetworksWhere {
  category: AdNetworkCategory
}

type Networks {
  collection: [Network!]!
}

type Network {
  id: String!
  name: String!
}

enum AdTypeCategory {
  BANNER
  INTERSTITIAL
  REWARD
  NATIVE
  AUDIO
  APP_OPEN
  COLLAPSIBLE_BANNER
  MREC
  UNKNOWN
}

input ToolPermissionForm {
  tool: String!
  action: String!
}

input CreateUserForm {
  email: String!
  fullName: String!
  kind: UserKind!
  toolPermissions: [ToolPermissionForm!]!
  password: String
  hasPassword: Boolean!
  note: String
}

input UpdateUserForm {
  fullName: String
  kind: UserKind
  toolPermissions: [ToolPermissionForm!]
  password: String
  hasPassword: Boolean
  note: String
}

input UpdateUserWhere {
  id: ID!
}

input DeleteUsersWhere {
  ids: [ID!]!
}

input UpdateGameRolesArgs {
  forms: [UpdateGameRolesGame!]!
}

input UpdateGameRolesGame {
  gameId: ID!
  roles: [UpdateGameRole!]!
}

input UpdateGameRole {
  id: ID!
  users: [String!]
}

input DeleteTeamWhere {
  id: Int!
}

type GameRoleMembership {
  id: ID!
  roleId: String!
  storeId: ID!
  users: [GameRoleMembershipUser!]!
}

type GameRoleMembershipUser {
  email: String!
}

type Team {
  id: Int!
  name: String!
  roleId: String!
  leader: User
  members: [User!]!
}

type Teams {
  collection: [Team!]!
}

input CreateTeamForm {
  name: String!
  roleId: String!
  leaderEmail: String!
  memberEmails: [String!]!
}

input UpdateTeamForm {
  name: String!
  memberEmails: [String!]!
  leaderEmail: String
}

input UpdateTeamWhere {
  id: Int!
}

input UpdateConfigMapsWhere {
  scopes: [String!]!
}

type GameRoleUser {
  email: String!
}

type GameRole {
  id: ID!
  users: [GameRoleUser!]!
}

type GameRoles {
  id: ID!
  roles: [GameRole!]!
}

type GamesRoles {
  collection: [GameRoles!]!
}

input ReleaseMetricsWhere {
  gameId: String!
  date: Filter!
  countryCode: Filter
  version: Filter
}

type ReleaseMetrics {
  collection: [ReleaseMetric!]!
  viewPreset: ViewPreset!
}

type ReleaseMetric {
  date: Date
  version: String
  countryCode: String

  sessionCount: Float
  activeUserCount: Float
  dailyActiveUserCount: Float
  playtimeMsec: BigInt
  installCount: Float
  sessionCountPerActiveUser: Float
  retentionRate: Float
  adRevGrossAmount: Float
  impressionCount: Float
  adRevGrossAmountPerActiveUser: Float
  impressionCountPerActiveUser: Float
  activeUserNthDayCounts: JSON!
  sessionNthDayCounts: JSON!
  retentionNthDayRates: JSON!
  playtimeNthDayMsecs: JSON!
  lifetimeNthDayValues: JSON!
  adRevNthDayGrossAmounts: JSON!
  impressionNthDayCounts: JSON!
  ads: [ReleaseAdMetric!]
}

type ReleaseAdMetric {
  adTypeCategory: AdTypeCategory

  adRevGrossAmount: Float
  impressionCount: Float
  impressionCountPerActiveUser: Float
  adRevGrossAmountPerActiveUser: Float
  adRevNthDayGrossAmounts: JSON
  impressionNthDayCounts: JSON
}

type ReleaseVersions {
  collection: [ReleaseVersion!]!
}

type ReleaseVersion {
  version: String!
}

input ReleaseVersionsWhere {
  gameId: ID!
}

input UpdateGameMetricWhere {
  gameId: ID!
  date: String!
}

input UpdateGameMetricForm {
  override: UpdateGameMetricOverrideForm!
  metadata: UpdateGameMetricMetadataForm!
}

input UpdateGameMetricOverrideForm {
  revenue: Float
  cost: Float
}

input UpdateGameMetricMetadataForm {
  uaNote: String
  monetNote: String
  productNote: String
  versionNote: String
  note: String
}

input MetabaseChartWhere {
  id: String!
  params: [MetabaseChartParam!]!
}

input MetabaseChartParam {
  name: String!
  value: JSON!
}

type MetabaseChart {
  url: String!
}

input GameCostsWhere {
  gameId: ID!
  dateFrom: Date!
  dateTo: Date!
}

type GameCosts {
  collection: [GameCost!]!
  meta: GameCostsMeta
}

type GameCostsMeta {
  pageInfo: PageInfo!
  aggregation: GameCostsAggregation
}

type GameCostsAggregation {
  mmpAmount: Float
  totalAmount: Float
  preTaxAmount: Float
}

type GameCost {
  id: ID!
  type: String!
  date: Date!
  network: AdNetwork!

  mmpAmount: Float!
  preTaxAmount: Float!
  totalAmount: Float!
  taxAmount: Float!
}

input UpdateGameCostWhere {
  date: Date!
  gameId: ID!
  networkId: Int!
}

input UpdateGameCostForm {
  preTaxAmount: Float
}

input AggregateGameCostWhere {
  gameId: ID!
  dateFrom: Date!
  dateTo: Date!
}

input FirebaseVersionVariantsWhere {
  gameId: ID!
  experiment: Filter
}

type FirebaseVersionVariants {
  collection: [FirebaseVersionVariant!]!
}

type FirebaseVersionVariant {
  id: String!
  name: String!
  experiment: String!
}
