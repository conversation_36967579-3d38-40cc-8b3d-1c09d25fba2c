import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';

import { Context } from './context.js';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  BigInt: { input: bigint; output: bigint; }
  Date: { input: any; output: any; }
  FilterValue: { input: any; output: any; }
  JSON: { input: any; output: any; }
  UUID: { input: any; output: any; }
  Void: { input: any; output: any; }
};

export type AccessControl = {
  __typename?: 'AccessControl';
  roles: Array<RoleAccessControl>;
};

export type AccessControlRoleInput = {
  id: Scalars['ID']['input'];
  permits?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type AccessControlUpdateForm = {
  roles: Array<AccessControlRoleInput>;
};

export type AccessControlWhere = {
  subject: Scalars['String']['input'];
};

export type Ad = {
  __typename?: 'Ad';
  campaign: Campaign;
  campaignId: Scalars['ID']['output'];
  group: AdGroup;
  groupId: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type AdAgency = {
  __typename?: 'AdAgency';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type AdGroup = {
  __typename?: 'AdGroup';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type AdMetric = {
  __typename?: 'AdMetric';
  activeUserNthDayCounts: Scalars['JSON']['output'];
  adCostNonTaxAmount: Scalars['Float']['output'];
  adId?: Maybe<Scalars['String']['output']>;
  adRevGrossAmount: Scalars['Float']['output'];
  adRevNthDayGrossAmounts: Scalars['JSON']['output'];
  agencyId?: Maybe<Scalars['Int']['output']>;
  campaignId?: Maybe<Scalars['String']['output']>;
  clickCount: Scalars['Float']['output'];
  countryCode?: Maybe<Scalars['String']['output']>;
  cpc: Scalars['Float']['output'];
  cpi: Scalars['Float']['output'];
  ctr: Scalars['Float']['output'];
  cvr: Scalars['Float']['output'];
  dailyActiveUserCount: Scalars['Float']['output'];
  date?: Maybe<Scalars['Date']['output']>;
  groupId?: Maybe<Scalars['String']['output']>;
  impressionCount: Scalars['Float']['output'];
  installCount: Scalars['Float']['output'];
  ipm: Scalars['Float']['output'];
  retentionNthDayRates: Scalars['JSON']['output'];
  retentionRate: Scalars['Float']['output'];
  roas: Scalars['Float']['output'];
  roasNthDayRates: Scalars['JSON']['output'];
  sessionCount: Scalars['Int']['output'];
  sessionNthDayCounts: Scalars['JSON']['output'];
};

export type AdMetrics = {
  __typename?: 'AdMetrics';
  adGroups: Array<AdGroup>;
  ads: Array<Ad>;
  agencies: Array<AdAgency>;
  campaigns: Array<Campaign>;
  collection: Array<AdMetric>;
  viewPreset: ViewPreset;
};

export type AdMetricsWhere = {
  adCostNonTaxAmount?: InputMaybe<Filter>;
  adId?: InputMaybe<Filter>;
  adRevGrossAmount?: InputMaybe<Filter>;
  agencyId?: InputMaybe<Filter>;
  campaignId?: InputMaybe<Filter>;
  clickCount?: InputMaybe<Filter>;
  countryCode?: InputMaybe<Filter>;
  cpc?: InputMaybe<Filter>;
  cpi?: InputMaybe<Filter>;
  ctr?: InputMaybe<Filter>;
  cvr?: InputMaybe<Filter>;
  date: Filter;
  gameId: Scalars['ID']['input'];
  groupId?: InputMaybe<Filter>;
  impressionCount?: InputMaybe<Filter>;
  installCount?: InputMaybe<Filter>;
  ipm?: InputMaybe<Filter>;
  roas?: InputMaybe<Filter>;
};

export type AdNetwork = {
  __typename?: 'AdNetwork';
  id: Scalars['Int']['output'];
  inputType: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export enum AdNetworkCategory {
  Appsflyer = 'APPSFLYER',
  Firebase = 'FIREBASE'
}

export type AdNetworks = {
  __typename?: 'AdNetworks';
  collection: Array<AdNetwork>;
};

export type AdPerformance = {
  __typename?: 'AdPerformance';
  adType: Scalars['Int']['output'];
  arpDau: Scalars['Float']['output'];
  dailyActiveUserCount: Scalars['Int']['output'];
  ecpm: Scalars['Float']['output'];
  impressionCount: Scalars['Int']['output'];
  impsDau: Scalars['Float']['output'];
  revenue: Scalars['Float']['output'];
};

export type AdType = {
  __typename?: 'AdType';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  order: Scalars['Int']['output'];
};

export enum AdTypeCategory {
  AppOpen = 'APP_OPEN',
  Audio = 'AUDIO',
  Banner = 'BANNER',
  CollapsibleBanner = 'COLLAPSIBLE_BANNER',
  Interstitial = 'INTERSTITIAL',
  Mrec = 'MREC',
  Native = 'NATIVE',
  Reward = 'REWARD',
  Unknown = 'UNKNOWN'
}

export type AdTypes = {
  __typename?: 'AdTypes';
  collection: Array<AdType>;
};

export type AdmobGames = {
  __typename?: 'AdmobGames';
  collection: Array<Scalars['JSON']['output']>;
};

export type AdmobMetrics = {
  __typename?: 'AdmobMetrics';
  mediation: Array<Scalars['JSON']['output']>;
  network: Array<Scalars['JSON']['output']>;
};

export type AdmobMetricsWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  formats: Array<Scalars['String']['input']>;
  gameId: Scalars['String']['input'];
};

export type AgencyMetric = {
  __typename?: 'AgencyMetric';
  cost: Scalars['Float']['output'];
  paidInstalls: Scalars['Int']['output'];
  revenue: Scalars['Float']['output'];
};

export type AggregateGameAgencyCostWhere = {
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  gameId: Scalars['ID']['input'];
};

export type AggregateGameCostWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};

export type AggregateGameNetworkRevenueWhere = {
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  gameId: Scalars['ID']['input'];
};

export type AggregatePlaytimeWhere = {
  activeDateFrom: Scalars['Date']['input'];
  activeDateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
  installDateFrom?: InputMaybe<Scalars['Date']['input']>;
  installDateTo?: InputMaybe<Scalars['Date']['input']>;
  versions: Array<Scalars['String']['input']>;
};

export type Attribute = {
  __typename?: 'Attribute';
  aggregate?: Maybe<Scalars['String']['output']>;
  displayName: Scalars['String']['output'];
  name: Scalars['String']['output'];
  permission: Scalars['String']['output'];
};

export type Attributes = {
  __typename?: 'Attributes';
  collection: Array<Attribute>;
};

export type AttributesWhere = {
  gameId?: InputMaybe<Scalars['ID']['input']>;
  modelName: Scalars['String']['input'];
};

export type BudgetRequest = {
  __typename?: 'BudgetRequest';
  amount: Scalars['Float']['output'];
  createdAt: Scalars['Date']['output'];
  createdBy: UserPublicInfo;
  createdById: Scalars['ID']['output'];
  description?: Maybe<Scalars['String']['output']>;
  expirationDate: Scalars['Date']['output'];
  game: Game;
  gameId: Scalars['ID']['output'];
  id: Scalars['Int']['output'];
  lastAction?: Maybe<Scalars['String']['output']>;
  step: WorkflowStep;
  stepId: Scalars['Int']['output'];
  workflow: Workflow;
  workflowId: Scalars['Int']['output'];
};

export type BudgetRequests = {
  __typename?: 'BudgetRequests';
  _debug: Scalars['JSON']['output'];
  collection: Array<BudgetRequest>;
  pageInfo: PageInfo;
};

export type BudgetRequestsWhere = {
  assigneeId?: InputMaybe<Scalars['ID']['input']>;
  createdAt: Filter;
  gameId?: InputMaybe<Filter>;
  lastAction?: InputMaybe<Filter>;
  stepId?: InputMaybe<Filter>;
  workflowId?: InputMaybe<Filter>;
};

export type Campaign = {
  __typename?: 'Campaign';
  agency?: Maybe<AdAgency>;
  agencyId: Scalars['Int']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type ConfigMap = {
  __typename?: 'ConfigMap';
  id: Scalars['ID']['output'];
  sole: Scalars['JSON']['output'];
};

export type ConfigMapCollection = {
  __typename?: 'ConfigMapCollection';
  id: Scalars['ID']['output'];
  items: Array<Scalars['JSON']['output']>;
};

export type CreateBudgetRequestForm = {
  amount: Scalars['Float']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  expirationDate: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
  workflowId: Scalars['Int']['input'];
};

export type CreateTeamForm = {
  leaderEmail: Scalars['String']['input'];
  memberEmails: Array<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  roleId: Scalars['String']['input'];
};

export type CreateUserForm = {
  email: Scalars['String']['input'];
  fullName: Scalars['String']['input'];
  hasPassword: Scalars['Boolean']['input'];
  kind: UserKind;
  note?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  toolPermissions: Array<ToolPermissionForm>;
};

export type CreateViewPresetForm = {
  attributes: Array<ViewPresetAttributeForm>;
  name: Scalars['String']['input'];
  pageId: Scalars['String']['input'];
};

export type CreateWorkflowForm = {
  name: Scalars['String']['input'];
  roleId: Scalars['String']['input'];
  steps: Array<WorkflowStepForm>;
};

export type DashboardNotification = {
  __typename?: 'DashboardNotification';
  id: Scalars['Int']['output'];
  isPinned: Scalars['Boolean']['output'];
  isVisible: Scalars['Boolean']['output'];
  message: Scalars['String']['output'];
};

export type DashboardNotifications = {
  __typename?: 'DashboardNotifications';
  collection: Array<DashboardNotification>;
};

export enum DataSource {
  Origin = 'ORIGIN',
  Snapshot = 'SNAPSHOT'
}

export type DeleteBudgetRequestsWhere = {
  ids: Array<Scalars['Int']['input']>;
};

export type DeleteTeamWhere = {
  id: Scalars['Int']['input'];
};

export type DeleteUsersWhere = {
  ids: Array<Scalars['ID']['input']>;
};

export type DeleteViewPresetsWhere = {
  ids: Array<Scalars['Int']['input']>;
};

export type Filter = {
  operator: FilterOperator;
  values: Array<Scalars['FilterValue']['input']>;
};

export enum FilterOperator {
  Between = 'BETWEEN',
  Eq = 'EQ',
  Gt = 'GT',
  Gte = 'GTE',
  In = 'IN',
  Lt = 'LT',
  Lte = 'LTE',
  Ne = 'NE'
}

export type FirebaseAdMetric = {
  __typename?: 'FirebaseAdMetric';
  adRevGrossAmount: Scalars['Float']['output'];
  adRevGrossAmountPerActiveUser: Scalars['Float']['output'];
  adTypeCategory: AdTypeCategory;
  impressionCount: Scalars['Float']['output'];
  impressionCountPerActiveUser: Scalars['Float']['output'];
};

export type FirebaseExperiment = {
  __typename?: 'FirebaseExperiment';
  name: Scalars['String']['output'];
};

export type FirebaseExperiments = {
  __typename?: 'FirebaseExperiments';
  collection: Array<FirebaseExperiment>;
};

export type FirebaseExperimentsWhere = {
  gameId: Scalars['ID']['input'];
};

export type FirebaseMetric = {
  __typename?: 'FirebaseMetric';
  activeUserNthDayCounts: Scalars['JSON']['output'];
  adRevNthDayGrossAmounts: Scalars['JSON']['output'];
  ads: Array<FirebaseAdMetric>;
  countryCode?: Maybe<Scalars['String']['output']>;
  dailyActiveUserCount: Scalars['Float']['output'];
  date?: Maybe<Scalars['Date']['output']>;
  impressionNthDayCounts: Scalars['JSON']['output'];
  installCount: Scalars['Int']['output'];
  playtimeMsec: Scalars['BigInt']['output'];
  playtimeNthDayMsecs: Scalars['JSON']['output'];
  retentionNthDayRates: Scalars['JSON']['output'];
  retentionRate: Scalars['Float']['output'];
  sessionCount: Scalars['Int']['output'];
  sessionCountPerActiveUser: Scalars['Float']['output'];
  sessionNthDayCounts: Scalars['JSON']['output'];
  variantId?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['String']['output']>;
};

export type FirebaseMetrics = {
  __typename?: 'FirebaseMetrics';
  collection: Array<FirebaseMetric>;
  variants: Array<FirebaseVersionVariant>;
  viewPreset: ViewPreset;
};

export type FirebaseMetricsWhere = {
  countryCode?: InputMaybe<Filter>;
  date: Filter;
  experiment: Filter;
  gameId: Scalars['ID']['input'];
  mediationId?: InputMaybe<Filter>;
  networkId?: InputMaybe<Filter>;
  variantId?: InputMaybe<Filter>;
};

export type FirebaseVersionVariant = {
  __typename?: 'FirebaseVersionVariant';
  experiment: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type FirebaseVersionVariants = {
  __typename?: 'FirebaseVersionVariants';
  collection: Array<FirebaseVersionVariant>;
};

export type FirebaseVersionVariantsWhere = {
  experiment?: InputMaybe<Filter>;
  gameId: Scalars['ID']['input'];
};

export type Game = {
  __typename?: 'Game';
  id: Scalars['ID']['output'];
  isActive: Scalars['Boolean']['output'];
  isInhouse: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  packageName: Scalars['String']['output'];
  platform: Scalars['String']['output'];
};

export type GameAgencyCost = {
  __typename?: 'GameAgencyCost';
  agency: AdAgency;
  agencyId: Scalars['Int']['output'];
  date: Scalars['Date']['output'];
  mediationCost: Scalars['Float']['output'];
  metric: AgencyMetric;
  totalCost: Scalars['Float']['output'];
  varianceRate: Scalars['Float']['output'];
};

export type GameAgencyCosts = {
  __typename?: 'GameAgencyCosts';
  collection: Array<GameAgencyCost>;
  pageInfo: PageInfo;
};

export type GameAgencyCostsWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};

export type GameCost = {
  __typename?: 'GameCost';
  date: Scalars['Date']['output'];
  id: Scalars['ID']['output'];
  mmpAmount: Scalars['Float']['output'];
  network: AdNetwork;
  preTaxAmount: Scalars['Float']['output'];
  taxAmount: Scalars['Float']['output'];
  totalAmount: Scalars['Float']['output'];
  type: Scalars['String']['output'];
};

export type GameCosts = {
  __typename?: 'GameCosts';
  collection: Array<GameCost>;
  meta?: Maybe<GameCostsMeta>;
};

export type GameCostsAggregation = {
  __typename?: 'GameCostsAggregation';
  mmpAmount?: Maybe<Scalars['Float']['output']>;
  preTaxAmount?: Maybe<Scalars['Float']['output']>;
  totalAmount?: Maybe<Scalars['Float']['output']>;
};

export type GameCostsMeta = {
  __typename?: 'GameCostsMeta';
  aggregation?: Maybe<GameCostsAggregation>;
  pageInfo: PageInfo;
};

export type GameCostsWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};

export type GameCreativeMetric = {
  __typename?: 'GameCreativeMetric';
  adGroup: Scalars['String']['output'];
  adGroupStartDate: Scalars['Date']['output'];
  adSet: Scalars['String']['output'];
  agency: AdAgency;
  campaign: Scalars['String']['output'];
  clickCount: Scalars['Int']['output'];
  clickthroughRate: Scalars['Float']['output'];
  conversionRate: Scalars['Float']['output'];
  cpi: Scalars['Float']['output'];
  date: Scalars['Date']['output'];
  editor: Scalars['String']['output'];
  grossRevenueAmount: Scalars['Float']['output'];
  id: Scalars['Int']['output'];
  impressionCount: Scalars['Int']['output'];
  installCount: Scalars['Int']['output'];
  isPlayable: Scalars['Boolean']['output'];
  preTaxCostAmount: Scalars['Float']['output'];
  roasRate: Scalars['Float']['output'];
  targetRoasRate: Scalars['Float']['output'];
};

export type GameCreativeMetrics = {
  __typename?: 'GameCreativeMetrics';
  collection: Array<GameCreativeMetric>;
  pageInfo: PageInfo;
};

export type GameCreativeMetricsWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};

export type GameLevelDrop = {
  __typename?: 'GameLevelDrop';
  activeUserCount: Scalars['Int']['output'];
  attemptCountPerUser: Scalars['Float']['output'];
  completionRate: Scalars['Float']['output'];
  level: Scalars['Int']['output'];
  overallUserDropRate: Scalars['Float']['output'];
  playtimeSecPerUser: Scalars['Float']['output'];
  userDropCount: Scalars['Int']['output'];
  userDropRate: Scalars['Float']['output'];
  version: Scalars['String']['output'];
  winRate: Scalars['Float']['output'];
  world: Scalars['Int']['output'];
};

export type GameLevelDropVersionsWhere = {
  gameId: Scalars['ID']['input'];
};

export type GameLevelDrops = {
  __typename?: 'GameLevelDrops';
  collection: Array<GameLevelDrop>;
};

export type GameLevelDropsWhere = {
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  gameId: Scalars['ID']['input'];
  installDateFrom?: InputMaybe<Scalars['Date']['input']>;
  installDateTo?: InputMaybe<Scalars['Date']['input']>;
  versions: Array<Scalars['String']['input']>;
};

export type GameMetric = {
  __typename?: 'GameMetric';
  adaptiveAdmobImpsDau: Scalars['Float']['output'];
  aoaAdmobImpsDau: Scalars['Float']['output'];
  aoaImpsDau: Scalars['Float']['output'];
  audioImpsDau: Scalars['Float']['output'];
  averageSession: Scalars['Float']['output'];
  bannerImpsDau: Scalars['Float']['output'];
  collapseAdmobImpsDau: Scalars['Float']['output'];
  cost: Scalars['Float']['output'];
  cpi: Scalars['Float']['output'];
  date?: Maybe<Scalars['Date']['output']>;
  gameId: Scalars['ID']['output'];
  id: Scalars['Int']['output'];
  interImpsDau: Scalars['Float']['output'];
  metadata?: Maybe<GameMetricMetadata>;
  monetNote?: Maybe<Scalars['String']['output']>;
  mrecAdmobImpsDau: Scalars['Float']['output'];
  mrecImpsDau: Scalars['Float']['output'];
  nativeAdmobImpsDau: Scalars['Float']['output'];
  note?: Maybe<Scalars['String']['output']>;
  organicInstalls: Scalars['Int']['output'];
  organicPercentage: Scalars['Float']['output'];
  paidInstalls: Scalars['Int']['output'];
  playtime: Scalars['Float']['output'];
  productNote?: Maybe<Scalars['String']['output']>;
  profit: Scalars['Float']['output'];
  retentionRateDay1: Scalars['Float']['output'];
  retentionRateDay3: Scalars['Float']['output'];
  retentionRateDay7: Scalars['Float']['output'];
  revenue: Scalars['Float']['output'];
  rewardImpsDau: Scalars['Float']['output'];
  roas: Scalars['Float']['output'];
  totalInstalls: Scalars['Int']['output'];
  uaNote?: Maybe<Scalars['String']['output']>;
  versionNote?: Maybe<Scalars['String']['output']>;
};

export type GameMetricMetadata = {
  __typename?: 'GameMetricMetadata';
  id: Scalars['Int']['output'];
  monetNote?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  productNote?: Maybe<Scalars['String']['output']>;
  uaNote?: Maybe<Scalars['String']['output']>;
  versionNote?: Maybe<Scalars['String']['output']>;
};

export type GameMetricWhere = {
  dateGte?: InputMaybe<Scalars['Date']['input']>;
  dateLte?: InputMaybe<Scalars['Date']['input']>;
  gameId: Scalars['ID']['input'];
};

export type GameMetrics = {
  __typename?: 'GameMetrics';
  collection: Array<GameMetric>;
  pageInfo: PageInfo;
};

export type GameNetworkRevenue = {
  __typename?: 'GameNetworkRevenue';
  date: Scalars['Date']['output'];
  mediationRevenue: Scalars['Float']['output'];
  metric: AgencyMetric;
  network: AdNetwork;
  networkId: Scalars['Int']['output'];
  revenue: Scalars['Float']['output'];
  varianceRate: Scalars['Float']['output'];
};

export type GameNetworkRevenues = {
  __typename?: 'GameNetworkRevenues';
  collection: Array<GameNetworkRevenue>;
  pageInfo: PageInfo;
};

export type GameNetworkRevenuesWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};

export type GamePerformanceCriteria = {
  __typename?: 'GamePerformanceCriteria';
  conclusion: GamePerformanceCriteriaConclusion;
  metrics: Scalars['JSON']['output'];
};

export enum GamePerformanceCriteriaConclusion {
  Drop = 'Drop',
  Fail = 'Fail',
  Pass = 'Pass'
}

export type GamePerformanceSetting = {
  __typename?: 'GamePerformanceSetting';
  criterias: Array<GamePerformanceCriteria>;
  gameId: Scalars['ID']['output'];
};

export type GameRetentionRate = {
  __typename?: 'GameRetentionRate';
  date: Scalars['Date']['output'];
  day1: Scalars['Float']['output'];
  day2: Scalars['Float']['output'];
  day3: Scalars['Float']['output'];
  day4: Scalars['Float']['output'];
  day5: Scalars['Float']['output'];
  day6: Scalars['Float']['output'];
  day7: Scalars['Float']['output'];
  gameId: Scalars['ID']['output'];
  newUsers: Scalars['Int']['output'];
};

export type GameRetentionRates = {
  __typename?: 'GameRetentionRates';
  collection: Array<GameRetentionRate>;
};

export type GameRetentionRatesWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
  versions: Array<Scalars['String']['input']>;
};

export type GameReview = {
  __typename?: 'GameReview';
  date: Scalars['Date']['output'];
  gameId: Scalars['ID']['output'];
  marketingNote?: Maybe<Scalars['String']['output']>;
  productNote?: Maybe<Scalars['String']['output']>;
};

export type GameRewardUsage = {
  __typename?: 'GameRewardUsage';
  level: Scalars['Int']['output'];
  location: Scalars['String']['output'];
  useCount: Scalars['Int']['output'];
  world: Scalars['Int']['output'];
};

export type GameRewardUsages = {
  __typename?: 'GameRewardUsages';
  collection: Array<GameRewardUsage>;
  versions: Array<Scalars['String']['output']>;
};

export type GameRewardUsagesWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
  versions: Array<Scalars['String']['input']>;
};

export type GameRole = {
  __typename?: 'GameRole';
  id: Scalars['ID']['output'];
  users: Array<GameRoleUser>;
};

export type GameRoleMembership = {
  __typename?: 'GameRoleMembership';
  id: Scalars['ID']['output'];
  roleId: Scalars['String']['output'];
  storeId: Scalars['ID']['output'];
  users: Array<GameRoleMembershipUser>;
};

export type GameRoleMembershipUser = {
  __typename?: 'GameRoleMembershipUser';
  email: Scalars['String']['output'];
};

export type GameRoleUser = {
  __typename?: 'GameRoleUser';
  email: Scalars['String']['output'];
};

export type GameRoles = {
  __typename?: 'GameRoles';
  id: Scalars['ID']['output'];
  roles: Array<GameRole>;
};

export type GameStudio = {
  __typename?: 'GameStudio';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type GameStudioMetric = {
  __typename?: 'GameStudioMetric';
  game: Game;
  gameId: Scalars['ID']['output'];
  mmpCostAmount: Scalars['Float']['output'];
  profit: Scalars['Float']['output'];
  revenue: Scalars['Float']['output'];
  totalAgencyCost: Scalars['Float']['output'];
  totalInstalls: Scalars['Int']['output'];
};

export type GameStudioMetrics = {
  __typename?: 'GameStudioMetrics';
  collection: Array<GameStudioMetric>;
};

export type GameStudioMetricsWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  studioId: Scalars['Int']['input'];
};

export type GameStudios = {
  __typename?: 'GameStudios';
  collection: Array<GameStudio>;
};

export type Games = {
  __typename?: 'Games';
  collection: Array<Game>;
  pageInfo: PageInfo;
};

export type GamesRoles = {
  __typename?: 'GamesRoles';
  collection: Array<GameRoles>;
};

export type GamesWhere = {
  keyword?: InputMaybe<Scalars['String']['input']>;
  userIds?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type Group = {
  fields: Array<Scalars['String']['input']>;
};

export type Mediation = {
  __typename?: 'Mediation';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type Mediations = {
  __typename?: 'Mediations';
  collection: Array<Mediation>;
};

export type MetabaseChart = {
  __typename?: 'MetabaseChart';
  url: Scalars['String']['output'];
};

export type MetabaseChartParam = {
  name: Scalars['String']['input'];
  value: Scalars['JSON']['input'];
};

export type MetabaseChartWhere = {
  id: Scalars['String']['input'];
  params: Array<MetabaseChartParam>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createBudgetRequest: BudgetRequest;
  createTeam: Team;
  createUser: User;
  createViewPreset: ViewPreset;
  createWorkflow: Workflow;
  deleteBudgetRequests?: Maybe<Scalars['Void']['output']>;
  deleteTeam?: Maybe<Scalars['Void']['output']>;
  deleteUsers?: Maybe<Scalars['Void']['output']>;
  deleteViewPresets?: Maybe<Scalars['Void']['output']>;
  updateAccessControl: AccessControl;
  updateBudgetRequests: Array<BudgetRequest>;
  updateBudgetRequestsState: Array<BudgetRequest>;
  updateConfigMaps?: Maybe<Scalars['Void']['output']>;
  updateGameCost?: Maybe<Scalars['Void']['output']>;
  updateGameMetric: GameMetric;
  updateGameRoles: GamesRoles;
  updateProfile: User;
  updateTeam: Team;
  updateUser: User;
  updateWorkflow: Workflow;
};


export type MutationCreateBudgetRequestArgs = {
  form: CreateBudgetRequestForm;
};


export type MutationCreateTeamArgs = {
  form: CreateTeamForm;
};


export type MutationCreateUserArgs = {
  form: CreateUserForm;
};


export type MutationCreateViewPresetArgs = {
  form: CreateViewPresetForm;
};


export type MutationCreateWorkflowArgs = {
  form: CreateWorkflowForm;
};


export type MutationDeleteBudgetRequestsArgs = {
  where: DeleteBudgetRequestsWhere;
};


export type MutationDeleteTeamArgs = {
  where: DeleteTeamWhere;
};


export type MutationDeleteUsersArgs = {
  where: DeleteUsersWhere;
};


export type MutationDeleteViewPresetsArgs = {
  where: DeleteViewPresetsWhere;
};


export type MutationUpdateAccessControlArgs = {
  form: AccessControlUpdateForm;
  where: AccessControlWhere;
};


export type MutationUpdateBudgetRequestsArgs = {
  forms: Array<UpdateBudgetRequestForm>;
};


export type MutationUpdateBudgetRequestsStateArgs = {
  forms: Array<UpdateBudgetRequestStateForm>;
};


export type MutationUpdateConfigMapsArgs = {
  where: UpdateConfigMapsWhere;
};


export type MutationUpdateGameCostArgs = {
  form: UpdateGameCostForm;
  where: UpdateGameCostWhere;
};


export type MutationUpdateGameMetricArgs = {
  form: UpdateGameMetricForm;
  where: UpdateGameMetricWhere;
};


export type MutationUpdateGameRolesArgs = {
  forms: Array<UpdateGameRolesGame>;
};


export type MutationUpdateProfileArgs = {
  form: UpdateProfileForm;
};


export type MutationUpdateTeamArgs = {
  form: UpdateTeamForm;
  where: UpdateTeamWhere;
};


export type MutationUpdateUserArgs = {
  form: UpdateUserForm;
  where: UpdateUserWhere;
};


export type MutationUpdateWorkflowArgs = {
  form: UpdateWorkflowForm;
};

export type Network = {
  __typename?: 'Network';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type Networks = {
  __typename?: 'Networks';
  collection: Array<Network>;
};

export type NetworksWhere = {
  category?: InputMaybe<AdNetworkCategory>;
};

export type Offset = {
  page?: InputMaybe<Scalars['Int']['input']>;
  perPage?: InputMaybe<Scalars['Int']['input']>;
};

export enum Operator {
  Eq = 'eq',
  Gt = 'gt',
  Gte = 'gte',
  In = 'in',
  Lt = 'lt',
  Lte = 'lte',
  Ne = 'ne',
  Nin = 'nin'
}

export type Order = {
  direction?: InputMaybe<OrderDirection>;
  field?: InputMaybe<Scalars['String']['input']>;
};

export enum OrderDirection {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type PageInfo = {
  __typename?: 'PageInfo';
  currentPage: Scalars['Int']['output'];
  firstPage?: Maybe<Scalars['Int']['output']>;
  lastPage: Scalars['Int']['output'];
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PlaytimeAggregation = {
  __typename?: 'PlaytimeAggregation';
  engagementSessionCount: Scalars['Float']['output'];
  playtimeSec: Scalars['Float']['output'];
};

export type Preset = {
  viewPresetId?: InputMaybe<Scalars['Int']['input']>;
};

export type ProductMetricVersionsWhere = {
  dateFrom: Scalars['Date']['input'];
  dateTo: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};

export type Query = {
  __typename?: 'Query';
  accessControl: AccessControl;
  adMetrics: AdMetrics;
  adNetworks: AdNetworks;
  adTypes: AdTypes;
  admobGames: AdmobGames;
  admobMetrics: AdmobMetrics;
  aggregateGameAgencyCost: GameAgencyCosts;
  aggregateGameCost: GameCost;
  aggregateGameMetric: GameMetric;
  aggregateGameNetworkRevenue: GameNetworkRevenues;
  aggregatePlaytime: PlaytimeAggregation;
  attributes: Attributes;
  budgetRequests: BudgetRequests;
  configMapCollections: Array<ConfigMapCollection>;
  configMaps: Array<ConfigMap>;
  dashboardNotifications: DashboardNotifications;
  firebaseExperiments: FirebaseExperiments;
  firebaseMetrics: FirebaseMetrics;
  firebaseVersionVariants: FirebaseVersionVariants;
  game: Game;
  gameAgencyCosts: GameAgencyCosts;
  gameCosts: GameCosts;
  gameCreativeMetrics: GameCreativeMetrics;
  gameDailyLevelDropVersions: Versions;
  gameLevelDropVersions: Versions;
  gameLevelDrops: GameLevelDrops;
  gameMetrics: GameMetrics;
  gameNetworkRevenues: GameNetworkRevenues;
  gamePerformanceSetting: GamePerformanceSetting;
  gamePlaytimeVersions: Versions;
  gameRetentionRateVersions: Versions;
  gameRetentionRates: GameRetentionRates;
  gameReview: GameReview;
  gameRewardUsageVersions: Versions;
  gameRewardUsages: GameRewardUsages;
  gameRoles: GamesRoles;
  gameStudioMetrics: GameStudioMetrics;
  gameStudios: GameStudios;
  games: Games;
  mediations: Mediations;
  metabaseChart: MetabaseChart;
  networks: Networks;
  profile: User;
  releaseMetrics: ReleaseMetrics;
  releaseVersions: ReleaseVersions;
  sideMenus: SideMenus;
  team: Team;
  teams: Array<Team>;
  users: Users;
  v2: V2Query;
  viewPresets: ViewPresets;
  workflowStepActions: WorkflowStepActions;
  workflows: Workflows;
};


export type QueryAccessControlArgs = {
  where: AccessControlWhere;
};


export type QueryAdMetricsArgs = {
  group: Group;
  preset?: InputMaybe<Preset>;
  where: AdMetricsWhere;
};


export type QueryAdmobMetricsArgs = {
  where: AdmobMetricsWhere;
};


export type QueryAggregateGameAgencyCostArgs = {
  where: AggregateGameAgencyCostWhere;
};


export type QueryAggregateGameCostArgs = {
  id: Scalars['ID']['input'];
  where: AggregateGameCostWhere;
};


export type QueryAggregateGameMetricArgs = {
  where: GameMetricWhere;
};


export type QueryAggregateGameNetworkRevenueArgs = {
  where: AggregateGameNetworkRevenueWhere;
};


export type QueryAggregatePlaytimeArgs = {
  where: AggregatePlaytimeWhere;
};


export type QueryAttributesArgs = {
  where: AttributesWhere;
};


export type QueryBudgetRequestsArgs = {
  offset?: InputMaybe<Offset>;
  where: BudgetRequestsWhere;
};


export type QueryConfigMapCollectionsArgs = {
  ids: Array<Scalars['ID']['input']>;
};


export type QueryConfigMapsArgs = {
  ids: Array<Scalars['ID']['input']>;
};


export type QueryFirebaseExperimentsArgs = {
  where: FirebaseExperimentsWhere;
};


export type QueryFirebaseMetricsArgs = {
  group?: InputMaybe<Group>;
  preset?: InputMaybe<Preset>;
  where: FirebaseMetricsWhere;
};


export type QueryFirebaseVersionVariantsArgs = {
  where: FirebaseVersionVariantsWhere;
};


export type QueryGameArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGameAgencyCostsArgs = {
  offset?: InputMaybe<Offset>;
  order: Order;
  where: GameAgencyCostsWhere;
};


export type QueryGameCostsArgs = {
  offset?: InputMaybe<Offset>;
  where: GameCostsWhere;
};


export type QueryGameCreativeMetricsArgs = {
  offset?: InputMaybe<Offset>;
  order: Order;
  where: GameCreativeMetricsWhere;
};


export type QueryGameDailyLevelDropVersionsArgs = {
  where: ProductMetricVersionsWhere;
};


export type QueryGameLevelDropVersionsArgs = {
  where: GameLevelDropVersionsWhere;
};


export type QueryGameLevelDropsArgs = {
  where: GameLevelDropsWhere;
};


export type QueryGameMetricsArgs = {
  offset?: InputMaybe<Offset>;
  order?: InputMaybe<Order>;
  where: GameMetricWhere;
};


export type QueryGameNetworkRevenuesArgs = {
  offset?: InputMaybe<Offset>;
  order: Order;
  where: GameNetworkRevenuesWhere;
};


export type QueryGamePerformanceSettingArgs = {
  gameId: Scalars['ID']['input'];
};


export type QueryGamePlaytimeVersionsArgs = {
  where: ProductMetricVersionsWhere;
};


export type QueryGameRetentionRateVersionsArgs = {
  where: ProductMetricVersionsWhere;
};


export type QueryGameRetentionRatesArgs = {
  where: GameRetentionRatesWhere;
};


export type QueryGameReviewArgs = {
  date: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
};


export type QueryGameRewardUsageVersionsArgs = {
  where: ProductMetricVersionsWhere;
};


export type QueryGameRewardUsagesArgs = {
  group: Group;
  order: Order;
  where: GameRewardUsagesWhere;
};


export type QueryGameRolesArgs = {
  gameIds: Array<Scalars['ID']['input']>;
};


export type QueryGameStudioMetricsArgs = {
  where: GameStudioMetricsWhere;
};


export type QueryGamesArgs = {
  offset?: InputMaybe<Offset>;
  where: GamesWhere;
};


export type QueryMetabaseChartArgs = {
  where: MetabaseChartWhere;
};


export type QueryNetworksArgs = {
  where: NetworksWhere;
};


export type QueryReleaseMetricsArgs = {
  group?: InputMaybe<Group>;
  preset?: InputMaybe<Preset>;
  where: ReleaseMetricsWhere;
};


export type QueryReleaseVersionsArgs = {
  where: ReleaseVersionsWhere;
};


export type QueryTeamArgs = {
  id: Scalars['Int']['input'];
};


export type QueryUsersArgs = {
  offset?: InputMaybe<Offset>;
  where: UsersWhere;
};


export type QueryViewPresetsArgs = {
  where: ViewPresetsWhere;
};

export type ReleaseAdMetric = {
  __typename?: 'ReleaseAdMetric';
  adRevGrossAmount?: Maybe<Scalars['Float']['output']>;
  adRevGrossAmountPerActiveUser?: Maybe<Scalars['Float']['output']>;
  adRevNthDayGrossAmounts?: Maybe<Scalars['JSON']['output']>;
  adTypeCategory?: Maybe<AdTypeCategory>;
  impressionCount?: Maybe<Scalars['Float']['output']>;
  impressionCountPerActiveUser?: Maybe<Scalars['Float']['output']>;
  impressionNthDayCounts?: Maybe<Scalars['JSON']['output']>;
};

export type ReleaseMetric = {
  __typename?: 'ReleaseMetric';
  activeUserCount?: Maybe<Scalars['Float']['output']>;
  activeUserNthDayCounts: Scalars['JSON']['output'];
  adRevGrossAmount?: Maybe<Scalars['Float']['output']>;
  adRevGrossAmountPerActiveUser?: Maybe<Scalars['Float']['output']>;
  adRevNthDayGrossAmounts: Scalars['JSON']['output'];
  ads?: Maybe<Array<ReleaseAdMetric>>;
  countryCode?: Maybe<Scalars['String']['output']>;
  dailyActiveUserCount?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  impressionCount?: Maybe<Scalars['Float']['output']>;
  impressionCountPerActiveUser?: Maybe<Scalars['Float']['output']>;
  impressionNthDayCounts: Scalars['JSON']['output'];
  installCount?: Maybe<Scalars['Float']['output']>;
  lifetimeNthDayValues: Scalars['JSON']['output'];
  playtimeMsec?: Maybe<Scalars['BigInt']['output']>;
  playtimeNthDayMsecs: Scalars['JSON']['output'];
  retentionNthDayRates: Scalars['JSON']['output'];
  retentionRate?: Maybe<Scalars['Float']['output']>;
  sessionCount?: Maybe<Scalars['Float']['output']>;
  sessionCountPerActiveUser?: Maybe<Scalars['Float']['output']>;
  sessionNthDayCounts: Scalars['JSON']['output'];
  version?: Maybe<Scalars['String']['output']>;
};

export type ReleaseMetrics = {
  __typename?: 'ReleaseMetrics';
  collection: Array<ReleaseMetric>;
  viewPreset: ViewPreset;
};

export type ReleaseMetricsWhere = {
  countryCode?: InputMaybe<Filter>;
  date: Filter;
  gameId: Scalars['String']['input'];
  version?: InputMaybe<Filter>;
};

export type ReleaseVersion = {
  __typename?: 'ReleaseVersion';
  version: Scalars['String']['output'];
};

export type ReleaseVersions = {
  __typename?: 'ReleaseVersions';
  collection: Array<ReleaseVersion>;
};

export type ReleaseVersionsWhere = {
  gameId: Scalars['ID']['input'];
};

export type RoleAccessControl = {
  __typename?: 'RoleAccessControl';
  permits: Array<Scalars['String']['output']>;
  roleId: Scalars['String']['output'];
  subject: Scalars['String']['output'];
};

export type SideMenu = {
  __typename?: 'SideMenu';
  group: Scalars['String']['output'];
  icon: Scalars['String']['output'];
  name: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type SideMenus = {
  __typename?: 'SideMenus';
  collection: Array<SideMenu>;
};

export type Team = {
  __typename?: 'Team';
  id: Scalars['Int']['output'];
  leader?: Maybe<User>;
  leaderId?: Maybe<Scalars['ID']['output']>;
  members: Array<User>;
  name: Scalars['String']['output'];
  roleId: Scalars['String']['output'];
};

export type Teams = {
  __typename?: 'Teams';
  collection: Array<Team>;
};

export type ToolPermission = {
  __typename?: 'ToolPermission';
  action: Scalars['String']['output'];
  tool: Scalars['String']['output'];
};

export type ToolPermissionForm = {
  action: Scalars['String']['input'];
  tool: Scalars['String']['input'];
};

export type UpdateBudgetRequestForm = {
  amount: Scalars['Float']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  expirationDate: Scalars['Date']['input'];
  id: Scalars['Int']['input'];
};

export type UpdateBudgetRequestStateForm = {
  id: Scalars['Int']['input'];
  lastAction?: InputMaybe<Scalars['String']['input']>;
  stepId: Scalars['Int']['input'];
};

export type UpdateConfigMapsWhere = {
  scopes: Array<Scalars['String']['input']>;
};

export type UpdateGameCostForm = {
  preTaxAmount?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateGameCostWhere = {
  date: Scalars['Date']['input'];
  gameId: Scalars['ID']['input'];
  networkId: Scalars['Int']['input'];
};

export type UpdateGameMetricForm = {
  metadata: UpdateGameMetricMetadataForm;
  override: UpdateGameMetricOverrideForm;
};

export type UpdateGameMetricMetadataForm = {
  monetNote?: InputMaybe<Scalars['String']['input']>;
  note?: InputMaybe<Scalars['String']['input']>;
  productNote?: InputMaybe<Scalars['String']['input']>;
  uaNote?: InputMaybe<Scalars['String']['input']>;
  versionNote?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateGameMetricOverrideForm = {
  cost?: InputMaybe<Scalars['Float']['input']>;
  revenue?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateGameMetricWhere = {
  date: Scalars['String']['input'];
  gameId: Scalars['ID']['input'];
};

export type UpdateGameRole = {
  id: Scalars['ID']['input'];
  users?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateGameRolesArgs = {
  forms: Array<UpdateGameRolesGame>;
};

export type UpdateGameRolesGame = {
  gameId: Scalars['ID']['input'];
  roles: Array<UpdateGameRole>;
};

export type UpdateProfileForm = {
  password: Scalars['String']['input'];
  passwordConfirmation: Scalars['String']['input'];
};

export type UpdateTeamForm = {
  leaderEmail?: InputMaybe<Scalars['String']['input']>;
  memberEmails: Array<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type UpdateTeamWhere = {
  id: Scalars['Int']['input'];
};

export type UpdateUserForm = {
  fullName?: InputMaybe<Scalars['String']['input']>;
  hasPassword?: InputMaybe<Scalars['Boolean']['input']>;
  kind?: InputMaybe<UserKind>;
  note?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  toolPermissions?: InputMaybe<Array<ToolPermissionForm>>;
};

export type UpdateUserWhere = {
  id: Scalars['ID']['input'];
};

export type UpdateWorkflowForm = {
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  steps?: InputMaybe<Array<WorkflowStepForm>>;
};

export type User = {
  __typename?: 'User';
  email: Scalars['String']['output'];
  fullName: Scalars['String']['output'];
  hasPassword: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  inchargedGames: Array<UserInchargedGame>;
  isDeleted: Scalars['Boolean']['output'];
  kind: UserKind;
  ledTeam?: Maybe<Team>;
  note?: Maybe<Scalars['String']['output']>;
  team?: Maybe<Team>;
  teamLead?: Maybe<Team>;
  toolPermissions: Array<ToolPermission>;
};

export type UserInchargedGame = {
  __typename?: 'UserInchargedGame';
  roleId: Scalars['String']['output'];
  storeId: Scalars['ID']['output'];
};

export enum UserKind {
  Inhouse = 'INHOUSE',
  Partner = 'PARTNER'
}

export type UserPublicInfo = {
  __typename?: 'UserPublicInfo';
  fullName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
};

export type Users = {
  __typename?: 'Users';
  collection: Array<User>;
  pageInfo: PageInfo;
};

export type UsersWhere = {
  email?: InputMaybe<Scalars['String']['input']>;
};

export type V2GameMetric = {
  __typename?: 'V2GameMetric';
  adPerformances: Array<AdPerformance>;
  cost: Scalars['Float']['output'];
  cpi: Scalars['Float']['output'];
  dailyActiveUsers: Scalars['Int']['output'];
  date?: Maybe<Scalars['Date']['output']>;
  gameId: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  monetNote?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  organicInstalls: Scalars['Int']['output'];
  organicPercentage: Scalars['Float']['output'];
  paidInstalls: Scalars['Int']['output'];
  playtime: Scalars['Float']['output'];
  productNote?: Maybe<Scalars['String']['output']>;
  profit: Scalars['Float']['output'];
  retentionRateDay1: Scalars['Float']['output'];
  retentionRateDay3: Scalars['Float']['output'];
  retentionRateDay7: Scalars['Float']['output'];
  revenue: Scalars['Float']['output'];
  roas: Scalars['Float']['output'];
  sessions: Scalars['Float']['output'];
  totalInstalls: Scalars['Int']['output'];
  uaNote?: Maybe<Scalars['String']['output']>;
  versionNote?: Maybe<Scalars['String']['output']>;
};

export type V2GameMetricWhere = {
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  gameId: Scalars['ID']['input'];
};

export type V2GameMetrics = {
  __typename?: 'V2GameMetrics';
  collection: Array<V2GameMetric>;
  pageInfo: PageInfo;
};

export type V2Query = {
  __typename?: 'V2Query';
  aggregateGameMetrics: V2GameMetric;
  gameMetrics: V2GameMetrics;
};


export type V2QueryAggregateGameMetricsArgs = {
  id: Scalars['String']['input'];
  source?: InputMaybe<DataSource>;
  where: V2GameMetricWhere;
};


export type V2QueryGameMetricsArgs = {
  offset?: InputMaybe<Offset>;
  order?: InputMaybe<Order>;
  source?: InputMaybe<DataSource>;
  where: V2GameMetricWhere;
};

export type Versions = {
  __typename?: 'Versions';
  collection: Array<Scalars['String']['output']>;
};

export type ViewPreset = {
  __typename?: 'ViewPreset';
  attributes: Array<ViewPresetAttribute>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type ViewPresetAttribute = {
  __typename?: 'ViewPresetAttribute';
  cohortDays: Array<Scalars['Int']['output']>;
  isCohort: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
};

export type ViewPresetAttributeForm = {
  cohortDays: Array<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
};

export type ViewPresets = {
  __typename?: 'ViewPresets';
  collection: Array<ViewPreset>;
};

export type ViewPresetsWhere = {
  pageId: Scalars['String']['input'];
};

export type Workflow = {
  __typename?: 'Workflow';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  roleId: Scalars['String']['output'];
  steps: Array<WorkflowStep>;
};

export type WorkflowStep = {
  __typename?: 'WorkflowStep';
  action: Scalars['String']['output'];
  alternateAction: Scalars['String']['output'];
  assignee: UserPublicInfo;
  assigneeId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type WorkflowStepAction = {
  __typename?: 'WorkflowStepAction';
  action: Scalars['String']['output'];
};

export type WorkflowStepActions = {
  __typename?: 'WorkflowStepActions';
  collection: Array<WorkflowStepAction>;
};

export type WorkflowStepForm = {
  action: Scalars['String']['input'];
  alternateAction: Scalars['String']['input'];
  assigneeId: Scalars['ID']['input'];
  name: Scalars['String']['input'];
};

export type Workflows = {
  __typename?: 'Workflows';
  collection: Array<Workflow>;
};

export type WithIndex<TObject> = TObject & Record<string, any>;
export type ResolversObject<TObject> = WithIndex<TObject>;

export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;



/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = ResolversObject<{
  AccessControl: ResolverTypeWrapper<AccessControl>;
  AccessControlRoleInput: AccessControlRoleInput;
  AccessControlUpdateForm: AccessControlUpdateForm;
  AccessControlWhere: AccessControlWhere;
  Ad: ResolverTypeWrapper<Ad>;
  AdAgency: ResolverTypeWrapper<AdAgency>;
  AdGroup: ResolverTypeWrapper<AdGroup>;
  AdMetric: ResolverTypeWrapper<AdMetric>;
  AdMetrics: ResolverTypeWrapper<AdMetrics>;
  AdMetricsWhere: AdMetricsWhere;
  AdNetwork: ResolverTypeWrapper<AdNetwork>;
  AdNetworkCategory: AdNetworkCategory;
  AdNetworks: ResolverTypeWrapper<AdNetworks>;
  AdPerformance: ResolverTypeWrapper<AdPerformance>;
  AdType: ResolverTypeWrapper<AdType>;
  AdTypeCategory: AdTypeCategory;
  AdTypes: ResolverTypeWrapper<AdTypes>;
  AdmobGames: ResolverTypeWrapper<AdmobGames>;
  AdmobMetrics: ResolverTypeWrapper<AdmobMetrics>;
  AdmobMetricsWhere: AdmobMetricsWhere;
  AgencyMetric: ResolverTypeWrapper<AgencyMetric>;
  AggregateGameAgencyCostWhere: AggregateGameAgencyCostWhere;
  AggregateGameCostWhere: AggregateGameCostWhere;
  AggregateGameNetworkRevenueWhere: AggregateGameNetworkRevenueWhere;
  AggregatePlaytimeWhere: AggregatePlaytimeWhere;
  Attribute: ResolverTypeWrapper<Attribute>;
  Attributes: ResolverTypeWrapper<Attributes>;
  AttributesWhere: AttributesWhere;
  BigInt: ResolverTypeWrapper<Scalars['BigInt']['output']>;
  Boolean: ResolverTypeWrapper<Scalars['Boolean']['output']>;
  BudgetRequest: ResolverTypeWrapper<BudgetRequest>;
  BudgetRequests: ResolverTypeWrapper<BudgetRequests>;
  BudgetRequestsWhere: BudgetRequestsWhere;
  Campaign: ResolverTypeWrapper<Campaign>;
  ConfigMap: ResolverTypeWrapper<ConfigMap>;
  ConfigMapCollection: ResolverTypeWrapper<ConfigMapCollection>;
  CreateBudgetRequestForm: CreateBudgetRequestForm;
  CreateTeamForm: CreateTeamForm;
  CreateUserForm: CreateUserForm;
  CreateViewPresetForm: CreateViewPresetForm;
  CreateWorkflowForm: CreateWorkflowForm;
  DashboardNotification: ResolverTypeWrapper<DashboardNotification>;
  DashboardNotifications: ResolverTypeWrapper<DashboardNotifications>;
  DataSource: DataSource;
  Date: ResolverTypeWrapper<Scalars['Date']['output']>;
  DeleteBudgetRequestsWhere: DeleteBudgetRequestsWhere;
  DeleteTeamWhere: DeleteTeamWhere;
  DeleteUsersWhere: DeleteUsersWhere;
  DeleteViewPresetsWhere: DeleteViewPresetsWhere;
  Filter: Filter;
  FilterOperator: FilterOperator;
  FilterValue: ResolverTypeWrapper<Scalars['FilterValue']['output']>;
  FirebaseAdMetric: ResolverTypeWrapper<FirebaseAdMetric>;
  FirebaseExperiment: ResolverTypeWrapper<FirebaseExperiment>;
  FirebaseExperiments: ResolverTypeWrapper<FirebaseExperiments>;
  FirebaseExperimentsWhere: FirebaseExperimentsWhere;
  FirebaseMetric: ResolverTypeWrapper<FirebaseMetric>;
  FirebaseMetrics: ResolverTypeWrapper<FirebaseMetrics>;
  FirebaseMetricsWhere: FirebaseMetricsWhere;
  FirebaseVersionVariant: ResolverTypeWrapper<FirebaseVersionVariant>;
  FirebaseVersionVariants: ResolverTypeWrapper<FirebaseVersionVariants>;
  FirebaseVersionVariantsWhere: FirebaseVersionVariantsWhere;
  Float: ResolverTypeWrapper<Scalars['Float']['output']>;
  Game: ResolverTypeWrapper<Game>;
  GameAgencyCost: ResolverTypeWrapper<GameAgencyCost>;
  GameAgencyCosts: ResolverTypeWrapper<GameAgencyCosts>;
  GameAgencyCostsWhere: GameAgencyCostsWhere;
  GameCost: ResolverTypeWrapper<GameCost>;
  GameCosts: ResolverTypeWrapper<GameCosts>;
  GameCostsAggregation: ResolverTypeWrapper<GameCostsAggregation>;
  GameCostsMeta: ResolverTypeWrapper<GameCostsMeta>;
  GameCostsWhere: GameCostsWhere;
  GameCreativeMetric: ResolverTypeWrapper<GameCreativeMetric>;
  GameCreativeMetrics: ResolverTypeWrapper<GameCreativeMetrics>;
  GameCreativeMetricsWhere: GameCreativeMetricsWhere;
  GameLevelDrop: ResolverTypeWrapper<GameLevelDrop>;
  GameLevelDropVersionsWhere: GameLevelDropVersionsWhere;
  GameLevelDrops: ResolverTypeWrapper<GameLevelDrops>;
  GameLevelDropsWhere: GameLevelDropsWhere;
  GameMetric: ResolverTypeWrapper<GameMetric>;
  GameMetricMetadata: ResolverTypeWrapper<GameMetricMetadata>;
  GameMetricWhere: GameMetricWhere;
  GameMetrics: ResolverTypeWrapper<GameMetrics>;
  GameNetworkRevenue: ResolverTypeWrapper<GameNetworkRevenue>;
  GameNetworkRevenues: ResolverTypeWrapper<GameNetworkRevenues>;
  GameNetworkRevenuesWhere: GameNetworkRevenuesWhere;
  GamePerformanceCriteria: ResolverTypeWrapper<GamePerformanceCriteria>;
  GamePerformanceCriteriaConclusion: GamePerformanceCriteriaConclusion;
  GamePerformanceSetting: ResolverTypeWrapper<GamePerformanceSetting>;
  GameRetentionRate: ResolverTypeWrapper<GameRetentionRate>;
  GameRetentionRates: ResolverTypeWrapper<GameRetentionRates>;
  GameRetentionRatesWhere: GameRetentionRatesWhere;
  GameReview: ResolverTypeWrapper<GameReview>;
  GameRewardUsage: ResolverTypeWrapper<GameRewardUsage>;
  GameRewardUsages: ResolverTypeWrapper<GameRewardUsages>;
  GameRewardUsagesWhere: GameRewardUsagesWhere;
  GameRole: ResolverTypeWrapper<GameRole>;
  GameRoleMembership: ResolverTypeWrapper<GameRoleMembership>;
  GameRoleMembershipUser: ResolverTypeWrapper<GameRoleMembershipUser>;
  GameRoleUser: ResolverTypeWrapper<GameRoleUser>;
  GameRoles: ResolverTypeWrapper<GameRoles>;
  GameStudio: ResolverTypeWrapper<GameStudio>;
  GameStudioMetric: ResolverTypeWrapper<GameStudioMetric>;
  GameStudioMetrics: ResolverTypeWrapper<GameStudioMetrics>;
  GameStudioMetricsWhere: GameStudioMetricsWhere;
  GameStudios: ResolverTypeWrapper<GameStudios>;
  Games: ResolverTypeWrapper<Games>;
  GamesRoles: ResolverTypeWrapper<GamesRoles>;
  GamesWhere: GamesWhere;
  Group: Group;
  ID: ResolverTypeWrapper<Scalars['ID']['output']>;
  Int: ResolverTypeWrapper<Scalars['Int']['output']>;
  JSON: ResolverTypeWrapper<Scalars['JSON']['output']>;
  Mediation: ResolverTypeWrapper<Mediation>;
  Mediations: ResolverTypeWrapper<Mediations>;
  MetabaseChart: ResolverTypeWrapper<MetabaseChart>;
  MetabaseChartParam: MetabaseChartParam;
  MetabaseChartWhere: MetabaseChartWhere;
  Mutation: ResolverTypeWrapper<{}>;
  Network: ResolverTypeWrapper<Network>;
  Networks: ResolverTypeWrapper<Networks>;
  NetworksWhere: NetworksWhere;
  Offset: Offset;
  Operator: Operator;
  Order: Order;
  OrderDirection: OrderDirection;
  PageInfo: ResolverTypeWrapper<PageInfo>;
  PlaytimeAggregation: ResolverTypeWrapper<PlaytimeAggregation>;
  Preset: Preset;
  ProductMetricVersionsWhere: ProductMetricVersionsWhere;
  Query: ResolverTypeWrapper<{}>;
  ReleaseAdMetric: ResolverTypeWrapper<ReleaseAdMetric>;
  ReleaseMetric: ResolverTypeWrapper<ReleaseMetric>;
  ReleaseMetrics: ResolverTypeWrapper<ReleaseMetrics>;
  ReleaseMetricsWhere: ReleaseMetricsWhere;
  ReleaseVersion: ResolverTypeWrapper<ReleaseVersion>;
  ReleaseVersions: ResolverTypeWrapper<ReleaseVersions>;
  ReleaseVersionsWhere: ReleaseVersionsWhere;
  RoleAccessControl: ResolverTypeWrapper<RoleAccessControl>;
  SideMenu: ResolverTypeWrapper<SideMenu>;
  SideMenus: ResolverTypeWrapper<SideMenus>;
  String: ResolverTypeWrapper<Scalars['String']['output']>;
  Team: ResolverTypeWrapper<Team>;
  Teams: ResolverTypeWrapper<Teams>;
  ToolPermission: ResolverTypeWrapper<ToolPermission>;
  ToolPermissionForm: ToolPermissionForm;
  UUID: ResolverTypeWrapper<Scalars['UUID']['output']>;
  UpdateBudgetRequestForm: UpdateBudgetRequestForm;
  UpdateBudgetRequestStateForm: UpdateBudgetRequestStateForm;
  UpdateConfigMapsWhere: UpdateConfigMapsWhere;
  UpdateGameCostForm: UpdateGameCostForm;
  UpdateGameCostWhere: UpdateGameCostWhere;
  UpdateGameMetricForm: UpdateGameMetricForm;
  UpdateGameMetricMetadataForm: UpdateGameMetricMetadataForm;
  UpdateGameMetricOverrideForm: UpdateGameMetricOverrideForm;
  UpdateGameMetricWhere: UpdateGameMetricWhere;
  UpdateGameRole: UpdateGameRole;
  UpdateGameRolesArgs: UpdateGameRolesArgs;
  UpdateGameRolesGame: UpdateGameRolesGame;
  UpdateProfileForm: UpdateProfileForm;
  UpdateTeamForm: UpdateTeamForm;
  UpdateTeamWhere: UpdateTeamWhere;
  UpdateUserForm: UpdateUserForm;
  UpdateUserWhere: UpdateUserWhere;
  UpdateWorkflowForm: UpdateWorkflowForm;
  User: ResolverTypeWrapper<User>;
  UserInchargedGame: ResolverTypeWrapper<UserInchargedGame>;
  UserKind: UserKind;
  UserPublicInfo: ResolverTypeWrapper<UserPublicInfo>;
  Users: ResolverTypeWrapper<Users>;
  UsersWhere: UsersWhere;
  V2GameMetric: ResolverTypeWrapper<V2GameMetric>;
  V2GameMetricWhere: V2GameMetricWhere;
  V2GameMetrics: ResolverTypeWrapper<V2GameMetrics>;
  V2Query: ResolverTypeWrapper<V2Query>;
  Versions: ResolverTypeWrapper<Versions>;
  ViewPreset: ResolverTypeWrapper<ViewPreset>;
  ViewPresetAttribute: ResolverTypeWrapper<ViewPresetAttribute>;
  ViewPresetAttributeForm: ViewPresetAttributeForm;
  ViewPresets: ResolverTypeWrapper<ViewPresets>;
  ViewPresetsWhere: ViewPresetsWhere;
  Void: ResolverTypeWrapper<Scalars['Void']['output']>;
  Workflow: ResolverTypeWrapper<Workflow>;
  WorkflowStep: ResolverTypeWrapper<WorkflowStep>;
  WorkflowStepAction: ResolverTypeWrapper<WorkflowStepAction>;
  WorkflowStepActions: ResolverTypeWrapper<WorkflowStepActions>;
  WorkflowStepForm: WorkflowStepForm;
  Workflows: ResolverTypeWrapper<Workflows>;
}>;

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = ResolversObject<{
  AccessControl: AccessControl;
  AccessControlRoleInput: AccessControlRoleInput;
  AccessControlUpdateForm: AccessControlUpdateForm;
  AccessControlWhere: AccessControlWhere;
  Ad: Ad;
  AdAgency: AdAgency;
  AdGroup: AdGroup;
  AdMetric: AdMetric;
  AdMetrics: AdMetrics;
  AdMetricsWhere: AdMetricsWhere;
  AdNetwork: AdNetwork;
  AdNetworks: AdNetworks;
  AdPerformance: AdPerformance;
  AdType: AdType;
  AdTypes: AdTypes;
  AdmobGames: AdmobGames;
  AdmobMetrics: AdmobMetrics;
  AdmobMetricsWhere: AdmobMetricsWhere;
  AgencyMetric: AgencyMetric;
  AggregateGameAgencyCostWhere: AggregateGameAgencyCostWhere;
  AggregateGameCostWhere: AggregateGameCostWhere;
  AggregateGameNetworkRevenueWhere: AggregateGameNetworkRevenueWhere;
  AggregatePlaytimeWhere: AggregatePlaytimeWhere;
  Attribute: Attribute;
  Attributes: Attributes;
  AttributesWhere: AttributesWhere;
  BigInt: Scalars['BigInt']['output'];
  Boolean: Scalars['Boolean']['output'];
  BudgetRequest: BudgetRequest;
  BudgetRequests: BudgetRequests;
  BudgetRequestsWhere: BudgetRequestsWhere;
  Campaign: Campaign;
  ConfigMap: ConfigMap;
  ConfigMapCollection: ConfigMapCollection;
  CreateBudgetRequestForm: CreateBudgetRequestForm;
  CreateTeamForm: CreateTeamForm;
  CreateUserForm: CreateUserForm;
  CreateViewPresetForm: CreateViewPresetForm;
  CreateWorkflowForm: CreateWorkflowForm;
  DashboardNotification: DashboardNotification;
  DashboardNotifications: DashboardNotifications;
  Date: Scalars['Date']['output'];
  DeleteBudgetRequestsWhere: DeleteBudgetRequestsWhere;
  DeleteTeamWhere: DeleteTeamWhere;
  DeleteUsersWhere: DeleteUsersWhere;
  DeleteViewPresetsWhere: DeleteViewPresetsWhere;
  Filter: Filter;
  FilterValue: Scalars['FilterValue']['output'];
  FirebaseAdMetric: FirebaseAdMetric;
  FirebaseExperiment: FirebaseExperiment;
  FirebaseExperiments: FirebaseExperiments;
  FirebaseExperimentsWhere: FirebaseExperimentsWhere;
  FirebaseMetric: FirebaseMetric;
  FirebaseMetrics: FirebaseMetrics;
  FirebaseMetricsWhere: FirebaseMetricsWhere;
  FirebaseVersionVariant: FirebaseVersionVariant;
  FirebaseVersionVariants: FirebaseVersionVariants;
  FirebaseVersionVariantsWhere: FirebaseVersionVariantsWhere;
  Float: Scalars['Float']['output'];
  Game: Game;
  GameAgencyCost: GameAgencyCost;
  GameAgencyCosts: GameAgencyCosts;
  GameAgencyCostsWhere: GameAgencyCostsWhere;
  GameCost: GameCost;
  GameCosts: GameCosts;
  GameCostsAggregation: GameCostsAggregation;
  GameCostsMeta: GameCostsMeta;
  GameCostsWhere: GameCostsWhere;
  GameCreativeMetric: GameCreativeMetric;
  GameCreativeMetrics: GameCreativeMetrics;
  GameCreativeMetricsWhere: GameCreativeMetricsWhere;
  GameLevelDrop: GameLevelDrop;
  GameLevelDropVersionsWhere: GameLevelDropVersionsWhere;
  GameLevelDrops: GameLevelDrops;
  GameLevelDropsWhere: GameLevelDropsWhere;
  GameMetric: GameMetric;
  GameMetricMetadata: GameMetricMetadata;
  GameMetricWhere: GameMetricWhere;
  GameMetrics: GameMetrics;
  GameNetworkRevenue: GameNetworkRevenue;
  GameNetworkRevenues: GameNetworkRevenues;
  GameNetworkRevenuesWhere: GameNetworkRevenuesWhere;
  GamePerformanceCriteria: GamePerformanceCriteria;
  GamePerformanceSetting: GamePerformanceSetting;
  GameRetentionRate: GameRetentionRate;
  GameRetentionRates: GameRetentionRates;
  GameRetentionRatesWhere: GameRetentionRatesWhere;
  GameReview: GameReview;
  GameRewardUsage: GameRewardUsage;
  GameRewardUsages: GameRewardUsages;
  GameRewardUsagesWhere: GameRewardUsagesWhere;
  GameRole: GameRole;
  GameRoleMembership: GameRoleMembership;
  GameRoleMembershipUser: GameRoleMembershipUser;
  GameRoleUser: GameRoleUser;
  GameRoles: GameRoles;
  GameStudio: GameStudio;
  GameStudioMetric: GameStudioMetric;
  GameStudioMetrics: GameStudioMetrics;
  GameStudioMetricsWhere: GameStudioMetricsWhere;
  GameStudios: GameStudios;
  Games: Games;
  GamesRoles: GamesRoles;
  GamesWhere: GamesWhere;
  Group: Group;
  ID: Scalars['ID']['output'];
  Int: Scalars['Int']['output'];
  JSON: Scalars['JSON']['output'];
  Mediation: Mediation;
  Mediations: Mediations;
  MetabaseChart: MetabaseChart;
  MetabaseChartParam: MetabaseChartParam;
  MetabaseChartWhere: MetabaseChartWhere;
  Mutation: {};
  Network: Network;
  Networks: Networks;
  NetworksWhere: NetworksWhere;
  Offset: Offset;
  Order: Order;
  PageInfo: PageInfo;
  PlaytimeAggregation: PlaytimeAggregation;
  Preset: Preset;
  ProductMetricVersionsWhere: ProductMetricVersionsWhere;
  Query: {};
  ReleaseAdMetric: ReleaseAdMetric;
  ReleaseMetric: ReleaseMetric;
  ReleaseMetrics: ReleaseMetrics;
  ReleaseMetricsWhere: ReleaseMetricsWhere;
  ReleaseVersion: ReleaseVersion;
  ReleaseVersions: ReleaseVersions;
  ReleaseVersionsWhere: ReleaseVersionsWhere;
  RoleAccessControl: RoleAccessControl;
  SideMenu: SideMenu;
  SideMenus: SideMenus;
  String: Scalars['String']['output'];
  Team: Team;
  Teams: Teams;
  ToolPermission: ToolPermission;
  ToolPermissionForm: ToolPermissionForm;
  UUID: Scalars['UUID']['output'];
  UpdateBudgetRequestForm: UpdateBudgetRequestForm;
  UpdateBudgetRequestStateForm: UpdateBudgetRequestStateForm;
  UpdateConfigMapsWhere: UpdateConfigMapsWhere;
  UpdateGameCostForm: UpdateGameCostForm;
  UpdateGameCostWhere: UpdateGameCostWhere;
  UpdateGameMetricForm: UpdateGameMetricForm;
  UpdateGameMetricMetadataForm: UpdateGameMetricMetadataForm;
  UpdateGameMetricOverrideForm: UpdateGameMetricOverrideForm;
  UpdateGameMetricWhere: UpdateGameMetricWhere;
  UpdateGameRole: UpdateGameRole;
  UpdateGameRolesArgs: UpdateGameRolesArgs;
  UpdateGameRolesGame: UpdateGameRolesGame;
  UpdateProfileForm: UpdateProfileForm;
  UpdateTeamForm: UpdateTeamForm;
  UpdateTeamWhere: UpdateTeamWhere;
  UpdateUserForm: UpdateUserForm;
  UpdateUserWhere: UpdateUserWhere;
  UpdateWorkflowForm: UpdateWorkflowForm;
  User: User;
  UserInchargedGame: UserInchargedGame;
  UserPublicInfo: UserPublicInfo;
  Users: Users;
  UsersWhere: UsersWhere;
  V2GameMetric: V2GameMetric;
  V2GameMetricWhere: V2GameMetricWhere;
  V2GameMetrics: V2GameMetrics;
  V2Query: V2Query;
  Versions: Versions;
  ViewPreset: ViewPreset;
  ViewPresetAttribute: ViewPresetAttribute;
  ViewPresetAttributeForm: ViewPresetAttributeForm;
  ViewPresets: ViewPresets;
  ViewPresetsWhere: ViewPresetsWhere;
  Void: Scalars['Void']['output'];
  Workflow: Workflow;
  WorkflowStep: WorkflowStep;
  WorkflowStepAction: WorkflowStepAction;
  WorkflowStepActions: WorkflowStepActions;
  WorkflowStepForm: WorkflowStepForm;
  Workflows: Workflows;
}>;

export type AccessControlResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AccessControl'] = ResolversParentTypes['AccessControl']> = ResolversObject<{
  roles?: Resolver<Array<ResolversTypes['RoleAccessControl']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Ad'] = ResolversParentTypes['Ad']> = ResolversObject<{
  campaign?: Resolver<ResolversTypes['Campaign'], ParentType, ContextType>;
  campaignId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  group?: Resolver<ResolversTypes['AdGroup'], ParentType, ContextType>;
  groupId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdAgencyResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdAgency'] = ResolversParentTypes['AdAgency']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdGroupResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdGroup'] = ResolversParentTypes['AdGroup']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdMetric'] = ResolversParentTypes['AdMetric']> = ResolversObject<{
  activeUserNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  adCostNonTaxAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  adId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  adRevGrossAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  adRevNthDayGrossAmounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  agencyId?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  campaignId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  clickCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  countryCode?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  cpc?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  cpi?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  ctr?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  cvr?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  dailyActiveUserCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  groupId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  impressionCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  installCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  ipm?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionNthDayRates?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  retentionRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  roas?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  roasNthDayRates?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  sessionCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  sessionNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdMetrics'] = ResolversParentTypes['AdMetrics']> = ResolversObject<{
  adGroups?: Resolver<Array<ResolversTypes['AdGroup']>, ParentType, ContextType>;
  ads?: Resolver<Array<ResolversTypes['Ad']>, ParentType, ContextType>;
  agencies?: Resolver<Array<ResolversTypes['AdAgency']>, ParentType, ContextType>;
  campaigns?: Resolver<Array<ResolversTypes['Campaign']>, ParentType, ContextType>;
  collection?: Resolver<Array<ResolversTypes['AdMetric']>, ParentType, ContextType>;
  viewPreset?: Resolver<ResolversTypes['ViewPreset'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdNetworkResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdNetwork'] = ResolversParentTypes['AdNetwork']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  inputType?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdNetworksResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdNetworks'] = ResolversParentTypes['AdNetworks']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['AdNetwork']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdPerformanceResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdPerformance'] = ResolversParentTypes['AdPerformance']> = ResolversObject<{
  adType?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  arpDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  dailyActiveUserCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  ecpm?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  impressionCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  impsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  revenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdTypeResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdType'] = ResolversParentTypes['AdType']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  order?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdTypesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdTypes'] = ResolversParentTypes['AdTypes']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['AdType']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdmobGamesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdmobGames'] = ResolversParentTypes['AdmobGames']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AdmobMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AdmobMetrics'] = ResolversParentTypes['AdmobMetrics']> = ResolversObject<{
  mediation?: Resolver<Array<ResolversTypes['JSON']>, ParentType, ContextType>;
  network?: Resolver<Array<ResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AgencyMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AgencyMetric'] = ResolversParentTypes['AgencyMetric']> = ResolversObject<{
  cost?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  paidInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  revenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AttributeResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Attribute'] = ResolversParentTypes['Attribute']> = ResolversObject<{
  aggregate?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  displayName?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  permission?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type AttributesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Attributes'] = ResolversParentTypes['Attributes']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['Attribute']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface BigIntScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['BigInt'], any> {
  name: 'BigInt';
}

export type BudgetRequestResolvers<ContextType = Context, ParentType extends ResolversParentTypes['BudgetRequest'] = ResolversParentTypes['BudgetRequest']> = ResolversObject<{
  amount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  createdBy?: Resolver<ResolversTypes['UserPublicInfo'], ParentType, ContextType>;
  createdById?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  expirationDate?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  game?: Resolver<ResolversTypes['Game'], ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  lastAction?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  step?: Resolver<ResolversTypes['WorkflowStep'], ParentType, ContextType>;
  stepId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  workflow?: Resolver<ResolversTypes['Workflow'], ParentType, ContextType>;
  workflowId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type BudgetRequestsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['BudgetRequests'] = ResolversParentTypes['BudgetRequests']> = ResolversObject<{
  _debug?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  collection?: Resolver<Array<ResolversTypes['BudgetRequest']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type CampaignResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Campaign'] = ResolversParentTypes['Campaign']> = ResolversObject<{
  agency?: Resolver<Maybe<ResolversTypes['AdAgency']>, ParentType, ContextType>;
  agencyId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ConfigMapResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ConfigMap'] = ResolversParentTypes['ConfigMap']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  sole?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ConfigMapCollectionResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ConfigMapCollection'] = ResolversParentTypes['ConfigMapCollection']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  items?: Resolver<Array<ResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type DashboardNotificationResolvers<ContextType = Context, ParentType extends ResolversParentTypes['DashboardNotification'] = ResolversParentTypes['DashboardNotification']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  isPinned?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  isVisible?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  message?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type DashboardNotificationsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['DashboardNotifications'] = ResolversParentTypes['DashboardNotifications']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['DashboardNotification']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface DateScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Date'], any> {
  name: 'Date';
}

export interface FilterValueScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['FilterValue'], any> {
  name: 'FilterValue';
}

export type FirebaseAdMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseAdMetric'] = ResolversParentTypes['FirebaseAdMetric']> = ResolversObject<{
  adRevGrossAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  adRevGrossAmountPerActiveUser?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  adTypeCategory?: Resolver<ResolversTypes['AdTypeCategory'], ParentType, ContextType>;
  impressionCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  impressionCountPerActiveUser?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type FirebaseExperimentResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseExperiment'] = ResolversParentTypes['FirebaseExperiment']> = ResolversObject<{
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type FirebaseExperimentsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseExperiments'] = ResolversParentTypes['FirebaseExperiments']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['FirebaseExperiment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type FirebaseMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseMetric'] = ResolversParentTypes['FirebaseMetric']> = ResolversObject<{
  activeUserNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  adRevNthDayGrossAmounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  ads?: Resolver<Array<ResolversTypes['FirebaseAdMetric']>, ParentType, ContextType>;
  countryCode?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  dailyActiveUserCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  impressionNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  installCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  playtimeMsec?: Resolver<ResolversTypes['BigInt'], ParentType, ContextType>;
  playtimeNthDayMsecs?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  retentionNthDayRates?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  retentionRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  sessionCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  sessionCountPerActiveUser?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  sessionNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  variantId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  version?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type FirebaseMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseMetrics'] = ResolversParentTypes['FirebaseMetrics']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['FirebaseMetric']>, ParentType, ContextType>;
  variants?: Resolver<Array<ResolversTypes['FirebaseVersionVariant']>, ParentType, ContextType>;
  viewPreset?: Resolver<ResolversTypes['ViewPreset'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type FirebaseVersionVariantResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseVersionVariant'] = ResolversParentTypes['FirebaseVersionVariant']> = ResolversObject<{
  experiment?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type FirebaseVersionVariantsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['FirebaseVersionVariants'] = ResolversParentTypes['FirebaseVersionVariants']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['FirebaseVersionVariant']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Game'] = ResolversParentTypes['Game']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  isActive?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  isInhouse?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  packageName?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  platform?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameAgencyCostResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameAgencyCost'] = ResolversParentTypes['GameAgencyCost']> = ResolversObject<{
  agency?: Resolver<ResolversTypes['AdAgency'], ParentType, ContextType>;
  agencyId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  mediationCost?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  metric?: Resolver<ResolversTypes['AgencyMetric'], ParentType, ContextType>;
  totalCost?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  varianceRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameAgencyCostsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameAgencyCosts'] = ResolversParentTypes['GameAgencyCosts']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameAgencyCost']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameCostResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameCost'] = ResolversParentTypes['GameCost']> = ResolversObject<{
  date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  mmpAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  network?: Resolver<ResolversTypes['AdNetwork'], ParentType, ContextType>;
  preTaxAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  taxAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  totalAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameCostsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameCosts'] = ResolversParentTypes['GameCosts']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameCost']>, ParentType, ContextType>;
  meta?: Resolver<Maybe<ResolversTypes['GameCostsMeta']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameCostsAggregationResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameCostsAggregation'] = ResolversParentTypes['GameCostsAggregation']> = ResolversObject<{
  mmpAmount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  preTaxAmount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  totalAmount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameCostsMetaResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameCostsMeta'] = ResolversParentTypes['GameCostsMeta']> = ResolversObject<{
  aggregation?: Resolver<Maybe<ResolversTypes['GameCostsAggregation']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameCreativeMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameCreativeMetric'] = ResolversParentTypes['GameCreativeMetric']> = ResolversObject<{
  adGroup?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  adGroupStartDate?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  adSet?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  agency?: Resolver<ResolversTypes['AdAgency'], ParentType, ContextType>;
  campaign?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  clickCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  clickthroughRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  conversionRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  cpi?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  editor?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  grossRevenueAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  impressionCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  installCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  isPlayable?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  preTaxCostAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  roasRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  targetRoasRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameCreativeMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameCreativeMetrics'] = ResolversParentTypes['GameCreativeMetrics']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameCreativeMetric']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameLevelDropResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameLevelDrop'] = ResolversParentTypes['GameLevelDrop']> = ResolversObject<{
  activeUserCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  attemptCountPerUser?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  completionRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  level?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  overallUserDropRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  playtimeSecPerUser?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  userDropCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  userDropRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  version?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  winRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  world?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameLevelDropsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameLevelDrops'] = ResolversParentTypes['GameLevelDrops']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameLevelDrop']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameMetric'] = ResolversParentTypes['GameMetric']> = ResolversObject<{
  adaptiveAdmobImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  aoaAdmobImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  aoaImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  audioImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  averageSession?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  bannerImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  collapseAdmobImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  cost?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  cpi?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  interImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  metadata?: Resolver<Maybe<ResolversTypes['GameMetricMetadata']>, ParentType, ContextType>;
  monetNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  mrecAdmobImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  mrecImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  nativeAdmobImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  note?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  organicInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  organicPercentage?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  paidInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  playtime?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  productNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  profit?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionRateDay1?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionRateDay3?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionRateDay7?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  revenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  rewardImpsDau?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  roas?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  totalInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  uaNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  versionNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameMetricMetadataResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameMetricMetadata'] = ResolversParentTypes['GameMetricMetadata']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  monetNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  note?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  productNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  uaNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  versionNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameMetrics'] = ResolversParentTypes['GameMetrics']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameMetric']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameNetworkRevenueResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameNetworkRevenue'] = ResolversParentTypes['GameNetworkRevenue']> = ResolversObject<{
  date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  mediationRevenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  metric?: Resolver<ResolversTypes['AgencyMetric'], ParentType, ContextType>;
  network?: Resolver<ResolversTypes['AdNetwork'], ParentType, ContextType>;
  networkId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  revenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  varianceRate?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameNetworkRevenuesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameNetworkRevenues'] = ResolversParentTypes['GameNetworkRevenues']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameNetworkRevenue']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GamePerformanceCriteriaResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GamePerformanceCriteria'] = ResolversParentTypes['GamePerformanceCriteria']> = ResolversObject<{
  conclusion?: Resolver<ResolversTypes['GamePerformanceCriteriaConclusion'], ParentType, ContextType>;
  metrics?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GamePerformanceSettingResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GamePerformanceSetting'] = ResolversParentTypes['GamePerformanceSetting']> = ResolversObject<{
  criterias?: Resolver<Array<ResolversTypes['GamePerformanceCriteria']>, ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRetentionRateResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRetentionRate'] = ResolversParentTypes['GameRetentionRate']> = ResolversObject<{
  date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  day1?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  day2?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  day3?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  day4?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  day5?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  day6?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  day7?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  newUsers?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRetentionRatesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRetentionRates'] = ResolversParentTypes['GameRetentionRates']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameRetentionRate']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameReviewResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameReview'] = ResolversParentTypes['GameReview']> = ResolversObject<{
  date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  marketingNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  productNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRewardUsageResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRewardUsage'] = ResolversParentTypes['GameRewardUsage']> = ResolversObject<{
  level?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  location?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  useCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  world?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRewardUsagesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRewardUsages'] = ResolversParentTypes['GameRewardUsages']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameRewardUsage']>, ParentType, ContextType>;
  versions?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRoleResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRole'] = ResolversParentTypes['GameRole']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  users?: Resolver<Array<ResolversTypes['GameRoleUser']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRoleMembershipResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRoleMembership'] = ResolversParentTypes['GameRoleMembership']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  roleId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  storeId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  users?: Resolver<Array<ResolversTypes['GameRoleMembershipUser']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRoleMembershipUserResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRoleMembershipUser'] = ResolversParentTypes['GameRoleMembershipUser']> = ResolversObject<{
  email?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRoleUserResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRoleUser'] = ResolversParentTypes['GameRoleUser']> = ResolversObject<{
  email?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameRolesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameRoles'] = ResolversParentTypes['GameRoles']> = ResolversObject<{
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  roles?: Resolver<Array<ResolversTypes['GameRole']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameStudioResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameStudio'] = ResolversParentTypes['GameStudio']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameStudioMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameStudioMetric'] = ResolversParentTypes['GameStudioMetric']> = ResolversObject<{
  game?: Resolver<ResolversTypes['Game'], ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  mmpCostAmount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  profit?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  revenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  totalAgencyCost?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  totalInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameStudioMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameStudioMetrics'] = ResolversParentTypes['GameStudioMetrics']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameStudioMetric']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GameStudiosResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GameStudios'] = ResolversParentTypes['GameStudios']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameStudio']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GamesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Games'] = ResolversParentTypes['Games']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['Game']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GamesRolesResolvers<ContextType = Context, ParentType extends ResolversParentTypes['GamesRoles'] = ResolversParentTypes['GamesRoles']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['GameRoles']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface JsonScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['JSON'], any> {
  name: 'JSON';
}

export type MediationResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Mediation'] = ResolversParentTypes['Mediation']> = ResolversObject<{
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MediationsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Mediations'] = ResolversParentTypes['Mediations']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['Mediation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MetabaseChartResolvers<ContextType = Context, ParentType extends ResolversParentTypes['MetabaseChart'] = ResolversParentTypes['MetabaseChart']> = ResolversObject<{
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MutationResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = ResolversObject<{
  createBudgetRequest?: Resolver<ResolversTypes['BudgetRequest'], ParentType, ContextType, RequireFields<MutationCreateBudgetRequestArgs, 'form'>>;
  createTeam?: Resolver<ResolversTypes['Team'], ParentType, ContextType, RequireFields<MutationCreateTeamArgs, 'form'>>;
  createUser?: Resolver<ResolversTypes['User'], ParentType, ContextType, RequireFields<MutationCreateUserArgs, 'form'>>;
  createViewPreset?: Resolver<ResolversTypes['ViewPreset'], ParentType, ContextType, RequireFields<MutationCreateViewPresetArgs, 'form'>>;
  createWorkflow?: Resolver<ResolversTypes['Workflow'], ParentType, ContextType, RequireFields<MutationCreateWorkflowArgs, 'form'>>;
  deleteBudgetRequests?: Resolver<Maybe<ResolversTypes['Void']>, ParentType, ContextType, RequireFields<MutationDeleteBudgetRequestsArgs, 'where'>>;
  deleteTeam?: Resolver<Maybe<ResolversTypes['Void']>, ParentType, ContextType, RequireFields<MutationDeleteTeamArgs, 'where'>>;
  deleteUsers?: Resolver<Maybe<ResolversTypes['Void']>, ParentType, ContextType, RequireFields<MutationDeleteUsersArgs, 'where'>>;
  deleteViewPresets?: Resolver<Maybe<ResolversTypes['Void']>, ParentType, ContextType, RequireFields<MutationDeleteViewPresetsArgs, 'where'>>;
  updateAccessControl?: Resolver<ResolversTypes['AccessControl'], ParentType, ContextType, RequireFields<MutationUpdateAccessControlArgs, 'form' | 'where'>>;
  updateBudgetRequests?: Resolver<Array<ResolversTypes['BudgetRequest']>, ParentType, ContextType, RequireFields<MutationUpdateBudgetRequestsArgs, 'forms'>>;
  updateBudgetRequestsState?: Resolver<Array<ResolversTypes['BudgetRequest']>, ParentType, ContextType, RequireFields<MutationUpdateBudgetRequestsStateArgs, 'forms'>>;
  updateConfigMaps?: Resolver<Maybe<ResolversTypes['Void']>, ParentType, ContextType, RequireFields<MutationUpdateConfigMapsArgs, 'where'>>;
  updateGameCost?: Resolver<Maybe<ResolversTypes['Void']>, ParentType, ContextType, RequireFields<MutationUpdateGameCostArgs, 'form' | 'where'>>;
  updateGameMetric?: Resolver<ResolversTypes['GameMetric'], ParentType, ContextType, RequireFields<MutationUpdateGameMetricArgs, 'form' | 'where'>>;
  updateGameRoles?: Resolver<ResolversTypes['GamesRoles'], ParentType, ContextType, RequireFields<MutationUpdateGameRolesArgs, 'forms'>>;
  updateProfile?: Resolver<ResolversTypes['User'], ParentType, ContextType, RequireFields<MutationUpdateProfileArgs, 'form'>>;
  updateTeam?: Resolver<ResolversTypes['Team'], ParentType, ContextType, RequireFields<MutationUpdateTeamArgs, 'form' | 'where'>>;
  updateUser?: Resolver<ResolversTypes['User'], ParentType, ContextType, RequireFields<MutationUpdateUserArgs, 'form' | 'where'>>;
  updateWorkflow?: Resolver<ResolversTypes['Workflow'], ParentType, ContextType, RequireFields<MutationUpdateWorkflowArgs, 'form'>>;
}>;

export type NetworkResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Network'] = ResolversParentTypes['Network']> = ResolversObject<{
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type NetworksResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Networks'] = ResolversParentTypes['Networks']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['Network']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type PageInfoResolvers<ContextType = Context, ParentType extends ResolversParentTypes['PageInfo'] = ResolversParentTypes['PageInfo']> = ResolversObject<{
  currentPage?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  firstPage?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  lastPage?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  perPage?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  total?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type PlaytimeAggregationResolvers<ContextType = Context, ParentType extends ResolversParentTypes['PlaytimeAggregation'] = ResolversParentTypes['PlaytimeAggregation']> = ResolversObject<{
  engagementSessionCount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  playtimeSec?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type QueryResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = ResolversObject<{
  accessControl?: Resolver<ResolversTypes['AccessControl'], ParentType, ContextType, RequireFields<QueryAccessControlArgs, 'where'>>;
  adMetrics?: Resolver<ResolversTypes['AdMetrics'], ParentType, ContextType, RequireFields<QueryAdMetricsArgs, 'group' | 'where'>>;
  adNetworks?: Resolver<ResolversTypes['AdNetworks'], ParentType, ContextType>;
  adTypes?: Resolver<ResolversTypes['AdTypes'], ParentType, ContextType>;
  admobGames?: Resolver<ResolversTypes['AdmobGames'], ParentType, ContextType>;
  admobMetrics?: Resolver<ResolversTypes['AdmobMetrics'], ParentType, ContextType, RequireFields<QueryAdmobMetricsArgs, 'where'>>;
  aggregateGameAgencyCost?: Resolver<ResolversTypes['GameAgencyCosts'], ParentType, ContextType, RequireFields<QueryAggregateGameAgencyCostArgs, 'where'>>;
  aggregateGameCost?: Resolver<ResolversTypes['GameCost'], ParentType, ContextType, RequireFields<QueryAggregateGameCostArgs, 'id' | 'where'>>;
  aggregateGameMetric?: Resolver<ResolversTypes['GameMetric'], ParentType, ContextType, RequireFields<QueryAggregateGameMetricArgs, 'where'>>;
  aggregateGameNetworkRevenue?: Resolver<ResolversTypes['GameNetworkRevenues'], ParentType, ContextType, RequireFields<QueryAggregateGameNetworkRevenueArgs, 'where'>>;
  aggregatePlaytime?: Resolver<ResolversTypes['PlaytimeAggregation'], ParentType, ContextType, RequireFields<QueryAggregatePlaytimeArgs, 'where'>>;
  attributes?: Resolver<ResolversTypes['Attributes'], ParentType, ContextType, RequireFields<QueryAttributesArgs, 'where'>>;
  budgetRequests?: Resolver<ResolversTypes['BudgetRequests'], ParentType, ContextType, RequireFields<QueryBudgetRequestsArgs, 'where'>>;
  configMapCollections?: Resolver<Array<ResolversTypes['ConfigMapCollection']>, ParentType, ContextType, RequireFields<QueryConfigMapCollectionsArgs, 'ids'>>;
  configMaps?: Resolver<Array<ResolversTypes['ConfigMap']>, ParentType, ContextType, RequireFields<QueryConfigMapsArgs, 'ids'>>;
  dashboardNotifications?: Resolver<ResolversTypes['DashboardNotifications'], ParentType, ContextType>;
  firebaseExperiments?: Resolver<ResolversTypes['FirebaseExperiments'], ParentType, ContextType, RequireFields<QueryFirebaseExperimentsArgs, 'where'>>;
  firebaseMetrics?: Resolver<ResolversTypes['FirebaseMetrics'], ParentType, ContextType, RequireFields<QueryFirebaseMetricsArgs, 'where'>>;
  firebaseVersionVariants?: Resolver<ResolversTypes['FirebaseVersionVariants'], ParentType, ContextType, RequireFields<QueryFirebaseVersionVariantsArgs, 'where'>>;
  game?: Resolver<ResolversTypes['Game'], ParentType, ContextType, RequireFields<QueryGameArgs, 'id'>>;
  gameAgencyCosts?: Resolver<ResolversTypes['GameAgencyCosts'], ParentType, ContextType, RequireFields<QueryGameAgencyCostsArgs, 'order' | 'where'>>;
  gameCosts?: Resolver<ResolversTypes['GameCosts'], ParentType, ContextType, RequireFields<QueryGameCostsArgs, 'where'>>;
  gameCreativeMetrics?: Resolver<ResolversTypes['GameCreativeMetrics'], ParentType, ContextType, RequireFields<QueryGameCreativeMetricsArgs, 'order' | 'where'>>;
  gameDailyLevelDropVersions?: Resolver<ResolversTypes['Versions'], ParentType, ContextType, RequireFields<QueryGameDailyLevelDropVersionsArgs, 'where'>>;
  gameLevelDropVersions?: Resolver<ResolversTypes['Versions'], ParentType, ContextType, RequireFields<QueryGameLevelDropVersionsArgs, 'where'>>;
  gameLevelDrops?: Resolver<ResolversTypes['GameLevelDrops'], ParentType, ContextType, RequireFields<QueryGameLevelDropsArgs, 'where'>>;
  gameMetrics?: Resolver<ResolversTypes['GameMetrics'], ParentType, ContextType, RequireFields<QueryGameMetricsArgs, 'where'>>;
  gameNetworkRevenues?: Resolver<ResolversTypes['GameNetworkRevenues'], ParentType, ContextType, RequireFields<QueryGameNetworkRevenuesArgs, 'order' | 'where'>>;
  gamePerformanceSetting?: Resolver<ResolversTypes['GamePerformanceSetting'], ParentType, ContextType, RequireFields<QueryGamePerformanceSettingArgs, 'gameId'>>;
  gamePlaytimeVersions?: Resolver<ResolversTypes['Versions'], ParentType, ContextType, RequireFields<QueryGamePlaytimeVersionsArgs, 'where'>>;
  gameRetentionRateVersions?: Resolver<ResolversTypes['Versions'], ParentType, ContextType, RequireFields<QueryGameRetentionRateVersionsArgs, 'where'>>;
  gameRetentionRates?: Resolver<ResolversTypes['GameRetentionRates'], ParentType, ContextType, RequireFields<QueryGameRetentionRatesArgs, 'where'>>;
  gameReview?: Resolver<ResolversTypes['GameReview'], ParentType, ContextType, RequireFields<QueryGameReviewArgs, 'date' | 'gameId'>>;
  gameRewardUsageVersions?: Resolver<ResolversTypes['Versions'], ParentType, ContextType, RequireFields<QueryGameRewardUsageVersionsArgs, 'where'>>;
  gameRewardUsages?: Resolver<ResolversTypes['GameRewardUsages'], ParentType, ContextType, RequireFields<QueryGameRewardUsagesArgs, 'group' | 'order' | 'where'>>;
  gameRoles?: Resolver<ResolversTypes['GamesRoles'], ParentType, ContextType, RequireFields<QueryGameRolesArgs, 'gameIds'>>;
  gameStudioMetrics?: Resolver<ResolversTypes['GameStudioMetrics'], ParentType, ContextType, RequireFields<QueryGameStudioMetricsArgs, 'where'>>;
  gameStudios?: Resolver<ResolversTypes['GameStudios'], ParentType, ContextType>;
  games?: Resolver<ResolversTypes['Games'], ParentType, ContextType, RequireFields<QueryGamesArgs, 'where'>>;
  mediations?: Resolver<ResolversTypes['Mediations'], ParentType, ContextType>;
  metabaseChart?: Resolver<ResolversTypes['MetabaseChart'], ParentType, ContextType, RequireFields<QueryMetabaseChartArgs, 'where'>>;
  networks?: Resolver<ResolversTypes['Networks'], ParentType, ContextType, RequireFields<QueryNetworksArgs, 'where'>>;
  profile?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  releaseMetrics?: Resolver<ResolversTypes['ReleaseMetrics'], ParentType, ContextType, RequireFields<QueryReleaseMetricsArgs, 'where'>>;
  releaseVersions?: Resolver<ResolversTypes['ReleaseVersions'], ParentType, ContextType, RequireFields<QueryReleaseVersionsArgs, 'where'>>;
  sideMenus?: Resolver<ResolversTypes['SideMenus'], ParentType, ContextType>;
  team?: Resolver<ResolversTypes['Team'], ParentType, ContextType, RequireFields<QueryTeamArgs, 'id'>>;
  teams?: Resolver<Array<ResolversTypes['Team']>, ParentType, ContextType>;
  users?: Resolver<ResolversTypes['Users'], ParentType, ContextType, RequireFields<QueryUsersArgs, 'where'>>;
  v2?: Resolver<ResolversTypes['V2Query'], ParentType, ContextType>;
  viewPresets?: Resolver<ResolversTypes['ViewPresets'], ParentType, ContextType, RequireFields<QueryViewPresetsArgs, 'where'>>;
  workflowStepActions?: Resolver<ResolversTypes['WorkflowStepActions'], ParentType, ContextType>;
  workflows?: Resolver<ResolversTypes['Workflows'], ParentType, ContextType>;
}>;

export type ReleaseAdMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ReleaseAdMetric'] = ResolversParentTypes['ReleaseAdMetric']> = ResolversObject<{
  adRevGrossAmount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  adRevGrossAmountPerActiveUser?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  adRevNthDayGrossAmounts?: Resolver<Maybe<ResolversTypes['JSON']>, ParentType, ContextType>;
  adTypeCategory?: Resolver<Maybe<ResolversTypes['AdTypeCategory']>, ParentType, ContextType>;
  impressionCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  impressionCountPerActiveUser?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  impressionNthDayCounts?: Resolver<Maybe<ResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ReleaseMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ReleaseMetric'] = ResolversParentTypes['ReleaseMetric']> = ResolversObject<{
  activeUserCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  activeUserNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  adRevGrossAmount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  adRevGrossAmountPerActiveUser?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  adRevNthDayGrossAmounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  ads?: Resolver<Maybe<Array<ResolversTypes['ReleaseAdMetric']>>, ParentType, ContextType>;
  countryCode?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  dailyActiveUserCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  impressionCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  impressionCountPerActiveUser?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  impressionNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  installCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  lifetimeNthDayValues?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  playtimeMsec?: Resolver<Maybe<ResolversTypes['BigInt']>, ParentType, ContextType>;
  playtimeNthDayMsecs?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  retentionNthDayRates?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  retentionRate?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  sessionCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  sessionCountPerActiveUser?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  sessionNthDayCounts?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  version?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ReleaseMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ReleaseMetrics'] = ResolversParentTypes['ReleaseMetrics']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['ReleaseMetric']>, ParentType, ContextType>;
  viewPreset?: Resolver<ResolversTypes['ViewPreset'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ReleaseVersionResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ReleaseVersion'] = ResolversParentTypes['ReleaseVersion']> = ResolversObject<{
  version?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ReleaseVersionsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ReleaseVersions'] = ResolversParentTypes['ReleaseVersions']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['ReleaseVersion']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type RoleAccessControlResolvers<ContextType = Context, ParentType extends ResolversParentTypes['RoleAccessControl'] = ResolversParentTypes['RoleAccessControl']> = ResolversObject<{
  permits?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  roleId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  subject?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type SideMenuResolvers<ContextType = Context, ParentType extends ResolversParentTypes['SideMenu'] = ResolversParentTypes['SideMenu']> = ResolversObject<{
  group?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  icon?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  path?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type SideMenusResolvers<ContextType = Context, ParentType extends ResolversParentTypes['SideMenus'] = ResolversParentTypes['SideMenus']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['SideMenu']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type TeamResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Team'] = ResolversParentTypes['Team']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  leader?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  leaderId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  members?: Resolver<Array<ResolversTypes['User']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  roleId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type TeamsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Teams'] = ResolversParentTypes['Teams']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['Team']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ToolPermissionResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ToolPermission'] = ResolversParentTypes['ToolPermission']> = ResolversObject<{
  action?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  tool?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface UuidScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['UUID'], any> {
  name: 'UUID';
}

export type UserResolvers<ContextType = Context, ParentType extends ResolversParentTypes['User'] = ResolversParentTypes['User']> = ResolversObject<{
  email?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  fullName?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  hasPassword?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  inchargedGames?: Resolver<Array<ResolversTypes['UserInchargedGame']>, ParentType, ContextType>;
  isDeleted?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  kind?: Resolver<ResolversTypes['UserKind'], ParentType, ContextType>;
  ledTeam?: Resolver<Maybe<ResolversTypes['Team']>, ParentType, ContextType>;
  note?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  team?: Resolver<Maybe<ResolversTypes['Team']>, ParentType, ContextType>;
  teamLead?: Resolver<Maybe<ResolversTypes['Team']>, ParentType, ContextType>;
  toolPermissions?: Resolver<Array<ResolversTypes['ToolPermission']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type UserInchargedGameResolvers<ContextType = Context, ParentType extends ResolversParentTypes['UserInchargedGame'] = ResolversParentTypes['UserInchargedGame']> = ResolversObject<{
  roleId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  storeId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type UserPublicInfoResolvers<ContextType = Context, ParentType extends ResolversParentTypes['UserPublicInfo'] = ResolversParentTypes['UserPublicInfo']> = ResolversObject<{
  fullName?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type UsersResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Users'] = ResolversParentTypes['Users']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['User']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type V2GameMetricResolvers<ContextType = Context, ParentType extends ResolversParentTypes['V2GameMetric'] = ResolversParentTypes['V2GameMetric']> = ResolversObject<{
  adPerformances?: Resolver<Array<ResolversTypes['AdPerformance']>, ParentType, ContextType>;
  cost?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  cpi?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  dailyActiveUsers?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  gameId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  monetNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  note?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  organicInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  organicPercentage?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  paidInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  playtime?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  productNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  profit?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionRateDay1?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionRateDay3?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  retentionRateDay7?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  revenue?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  roas?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  sessions?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  totalInstalls?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  uaNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  versionNote?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type V2GameMetricsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['V2GameMetrics'] = ResolversParentTypes['V2GameMetrics']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['V2GameMetric']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type V2QueryResolvers<ContextType = Context, ParentType extends ResolversParentTypes['V2Query'] = ResolversParentTypes['V2Query']> = ResolversObject<{
  aggregateGameMetrics?: Resolver<ResolversTypes['V2GameMetric'], ParentType, ContextType, RequireFields<V2QueryAggregateGameMetricsArgs, 'id' | 'where'>>;
  gameMetrics?: Resolver<ResolversTypes['V2GameMetrics'], ParentType, ContextType, RequireFields<V2QueryGameMetricsArgs, 'where'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type VersionsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Versions'] = ResolversParentTypes['Versions']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ViewPresetResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ViewPreset'] = ResolversParentTypes['ViewPreset']> = ResolversObject<{
  attributes?: Resolver<Array<ResolversTypes['ViewPresetAttribute']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ViewPresetAttributeResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ViewPresetAttribute'] = ResolversParentTypes['ViewPresetAttribute']> = ResolversObject<{
  cohortDays?: Resolver<Array<ResolversTypes['Int']>, ParentType, ContextType>;
  isCohort?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type ViewPresetsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ViewPresets'] = ResolversParentTypes['ViewPresets']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['ViewPreset']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface VoidScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Void'], any> {
  name: 'Void';
}

export type WorkflowResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Workflow'] = ResolversParentTypes['Workflow']> = ResolversObject<{
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  roleId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  steps?: Resolver<Array<ResolversTypes['WorkflowStep']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type WorkflowStepResolvers<ContextType = Context, ParentType extends ResolversParentTypes['WorkflowStep'] = ResolversParentTypes['WorkflowStep']> = ResolversObject<{
  action?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  alternateAction?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  assignee?: Resolver<ResolversTypes['UserPublicInfo'], ParentType, ContextType>;
  assigneeId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type WorkflowStepActionResolvers<ContextType = Context, ParentType extends ResolversParentTypes['WorkflowStepAction'] = ResolversParentTypes['WorkflowStepAction']> = ResolversObject<{
  action?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type WorkflowStepActionsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['WorkflowStepActions'] = ResolversParentTypes['WorkflowStepActions']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['WorkflowStepAction']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type WorkflowsResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Workflows'] = ResolversParentTypes['Workflows']> = ResolversObject<{
  collection?: Resolver<Array<ResolversTypes['Workflow']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type Resolvers<ContextType = Context> = ResolversObject<{
  AccessControl?: AccessControlResolvers<ContextType>;
  Ad?: AdResolvers<ContextType>;
  AdAgency?: AdAgencyResolvers<ContextType>;
  AdGroup?: AdGroupResolvers<ContextType>;
  AdMetric?: AdMetricResolvers<ContextType>;
  AdMetrics?: AdMetricsResolvers<ContextType>;
  AdNetwork?: AdNetworkResolvers<ContextType>;
  AdNetworks?: AdNetworksResolvers<ContextType>;
  AdPerformance?: AdPerformanceResolvers<ContextType>;
  AdType?: AdTypeResolvers<ContextType>;
  AdTypes?: AdTypesResolvers<ContextType>;
  AdmobGames?: AdmobGamesResolvers<ContextType>;
  AdmobMetrics?: AdmobMetricsResolvers<ContextType>;
  AgencyMetric?: AgencyMetricResolvers<ContextType>;
  Attribute?: AttributeResolvers<ContextType>;
  Attributes?: AttributesResolvers<ContextType>;
  BigInt?: GraphQLScalarType;
  BudgetRequest?: BudgetRequestResolvers<ContextType>;
  BudgetRequests?: BudgetRequestsResolvers<ContextType>;
  Campaign?: CampaignResolvers<ContextType>;
  ConfigMap?: ConfigMapResolvers<ContextType>;
  ConfigMapCollection?: ConfigMapCollectionResolvers<ContextType>;
  DashboardNotification?: DashboardNotificationResolvers<ContextType>;
  DashboardNotifications?: DashboardNotificationsResolvers<ContextType>;
  Date?: GraphQLScalarType;
  FilterValue?: GraphQLScalarType;
  FirebaseAdMetric?: FirebaseAdMetricResolvers<ContextType>;
  FirebaseExperiment?: FirebaseExperimentResolvers<ContextType>;
  FirebaseExperiments?: FirebaseExperimentsResolvers<ContextType>;
  FirebaseMetric?: FirebaseMetricResolvers<ContextType>;
  FirebaseMetrics?: FirebaseMetricsResolvers<ContextType>;
  FirebaseVersionVariant?: FirebaseVersionVariantResolvers<ContextType>;
  FirebaseVersionVariants?: FirebaseVersionVariantsResolvers<ContextType>;
  Game?: GameResolvers<ContextType>;
  GameAgencyCost?: GameAgencyCostResolvers<ContextType>;
  GameAgencyCosts?: GameAgencyCostsResolvers<ContextType>;
  GameCost?: GameCostResolvers<ContextType>;
  GameCosts?: GameCostsResolvers<ContextType>;
  GameCostsAggregation?: GameCostsAggregationResolvers<ContextType>;
  GameCostsMeta?: GameCostsMetaResolvers<ContextType>;
  GameCreativeMetric?: GameCreativeMetricResolvers<ContextType>;
  GameCreativeMetrics?: GameCreativeMetricsResolvers<ContextType>;
  GameLevelDrop?: GameLevelDropResolvers<ContextType>;
  GameLevelDrops?: GameLevelDropsResolvers<ContextType>;
  GameMetric?: GameMetricResolvers<ContextType>;
  GameMetricMetadata?: GameMetricMetadataResolvers<ContextType>;
  GameMetrics?: GameMetricsResolvers<ContextType>;
  GameNetworkRevenue?: GameNetworkRevenueResolvers<ContextType>;
  GameNetworkRevenues?: GameNetworkRevenuesResolvers<ContextType>;
  GamePerformanceCriteria?: GamePerformanceCriteriaResolvers<ContextType>;
  GamePerformanceSetting?: GamePerformanceSettingResolvers<ContextType>;
  GameRetentionRate?: GameRetentionRateResolvers<ContextType>;
  GameRetentionRates?: GameRetentionRatesResolvers<ContextType>;
  GameReview?: GameReviewResolvers<ContextType>;
  GameRewardUsage?: GameRewardUsageResolvers<ContextType>;
  GameRewardUsages?: GameRewardUsagesResolvers<ContextType>;
  GameRole?: GameRoleResolvers<ContextType>;
  GameRoleMembership?: GameRoleMembershipResolvers<ContextType>;
  GameRoleMembershipUser?: GameRoleMembershipUserResolvers<ContextType>;
  GameRoleUser?: GameRoleUserResolvers<ContextType>;
  GameRoles?: GameRolesResolvers<ContextType>;
  GameStudio?: GameStudioResolvers<ContextType>;
  GameStudioMetric?: GameStudioMetricResolvers<ContextType>;
  GameStudioMetrics?: GameStudioMetricsResolvers<ContextType>;
  GameStudios?: GameStudiosResolvers<ContextType>;
  Games?: GamesResolvers<ContextType>;
  GamesRoles?: GamesRolesResolvers<ContextType>;
  JSON?: GraphQLScalarType;
  Mediation?: MediationResolvers<ContextType>;
  Mediations?: MediationsResolvers<ContextType>;
  MetabaseChart?: MetabaseChartResolvers<ContextType>;
  Mutation?: MutationResolvers<ContextType>;
  Network?: NetworkResolvers<ContextType>;
  Networks?: NetworksResolvers<ContextType>;
  PageInfo?: PageInfoResolvers<ContextType>;
  PlaytimeAggregation?: PlaytimeAggregationResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  ReleaseAdMetric?: ReleaseAdMetricResolvers<ContextType>;
  ReleaseMetric?: ReleaseMetricResolvers<ContextType>;
  ReleaseMetrics?: ReleaseMetricsResolvers<ContextType>;
  ReleaseVersion?: ReleaseVersionResolvers<ContextType>;
  ReleaseVersions?: ReleaseVersionsResolvers<ContextType>;
  RoleAccessControl?: RoleAccessControlResolvers<ContextType>;
  SideMenu?: SideMenuResolvers<ContextType>;
  SideMenus?: SideMenusResolvers<ContextType>;
  Team?: TeamResolvers<ContextType>;
  Teams?: TeamsResolvers<ContextType>;
  ToolPermission?: ToolPermissionResolvers<ContextType>;
  UUID?: GraphQLScalarType;
  User?: UserResolvers<ContextType>;
  UserInchargedGame?: UserInchargedGameResolvers<ContextType>;
  UserPublicInfo?: UserPublicInfoResolvers<ContextType>;
  Users?: UsersResolvers<ContextType>;
  V2GameMetric?: V2GameMetricResolvers<ContextType>;
  V2GameMetrics?: V2GameMetricsResolvers<ContextType>;
  V2Query?: V2QueryResolvers<ContextType>;
  Versions?: VersionsResolvers<ContextType>;
  ViewPreset?: ViewPresetResolvers<ContextType>;
  ViewPresetAttribute?: ViewPresetAttributeResolvers<ContextType>;
  ViewPresets?: ViewPresetsResolvers<ContextType>;
  Void?: GraphQLScalarType;
  Workflow?: WorkflowResolvers<ContextType>;
  WorkflowStep?: WorkflowStepResolvers<ContextType>;
  WorkflowStepAction?: WorkflowStepActionResolvers<ContextType>;
  WorkflowStepActions?: WorkflowStepActionsResolvers<ContextType>;
  Workflows?: WorkflowsResolvers<ContextType>;
}>;

