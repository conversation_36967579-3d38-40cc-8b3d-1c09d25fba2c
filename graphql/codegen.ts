import type { CodegenConfig } from '@graphql-codegen/cli'
import { BigIntResolver } from 'graphql-scalars'

const config: CodegenConfig = {
  overwrite: true,
  schema: 'graphql/schema.graphql',
  documents: ['resources/js/**/*.tsx', '!resources/js/graphql/**/*', 'resources/js/graphql.ts'],
  ignoreNoDocuments: true,
  generates: {
    'graphql/main.ts': {
      plugins: ['typescript', 'typescript-resolvers'],
      config: {
        useIndexSignature: true,
        contextType: './context.js#Context',
        scalars: {
          BigInt: BigIntResolver.extensions.codegenScalarType,
        },
      },
    },
    './graphql/graphql.schema.json': {
      plugins: ['introspection'],
    },
    './resources/js/gql/': {
      preset: 'client',
      presetConfig: {
        fragmentMasking: false,
      },
    },
  },
}

export default config
