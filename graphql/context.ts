import { ContainerResolver } from '@adonisjs/core/container'
import { HttpContext } from '@adonisjs/core/http'
import { ContainerBindings } from '@adonisjs/core/types'

import { GameMetricMetadataLoader } from '#dataloaders/game_metric_metadata_loader'
import { AdPerformanceLoader } from '#dataloaders/ad_performance_loader'
import { AdNetworkLoader } from '#dataloaders/ad_network_loader'
import { AdAgencyLoader } from '#dataloaders/ad_agency_loader'
import { GameMetricLoader } from '#dataloaders/game_metric_loader'
import { GameLoader } from '#dataloaders/game_loader'
import { UserLoader } from '#dataloaders/user_loader'
import { WorkflowLoader } from '#dataloaders/workflow_loader'
import { AdGroupLoader } from '#dataloaders/ad_group_loader'
import { CampaignLoader } from '#dataloaders/campaign_loader'
import { FirebaseAdMetricLoader } from '#dataloaders/firebase_ad_metric_loader'
import { TeamLoader } from '#dataloaders/team_data_loader'
import { UserGameMembershipLoader } from '#dataloaders/user_game_membership_loader'
import { ReleaseAdMetricLoader } from '#dataloaders/release_ad_metric_loader'

import { DataSource } from './main.js'

export type Context = {
  resolver: ContainerResolver<ContainerBindings>
  http: HttpContext
  dataloaders: {
    gameMetricMetadata: GameMetricMetadataLoader
    adPerformance: AdPerformanceLoader
    adNetwork: AdNetworkLoader
    adAgency: AdAgencyLoader
    gameMetric: GameMetricLoader
    game: GameLoader
    user: UserLoader
    workflow: WorkflowLoader
    adGroup: AdGroupLoader
    campaign: CampaignLoader
    firebaseAdMetric: FirebaseAdMetricLoader
    releaseAdMetric: ReleaseAdMetricLoader
    team: TeamLoader
    userGameMembership: UserGameMembershipLoader
  }
  source: DataSource
}
