{"__schema": {"queryType": {"name": "Query", "kind": "OBJECT"}, "mutationType": {"name": "Mutation", "kind": "OBJECT"}, "subscriptionType": null, "types": [{"kind": "OBJECT", "name": "AccessControl", "description": null, "isOneOf": null, "fields": [{"name": "roles", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RoleAccessControl", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AccessControlRoleInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "permits", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AccessControlUpdateForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roles", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AccessControlRoleInput", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AccessControlWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "subject", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Ad", "description": null, "isOneOf": null, "fields": [{"name": "campaign", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Campaign", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "campaignId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "group", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdGroup", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "groupId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdAgency", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdGroup", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdMetric", "description": null, "isOneOf": null, "fields": [{"name": "activeUserNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adCostNonTaxAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevGrossAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevNthDayGrossAmounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "agencyId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "campaignId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "clickCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "cpc", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cpi", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ctr", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cvr", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dailyActiveUserCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "groupId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "installCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ipm", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionNthDayRates", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roas", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roasNthDayRates", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdMetrics", "description": null, "isOneOf": null, "fields": [{"name": "adGroups", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdGroup", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ads", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Ad", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "agencies", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdAgency", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "campaigns", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Campaign", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "viewPreset", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPreset", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AdMetricsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "adCostNonTaxAmount", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "adId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevGrossAmount", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "agencyId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "campaignId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "clickCount", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "cpc", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "cpi", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ctr", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "cvr", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "groupId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "installCount", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ipm", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roas", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdNetwork", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputType", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "AdNetworkCategory", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "APPSFLYER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FIREBASE", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "AdNetworks", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdNetwork", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdPerformance", "description": null, "isOneOf": null, "fields": [{"name": "adType", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "arpDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dailyActiveUserCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ecpm", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "imps<PERSON><PERSON>", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdType", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "AdTypeCategory", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "APP_OPEN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "AUDIO", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "BANNER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "COLLAPSIBLE_BANNER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "INTERSTITIAL", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MREC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "NATIVE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "REWARD", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "UNKNOWN", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "AdTypes", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdType", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdmobGames", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AdmobMetrics", "description": null, "isOneOf": null, "fields": [{"name": "mediation", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "network", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AdmobMetricsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "formats", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AgencyMetric", "description": null, "isOneOf": null, "fields": [{"name": "cost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paidInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AggregateGameAgencyCostWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AggregateGameCostWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AggregateGameNetworkRevenueWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AggregatePlaytimeWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "activeDateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "activeDateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "installDateFrom", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "installDateTo", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "versions", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Attribute", "description": null, "isOneOf": null, "fields": [{"name": "aggregate", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "displayName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "permission", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Attributes", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Attribute", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AttributesWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "gameId", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "modelName", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "BigInt", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BudgetRequest", "description": null, "isOneOf": null, "fields": [{"name": "amount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "created<PERSON>y", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "UserPublicInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdById", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "expirationDate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "game", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Game", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastAction", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "step", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "WorkflowStep", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "stepId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "workflow", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Workflow", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "workflowId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BudgetRequests", "description": null, "isOneOf": null, "fields": [{"name": "_debug", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BudgetRequest", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "BudgetRequestsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "assigneeId", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "lastAction", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "stepId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "workflowId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Campaign", "description": null, "isOneOf": null, "fields": [{"name": "agency", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "AdAgency", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "agencyId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ConfigMap", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sole", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ConfigMapCollection", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "items", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateBudgetRequestForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "amount", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "expirationDate", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "workflowId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateTeamForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "leaderEmail", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "memberEmails", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roleId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateUserForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "email", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "fullName", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hasPassword", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "kind", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "UserKind", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "password", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "toolPermissions", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ToolPermissionForm", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateViewPresetForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "attributes", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ViewPresetAttributeForm", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateWorkflowForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roleId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "steps", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "WorkflowStepForm", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DashboardNotification", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isPinned", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isVisible", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DashboardNotifications", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DashboardNotification", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "DataSource", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ORIGIN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "SNAPSHOT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "Date", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DeleteBudgetRequestsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "ids", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DeleteTeamWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DeleteUsersWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "ids", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DeleteViewPresetsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "ids", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "Filter", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "operator", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "FilterOperator", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "values", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "FilterValue", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "FilterOperator", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "BETWEEN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "EQ", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "GT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "GTE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "IN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LTE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "NE", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "FilterValue", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseAdMetric", "description": null, "isOneOf": null, "fields": [{"name": "adRevGrossAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevGrossAmountPerActiveUser", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adTypeCategory", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "AdTypeCategory", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCountPerActiveUser", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseExperiment", "description": null, "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseExperiments", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseExperiment", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "FirebaseExperimentsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseMetric", "description": null, "isOneOf": null, "fields": [{"name": "activeUserNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevNthDayGrossAmounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ads", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseAdMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dailyActiveUserCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "installCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtimeMsec", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "BigInt", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtimeNthDayMsecs", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionNthDayRates", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionCountPerActiveUser", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "variantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseMetrics", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "variants", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseVersionVariant", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "viewPreset", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPreset", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "FirebaseMetricsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "countryCode", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "experiment", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mediationId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "networkId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "variantId", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseVersionVariant", "description": null, "isOneOf": null, "fields": [{"name": "experiment", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "FirebaseVersionVariants", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseVersionVariant", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "FirebaseVersionVariantsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "experiment", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Game", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isActive", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isInhouse", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "packageName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "platform", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameAgencyCost", "description": null, "isOneOf": null, "fields": [{"name": "agency", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdAgency", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "agencyId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mediationCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metric", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AgencyMetric", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "varianceRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameAgencyCosts", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameAgencyCost", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameAgencyCostsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameCost", "description": null, "isOneOf": null, "fields": [{"name": "date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mmpAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "network", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdNetwork", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "preTaxAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "taxAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameCosts", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameCost", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "meta", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "GameCostsMeta", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameCostsAggregation", "description": null, "isOneOf": null, "fields": [{"name": "mmpAmount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "preTaxAmount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalAmount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameCostsMeta", "description": null, "isOneOf": null, "fields": [{"name": "aggregation", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "GameCostsAggregation", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameCostsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameCreativeMetric", "description": null, "isOneOf": null, "fields": [{"name": "adGroup", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adGroupStartDate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adSet", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "agency", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdAgency", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "campaign", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "clickCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "clickthroughRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "conversionRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cpi", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "editor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "grossRevenueAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "installCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isPlayable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "preTaxCostAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roasRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "targetRoasRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameCreativeMetrics", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameCreativeMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameCreativeMetricsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameLevelDrop", "description": null, "isOneOf": null, "fields": [{"name": "activeUserCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "attemptCountPerUser", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "completionRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "level", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "overallUserDropRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtimeSecPerUser", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "userDropCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "userDropRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "winRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "world", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameLevelDropVersionsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameLevelDrops", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameLevelDrop", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameLevelDropsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "installDateFrom", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "installDateTo", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "versions", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameMetric", "description": null, "isOneOf": null, "fields": [{"name": "adaptiveAdmobImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aoaAdmobImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aoaImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "audioImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "averageSession", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "bannerImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "collapseAdmobImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cpi", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metadata", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "GameMetricMetadata", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "monetNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mrecAdmobImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mrecImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nativeAdmobImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "organicInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "organicPercentage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paidInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "productNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "profit", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRateDay1", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRateDay3", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRateDay7", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "rewardImpsDau", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roas", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "uaNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "versionNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameMetricMetadata", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "monetNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "productNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "uaNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "versionNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameMetricWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateGte", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateLte", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameMetrics", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameNetworkRevenue", "description": null, "isOneOf": null, "fields": [{"name": "date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mediationRevenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metric", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AgencyMetric", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "network", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdNetwork", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "networkId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "varianceRate", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameNetworkRevenues", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameNetworkRevenue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameNetworkRevenuesWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GamePerformanceCriteria", "description": null, "isOneOf": null, "fields": [{"name": "conclusion", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "GamePerformanceCriteriaConclusion", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metrics", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "GamePerformanceCriteriaConclusion", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Drop", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Fail", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Pass", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "GamePerformanceSetting", "description": null, "isOneOf": null, "fields": [{"name": "criterias", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GamePerformanceCriteria", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRetentionRate", "description": null, "isOneOf": null, "fields": [{"name": "date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day1", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day2", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day3", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day4", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day5", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day6", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "day7", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "newUsers", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRetentionRates", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRetentionRate", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameRetentionRatesWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "versions", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameReview", "description": null, "isOneOf": null, "fields": [{"name": "date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "marketingNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "productNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRewardUsage", "description": null, "isOneOf": null, "fields": [{"name": "level", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "location", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "useCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "world", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRewardUsages", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRewardUsage", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "versions", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameRewardUsagesWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "versions", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRole", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "users", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRoleUser", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRoleMembership", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roleId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "storeId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "users", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRoleMembershipUser", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRoleMembershipUser", "description": null, "isOneOf": null, "fields": [{"name": "email", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRoleUser", "description": null, "isOneOf": null, "fields": [{"name": "email", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameRoles", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roles", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRole", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameStudio", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameStudioMetric", "description": null, "isOneOf": null, "fields": [{"name": "game", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Game", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mmpCostAmount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "profit", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalAgencyCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameStudioMetrics", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameStudioMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GameStudioMetricsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "studioId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GameStudios", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameStudio", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Games", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Game", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "GamesRoles", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRoles", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GamesWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "keyword", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "userIds", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "Group", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "fields", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "ID", "description": "The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `\"4\"`) or integer (such as `4`) input value will be accepted as an ID.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "JSON", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mediation", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mediations", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Mediation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Metabase<PERSON>hart", "description": null, "isOneOf": null, "fields": [{"name": "url", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "MetabaseChartParam", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "MetabaseChartWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "params", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "MetabaseChartParam", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "isOneOf": null, "fields": [{"name": "createBudgetRequest", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateBudgetRequestForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BudgetRequest", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTeam", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateTeamForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Team", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createUser", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateUserForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createViewPreset", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateViewPresetForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPreset", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createWorkflow", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateWorkflowForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Workflow", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteBudgetRequests", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DeleteBudgetRequestsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "SCALAR", "name": "Void", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteTeam", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DeleteTeamWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "SCALAR", "name": "Void", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteUsers", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DeleteUsersWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "SCALAR", "name": "Void", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteViewPresets", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DeleteViewPresetsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "SCALAR", "name": "Void", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateAccessControl", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AccessControlUpdateForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AccessControlWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AccessControl", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateBudgetRequests", "description": null, "args": [{"name": "forms", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateBudgetRequestForm", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BudgetRequest", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateBudgetRequestsState", "description": null, "args": [{"name": "forms", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateBudgetRequestStateForm", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BudgetRequest", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateConfigMaps", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateConfigMapsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "SCALAR", "name": "Void", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateGameCost", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameCostForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameCostWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "SCALAR", "name": "Void", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateGameMetric", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameMetric", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateGameRoles", "description": null, "args": [{"name": "forms", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameRolesGame", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GamesRoles", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateProfile", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateProfileForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTeam", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateTeamForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateTeamWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Team", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateUser", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateUserForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateUserWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateWorkflow", "description": null, "args": [{"name": "form", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateWorkflowForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Workflow", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Network", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Networks", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Network", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "NetworksWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "category", "description": null, "type": {"kind": "ENUM", "name": "AdNetworkCategory", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "Offset", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "page", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "perPage", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "Operator", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "eq", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gt", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gte", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "in", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "lt", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "lte", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ne", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "nin", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "Order", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "direction", "description": null, "type": {"kind": "ENUM", "name": "OrderDirection", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "field", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "OrderDirection", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "PageInfo", "description": null, "isOneOf": null, "fields": [{"name": "currentPage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "firstPage", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastPage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "perPage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "total", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "PlaytimeAggregation", "description": null, "isOneOf": null, "fields": [{"name": "engagementSessionCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtimeSec", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "Preset", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "viewPresetId", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ProductMetricVersionsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Query", "description": null, "isOneOf": null, "fields": [{"name": "accessControl", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AccessControlWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AccessControl", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adMetrics", "description": null, "args": [{"name": "group", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Group", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "preset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Preset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AdMetricsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adNetworks", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdNetworks", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adTypes", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdTypes", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "admobGames", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdmobGames", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "admobMetrics", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AdmobMetricsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdmobMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aggregateGameAgencyCost", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AggregateGameAgencyCostWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameAgencyCosts", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aggregateGameCost", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AggregateGameCostWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameCost", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aggregateGameMetric", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameMetricWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameMetric", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aggregateGameNetworkRevenue", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AggregateGameNetworkRevenueWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameNetworkRevenues", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "aggregatePlaytime", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AggregatePlaytimeWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PlaytimeAggregation", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "attributes", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AttributesWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Attributes", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "budgetRequests", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "BudgetRequestsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "BudgetRequests", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "configMapCollections", "description": null, "args": [{"name": "ids", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ConfigMapCollection", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "configMaps", "description": null, "args": [{"name": "ids", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ConfigMap", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dashboardNotifications", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DashboardNotifications", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "firebaseExperiments", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "FirebaseExperimentsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseExperiments", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "firebaseMetrics", "description": null, "args": [{"name": "group", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Group", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "preset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Preset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "FirebaseMetricsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "firebaseVersionVariants", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "FirebaseVersionVariantsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FirebaseVersionVariants", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "game", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Game", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameAgencyCosts", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Order", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameAgencyCostsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameAgencyCosts", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameCosts", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameCostsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameCosts", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameCreativeMetrics", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Order", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameCreativeMetricsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameCreativeMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameDailyLevelDropVersions", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ProductMetricVersionsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Versions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameLevelDropVersions", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameLevelDropVersionsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Versions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameLevelDrops", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameLevelDropsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameLevelDrops", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameMetrics", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Order", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameMetricWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameNetworkRevenues", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Order", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameNetworkRevenuesWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameNetworkRevenues", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gamePerformanceSetting", "description": null, "args": [{"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GamePerformanceSetting", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gamePlaytimeVersions", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ProductMetricVersionsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Versions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameRetentionRateVersions", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ProductMetricVersionsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Versions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameRetentionRates", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameRetentionRatesWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRetentionRates", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameReview", "description": null, "args": [{"name": "date", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameReview", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameRewardUsageVersions", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ProductMetricVersionsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Versions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameRewardUsages", "description": null, "args": [{"name": "group", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Group", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Order", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameRewardUsagesWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameRewardUsages", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameRoles", "description": null, "args": [{"name": "gameIds", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GamesRoles", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameStudioMetrics", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GameStudioMetricsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameStudioMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameStudios", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GameStudios", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "games", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GamesWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Games", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mediations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Mediations", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metabaseChart", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "MetabaseChartWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Metabase<PERSON>hart", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "networks", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "NetworksWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Networks", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "profile", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "releaseMetrics", "description": null, "args": [{"name": "group", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Group", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "preset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Preset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ReleaseMetricsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ReleaseMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "releaseVersions", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ReleaseVersionsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ReleaseVersions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sideMenus", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SideMenus", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "team", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Team", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "teams", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Team", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "users", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UsersWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Users", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "v2", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "V2Query", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "viewPresets", "description": null, "args": [{"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ViewPresetsWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPresets", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "workflowStepActions", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "WorkflowStepActions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "workflows", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Workflows", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ReleaseAdMetric", "description": null, "isOneOf": null, "fields": [{"name": "adRevGrossAmount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevGrossAmountPerActiveUser", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevNthDayGrossAmounts", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "adTypeCategory", "description": null, "args": [], "type": {"kind": "ENUM", "name": "AdTypeCategory", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCountPerActiveUser", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionNthDayCounts", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ReleaseMetric", "description": null, "isOneOf": null, "fields": [{"name": "activeUserCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "activeUserNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevGrossAmount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevGrossAmountPerActiveUser", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "adRevNthDayGrossAmounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ads", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ReleaseAdMetric", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dailyActiveUserCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionCountPerActiveUser", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "impressionNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "installCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lifetimeNthDayV<PERSON>ues", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtimeMsec", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "BigInt", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtimeNthDayMsecs", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionNthDayRates", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRate", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionCountPerActiveUser", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionNthDayCounts", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ReleaseMetrics", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ReleaseMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "viewPreset", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPreset", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ReleaseMetricsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "countryCode", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Filter", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ReleaseVersion", "description": null, "isOneOf": null, "fields": [{"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ReleaseVersions", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ReleaseVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ReleaseVersionsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "RoleAccessControl", "description": null, "isOneOf": null, "fields": [{"name": "permits", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roleId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "subject", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SideMenu", "description": null, "isOneOf": null, "fields": [{"name": "group", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "icon", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "path", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SideMenus", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SideMenu", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Team", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "leader", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "User", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "leaderId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "members", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roleId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Teams", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Team", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ToolPermission", "description": null, "isOneOf": null, "fields": [{"name": "action", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tool", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ToolPermissionForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "action", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "tool", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "UUID", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateBudgetRequestForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "amount", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "expirationDate", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateBudgetRequestStateForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "lastAction", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "stepId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateConfigMapsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "scopes", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameCostForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "preTaxAmount", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameCostWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "date", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Date", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "networkId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "metadata", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricMetadataForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "override", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricOverrideForm", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricMetadataForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "monetNote", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "productNote", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "uaNote", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "versionNote", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricOverrideForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "cost", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameMetricWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "date", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameRole", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "users", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameRolesArgs", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "forms", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameRolesGame", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateGameRolesGame", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roles", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateGameRole", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateProfileForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "password", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "passwordConfirmation", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateTeamForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "leaderEmail", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "memberEmails", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateTeamWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateUserForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "fullName", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hasPassword", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "kind", "description": null, "type": {"kind": "ENUM", "name": "UserKind", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "password", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "toolPermissions", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ToolPermissionForm", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateUserWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateWorkflowForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "steps", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "WorkflowStepForm", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "User", "description": null, "isOneOf": null, "fields": [{"name": "email", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "fullName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasPassword", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inchargedGames", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "UserInchargedGame", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeleted", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "UserKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ledTeam", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Team", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "team", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Team", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "teamLead", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Team", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "toolPermissions", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ToolPermission", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "UserInchargedGame", "description": null, "isOneOf": null, "fields": [{"name": "roleId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "storeId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "UserKind", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "INHOUSE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PARTNER", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "UserPublicInfo", "description": null, "isOneOf": null, "fields": [{"name": "fullName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Users", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UsersWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "email", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "V2GameMetric", "description": null, "isOneOf": null, "fields": [{"name": "adPerformances", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AdPerformance", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "cpi", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dailyActiveUsers", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "date", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "monetNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "note", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "organicInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "organicPercentage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paidInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "playtime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "productNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "profit", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRateDay1", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRateDay3", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "retentionRateDay7", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "revenue", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roas", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessions", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalInstalls", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "uaNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "versionNote", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "V2GameMetricWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "dateFrom", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dateTo", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "gameId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "V2GameMetrics", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "V2GameMetric", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "V2Query", "description": null, "isOneOf": null, "fields": [{"name": "aggregateGameMetrics", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "source", "description": null, "type": {"kind": "ENUM", "name": "DataSource", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "V2GameMetricWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "V2GameMetric", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gameMetrics", "description": null, "args": [{"name": "offset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Offset", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "Order", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "source", "description": null, "type": {"kind": "ENUM", "name": "DataSource", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "where", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "V2GameMetricWhere", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "V2GameMetrics", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Versions", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ViewPreset", "description": null, "isOneOf": null, "fields": [{"name": "attributes", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPresetAttribute", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ViewPresetAttribute", "description": null, "isOneOf": null, "fields": [{"name": "cohortDays", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isCohort", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ViewPresetAttributeForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "cohortDays", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ViewPresets", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ViewPreset", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ViewPresetsWhere", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "pageId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Void", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Workflow", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roleId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "steps", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "WorkflowStep", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WorkflowStep", "description": null, "isOneOf": null, "fields": [{"name": "action", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "alternateAction", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "assignee", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "UserPublicInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "assigneeId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WorkflowStepAction", "description": null, "isOneOf": null, "fields": [{"name": "action", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WorkflowStepActions", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "WorkflowStepAction", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "WorkflowStepForm", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "action", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "alternateAction", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "assigneeId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Workflows", "description": null, "isOneOf": null, "fields": [{"name": "collection", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Workflow", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "isOneOf": null, "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "isOneOf": null, "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByURL", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOneOf", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "isRepeatable": false, "locations": ["ARGUMENT_DEFINITION", "ENUM_VALUE", "FIELD_DEFINITION", "INPUT_FIELD_DEFINITION"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\"", "isDeprecated": false, "deprecationReason": null}]}, {"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "oneOf", "description": "Indicates exactly one field must be supplied and this field must not be `null`.", "isRepeatable": false, "locations": ["INPUT_OBJECT"], "args": []}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behavior of this scalar.", "isRepeatable": false, "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behavior of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}]}}