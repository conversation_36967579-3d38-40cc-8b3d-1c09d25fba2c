/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/
import router from '@adonisjs/core/services/router'
import transmit from '@adonisjs/transmit/services/main'

import { middleware } from '#start/kernel'
import { Tool } from '#config/enums'

router
  .group(() => {
    router.get('readiness', [() => import('#controllers/health_checks_controller'), 'readiness'])
    router.get('liveness', [() => import('#controllers/health_checks_controller'), 'liveness'])
  })
  .prefix('health')

router
  .group(() => {
    router
      .group(() => {
        router
          .resource('documents', () => import('#controllers/api/v1/documents_controller'))
          .only(['show'])
        router
          .post('games', [() => import('#controllers/api/v1/games_controller'), 'create'])
          .as('games.create')
      })
      .use(middleware.auth({ guards: ['basic'] }))
    router.group(() => {}).use(middleware.auth({ guards: ['basic'] }))
  })
  .prefix('api/v1')
  .as('v1')

router
  .group(() => {
    router.post('github', [() => import('#controllers/webhooks_controller'), 'github']).as('github')
  })
  .prefix('webhooks')
  .as('webhooks')
  .use(middleware.json())

router
  .group(() => {
    router
      .group(() => {
        router.get('redirect', [() => import('#controllers/auth/google_controller'), 'redirect'])
        router.get('callback', [() => import('#controllers/auth/google_controller'), 'callback'])
      })
      .prefix('google')

    router
      .resource(
        'game_release_proposals',
        () => import('#controllers/game/release_proposals_controller')
      )
      .only(['create', 'store'])
  })
  .use(middleware.shield())

router
  .group(() => {
    router.resource('games', () => import('#controllers/partner/games_controller')).only(['index'])

    router
      .resource('games.metrics', () => import('#controllers/partner/game_metrics_controller'))
      .only(['index'])
  })
  .prefix('partner')
  .as('partner')
  .use([middleware.shield(), middleware.auth({ guards: ['web'] })])

router
  .group(() => {
    router.get('/', [() => import('#controllers/home_controller')]).as('home')

    router
      .delete('sessions', [() => import('#controllers/auth/sessions_controller'), 'destroy'])
      .as('sessions.destroy')

    router
      .group(() => {
        router
          .resource(
            'games.metrics',
            () => import('#controllers/dashboard/game/game_metrics_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.level_drops',
            () => import('#controllers/dashboard/game/game_level_drops_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.review',
            () => import('#controllers/dashboard/game/game_reviews_controller')
          )
          .params({ review: 'date' })
          .only(['index', 'update'])

        router
          .resource(
            'games.performance_settings',
            () => import('#controllers/dashboard/game/game_performance_settings_controller')
          )
          .params({ games: 'storeId' })
          .only(['update'])

        router
          .resource(
            'games.product_metrics',
            () => import('#controllers/dashboard/game/product_metrics_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.creative_metrics',
            () => import('#controllers/dashboard/game/creative_metrics_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.costs',
            () => import('#controllers/dashboard/game/game_costs_controller')
          )
          .only(['index', 'update'])

        router
          .resource(
            'game_revenues',
            () => import('#controllers/dashboard/game_revenues_controller')
          )
          .only(['index'])

        router
          .resource(
            'team_revenues',
            () => import('#controllers/dashboard/team_revenues_controller')
          )
          .only(['index'])

        router
          .resource('games', () => import('#controllers/dashboard/game/games_controller'))
          .only(['index', 'show'])

        router
          .resource(
            'games.revenues',
            () => import('#controllers/dashboard/game/game_revenues_controller')
          )
          .params({ revenues: 'date' })
          .only(['index', 'update'])

        router
          .resource(
            'games.overview',
            () => import('#controllers/dashboard/game/game_charts_controller')
          )
          .only(['show'])

        router
          .resource(
            'games.campaign_metrics',
            () => import('#controllers/dashboard/game/campaign_metrics_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.release-metrics',
            () => import('#controllers/dashboard/game/release_metrics_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.firebase-experiments',
            () => import('#controllers/dashboard/game/firebase_experiments_controller')
          )
          .only(['index'])

        router
          .resource(
            'games.metrics-next',
            () => import('#controllers/dashboard/game/next/game_metrics_controller')
          )
          .only(['index'])

        router
          .resource(
            'game_metric_attributes',
            () => import('#controllers/dashboard/game/game_metric_attributes_controller')
          )
          .only(['index'])

        router
          .resource(
            'revenue_reports',
            () => import('#controllers/dashboard/revenue_reports_controller')
          )
          .only(['index'])

        router
          .resource(
            'game_studio_metrics',
            () => import('#controllers/dashboard/game_studio_metrics_controller')
          )
          .only(['index'])

        router
          .resource('acls', () => import('#controllers/dashboard/access_controls_controller'))
          .params({ acls: 'subject' })
          .only(['update', 'show'])

        router
          .resource(
            'notifications',
            () => import('#controllers/dashboard/notifications_controller')
          )
          .only(['store', 'update'])

        router
          .resource(
            'budget_requests',
            () => import('#controllers/dashboard/budget_requests_controller')
          )
          .only(['index', 'update'])

        router
          .group(() => {
            router.resource(
              'admobs',
              () => import('#controllers/dashboard/monet/admobs_controller')
            )
          })
          .prefix('monet')
          .as('monet')
      })
      .as('dashboard')
      .prefix('dash')

    router.resource('workflows', () => import('#controllers/workflows_controller'))

    router
      .group(() => {
        router
          .resource(
            'access_controls',
            () => import('#controllers/github/access_controls_controller')
          )
          .only(['index', 'update'])
          .as('acl')

        router
          .resource('teams', () => import('#controllers/github/teams_controller'))
          .only(['update'])
          .params({ teams: 'name' })

        router
          .resource('statuses', () => import('#controllers/github/statuses_controller'))
          .only(['index'])

        router
          .resource(
            'protected_branches',
            () => import('#controllers/github/protected_branches_controller')
          )
          .only(['index', 'update'])
      })
      .use([middleware.toolManager({ tool: Tool.GitHub })])
      .prefix('github')
      .as('github')

    router
      .resource('documents', () => import('#controllers/documents/documents_controller'))
      .only(['edit', 'update', 'index'])

    router
      .group(() => {
        router
          .resource(
            'release_approvals',
            () => import('#controllers/game/release_approvals_controller')
          )
          .only(['store', 'update'])

        router
          .resource(
            'release_proposals',
            () => import('#controllers/game/release_proposals_controller')
          )
          .only(['index'])

        router
          .resource(
            'release_proposals.approvals',
            () => import('#controllers/game/release_approvals_controller')
          )
          .only(['index'])
      })
      .prefix('game')
      .as('game')

    router.resource('me', () => import('#controllers/me_controller')).only(['index'])

    router
      .group(() => {
        router
          .resource('report', () => import('#controllers/me/reports_controller'))
          .only(['index'])

        router
          .resource('profile', () => import('#controllers/me/profiles_controller'))
          .only(['update', 'index'])
      })
      .prefix('me')

    router
      .group(() => {
        router
          .resource('users', () => import('#controllers/system/users_controller'))
          .only(['index', 'update', 'destroy', 'store'])
          .use(['index'], [middleware.pagination({ perPageMax: 999999 })])

        router
          .get('users/:id/simulate', [
            () => import('#controllers/system/users_controller'),
            'simulate',
          ])
          .as('users.simulate')

        router.resource('teams', () => import('#controllers/system/teams_controller'))
      })
      .prefix('system')
      .as('system')

    router
      .post('policies', [() => import('#controllers/web/policies_controller'), 'index'])
      .as('policies.index')

    router
      .resource('ad_networks', () => import('#controllers/web/ad_networks_controller'))
      .only(['index'])

    router.resource('changelog', () => import('#controllers/changelogs_controller')).only(['index'])
  })
  .use([middleware.auth({ guards: ['web'] }), middleware.shield()])

router
  .group(() => {
    router
      .post('/graphql', () => {})
      .use([middleware.json(), middleware.graphqlContext(), middleware.graphql()])

    router.get('/graphiql', [() => import('#controllers/graphiqls_controller')])
  })
  .use([middleware.auth({ guards: ['basic', 'web'] })])

router
  .group(() => {
    router
      .resource('sessions', () => import('#controllers/auth/sessions_controller'))
      .only(['create', 'store'])
  })
  .use([middleware.guest({ guards: ['web'] }), middleware.shield()])

/**
 * SSE
 */
transmit.registerRoutes((route) => {
  if (route.getPattern() === '__transmit/events') {
    route.middleware(middleware.auth({ guards: ['web', 'basic'] }))
    return
  }
})
