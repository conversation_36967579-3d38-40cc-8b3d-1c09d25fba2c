import { HealthChecks } from '@adonisjs/core/health'
import { DbChe<PERSON> } from '@adonisjs/lucid/database'
import { RedisCheck } from '@adonisjs/redis'
import redis from '@adonisjs/redis/services/main'
import db from '@adonisjs/lucid/services/db'
import { MigrationCheck } from '@mirai-game-studio/adonis-sdk/health_check'
import app from '@adonisjs/core/services/app'

export const healthChecks = new HealthChecks().register([
  new RedisCheck(redis.connection()),
  new DbCheck(db.connection('postgres')),
  new DbCheck(db.connection('dataWarehouse')),
  new MigrationCheck(db, app, db.connection('postgres')),
  new MigrationCheck(db, app, db.connection('dataWarehouse')),
])
