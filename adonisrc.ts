import { defineConfig } from '@adonisjs/core/app'

export default defineConfig({
  assetsBundler: false,
  hooks: {
    onBuildStarting: [() => import('@adonisjs/vite/build_hook')],
  },

  /*
  |--------------------------------------------------------------------------
  | Commands
  |--------------------------------------------------------------------------
  |
  | List of ace commands to register from packages. The application commands
  | will be scanned automatically from the "./commands" directory.
  |
  */
  commands: [
    () => import('@adonisjs/core/commands'),
    () => import('@adonisjs/lucid/commands'),
    () => import('@mirai-game-studio/adonis-sdk/commands'),
    () => import('@adonisjs/bouncer/commands'),
  ],

  /*
  |--------------------------------------------------------------------------
  | Service providers
  |--------------------------------------------------------------------------
  |
  | List of service providers to import and register when booting the
  | application
  |
  */
  providers: [
    () => import('@adonisjs/core/providers/app_provider'),
    () => import('@adonisjs/core/providers/hash_provider'),
    {
      file: () => import('@adonisjs/core/providers/repl_provider'),
      environment: ['repl', 'test'],
    },
    () => import('@adonisjs/core/providers/vinejs_provider'),
    () => import('@adonisjs/cors/cors_provider'),
    () => import('@adonisjs/lucid/database_provider'),
    () => import('@adonisjs/auth/auth_provider'),
    () => import('@adonisjs/redis/redis_provider'),
    () => import('@adonisjs/i18n/i18n_provider'),
    () => import('@adonisjs/transmit/transmit_provider'),
    () => import('@adonisjs/inertia/inertia_provider'),
    () => import('@adonisjs/bouncer/bouncer_provider'),
    () => import('@adonisjs/core/providers/edge_provider'),
    () => import('@adonisjs/session/session_provider'),
    () => import('@adonisjs/vite/vite_provider'),
    () => import('@adonisjs/shield/shield_provider'),
    () => import('@adonisjs/static/static_provider'),
    () => import('@adonisjs/ally/ally_provider'),

    () => import('@mirai-game-studio/adonis-sdk/providers/queue_provider'),
    () => import('@mirai-game-studio/adonis-sdk/providers/sentry_provider'),
    () => import('@mirai-game-studio/adonis-sdk/providers/multi_format_provider'),
    () => import('@mirai-game-studio/adonis-sdk/providers/config_map_provider'),
    () => import('@mirai-game-studio/adonis-sdk/providers/graphql_provider'),
    () => import('@mirai-game-studio/adonis-sdk/providers/journal_provider'),
    () => import('@mirai-game-studio/adonis-sdk/providers/pubsub_provider'),

    () => import('#providers/app_provider'),
    () => import('#providers/discord_bot_provider'),
    () => import('#providers/vaultkey_provider'),
    () => import('#providers/config_map_provider'),
    () => import('#providers/pubsub_provider'),
  ],

  /*
  |--------------------------------------------------------------------------
  | Preloads
  |--------------------------------------------------------------------------
  |
  | List of modules to import before starting the application.
  |
  */
  preloads: [
    () => import('#start/routes'),
    () => import('#start/kernel'),
    () => import('#start/query'),
    () => import('#start/transmit'),
  ],

  /*
  |--------------------------------------------------------------------------
  | Tests
  |--------------------------------------------------------------------------
  |
  | List of test suites to organize tests by their type. Feel free to remove
  | and add additional suites.
  |
  */
  tests: {
    suites: [
      {
        files: ['tests/unit/**/*.spec(.ts|.js)'],
        name: 'unit',
        timeout: 2000,
      },
      {
        files: ['tests/functional/**/*.spec(.ts|.js)'],
        name: 'functional',
        timeout: 30000,
      },
    ],
    forceExit: false,
  },
  metaFiles: [
    {
      pattern: 'resources/views/**/*.edge',
      reloadServer: false,
    },
    {
      pattern: 'public/**',
      reloadServer: false,
    },
    {
      pattern: 'config/**/*.yml',
      reloadServer: true,
    },
    {
      pattern: 'resources/lang/**/*.{json,yaml,yml}',
      reloadServer: false,
    },
    {
      pattern: 'tests/fixtures/**/*',
      reloadServer: true,
    },
    {
      pattern: 'graphql/*.graphql',
      reloadServer: true,
    },
  ],
})
