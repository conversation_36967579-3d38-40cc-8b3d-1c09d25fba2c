services:
  postgresql:
    image: postgres:16-alpine
    # command:
    #   - postgres
    #   - -c
    #   - log_statement=all
    ports:
      - '${DB_PORT:-5432}:5432'
    restart: always
    volumes:
      - ${PG_DATA:-postgresql-data}:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD: ${DB_PASSWORD:-Sup3rS3cr3T}

  redis:
    image: redis:7-alpine
    volumes:
      - ${REDIS_DATA:-redis-data}:/data
    restart: always
    ports:
      - '${REDIS_PORT:-6379}:6379'

volumes:
  postgresql-data:
  redis-data:
