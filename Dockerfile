ARG STAGE=test

FROM node:22-alpine AS base



FROM base AS build-base

ARG BUILD_VERSION=production
ENV VITE_BUILD_VERSION=${BUILD_VERSION}

WORKDIR /app

RUN npm i -g pnpm@10

COPY package.json pnpm-lock.yaml ./
RUN --mount=type=secret,id=npmrc,target=/app/.npmrc pnpm install --frozen-lockfile --ignore-scripts

COPY . .

RUN NODE_ENV=production npm run build

FROM build-base AS test

FROM build-base AS staging

RUN --mount=type=secret,id=npmrc,target=/app/.npmrc rm -rf node_modules \
  && pnpm install --production --frozen-lockfile --ignore-scripts

FROM build-base AS production

RUN --mount=type=secret,id=npmrc,target=/app/.npmrc rm -rf node_modules \
  && pnpm install --production --frozen-lockfile --ignore-scripts

FROM ${STAGE} AS build

FROM base

WORKDIR /app/build

ARG REPO_COMMIT_ID=local
ARG BUILD_VERSION=production
ENV BUILD_VERSION=${BUILD_VERSION}
ENV REPO_COMMIT_ID=${REPO_COMMIT_ID}
ARG STAGE=test
ENV STAGE=${STAGE}
ARG NODE_CONFIG_ENV=development
ENV NODE_CONFIG_ENV=${NODE_CONFIG_ENV}

COPY --from=build /app/build /app/build
COPY --from=build /app/node_modules /app/build/node_modules
