import type { Preview } from '@storybook/react'
import React from 'react'
import '../resources/css/next.css'
import { ReactRoot } from '../resources/js/next/react'

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  decorators: [
    (Story) => (
      <ReactRoot>
        <div className="min-h-screen bg-background p-4">
          <Story />
        </div>
      </ReactRoot>
    ),
  ],
}

export default preview
