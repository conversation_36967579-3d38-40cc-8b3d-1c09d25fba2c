import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'game_metric_metadata'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.integer('metric_id').unsigned().notNullable().unique()

      table.text('note')
      table.text('ua_note')
      table.text('monet_note')
      table.text('product_note')
      table.text('version_note')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
