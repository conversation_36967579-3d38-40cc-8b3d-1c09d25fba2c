import { BaseSchema } from '@adonisjs/lucid/schema'

import GameMetricMetadatum from '#models/game_metric_metadatum'
import Game from '#models/game'

export default class extends BaseSchema {
  protected tableName = GameMetricMetadatum.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .text('game_id')
        .references(Game.columnName('storeId'))
        .inTable(Game.table)
        .onDelete('CASCADE')
        .nullable()
      table.date('date')

      table.unique(['date', 'game_id'])
    })

    // this.defer(async (db) => {
    //   const overrides = await GameMetricMetadatum.query({ client: db })
    //     .select('id', 'metricId')
    //     .preload('metric', (query) => {
    //       query.select('storeId', 'date')
    //     })

    //   await Promise.sequence(overrides, async (override) => {
    //     if (!override.metric) {
    //       return
    //     }

    //     await GameMetricMetadatum.query({ client: db }).where('id', override.id).update({
    //       date: override.metric.date,
    //       gameId: override.metric.storeId,
    //     })
    //   })
    // })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['date', 'game_id'])
      table.dropColumns('date', 'game_id')
    })
  }
}
