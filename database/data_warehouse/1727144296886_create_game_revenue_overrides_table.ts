import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'game_revenue_overrides'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.text('store_id').index()
      table.date('date')
      table.unique(['date', 'store_id'])

      table.float('tuning')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
