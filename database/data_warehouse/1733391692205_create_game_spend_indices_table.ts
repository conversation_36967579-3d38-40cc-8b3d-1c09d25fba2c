import { BaseSchema } from '@adonisjs/lucid/schema'

import GameSpend from '#models/game_spend'

export default class extends BaseSchema {
  protected tableName = GameSpend.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.index([GameSpend.columnName('storeId'), GameSpend.columnName('date')])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex([GameSpend.columnName('storeId'), GameSpend.columnName('date')])
    })
  }
}
