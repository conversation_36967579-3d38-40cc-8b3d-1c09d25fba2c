import { BaseSchema } from '@adonisjs/lucid/schema'

import GameMetricOverride from '#models/game_metric_override'

export default class extends BaseSchema {
  protected tableName = 'game_metric_override_journals'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table
        .integer('root_id')
        .references('id')
        .inTable(GameMetricOverride.table)
        .onDelete('CASCADE')
      table.float('revenue')
      table.float('cost')
      table.string('created_by_id')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
