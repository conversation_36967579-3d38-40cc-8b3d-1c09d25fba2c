import { BaseSchema } from '@adonisjs/lucid/schema'

import Game from '#models/game'
import GameMetricOverride from '#models/game_metric_override'

export default class extends BaseSchema {
  protected tableName = GameMetricOverride.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .text('game_id')
        .references(Game.columnName('storeId'))
        .inTable(Game.table)
        .onDelete('CASCADE')
        .nullable()
      table.date('date')

      table.unique(['date', 'game_id'])
    })

    // this.defer(async (db) => {
    //   const overrides = await GameMetricOverride.query({ client: db })
    //     .select('id', 'metricId')
    //     .preload('metric', (query) => {
    //       query.select('storeId', 'date')
    //     })

    //   await Promise.sequence(overrides, async (override) => {
    //     if (!override.metric) {
    //       return
    //     }

    //     await GameMetricOverride.query({ client: db }).where('id', override.id).update({
    //       date: override.metric.date,
    //       gameId: override.metric.storeId,
    //     })
    //   })
    // })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['date', 'game_id'])
      table.dropColumns('date', 'game_id')
    })
  }
}
