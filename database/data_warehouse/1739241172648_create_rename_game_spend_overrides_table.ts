import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected oldName = 'game_spend_overrides'
  protected newName = 'game_cost_overrides'

  async up() {
    this.schema.renameTable(this.oldName, this.newName)

    this.schema.alterTable(this.newName, (table) => {
      table.renameColumn('fee_free_amount', 'cost')
      table.renameColumn('spend_id', 'cost_id')
    })
  }

  async down() {
    this.schema.alterTable(this.newName, (table) => {
      table.renameColumn('cost', 'fee_free_amount')
      table.renameColumn('cost_id', 'spend_id')
    })

    this.schema.renameTable(this.newName, this.oldName)
  }
}
