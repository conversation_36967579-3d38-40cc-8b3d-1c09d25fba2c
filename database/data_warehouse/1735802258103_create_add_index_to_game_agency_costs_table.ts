import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_agency_cost'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.index(['store_id', 'agency_id', 'date'])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex(['store_id', 'agency_id', 'date'])
    })
  }
}
