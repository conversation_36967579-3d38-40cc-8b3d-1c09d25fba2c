import { BaseSchema } from '@adonisjs/lucid/schema'

import GameInstallation from '#models/game_installation'

export default class extends BaseSchema {
  protected tableName = GameInstallation.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.index([GameInstallation.columnName('gameId'), GameInstallation.columnName('date')])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex([GameInstallation.columnName('gameId'), GameInstallation.columnName('date')])
    })
  }
}
