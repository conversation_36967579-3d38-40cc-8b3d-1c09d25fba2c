import { BaseSchema } from '@adonisjs/lucid/schema'

import GameFirebaseMetric from '#models/game_firebase_metric'

export default class extends BaseSchema {
  protected tableName = GameFirebaseMetric.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.index([GameFirebaseMetric.columnName('gameId'), GameFirebaseMetric.columnName('date')])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex([
        GameFirebaseMetric.columnName('gameId'),
        GameFirebaseMetric.columnName('date'),
      ])
    })
  }
}
