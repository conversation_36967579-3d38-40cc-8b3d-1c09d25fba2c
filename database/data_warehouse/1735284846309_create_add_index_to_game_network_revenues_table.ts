import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_network_rev'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.index(['date', 'network_id', 'store_id'])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex(['date', 'network_id', 'store_id'])
    })
  }
}
