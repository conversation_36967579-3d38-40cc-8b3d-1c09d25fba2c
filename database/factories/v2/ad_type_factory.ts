import factory from '@adonisjs/lucid/factories'

import AdType from '#models/v2/ad_type'
import { AdNetworkCategory, AdTypeCategory } from '#config/enums'

export const AdTypeFactory = factory
  .define(AdType, async ({ faker }) => {
    const category = faker.helpers.arrayElement(Object.values(AdTypeCategory))

    return {
      id: category.replaceAll(' ', '_').toLowerCase(),
      name: category,
      category,
      networkCategory: faker.helpers.arrayElement(Object.values(AdNetworkCategory)),
    }
  })
  .build()
