import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import GameCreativeMetric from '#models/game_creative_metric'

import { GameFactory } from './game_factory.js'
import { AdAgencyFactory } from './ad_agency_factory.js'

export const GameCreativeMetricFactory = factory
  .define(GameCreativeMetric, async ({ faker }) => {
    return {
      editor: faker.person.firstName(),
      preTaxCostAmount: faker.number.float({ min: 0, max: 1000, multipleOf: 0.0001 }),
      adGroup: faker.color.human(),
      adSet: faker.food.ingredient(),
      campaign: faker.animal.petName(),
      targetRoasRate: faker.number.float({ min: 0, max: 300, multipleOf: 0.0001 }),
      clickCount: faker.number.int({ min: 0, max: 1000 }),
      grossRevenueAmount: faker.number.float({ min: 0, max: 1000, multipleOf: 0.0001 }),
      impressionCount: faker.number.int({ min: 0, max: 1000 }),
      installCount: faker.number.int({ min: 0, max: 1000 }),
      isPlayable: faker.datatype.boolean(),
      date: DateTime.fromJSDate(faker.date.recent({ days: 365 })),
    }
  })
  .relation('game', () => GameFactory)
  .relation('agency', () => AdAgencyFactory)
  .build()
