import factory from '@adonisjs/lucid/factories'
import parseDuration from 'parse-duration'

import GameMetric from '#models/game_metric'

import { FactoryHelpers } from './factory_helpers.js'

export const GameMetricFactory = factory
  .define(GameMetric, async ({ faker }) => {
    return {
      date: FactoryHelpers.dateBefore(),
      storeId: faker.string.uuid(),
      paidInstalls: faker.number.int({ max: 1_000_000 }),
      organicInstalls: faker.number.int({ max: 1_000_000 }),
      revenue: faker.number.float({ max: 100_000 }),
      retentionRateDay1: faker.number.float({ max: 1 }),
      retentionRateDay3: faker.number.float({ max: 1 }),
      retentionRateDay7: faker.number.float({ max: 1 }),
      bannerImpsDau: faker.number.float({ max: 100 }),
      interImpsDau: faker.number.float({ max: 100 }),
      rewardImpsDau: faker.number.float({ max: 100 }),
      aoaImpsDau: faker.number.float({ max: 100 }),
      mrecImpsDau: faker.number.float({ max: 100 }),
      audioImpsDau: faker.number.float({ max: 100 }),
      aoaAdmobImpsDau: faker.number.float({ max: 100 }),
      collapseAdmobImpsDau: faker.number.float({ max: 100 }),
      nativeAdmobImpsDau: faker.number.float({ max: 100 }),
      adaptiveAdmobImpsDau: faker.number.float({ max: 100 }),
      mrecAdmobImpsDau: faker.number.float({ max: 100 }),
      averageSession: faker.number.float({ max: 10 }),
      playtime: faker.number.int({ max: parseDuration('3h', 'second')! }),
    }
  })
  .build()
