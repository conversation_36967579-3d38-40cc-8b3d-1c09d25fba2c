import { DateTime } from 'luxon'
import factory from '@adonisjs/lucid/factories'

import GameRevenue from '#models/game_revenue'

export const GameRevenueFactory = factory
  .define(GameRevenue, async ({ faker }) => {
    return {
      adType: faker.helpers.arrayElement(Object.values(GameRevenue.adType)),
      dailyActiveUserCount: faker.number.int(1000),
      date: DateTime.fromJSDate(faker.date.recent({ days: 365 })),
      impressionCount: faker.number.int(1000),
      mediationId: faker.helpers.arrayElement(Object.values(GameRevenue.mediationId)),
      networkId: faker.helpers.arrayElement([1, 2, 3, 4, 5]),
      revenue: faker.number.float({ min: 1000, max: 10000 }),
      storeId: faker.string.uuid(),
      taxRate: faker.number.float({ min: 0, max: 0.1, multipleOf: 0.01 }),
    }
  })
  .build()
