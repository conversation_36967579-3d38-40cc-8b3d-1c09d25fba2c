import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import GameNetworkRevenue from '#models/game_network_revenue'

import { GameFactory } from './game_factory.js'
import { AdNetworkFactory } from './ad_network_factory.js'

export const GameNetworkRevenueFactory = factory
  .define(GameNetworkRevenue, async ({ faker }) => {
    return {
      date: DateTime.fromJSDate(faker.date.recent()),
      revenue: faker.number.float({ min: 0, max: 100, multipleOf: 0.0001 }),
      impressionCount: faker.number.int({ min: 0, max: 200_000 }),
    }
  })
  .relation('game', () => GameFactory)
  .relation('network', () => AdNetworkFactory)
  .build()
