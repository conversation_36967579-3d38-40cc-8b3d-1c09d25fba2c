import factory from '@adonisjs/lucid/factories'

import GameMetricMetadatum from '#models/game_metric_metadatum'

export const GameMetricMetadatumFactory = factory
  .define(GameMetricMetadatum, async ({ faker }) => {
    return {
      versionNote: faker.lorem.paragraphs({ min: 1, max: 3 }),
      monetNote: faker.lorem.paragraphs({ min: 1, max: 3 }),
      note: faker.lorem.paragraphs({ min: 1, max: 3 }),
      productNote: faker.lorem.paragraphs({ min: 1, max: 3 }),
      uaNote: faker.lorem.paragraphs({ min: 1, max: 3 }),
    }
  })
  .build()
