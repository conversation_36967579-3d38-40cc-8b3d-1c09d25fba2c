import factory from '@adonisjs/lucid/factories'

import AdNetwork from '#models/ad_agency'
import { AdNetworkInputType } from '#config/enums'

export const AdAgencyFactory = factory
  .define(AdNetwork, async ({ faker }) => {
    return {
      inputType: faker.helpers.arrayElement(Object.values(AdNetworkInputType)),
      name: faker.company.name(),
      isVisible: faker.helpers.arrayElement([true, false]),
    }
  })
  .build()
