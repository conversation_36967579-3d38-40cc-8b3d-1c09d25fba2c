import { DateTime } from 'luxon'
import factory from '@adonisjs/lucid/factories'

import GameSpend from '#models/game_spend'

import { GameFactory } from './game_factory.js'

export const GameSpendFactory = factory
  .define(GameSpend, async ({ faker }) => {
    const preTaxAmount = faker.number.float({ max: 10_000 })
    const tax = faker.number.float({ max: 0.2 })

    const adNetworkIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

    return {
      networkId: faker.helpers.arrayElement(adNetworkIds),
      tax,
      preTaxAmount,
      storeId: faker.string.uuid(),
      date: DateTime.fromJSDate(faker.date.past()),
    }
  })
  .relation('game', () => GameFactory)
  .build()
