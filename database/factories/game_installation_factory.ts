import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import GameInstallation from '#models/game_installation'

export const GameInstallationFactory = factory
  .define(GameInstallation, async ({ faker }) => {
    return {
      gameId: faker.string.uuid(),
      date: DateTime.fromJSDate(faker.date.recent()),
      paidInstalls: faker.number.int({ min: 0, max: 300_000 }),
      organicInstalls: faker.number.int({ min: 0, max: 50_000 }),
    }
  })
  .build()
