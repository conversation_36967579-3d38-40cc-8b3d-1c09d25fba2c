import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import GameAgencyCost from '#models/game_agency_cost'

import { GameFactory } from './game_factory.js'
import { AdAgencyFactory } from './ad_agency_factory.js'

export const GameAgencyCostFactory = factory
  .define(GameAgencyCost, async ({ faker }) => {
    return {
      date: DateTime.fromJSDate(faker.date.recent()),
      preTaxCost: faker.number.float({ min: 0, max: 3_500, multipleOf: 0.001 }),
      taxRate: faker.number.float({ min: 0, max: 0.1, multipleOf: 0.01 }),
    }
  })
  .relation('game', () => GameFactory)
  .relation('agency', () => AdAgencyFactory)
  .build()
