import factory from '@adonisjs/lucid/factories'
import app from '@adonisjs/core/services/app'
import { ConfigMapRegistry } from '@munkit/main'

import GameReleaseApproval from '#models/game_release_approval'
import { GameReleaseStatus } from '#config/enums'
import GameReleaseProposal from '#models/game_release_proposal'
import User from '#models/user'
import { enumValues } from '#utils/pure'

export const GameReleaseApprovalFactory = factory
  .define(GameReleaseApproval, async ({ faker }) => {
    const configMapRegistry = await app.container.make(ConfigMapRegistry)
    const publisherConfigMapCollection = configMapRegistry.get('cmap.pl.publisher')

    return {
      jobId: faker.number.int({ max: 10_000 }).toString(),
      publisherId: faker.helpers.arrayElement(publisherConfigMapCollection).id,
      releaseStatus: faker.helpers.arrayElement(enumValues(GameReleaseStatus)),
    }
  })
  .relation('proposal', () => GameReleaseProposal)
  .relation('reviewer', () => User)
  .build()
