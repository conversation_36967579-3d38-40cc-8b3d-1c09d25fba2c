import factory from '@adonisjs/lucid/factories'

import FirebaseVersionVariant from '#models/firebase_version_variant'

import { GameFactory } from './game_factory.js'

export const FirebaseVersionVariantFactory = factory
  .define(FirebaseVersionVariant, async ({ faker }) => {
    return {
      experiment: faker.word.noun(),
      name: faker.word.noun(),
      id: faker.string.uuid(),
    }
  })
  .relation('game', () => GameFactory)
  .build()
