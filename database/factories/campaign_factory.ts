import factory from '@adonisjs/lucid/factories'

import Campaign from '#models/campaign'

import { GameFactory } from './game_factory.js'

export const CampaignFactory = factory
  .define(Campaign, ({ faker }) => {
    return {
      id: faker.string.uuid(),
      name: faker.company.name(),
      mediaSourceId: faker.number.int({ min: 1, max: 50 }),
    }
  })
  .relation('game', () => GameFactory)
  .build()
