import factory from '@adonisjs/lucid/factories'

import GameRewardUsage from '#models/game_reward_usage'

import { GameFactory } from './game_factory.js'

export const GameRewardUsageFactory = factory
  .define(GameRewardUsage, async ({ faker }) => {
    return {
      isValidSemver: true,
      useCount: faker.number.int({ min: 1, max: 40_000 }),
      level: faker.number.int({ min: 0, max: 1_000 }),
      world: faker.number.int({ min: -1, max: 5_000 }),
      location: faker.color.human(),
      version: faker.system.semver(),
    }
  })
  .relation('game', () => GameFactory)

  .build()
