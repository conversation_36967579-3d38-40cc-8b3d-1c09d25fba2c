import factory from '@adonisjs/lucid/factories'

import GameReleaseProposal, { AndroidExtra } from '#models/game_release_proposal'
import { GamePlatform, GameReleaseProposalStatus } from '#config/enums'

export const GameReleaseProposalFactory = factory
  .define(GameReleaseProposal, async ({ faker }) => {
    return {
      installableDownloadUrl: 'http://localhost:3333/',
      platform: GamePlatform.Android,
      changelog: faker.lorem.sentence(),
      repository: faker.science.chemicalElement().name,
      revision: faker.finance.ethereumAddress(),
      semver: faker.system.semver(),
      status: faker.helpers.arrayElement(
        Object.values(GameReleaseProposalStatus).filter((e): e is GameReleaseProposalStatus =>
          Number.isInteger(e)
        )
      ),
      extra: new AndroidExtra().merge({
        symbolsDownloadUrl: 'http://localhost:3333/',
        versionCode: 1,
      }),
    }
  })
  .build()
