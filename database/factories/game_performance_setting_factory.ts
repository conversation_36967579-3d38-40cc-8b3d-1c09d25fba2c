import factory from '@adonisjs/lucid/factories'

import GamePerformanceSetting, { GamePerformanceCriteria } from '#models/game_performance_setting'
import { GameFactory } from '#database/factories/game_factory'
import { GamePerformanceCriteriaConclusion } from '#graphql/main'

export const GamePerformanceSettingFactory = factory
  .define(GamePerformanceSetting, async ({ faker }) => {
    return {
      criterias: Object.values(GamePerformanceCriteriaConclusion).map((conclusion) =>
        new GamePerformanceCriteria().merge({
          conclusion: conclusion as GamePerformanceCriteriaConclusion,
          metrics: {
            roas: faker.number.float({ min: -100, max: 100, multipleOf: 0.01 }),
            playtime: faker.number.int({ min: 0, max: 100 }),
            retentionRateDay1: faker.number.float({ min: 0, max: 100, multipleOf: 0.01 }),
            cpi: faker.number.float({ min: 0, max: 100, multipleOf: 0.01 }),
            monetImpsDau: faker.number.float({ min: 0, max: 100, multipleOf: 0.01 }),
          },
        })
      ),
    }
  })
  .relation('game', () => GameFactory)
  .build()
