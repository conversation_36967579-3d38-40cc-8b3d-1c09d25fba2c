import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import AdMetric from '#models/ad_metric'
import { makeCohortValues } from '#database/utils/cohort'

export const AdMetricFactory = factory
  .define(AdMetric, async ({ faker }) => {
    const activeUserCount = faker.number.int({ min: 0, max: 1000 })

    return {
      adCostNonTaxAmount: faker.number.float({ min: 0, max: 100, multipleOf: 0.00001 }),
      adRevGrossAmount: faker.number.float({ min: 0, max: 100, multipleOf: 0.00001 }),
      adRevNthDayGrossAmounts: Object.fromEntries(
        new Array(faker.number.int(10))
          .fill(0)
          .map((_, index) => [
            index.toString(),
            faker.number.float({ min: 0, max: 20, multipleOf: 0.00001 }),
          ])
      ),
      clickCount: faker.number.int({ min: 0, max: 1000 }),
      impressionCount: faker.number.int({ min: 0, max: 1000 }),
      installCount: faker.number.int({ min: 0, max: 1000 }),
      countryCode: faker.location.countryCode('alpha-2'),
      date: DateTime.fromJSDate(faker.date.recent()),
      activeUserCount: activeUserCount,
      sessionCount: activeUserCount + faker.number.int({ min: 0, max: 1000 }),
      activeUserNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 1000 })),
      sessionNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 1000 })),
    }
  })
  .build()
