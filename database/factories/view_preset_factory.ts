import factory from '@adonisjs/lucid/factories'
import app from '@adonisjs/core/services/app'

import ViewPreset, { ViewPresetAttribute, ViewPresetSchema } from '#models/view_preset'

import { UserFactory } from './user_factory.js'

export const ViewPresetFactory = factory
  .define(ViewPreset, async ({ faker }) => {
    const registry = await app.container.make('cmap.registry')
    const viewPresetConfigMapCollection = registry.get('cmap.viewpreset')
    const viewPresetConfigMap = faker.helpers.arrayElement(viewPresetConfigMapCollection)

    return {
      pageId: viewPresetConfigMap.pageId,
      name: faker.lorem.words(3),
      schema: new ViewPresetSchema().merge({
        attributes: viewPresetConfigMap.attributes.map((attr) => {
          return new ViewPresetAttribute().merge({
            name: attr.name,
            isCohort: attr.isCohort,
            cohortDays: attr.isCohort ? [1, 2, 3] : [],
          })
        }),
      }),
    }
  })
  .relation('user', () => UserFactory)
  .build()
