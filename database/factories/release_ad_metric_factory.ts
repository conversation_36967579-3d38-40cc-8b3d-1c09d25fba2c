import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import ReleaseAdMetric from '#models/release_ad_metric'
import { GameFactory } from '#database/factories/game_factory'
import { NetworkFactory } from '#database/factories/network_factory'
import { AdTypeFactory } from '#database/factories/v2/ad_type_factory'
import { makeCohortValues } from '#database/utils/cohort'

export const ReleaseAdMetricFactory = factory
  .define(ReleaseAdMetric, async ({ faker }) => {
    return {
      date: DateTime.fromJSDate(faker.date.recent({ days: 30 })),
      version: faker.system.semver(),
      countryCode: faker.location.countryCode('alpha-2'),
      impressionCount: faker.number.int({ min: 0, max: 10000 }),
      adRevGrossAmount: faker.number.float({ min: 0, max: 1000, multipleOf: 0.0001 }),
      adRevNthDayGrossAmounts: makeCohortValues(60, () =>
        faker.number.float({ min: 0, max: 1000, multipleOf: 0.0001 })
      ),
      impressionNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 10000 })),
    }
  })
  .relation('game', () => GameFactory)
  .relation('network', () => NetworkFactory)
  .relation('adType', () => AdTypeFactory)
  .build()
