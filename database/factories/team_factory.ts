import factory from '@adonisjs/lucid/factories'
import app from '@adonisjs/core/services/app'
import { DateTime } from 'luxon'

import Team from '#models/team'

import { UserFactory } from './user_factory.js'

export const TeamFactory = factory
  .define(Team, async ({ faker }) => {
    const roleConfigMapCollection = await app.container
      .make('cmap.registry')
      .then((r) => r.get('cmap.dash.role'))

    return {
      name: faker.animal.cat(),
      roleId: faker.helpers.arrayElement(roleConfigMapCollection).id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    }
  })
  .relation('members', () => UserFactory)
  .relation('leader', () => UserFactory)
  .build()
