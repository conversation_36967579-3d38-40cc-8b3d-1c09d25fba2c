import factory from '@adonisjs/lucid/factories'

import GameFirebaseMetric from '#models/game_firebase_metric'

export const GameFirebaseMetricFactory = factory
  .define(GameFirebaseMetric, async ({ faker }) => {
    return {
      retentionRateDay1: faker.number.float({ min: 0, max: 10 }),
      retentionRateDay3: faker.number.float({ min: 0, max: 4 }),
      retentionRateDay7: faker.number.float({ min: 0, max: 5 }),
      averageSession: faker.number.float({ min: 0, max: 10 }),
      playtime: faker.number.float({ min: 0, max: 5_000 }),
    }
  })
  .build()
