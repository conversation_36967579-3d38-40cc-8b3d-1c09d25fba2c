import factory from '@adonisjs/lucid/factories'

import GameDailyLevelDrop from '#models/game_daily_level_drop'

import { FactoryHelpers } from './factory_helpers.js'
import { GameFactory } from './game_factory.js'

export const GameDailyLevelDropFactory = factory
  .define(GameDailyLevelDrop, async ({ faker }) => {
    const attemptCount = faker.number.int({ min: 0, max: 500_000 })
    const winCount = faker.number.int({ min: 0, max: attemptCount })
    const loseCount = faker.number.int({ min: 0, max: attemptCount - winCount })
    const skipCount = faker.number.int({ min: 0, max: attemptCount - winCount - loseCount })
    const completeCount = faker.number.int({
      min: 0,
      max: attemptCount - winCount - loseCount - skipCount,
    })
    const activeUserCount = faker.number.int({ min: 0, max: attemptCount })

    return {
      date: FactoryHelpers.dateBefore(),
      installDate: FactoryHelpers.dateBefore(),
      level: faker.number.int({ min: -1, max: 10_000 }),
      world: faker.number.int({ min: -4, max: 5_000 }),
      attemptCount,
      winCount,
      skipCount,
      version: faker.system.semver(),
      totalPlaytimeSec: activeUserCount * faker.number.float({ min: 0, max: 100 }),
      isValidSemver: true,
      activeUserCount,
      completeCount,
      loseCount,
    }
  })
  .relation('game', () => GameFactory)
  .build()
