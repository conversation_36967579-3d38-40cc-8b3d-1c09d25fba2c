import factory from '@adonisjs/lucid/factories'

import Ad from '#models/ad'

import { CampaignFactory } from './campaign_factory.js'
import { AdGroupFactory } from './ad_group_factory.js'

export const AdFactory = factory
  .define(Ad, ({ faker }) => {
    return {
      id: faker.string.uuid(),
      name: faker.lorem.words(3),
    }
  })
  .relation('campaign', () => CampaignFactory)
  .relation('group', () => AdGroupFactory)
  .build()
