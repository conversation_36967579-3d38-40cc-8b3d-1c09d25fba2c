import factory from '@adonisjs/lucid/factories'

import Game from '#models/game'
import { GamePlatform } from '#config/enums'

export const GameFactory = factory
  .define(Game, async ({ faker }) => {
    return {
      name: faker.commerce.productName(),
      packageName: faker.string.alphanumeric(10),
      platform: faker.helpers.arrayElement([GamePlatform.Android, GamePlatform.iOS]),
      storeId: faker.string.uuid(),
      googleCloudProjectId: faker.string.uuid(),
      googleAnalyticsPropertyId: `4${faker.string.numeric(8)}`,
      isActive: faker.datatype.boolean(),
      isInhouse: faker.datatype.boolean(),
    }
  })
  .build()
