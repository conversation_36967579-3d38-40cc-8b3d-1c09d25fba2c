import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import FirebaseAdMetric from '#models/firebase_ad_metric'
import { makeCohortValues } from '#database/utils/cohort'

import { FirebaseVersionVariantFactory } from './firebase_version_variant_factory.js'
import { NetworkFactory } from './network_factory.js'
import { MediationFactory } from './mediation_factory.js'
import { AdTypeFactory } from './v2/ad_type_factory.js'

export const FirebaseAdMetricFactory = factory
  .define(FirebaseAdMetric, async ({ faker }) => {
    return {
      countryCode: faker.location.countryCode('alpha-2'),
      date: DateTime.fromJSDate(faker.date.recent()),
      version: faker.system.semver(),
      adRevGrossAmount: faker.number.float({ min: 0, max: 1000, multipleOf: 0.0001 }),
      impressionCount: faker.number.int({ min: 0, max: 1000 }),
      impressionNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 1000 })),
      adRevNthDayGrossAmounts: makeCohortValues(60, () =>
        faker.number.float({ min: 0, max: 1000, multipleOf: 0.0001 })
      ),
    }
  })
  .relation('variant', () => FirebaseVersionVariantFactory)
  .relation('network', () => NetworkFactory)
  .relation('mediation', () => MediationFactory)
  .relation('adType', () => AdTypeFactory)
  .build()
