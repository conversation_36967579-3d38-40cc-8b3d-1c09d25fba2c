import factory from '@adonisjs/lucid/factories'

import GameLevelDrop from '#models/game_level_drop'

import { GameFactory } from './game_factory.js'

export const GameLevelDropFactory = factory
  .define(GameLevelDrop, async ({ faker }) => {
    const attemptCount = faker.number.int({ min: 0, max: 500_000 })
    const winCount = faker.number.int({ min: 0, max: attemptCount })
    const loseCount = faker.number.int({ min: 0, max: attemptCount - winCount })
    const skipCount = faker.number.int({ min: 0, max: attemptCount - winCount - loseCount })
    const completeCount = faker.number.int({
      min: 0,
      max: attemptCount - winCount - loseCount - skipCount,
    })
    const activeUserCount = faker.number.int({ min: 0, max: attemptCount })

    return {
      level: faker.number.int({ min: -1, max: 10_000 }),
      world: faker.number.int({ min: -4, max: 5_000 }),
      attemptCount,
      winCount,
      skipCount,
      version: faker.system.semver(),
      totalPlaytimeSec: activeUserCount * faker.number.float({ min: 0, max: 100 }),
      isValidSemver: true,
      activeUserCount,
      completeCount,
      loseCount,
    }
  })
  .relation('game', () => GameFactory)
  .build()
