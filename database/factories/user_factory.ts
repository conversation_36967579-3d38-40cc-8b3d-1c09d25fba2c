import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import User, { ToolPermission } from '#models/user'
import { Permission, Tool } from '#config/enums'
import { UserKind } from '#graphql/main'

import { TeamFactory } from './team_factory.js'

export const UserFactory = factory
  .define(User, async ({ faker }) => {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      fullName: faker.person.fullName(),
      oauthProvider: 'google',
      oauthUserId: faker.string.uuid(),
      toolPermissions: [new ToolPermission().merge({ tool: Tool.All, action: Permission.Manage })],
      password: '123456',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      kind: UserKind.Inhouse,
    }
  })
  .state('partner', async (user) => {
    user.kind = UserKind.Partner
  })
  .state('all:manage', async (user) => {
    user.toolPermissions = [
      new ToolPermission().merge({ tool: Tool.All, action: Permission.Manage }),
    ]
  })
  .state('dashboard:manage', async (user) => {
    user.toolPermissions = [
      new ToolPermission().merge({ tool: Tool.Dashboard, action: Permission.Manage }),
    ]
  })
  .state('all:view', async (user) => {
    user.toolPermissions = [new ToolPermission().merge({ tool: Tool.All, action: Permission.View })]
  })
  .state('dashboard:view', async (user) => {
    user.toolPermissions = [
      new ToolPermission().merge({ tool: Tool.Dashboard, action: Permission.View }),
    ]
  })
  .relation('team', () => TeamFactory)
  .build()
