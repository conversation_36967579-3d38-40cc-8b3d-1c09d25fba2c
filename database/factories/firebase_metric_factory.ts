import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import FirebaseMetric from '#models/firebase_metric'
import { makeCohortValues } from '#database/utils/cohort'

import { FirebaseVersionVariantFactory } from './firebase_version_variant_factory.js'

export const FirebaseMetricFactory = factory
  .define(FirebaseMetric, async ({ faker }) => {
    return {
      date: DateTime.fromJSDate(faker.date.recent()),
      countryCode: faker.location.countryCode('alpha-2'),
      version: faker.system.semver(),
      activeUserCount: faker.number.int({ min: 0, max: 1000 }),
      sessionCount: faker.number.int({ min: 0, max: 1000 }),
      playtimeMsec: faker.number.bigInt({ min: 0, max: 1_000_000_000 }),
      installCount: faker.number.int({ min: 0, max: 1000 }),
      activeUserNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 1000 })),
      playtimeNthDayMsecs: makeCohortValues(60, () =>
        faker.number.bigInt({ min: 0, max: 1_000_000 }).toString()
      ),
      sessionNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 5000 })),
    }
  })
  .relation('variant', () => FirebaseVersionVariantFactory)
  .build()
