import factory from '@adonisjs/lucid/factories'

import GameSpendOverride from '#models/game_spend_override'

import { GameSpendFactory } from './game_spend_factory.js'

export const GameSpendOverrideFactory = factory
  .define(GameSpendOverride, async ({ faker }) => {
    return {
      preTaxAmount: faker.number.float({ max: 10_000 }),
    }
  })
  .relation('spend', () => GameSpendFactory)
  .build()
