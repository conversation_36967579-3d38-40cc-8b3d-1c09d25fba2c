import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import BudgetRequest from '#models/budget_request'

import { WorkflowStepFactory } from './workflow_factory.js'

export const BudgetRequestFactory = factory
  .define(BudgetRequest, async ({ faker }) => {
    const createdAt = DateTime.fromJSDate(faker.date.recent({ days: 60 }))
    return {
      amount: faker.number.float({ min: 0, max: 1_000, multipleOf: 0.01 }),
      step: await WorkflowStepFactory.make(),
      stepId: faker.number.int({ min: 1, max: 10 }),
      createdAt,
      expirationDate: createdAt.plus({ days: 5 }),
    }
  })
  .relation('workflow', () => WorkflowStepFactory)
  .build()
