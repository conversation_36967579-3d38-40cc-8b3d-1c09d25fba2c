import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import ReleaseMetric from '#models/release_metric'
import { GameFactory } from '#database/factories/game_factory'
import { makeCohortValues } from '#database/utils/cohort'

export const ReleaseMetricFactory = factory
  .define(ReleaseMetric, async ({ faker }) => {
    return {
      date: DateTime.fromJSDate(faker.date.recent({ days: 30 })),
      version: faker.system.semver(),
      countryCode: faker.location.countryCode('alpha-2'),
      installCount: faker.number.int({ min: 0, max: 10000 }),
      activeUserCount: faker.number.int({ min: 0, max: 1000 }),
      sessionCount: faker.number.int({ min: 0, max: 5000 }),
      playtimeMsec: faker.number.bigInt({ min: 0, max: 1000000 }),
      activeUserNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 1000 })),
      playtimeNthDayMsecs: makeCohortValues(60, () =>
        faker.number.bigInt({ min: 0, max: 1_000_000 }).toString()
      ),
      sessionNthDayCounts: makeCohortValues(60, () => faker.number.int({ min: 0, max: 5000 })),
    }
  })
  .relation('game', () => GameFactory)
  .build()
