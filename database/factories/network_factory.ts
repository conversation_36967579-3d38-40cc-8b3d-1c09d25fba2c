import factory from '@adonisjs/lucid/factories'

import Network from '#models/network'
import { AdNetworkCategory } from '#config/enums'

export const NetworkFactory = factory
  .define(Network, async ({ faker }) => {
    const companyName = faker.company.name()
    const id = companyName.replaceAll(' ', '_').toLowerCase()
    const category = faker.helpers.arrayElement(Object.values(AdNetworkCategory))

    return {
      id: `${category}_${id}`,
      name: companyName,
      category,
    }
  })
  .build()
