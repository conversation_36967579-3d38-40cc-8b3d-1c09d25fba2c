import factory from '@adonisjs/lucid/factories'

import Workflow, { WorkflowStep } from '#models/workflow'

import { UserFactory } from './user_factory.js'

export const WorkflowStepFactory = factory
  .define(WorkflowStep, async ({ faker }) => {
    return {
      name: faker.book.title(),
      action: faker.science.chemicalElement().name,
    }
  })
  .relation('assignee', () => UserFactory)
  .build()

export const WorkflowFactory = factory
  .define(Workflow, async ({ faker }) => {
    return {
      name: faker.animal.petName(),
      roleId: 'ua',
      steps: new Array(3).fill(0).map(() =>
        new WorkflowStep().merge({
          name: faker.book.title(),
          action: faker.science.chemicalElement().name,
          alternateAction: faker.science.chemicalElement().symbol,
        })
      ),
    }
  })
  .build()
