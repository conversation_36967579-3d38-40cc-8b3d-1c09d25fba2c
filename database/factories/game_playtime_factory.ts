import factory from '@adonisjs/lucid/factories'

import GamePlaytime from '#models/game_playtime'

import { GameFactory } from './game_factory.js'
import { FactoryHelpers } from './factory_helpers.js'

export const GamePlaytimeFactory = factory
  .define(GamePlaytime, async ({ faker }) => {
    const installDate = FactoryHelpers.dateBefore()
    return {
      activeDate: installDate.plus({ days: faker.number.int({ min: 0, max: 30 }) }),
      installDate,
      activeUserCount: faker.number.int({ min: 0, max: 50_000 }),
      version: faker.system.semver(),
      engagementSessionCount: faker.number.int({ min: 0, max: 60_000 }),
      engagementDurationSec: faker.number.int({ min: 0, max: 20_000_000 }),
      isValidSemver: true,
    }
  })
  .relation('game', () => GameFactory)
  .build()
