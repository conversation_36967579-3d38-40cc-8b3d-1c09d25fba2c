import factory from '@adonisjs/lucid/factories'
import { DateTime } from 'luxon'

import GameRetentionRate from '#models/game_retention_rate'

import { GameFactory } from './game_factory.js'

export const GameRetentionRateFactory = factory
  .define(GameRetentionRate, async ({ faker }) => {
    return {
      date: DateTime.fromJSDate(
        faker.date.recent({ days: faker.number.int({ min: 1, max: 365 }) })
      ),
      version: faker.system.semver(),
      isValidSemver: true,
      newUsers: faker.number.int({ min: 0, max: 1_000 }),
      usersDay1: faker.number.int({ min: 0, max: 1_000 }),
      usersDay2: faker.number.int({ min: 0, max: 1_000 }),
      usersDay3: faker.number.int({ min: 0, max: 1_000 }),
      usersDay4: faker.number.int({ min: 0, max: 1_000 }),
      usersDay5: faker.number.int({ min: 0, max: 1_000 }),
      usersDay6: faker.number.int({ min: 0, max: 1_000 }),
      usersDay7: faker.number.int({ min: 0, max: 1_000 }),
    }
  })
  .relation('game', () => GameFactory)
  .build()
