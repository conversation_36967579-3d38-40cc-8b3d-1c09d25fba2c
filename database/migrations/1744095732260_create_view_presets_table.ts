import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'view_presets'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.string('page_id').notNullable()
      table.index(['user_id', 'page_id'])

      table.string('name').notNullable()
      table.jsonb('schema').notNullable().defaultTo('{}')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
