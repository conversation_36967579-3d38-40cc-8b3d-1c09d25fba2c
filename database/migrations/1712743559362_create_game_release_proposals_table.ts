import { BaseSchema } from '@adonisjs/lucid/schema'

import { GameReleaseProposalStatus } from '#config/enums'

export default class extends BaseSchema {
  protected tableName = 'game_release_proposals'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('repository').notNullable()

      table.string('revision').notNullable()
      table.string('semver').notNullable()
      table.string('installable_download_url').notNullable()
      table.string('platform').notNullable()

      table.smallint('status').defaultTo(GameReleaseProposalStatus.Pending)

      table.jsonb('extra').defaultTo('{}')
      table.text('changelog').nullable()

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
