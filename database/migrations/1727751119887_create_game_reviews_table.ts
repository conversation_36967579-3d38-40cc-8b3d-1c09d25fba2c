import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'game_reviews'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.text('game_id').index().notNullable()
      table.date('date').notNullable()

      table.unique(['date', 'game_id'])

      table.text('marketing_note')
      table.text('product_note')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
