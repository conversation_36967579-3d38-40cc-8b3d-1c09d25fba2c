import { BaseSchema } from '@adonisjs/lucid/schema'

import ACL from '#models/acl'
import { AclSubject } from '#config/enums'
import GameMetric from '#models/game_metric'

export default class extends BaseSchema {
  protected tableName = 'new_acls'

  async up() {
    this.defer(async (db) => {
      await ACL.query({ client: db })
        .where({ subject: 'game_metric_attributes_write' })
        .update({
          subject: ACL.subject(AclSubject.AttributeWrite, GameMetric),
        })
    })

    this.defer(async (db) => {
      await ACL.query({ client: db })
        .where({ subject: 'game_metric_attributes' })
        .update({
          subject: ACL.subject(AclSubject.AttributeRead, GameMetric),
        })
    })
  }

  async down() {
    this.defer(async (db) => {
      await ACL.query({ client: db }).where({ subject: 'attribute:read:GameMetric' }).update({
        subject: 'game_metric_attributes',
      })
    })

    this.defer(async (db) => {
      await ACL.query({ client: db }).where({ subject: 'attribute:write:GameMetric' }).update({
        subject: 'game_metric_attributes_write',
      })
    })
  }
}
