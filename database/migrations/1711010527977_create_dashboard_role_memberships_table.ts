import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'dashboard_role_memberships'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('role_id').notNullable()
      table.string('store_id').notNullable()
      table.jsonb('users').index().defaultTo([])

      table.unique(['store_id', 'role_id'])

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
