import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'budget_requests'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.integer('step_id').notNullable()
      table.jsonb('step').defaultTo('{}').notNullable()
      table.float('amount').notNullable()
      table.text('game_id').notNullable()
      table
        .integer('workflow_id')
        .unsigned()
        .references('id')
        .inTable('workflows')
        .onDelete('CASCADE')

      table.date('expiration_date').notNullable()
      table.timestamp('deleted_at')

      table.text('created_by_id').notNullable().index()

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
