import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary().notNullable()
      table.string('full_name').nullable()
      table.string('email', 254).notNullable().unique()

      table.text('encrypted_password').nullable()

      table.string('oauth_provider')
      table.string('oauth_user_id')

      table.jsonb('tool_permissions').defaultTo('[]')

      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
