import { BaseSchema } from '@adonisjs/lucid/schema'

import { UserKind } from '#graphql/main'
import User from '#models/user'

export default class extends BaseSchema {
  protected tableName = User.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.text('kind').notNullable().defaultTo(UserKind.Inhouse)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('kind')
    })
  }
}
