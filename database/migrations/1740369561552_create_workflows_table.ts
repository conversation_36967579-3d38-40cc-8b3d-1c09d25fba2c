import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'workflows'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.jsonb('steps').defaultTo('[]').notNullable()
      table.text('name').notNullable()
      table.text('role_id').notNullable()

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
