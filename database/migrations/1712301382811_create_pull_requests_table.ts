import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'pull_requests'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()

      table.jsonb('reviewers').defaultTo('[]')
      table.jsonb('requested_reviewers').defaultTo('[]')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
