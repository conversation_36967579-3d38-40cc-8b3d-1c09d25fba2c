import { BaseSchema } from '@adonisjs/lucid/schema'

import { GameReleaseStatus } from '#config/enums'

export default class extends BaseSchema {
  protected tableName = 'game_release_approvals'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table
        .integer('proposal_id')
        .unsigned()
        .notNullable()
        .references('game_release_proposals.id')
        .onDelete('CASCADE')

      table.string('reviewer_id').notNullable().references('users.id').onDelete('CASCADE')
      table.string('publisher_id').notNullable()

      table.smallint('release_status').defaultTo(GameReleaseStatus.Pending).notNullable()
      table.string('job_id')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
