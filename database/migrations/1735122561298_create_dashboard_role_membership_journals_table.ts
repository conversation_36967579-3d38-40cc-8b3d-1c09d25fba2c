import { BaseSchema } from '@adonisjs/lucid/schema'

import DashboardRoleMembership from '#models/dashboard_role_membership'

export default class extends BaseSchema {
  protected tableName = 'dashboard_role_membership_journals'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.jsonb('users').defaultTo([])
      table
        .integer('root_id')
        .references('id')
        .inTable(DashboardRoleMembership.table)
        .onDelete('CASCADE')

      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
