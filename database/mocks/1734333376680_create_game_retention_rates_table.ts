import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_version_rr'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.text('version')
      table.text('store_id').references('store_id').inTable('md_app_information')
      table.date('date')

      table.index(['store_id', 'version', 'date'])

      table.integer('new_users')
      table.float('users_d1')
      table.float('users_d2')
      table.float('users_d3')
      table.float('users_d4')
      table.float('users_d5')
      table.float('users_d6')
      table.float('users_d7')

      table.boolean('flag_version')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
