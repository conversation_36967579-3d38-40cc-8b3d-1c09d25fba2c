import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'firebase_metrics'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.date('date')
      table.text('country_code')
      table.text('version')
      table
        .text('variant_id')
        .references('id')
        .inTable('firebase_version_variants')
        .onDelete('CASCADE')

      table.integer('active_user_count').defaultTo(0)
      table.integer('session_count').defaultTo(0)
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
