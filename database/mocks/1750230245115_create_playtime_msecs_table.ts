import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'release_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('playtime_msec').notNullable().defaultTo(0)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('playtime_msec')
    })
  }
}
