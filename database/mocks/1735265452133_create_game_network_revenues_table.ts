import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_network_rev'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('store_id')
      table.integer('network_id')
      table.date('date')
      table.float('revenue')
      table.integer('impressions')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
