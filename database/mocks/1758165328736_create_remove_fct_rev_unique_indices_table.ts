import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_rev'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['date', 'store_id', 'mediation_id', 'network_id', 'ad_type_id'])
      table.index(['date', 'store_id', 'mediation_id', 'network_id', 'ad_type_id'])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex(['date', 'store_id', 'mediation_id', 'network_id', 'ad_type_id'])
      table.unique(['date', 'store_id', 'mediation_id', 'network_id', 'ad_type_id'])
    })
  }
}
