import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'release_metrics'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.bigIncrements('id').primary()
      table
        .text('game_id')
        .unsigned()
        .references('store_id')
        .inTable('md_app_information')
        .onDelete('CASCADE')
        .notNullable()
      table.date('date').notNullable()
      table.text('version').notNullable()
      table.text('country_code').notNullable()
      table.integer('install_count').notNullable().defaultTo(0)
      table.integer('active_user_count').notNullable().defaultTo(0)

      // Add unique constraint
      table.unique(['game_id', 'version', 'date', 'country_code'], {
        indexName: 'release_metrics_unique',
      })
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
