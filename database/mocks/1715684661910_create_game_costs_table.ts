import { BaseSchema } from '@adonisjs/lucid/schema'

import GameSpend from '#models/game_spend'

export default class extends BaseSchema {
  protected tableName = GameSpend.table

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.date('date')
      table.text('store_id').index()
      table.integer('agency_id')

      table.unique(['date', 'store_id', 'agency_id'])

      table.float('cost')
      table.float('tax')
      table.string('flag_is_cal').defaultTo('C')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
