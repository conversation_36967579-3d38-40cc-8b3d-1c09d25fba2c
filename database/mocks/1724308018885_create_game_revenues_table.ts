import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_rev'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.date('date')
      table.text('store_id')
      table.integer('mediation_id')
      table.integer('network_id')
      table.integer('ad_type_id')

      table.index(['store_id', 'date', 'ad_type_id'])

      table.float('revenue')
      table.integer('impressions')
      table.integer('dau')
      table.float('tax')

      table.unique(['date', 'store_id', 'mediation_id', 'network_id', 'ad_type_id'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
