import { BaseSchema } from '@adonisjs/lucid/schema'

import GameInstallation from '#models/game_installation'

export default class extends BaseSchema {
  protected tableName = GameInstallation.table

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments(GameInstallation.columnName('id'))

      table.text(GameInstallation.columnName('gameId'))
      table.date(GameInstallation.columnName('date'))

      table.integer(GameInstallation.columnName('paidInstalls'))
      table.integer(GameInstallation.columnName('organicInstalls'))
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
