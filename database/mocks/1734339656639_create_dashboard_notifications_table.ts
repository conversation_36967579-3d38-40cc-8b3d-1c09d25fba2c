import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'toolkit_notification'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.text('notification')

      table.boolean('is_pin')
      table.boolean('is_show')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
