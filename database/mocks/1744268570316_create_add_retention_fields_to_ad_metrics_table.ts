import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'ad_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.jsonb('session_nth_day_counts').defaultTo('{}')
      table.jsonb('active_user_nth_day_counts').defaultTo('{}')
      table.integer('session_count').defaultTo(0)
      table.integer('active_user_count').defaultTo(0)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumns(
        'session_nth_day_counts',
        'active_user_nth_day_counts',
        'session_count',
        'active_user_count'
      )
    })
  }
}
