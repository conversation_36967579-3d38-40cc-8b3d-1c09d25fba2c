import { BaseSchema } from '@adonisjs/lucid/schema'

import Game from '#models/game'

export default class extends BaseSchema {
  protected tableName = 'md_app_information'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string(Game.columnName('storeId')).primary()

      table.string(Game.columnName('packageName'))
      table.string(Game.columnName('platform'))
      table.string(Game.columnName('name'))
      table.string(Game.columnName('googleCloudProjectId'))
      table.string(Game.columnName('googleAnalyticsPropertyId'))
      table.string('flag_partner')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
