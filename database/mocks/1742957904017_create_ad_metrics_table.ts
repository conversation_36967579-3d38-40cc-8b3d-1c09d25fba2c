import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'ad_metrics'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()

      table.string('country_code', 2).notNullable()
      table.date('date').notNullable()
      table.string('ad_id').index()
      table.string('campaign_id').references('id').inTable('campaigns').onDelete('CASCADE')

      table.unique(['date', 'country_code', 'ad_id', 'campaign_id'])

      table.integer('install_count').defaultTo(0)
      table.integer('impression_count').defaultTo(0)
      table.integer('click_count').defaultTo(0)

      table.float('ad_cost_non_tax_amount').defaultTo(0)
      table.float('ad_rev_gross_amount').defaultTo(0)
      table.jsonb('ad_rev_nth_day_gross_amounts').defaultTo('{}')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
