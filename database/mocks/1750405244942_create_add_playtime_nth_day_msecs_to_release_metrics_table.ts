import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'release_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.jsonb('playtime_nth_day_msecs').defaultTo('{}')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('playtime_nth_day_msecs')
    })
  }
}
