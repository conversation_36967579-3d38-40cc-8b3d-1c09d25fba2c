import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'release_ad_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.jsonb('impression_nth_day_counts').defaultTo('{}')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('impression_nth_day_counts')
    })
  }
}
