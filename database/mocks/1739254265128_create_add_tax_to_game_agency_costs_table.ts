import { BaseSchema } from '@adonisjs/lucid/schema'

import GameAgencyCost from '#models/game_agency_cost'

export default class extends BaseSchema {
  protected tableName = GameAgencyCost.table

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.float('tax').defaultTo(0)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('tax')
    })
  }
}
