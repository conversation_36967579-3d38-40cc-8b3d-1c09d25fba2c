import { BaseSchema } from '@adonisjs/lucid/schema'

import GamePlaytime from '#models/game_playtime'

export default class extends BaseSchema {
  protected tableName = GamePlaytime.table

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments(GamePlaytime.columnName('id'))

      table.index([
        GamePlaytime.columnName('activeDate'),
        GamePlaytime.columnName('installDate'),
        GamePlaytime.columnName('gameId'),
        GamePlaytime.columnName('version'),
      ])

      table.date(GamePlaytime.columnName('activeDate'))
      table.date(GamePlaytime.columnName('installDate'))
      table.text(GamePlaytime.columnName('gameId'))
      table.text(GamePlaytime.columnName('version'))
      table.integer(GamePlaytime.columnName('engagementSessionCount'))
      table.integer(GamePlaytime.columnName('engagementDurationSec'))
      table.integer(GamePlaytime.columnName('activeUserCount'))
      table.boolean(GamePlaytime.columnName('isValidSemver'))
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
