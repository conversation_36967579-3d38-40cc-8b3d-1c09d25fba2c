import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'ads'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.primary(['id', 'campaign_id'])

      table.string('id').index()

      table.string('name').notNullable()
      table.string('campaign_id').references('id').inTable('campaigns').onDelete('CASCADE')
      table.string('group_id').references('id').inTable('ad_groups').onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
