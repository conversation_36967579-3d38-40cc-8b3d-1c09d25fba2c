import { BaseSchema } from '@adonisjs/lucid/schema'

import GameFirebaseMetric from '#models/game_firebase_metric'

export default class extends BaseSchema {
  protected tableName = GameFirebaseMetric.table

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments(GameFirebaseMetric.columnName('id'))

      table.text(GameFirebaseMetric.columnName('gameId'))
      table.date(GameFirebaseMetric.columnName('date'))

      table.float(GameFirebaseMetric.columnName('retentionRateDay1'))
      table.float(GameFirebaseMetric.columnName('retentionRateDay3'))
      table.float(GameFirebaseMetric.columnName('retentionRateDay7'))
      table.float(GameFirebaseMetric.columnName('playtime'))
      table.float(GameFirebaseMetric.columnName('averageSession'))
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
