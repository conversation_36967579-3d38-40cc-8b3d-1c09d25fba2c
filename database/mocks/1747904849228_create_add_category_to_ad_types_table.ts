import { BaseSchema } from '@adonisjs/lucid/schema'

import { AdTypeCategory } from '#config/enums'

export default class extends BaseSchema {
  protected tableName = 'ad_types'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('category').defaultTo(AdTypeCategory.Unknown)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('category')
    })
  }
}
