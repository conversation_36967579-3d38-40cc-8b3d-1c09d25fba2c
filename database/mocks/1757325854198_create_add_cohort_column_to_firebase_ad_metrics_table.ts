import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'firebase_ad_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.jsonb('impression_nth_day_counts').defaultTo('{}')
      table.jsonb('ad_rev_nth_day_gross_amounts').defaultTo('{}')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('impression_nth_day_counts')
      table.dropColumn('ad_rev_nth_day_gross_amounts')
    })
  }
}
