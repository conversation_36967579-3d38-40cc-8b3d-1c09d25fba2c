import { BaseSchema } from '@adonisjs/lucid/schema'

import { AdNetworkCategory } from '#config/enums'

export default class extends BaseSchema {
  protected tableName = 'networks'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.text('category').notNullable().defaultTo(AdNetworkCategory.Firebase)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('category')
    })
  }
}
