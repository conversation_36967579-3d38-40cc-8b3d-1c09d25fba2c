import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'firebase_ad_metrics'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.date('date')
      table.text('country_code')
      table.text('version')
      table
        .text('variant_id')
        .references('id')
        .inTable('firebase_version_variants')
        .onDelete('CASCADE')

      table.text('network_id').references('id').inTable('networks').onDelete('CASCADE')
      table.text('mediation_id').references('id').inTable('mediations').onDelete('CASCADE')
      table.text('ad_type_id').references('id').inTable('ad_types').onDelete('CASCADE')

      table.float('ad_rev_gross_amount').defaultTo(0)
      table.integer('impression_count').defaultTo(0)

      table.unique(
        [
          'date',
          'country_code',
          'version',
          'variant_id',
          'network_id',
          'mediation_id',
          'ad_type_id',
        ],
        { indexName: 'firebase_ad_metrics_unique_dimension' }
      )
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
