import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'release_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.jsonb('active_user_nth_day_counts').defaultTo('{}')
      table.jsonb('session_nth_day_counts').defaultTo('{}')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('active_user_nth_day_counts')
      table.dropColumn('session_nth_day_counts')
    })
  }
}
