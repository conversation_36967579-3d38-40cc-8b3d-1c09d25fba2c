import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'release_ad_metrics'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.bigIncrements('id').primary()
      table
        .text('game_id')
        .unsigned()
        .references('store_id')
        .inTable('md_app_information')
        .onDelete('CASCADE')
        .notNullable()
      table.date('date').notNullable()
      table
        .text('network_id')
        .unsigned()
        .references('id')
        .inTable('networks')
        .onDelete('CASCADE')
        .notNullable()
      table
        .text('ad_type_id')
        .unsigned()
        .references('id')
        .inTable('ad_types')
        .onDelete('CASCADE')
        .notNullable()
      table.text('version').notNullable()
      table.text('country_code').notNullable()
      table.integer('impression_count').notNullable().defaultTo(0)
      table.float('ad_rev_gross_amount').notNullable().defaultTo(0)

      // Add unique constraint
      table.unique(['game_id', 'version', 'date', 'country_code', 'ad_type_id', 'network_id'], {
        indexName: 'release_ad_metrics_unique',
      })
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
