import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_creative_performance'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.date('date')
      table.text('store_id')
      table.text('campaign_name')
      table.text('ad_group_name')
      table.text('ad_set')
      table.float('camp_target_roas')
      table.float('cost')
      table.integer('clicks')
      table.integer('impressions')
      table.float('revenue')
      table.integer('installs')
      table.text('editor')
      table.boolean('playable')
      table.increments('id')
      table.integer('agency_id')

      table.index(['store_id', 'date'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
