import { BaseSchema } from '@adonisjs/lucid/schema'

import { AdNetworkCategory } from '#config/enums'

export default class extends BaseSchema {
  protected tableName = 'ad_types'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.text('network_category').notNullable().defaultTo(AdNetworkCategory.Firebase)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('network_category')
    })
  }
}
