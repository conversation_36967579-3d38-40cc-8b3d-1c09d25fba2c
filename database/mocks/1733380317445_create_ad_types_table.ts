import { BaseSchema } from '@adonisjs/lucid/schema'

import AdType from '#models/ad_type'

export default class extends BaseSchema {
  protected tableName = AdType.table

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments(AdType.columnName('id'))

      table.integer(AdType.columnName('order'))
      table.text(AdType.columnName('name'))
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
