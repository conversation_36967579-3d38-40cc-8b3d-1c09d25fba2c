import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'md_agency_setting'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('store_id')
      table.float('percent_rev')
      table.integer('rev_d')
      table.date('start_date')
      table.date('end_date')
      table.integer('agency_id')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
