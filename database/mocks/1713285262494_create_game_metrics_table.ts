import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'app_analysis'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.date('date')
      table.string('store_id')

      table.unique(['store_id', 'date'])

      table.integer('paid_installs')
      table.integer('organic_installs')
      table.integer('total_installs')
      table.float('organic_percent')
      table.float('dont_used_1')
      table.float('dont_used_2')
      table.float('dont_used_3')
      table.float('total_rev')
      table.float('dont_used_4')
      table.float('rr_d1')
      table.float('rr_d3')
      table.float('rr_d7')
      table.float('banner_impsdau')
      table.float('inter_impsdau')
      table.float('reward_impsdau')
      table.float('aoa_impsdau')
      table.float('mrec_impsdau')
      table.float('audio_impsdau')
      table.float('admob_aoa_impsdau')
      table.float('admob_collapse_impsdau')
      table.float('admob_native_impsdau')
      table.float('admob_adaptive_impsdau')
      table.float('admob_mrec_impsdau')
      table.float('avg_sessions')
      table.float('avg_engagement_time')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
