import { BaseSchema } from '@adonisjs/lucid/schema'

import GameRewardUsage from '#models/game_reward_usage'

export default class extends BaseSchema {
  protected tableName = GameRewardUsage.table

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments(GameRewardUsage.columnName('id'))

      table.date('date')

      table.index([
        GameRewardUsage.columnName('version'),
        GameRewardUsage.columnName('gameId'),
        GameRewardUsage.columnName('date'),
        GameRewardUsage.columnName('location'),
      ])

      table.text(GameRewardUsage.columnName('gameId'))
      table.text(GameRewardUsage.columnName('version'))
      table.text(GameRewardUsage.columnName('location'))
      table.integer(GameRewardUsage.columnName('world'))
      table.integer(GameRewardUsage.columnName('level'))
      table.integer(GameRewardUsage.columnName('useCount'))
      table.boolean(GameRewardUsage.columnName('isValidSemver'))
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
