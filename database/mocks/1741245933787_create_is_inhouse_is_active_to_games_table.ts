import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'md_app_information'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('is_active').defaultTo(true)
      table.boolean('is_inhouse').defaultTo(true)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('is_active')
      table.dropColumn('is_inhouse')
    })
  }
}
