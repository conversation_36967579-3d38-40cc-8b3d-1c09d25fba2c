import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'firebase_version_variants'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.text('id').primary()

      table.text('experiment')
      table.text('name')
      table.text('game_id').references('store_id').inTable('md_app_information').onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
