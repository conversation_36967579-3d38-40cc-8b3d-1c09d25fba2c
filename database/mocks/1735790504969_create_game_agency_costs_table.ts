import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_agency_cost'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.date('date')
      table.integer('agency_id')
      table.float('cost')
      table
        .text('store_id')
        .references('store_id')
        .inTable('md_app_information')
        .onDelete('CASCADE')

      table.index(['store_id', 'date'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
