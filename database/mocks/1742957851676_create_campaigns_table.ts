import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'campaigns'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()

      table.string('name').notNullable()
      table
        .string('game_id')
        .references('store_id')
        .inTable('md_app_information')
        .onDelete('CASCADE')
      table
        .integer('media_source_id')
        .unsigned()
        .references('id')
        .inTable('md_media_source')
        .onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
