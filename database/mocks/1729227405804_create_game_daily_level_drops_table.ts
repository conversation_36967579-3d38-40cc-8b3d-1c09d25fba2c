import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'fct_level_drop'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.text('store_id')
      table.text('version')
      table.date('date')
      table.date('install_date')
      table.index(['store_id', 'version', 'date', 'install_date'])

      table.integer('world_id')
      table.integer('level')

      table.integer('attempt_count')
      table.integer('win_count')
      table.integer('lose_count')
      table.integer('skip_count')
      table.integer('complete_count')
      table.integer('active_users')

      table.float('total_play_time').defaultTo(0)

      table.boolean('flag_version').defaultTo(true)
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
