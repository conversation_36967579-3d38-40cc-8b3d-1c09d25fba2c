import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'firebase_metrics'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('install_count').defaultTo(0)
      table.bigint('playtime_msec').defaultTo(0)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumns('install_count', 'playtime_msec')
    })
  }
}
