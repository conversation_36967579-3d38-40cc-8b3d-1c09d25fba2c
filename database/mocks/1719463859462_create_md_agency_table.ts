import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'md_agency'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('agency_name')
      table.string('status')
      table.boolean('is_show')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
