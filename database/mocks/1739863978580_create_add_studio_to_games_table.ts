import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'md_app_information'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('partner_id').unsigned()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumns('flag_partner', 'partner_id')
    })
  }
}
