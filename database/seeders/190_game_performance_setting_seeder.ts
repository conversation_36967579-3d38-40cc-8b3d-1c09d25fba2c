import { BaseSeeder } from '@adonisjs/lucid/seeders'
import parseDuration from 'parse-duration'

import GamePerformanceSetting, { GamePerformanceCriteria } from '#models/game_performance_setting'
import { GamePerformanceCriteriaConclusion } from '#graphql/main'

export default class extends BaseSeeder {
  async run() {
    await GamePerformanceSetting.updateOrCreate(
      {
        gameId: 'default',
      },
      {
        criterias: [
          new GamePerformanceCriteria().merge({
            conclusion: GamePerformanceCriteriaConclusion.Drop,
            metrics: {
              roas: 0.3,
              playtime: parseDuration('4m', 'second')!,
              retentionRateDay1: 0.2,
              cpi: 0.3,
              monetImpsDau: 2.5,
            },
          }),
          new GamePerformanceCriteria().merge({
            conclusion: GamePerformanceCriteriaConclusion.Pass,
            metrics: {
              roas: 0.7,
              playtime: parseDuration('6m', 'second')!,
              retentionRateDay1: 0.25,
              cpi: 0.2,
              monetImpsDau: 4.5,
            },
          }),
        ],
      }
    )
  }
}
