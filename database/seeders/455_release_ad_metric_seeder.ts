import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'
import { faker } from '@faker-js/faker'

import Game from '#models/game'
import Network from '#models/network'
import AdType from '#models/v2/ad_type'
import { ReleaseAdMetricFactory } from '#database/factories/release_ad_metric_factory'
import { LucidImportService } from '#services/lucid_import_service'
import ReleaseAdMetric from '#models/release_ad_metric'
import { COUNTRIES } from '#database/utils/constants'
import { AdNetworkCategory } from '#config/enums'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const networks = await Network.query().where('category', AdNetworkCategory.Appsflyer)
    const adTypes = await AdType.query().where('networkCategory', AdNetworkCategory.Appsflyer)

    const records = await Promise.all(
      games.map((game) =>
        Promise.sequence(networks, async (network) =>
          Promise.sequence(adTypes, async (adType) =>
            Promise.sequence(new Array(30).fill(0), async (_, dateIndex) =>
              Promise.sequence(new Array(3).fill(0), async (_2, versionIndex) => {
                return await Promise.sequence(
                  faker.helpers.arrayElements(COUNTRIES, { min: 3, max: 5 }),
                  async (countryCode) => {
                    return await ReleaseAdMetricFactory.merge({
                      gameId: game.storeId,
                      networkId: network.id,
                      adTypeId: adType.id,
                      date: DateTime.now().minus({ days: dateIndex }),
                      countryCode,
                      version: `1.${versionIndex}.0`,
                    }).make()
                  }
                )
              })
            )
          )
        )
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      ReleaseAdMetric,
      records.flat(Number.MAX_SAFE_INTEGER) as ReleaseAdMetric[],
      { chunkSize: 2_000 }
    )
  }
}
