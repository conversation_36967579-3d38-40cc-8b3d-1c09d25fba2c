import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { AdGroupFactory } from '#database/factories/ad_group_factory'
import AdGroups from '#models/ad_group'
import { LucidImportService } from '#services/lucid_import_service'
import Campaign from '#models/campaign'

export default class extends BaseSeeder {
  public async run() {
    const campaigns = await Campaign.all()
    const records = await Promise.sequence(campaigns, async () =>
      Promise.sequence(new Array(5).fill(0), async () => {
        return await AdGroupFactory.make()
      })
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(AdGroups, records.flat(Number.MAX_SAFE_INTEGER) as AdGroups[])
  }
}
