import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { GameRewardUsageFactory } from '#database/factories/game_reward_usage_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameRewardUsage from '#models/game_reward_usage'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const records = await Promise.sequence(games, async (game) => {
      return await Promise.sequence(new Array(3).fill(0), async (_, versionIndex) => {
        return await Promise.sequence(new Array(4).fill(0), async (_2, worldIndex) => {
          return await Promise.sequence(new Array(10).fill(0), async (_3, levelIndex) => {
            return await Promise.sequence(new Array(30).fill(0), async (_4, dateIndex) => {
              return await GameRewardUsageFactory.merge({
                gameId: game.id,
                version: `1.0.${versionIndex}`,
                level: levelIndex,
                world: worldIndex - 2,
                date: DateTime.now().minus({ days: dateIndex }),
              }).make()
            })
          })
        })
      })
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameRewardUsage,
      records.flat(Number.MAX_SAFE_INTEGER) as GameRewardUsage[]
    )
  }
}
