import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import { AdTypeFactory } from '#database/factories/ad_type_factory'
import { LucidImportService } from '#services/lucid_import_service'
import AdType from '#models/ad_type'

export default class extends BaseSeeder {
  async run() {
    const records = await AdTypeFactory.makeMany(5)

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(AdType, records)
  }
}
