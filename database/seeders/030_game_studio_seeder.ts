import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { GameStudioFactory } from '#database/factories/game_studio_factory'
import GameStudio from '#models/game_studio'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const records = await GameStudioFactory.makeMany(20)
    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(GameStudio, records)
  }
}
