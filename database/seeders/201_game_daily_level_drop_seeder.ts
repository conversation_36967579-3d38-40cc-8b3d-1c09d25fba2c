import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { GameDailyLevelDropFactory } from '#database/factories/game_daily_level_drop_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameDailyLevelDrop from '#models/game_daily_level_drop'

export default class extends BaseSeeder {
  async run() {
    const startDate = DateTime.now().minus({ days: 100 })
    const games = await Game.all()
    const records = await Promise.sequence(games, (game) =>
      Promise.sequence(new Array(3).fill(0), (_, versionIndex) =>
        Promise.sequence(new Array(10).fill(0), (_1, dateIndex) =>
          Promise.sequence(new Array(10).fill(0), async (_2, installDateIndex) =>
            Promise.sequence(new Array(30).fill(0), async (_3, levelIndex) => {
              return Promise.all([
                GameDailyLevelDropFactory.merge({
                  gameId: game.storeId,
                  version: `1.${versionIndex}.0`,
                  date: startDate.plus({ days: dateIndex + 1 }),
                  installDate: startDate.plus({ days: installDateIndex + 1 }),
                  level: levelIndex + 1,
                }).make(),
              ])
            })
          )
        )
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameDailyLevelDrop,
      records.flat(Number.MAX_SAFE_INTEGER) as GameDailyLevelDrop[]
    )
  }
}
