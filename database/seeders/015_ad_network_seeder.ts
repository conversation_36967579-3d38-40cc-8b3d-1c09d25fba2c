import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import { AdNetworkFactory } from '#database/factories/ad_network_factory'
import GameRevenue from '#models/game_revenue'
import { LucidImportService } from '#services/lucid_import_service'
import AdNetwork from '#models/ad_network'

export default class extends BaseSeeder {
  async run() {
    const ids = Object.values(GameRevenue.networkId)
    const records = await Promise.sequence(
      new Array(Math.max(Math.max(...ids), 30)).fill(0),
      (_, index) => AdNetworkFactory.merge({ id: index + 1 }).make()
    )
    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(AdNetwork, records.flat(Number.MAX_SAFE_INTEGER) as AdNetwork[])
  }
}
