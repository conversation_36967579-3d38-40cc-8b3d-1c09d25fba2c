import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { LucidImportService } from '#services/lucid_import_service'
import { DashboardNotificationFactory } from '#database/factories/dashboard_notification_factory'
import DashboardNotification from '#models/dashboard_notification'

export default class extends BaseSeeder {
  async run() {
    await DashboardNotificationFactory.merge({
      isPinned: true,
    }).create()

    const record = await Promise.sequence(new Array(10).fill(0), async () => {
      return await DashboardNotificationFactory.merge({
        isPinned: false,
      }).make()
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      DashboardNotification,
      record.flat(Number.MAX_SAFE_INTEGER) as DashboardNotification[]
    )
  }
}
