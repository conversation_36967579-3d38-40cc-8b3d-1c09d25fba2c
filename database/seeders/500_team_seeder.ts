import { chunk } from 'lodash-es'
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import { TeamFactory } from '#database/factories/team_factory'
import User from '#models/user'

export default class extends BaseSeeder {
  async run() {
    const users = await User.all()
    const configMapRegistry = await app.container.make('cmap.registry')
    const roleConfigMapCollection = configMapRegistry.get('cmap.dash.role')

    await Promise.sequence(
      chunk(users, Math.floor(users.length / roleConfigMapCollection.length)),
      async (userGroup, index) => {
        const leader = userGroup[0]
        const roleConfigMap = roleConfigMapCollection[index]
        if (!leader || !roleConfigMap) {
          return
        }

        const team = await TeamFactory.merge({
          leaderId: leader.id,
          roleId: roleConfigMap.id,
        }).create()

        const members = userGroup.slice(1)
        await team.related('members').saveMany(members)
      }
    )
  }
}
