import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { chunk } from 'lodash-es'
import app from '@adonisjs/core/services/app'

import GameMetric from '#models/game_metric'
import { GameMetricOverrideFactory } from '#database/factories/game_metric_override_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameMetricOverride from '#models/game_metric_override'

export default class extends BaseSeeder {
  async run() {
    const metrics = await GameMetric.query().select('id', 'storeId', 'date')

    const records = await Promise.sequence(chunk(metrics, 5), async (metricsChunk) => {
      return await Promise.all(
        metricsChunk.map(async (metric) => {
          return await GameMetricOverrideFactory.merge({
            date: metric.date,
            gameId: metric.storeId,
          }).make()
        })
      )
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameMetricOverride,
      records.flat(Number.MAX_SAFE_INTEGER) as GameMetricOverride[]
    )
  }
}
