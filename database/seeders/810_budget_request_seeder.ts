import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { faker } from '@faker-js/faker'
import { DateTime } from 'luxon'

import { BudgetRequestFactory } from '#database/factories/budget_request_factory'
import BudgetRequest from '#models/budget_request'
import Game from '#models/game'
import User from '#models/user'
import Workflow from '#models/workflow'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const workflows = await Workflow.all()
    const users = await User.query().preload('team').whereNotNull('teamId')

    const records = await Promise.sequence(games, (game) =>
      Promise.sequence(new Array(50).fill(0), async () => {
        const workflow = faker.helpers.arrayElement(workflows)
        const stepId = faker.number.int({ min: 0, max: workflow.steps.length - 1, multipleOf: 1 })
        const createdBy = faker.helpers.arrayElement(
          users.filter((u) => u.team.roleId === workflow.roleId)
        )

        return await BudgetRequestFactory.merge({
          workflowId: workflow.id,
          gameId: game.id,
          stepId,
          step: workflow.steps[stepId],
          updatedAt: DateTime.now(),
          createdAt: DateTime.fromJSDate(faker.date.recent({ days: 30 })),
          createdById: createdBy.id,
        }).make()
      })
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      BudgetRequest,
      records.flat(Number.MAX_SAFE_INTEGER) as BudgetRequest[]
    )
  }
}
