import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { faker } from '@faker-js/faker'

import User from '#models/user'
import Team from '#models/team'

export default class extends BaseSeeder {
  async run() {
    await User.query().update({ teamId: null })
    const users = await User.all().then((us) => new Set(us))
    const teams = await Team.all()

    const pickRandomUsers = (count: number) => {
      const randoms = faker.helpers.arrayElements(Array.from(users), count)
      randoms.forEach((u) => users.delete(u))
      return randoms
    }

    await Promise.sequence(teams, async (team) => {
      if (users.size < 3) {
        return
      }

      const teamUsers = pickRandomUsers(3)
      const [leader, ...members] = teamUsers

      await User.query()
        .whereIn(
          'id',
          members.map((u) => u.id)
        )
        .update({ teamId: team.id })

      await team.merge({ leaderId: leader.id }).save()
    })
  }
}
