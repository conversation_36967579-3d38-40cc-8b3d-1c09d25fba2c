import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import { GameAgencyCostFactory } from '#database/factories/game_agency_cost_factory'
import Game from '#models/game'
import GameSpend from '#models/game_spend'
import { LucidImportService } from '#services/lucid_import_service'
import GameAgencyCost from '#models/game_agency_cost'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const records = await Promise.sequence(games, async (game) => {
      const agencyIds = await GameSpend.query()
        .where('storeId', game.id)
        .select('networkId')
        .then((rs) => rs.map((r) => r.networkId))

      return await Promise.sequence(agencyIds, async (agencyId) => {
        return await Promise.sequence(
          new Array(60).fill(0),
          async (_, dateIndex) =>
            await GameAgencyCostFactory.merge({
              gameId: game.id,
              agencyId,
              date: DateTime.now().minus({ days: dateIndex }),
            }).make()
        )
      })
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameAgencyCost,
      records.flat(Number.MAX_SAFE_INTEGER) as GameAgencyCost[]
    )
  }
}
