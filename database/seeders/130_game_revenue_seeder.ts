import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import fastCartesian from 'fast-cartesian'
import { faker } from '@faker-js/faker'
import app from '@adonisjs/core/services/app'
import { uniq } from 'lodash-es'

import Game from '#models/game'
import GameRevenue from '#models/game_revenue'
import AdNetwork from '#models/ad_network'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.query()
    const networks = await AdNetwork.all()
    const today = DateTime.now()

    const records = await Promise.sequence(games, async (game) => {
      const dateToDau = new Map<string, number>()

      const combinations = fastCartesian([
        Object.values(GameRevenue.adType),
        Object.values(GameRevenue.mediationId),
        uniq(
          faker.helpers
            .arrayElements(networks, 2)
            .map((e) => e.id)
            .concat(Object.values(GameRevenue.networkId))
        ),
      ])

      return new Array(60).fill(0).flatMap((_, index) => {
        return combinations.map(([adType, mediationId, networkId]) => {
          const date = today.minus({ days: index + 1 })
          const dauKey = date.toFormat('yyyy-MM-dd')
          if (!dateToDau.has(dauKey)) {
            dateToDau.set(dauKey, faker.number.int({ min: 0, max: 2_000 }))
          }

          return new GameRevenue().merge({
            date,
            adType,
            mediationId,
            networkId,
            storeId: game.storeId,
            impressionCount: faker.number.int({ min: 0, max: 2_000 }),
            dailyActiveUserCount: dateToDau.get(dauKey),
            revenue: faker.number.float({ min: 0, max: 200, multipleOf: 0.01 }),
            taxRate: faker.number.float({ min: 0, max: 0.1, multipleOf: 0.01 }),
          })
        })
      })
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameRevenue,
      records.flat(Number.MAX_SAFE_INTEGER) as GameRevenue[]
    )
  }
}
