import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { groupBy, toPairs } from 'lodash-es'
import { faker } from '@faker-js/faker'

import { AdFactory } from '#database/factories/ad_factory'
import Ad from '#models/ad'
import { LucidImportService } from '#services/lucid_import_service'
import AdGroup from '#models/ad_group'
import Campaign from '#models/campaign'

export default class extends BaseSeeder {
  public async run() {
    let adGroups = await AdGroup.all()
    const campaigns = await Campaign.all()

    const campaignAdGroupPairs = toPairs(groupBy(campaigns, 'mediaSourceId')).map((kv) => {
      const mediaSourceCampaigns = kv[1]
      const mediaSourceAdGroups = faker.helpers.arrayElements(adGroups, 5)
      adGroups = adGroups.filter((adGroup) => !mediaSourceAdGroups.includes(adGroup))
      return [mediaSourceCampaigns, mediaSourceAdGroups] as const
    })

    const records = await Promise.sequence(campaignAdGroupPairs, async ([cs, ags]) =>
      Promise.sequence(ags, async (adGroup) => {
        return Promise.sequence(new Array(5).fill(0), async () => {
          return await AdFactory.merge({
            groupId: adGroup.id,
            campaignId: faker.helpers.arrayElement(cs).id,
          }).make()
        })
      })
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(Ad, records.flat(Number.MAX_SAFE_INTEGER) as Ad[])
  }
}
