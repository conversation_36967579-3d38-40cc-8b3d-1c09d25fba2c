import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'
import { DateTime } from 'luxon'
import { faker } from '@faker-js/faker'

import FirebaseVersionVariant from '#models/firebase_version_variant'
import { LucidImportService } from '#services/lucid_import_service'
import { FirebaseMetricFactory } from '#database/factories/firebase_metric_factory'
import FirebaseMetric from '#models/firebase_metric'
import FirebaseAdMetric from '#models/firebase_ad_metric'
import Mediation from '#models/mediation'
import Network from '#models/network'
import { FirebaseAdMetricFactory } from '#database/factories/firebase_ad_metric_factory'
import AdType from '#models/v2/ad_type'
import { AdNetworkCategory } from '#config/enums'
import { COUNTRIES } from '#database/utils/constants'

export default class extends BaseSeeder {
  async run() {
    const variants = await FirebaseVersionVariant.all()

    const networks = await Network.query().where('category', AdNetworkCategory.Firebase)
    const mediations = await Mediation.all()
    const adTypes = await AdType.query().where('networkCategory', AdNetworkCategory.Firebase)

    const metrics: FirebaseMetric[] = []

    const lucidImportService = await app.container.make(LucidImportService)

    await Promise.sequence(variants, async (variant) => {
      const adMetrics = await Promise.sequence(new Array(30).fill(0), async (_, dateIndex) => {
        return await Promise.sequence(new Array(3).fill(0), async (_2, versionIndex) =>
          Promise.sequence(
            faker.helpers.arrayElements(COUNTRIES, { min: 3, max: 5 }),
            async (countryCode) => {
              metrics.push(
                await FirebaseMetricFactory.merge({
                  variantId: variant.id,
                  date: DateTime.now().minus({ day: dateIndex }),
                  countryCode,
                  version: `1.${versionIndex}.0`,
                }).make()
              )

              return Promise.sequence(
                faker.helpers.arrayElements(mediations, { min: 1, max: 2 }),
                async (mediation) =>
                  Promise.sequence(networks, async (network) =>
                    Promise.sequence(
                      faker.helpers.arrayElements(adTypes, { min: 2, max: 3 }),
                      async (adType) => {
                        return await FirebaseAdMetricFactory.merge({
                          variantId: variant.id,
                          date: DateTime.now().minus({ day: dateIndex }),
                          countryCode,
                          version: `1.${versionIndex}.0`,
                          networkId: network.id,
                          mediationId: mediation.id,
                          adTypeId: adType.id,
                        }).make()
                      }
                    )
                  )
              )
            }
          )
        )
      })

      await lucidImportService.import(
        FirebaseAdMetric,
        adMetrics.flat(Infinity) as FirebaseAdMetric[],
        {
          chunkSize: 2_000,
          excludedAttributes: ['adRevGrossAmountPerActiveUser', 'impressionCountPerActiveUser'],
        }
      )
    })

    await lucidImportService.import(FirebaseMetric, metrics, {
      chunkSize: 2_000,
      excludedAttributes: [
        'retentionRate',
        'sessionCountPerActiveUser',
        'dailyActiveUserCount',
        'retentionNthDayRates',
        'adRevNthDayGrossAmounts',
        'impressionNthDayCounts',
      ],
    })
  }
}
