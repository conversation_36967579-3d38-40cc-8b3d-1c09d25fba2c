import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { faker } from '@faker-js/faker'
import app from '@adonisjs/core/services/app'

import { GameFactory } from '#database/factories/game_factory'
import GameStudio from '#models/game_studio'
import { LucidImportService } from '#services/lucid_import_service'
import Game from '#models/game'

export default class extends BaseSeeder {
  async run() {
    const studios = await GameStudio.all()
    const games = await GameFactory.merge({ isActive: true }).makeMany(5)
    games.forEach((game) => {
      game.studioId = faker.helpers.arrayElement(studios).id
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(Game, games)
  }
}
