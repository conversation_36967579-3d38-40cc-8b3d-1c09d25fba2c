import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { GameLevelDropFactory } from '#database/factories/game_level_drop_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameLevelDrop from '#models/game_level_drop'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()

    const records = await Promise.sequence(games, (game) =>
      Promise.sequence(new Array(3).fill(0), (_, versionIndex) =>
        Promise.sequence(new Array(3).fill(0), (_2, worldIndex) =>
          Promise.sequence(new Array(100).fill(0), async (_3, levelIndex) => {
            return await GameLevelDropFactory.merge({
              gameId: game.storeId,
              version: `1.0.${versionIndex}`,
              level: levelIndex - 4,
              world: worldIndex - 1,
            }).make()
          })
        )
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameLevelDrop,
      records.flat(Number.MAX_SAFE_INTEGER) as GameLevelDrop[]
    )
  }
}
