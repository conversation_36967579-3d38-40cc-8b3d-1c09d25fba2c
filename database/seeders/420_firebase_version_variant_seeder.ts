import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { LucidImportService } from '#services/lucid_import_service'
import FirebaseVersionVariant from '#models/firebase_version_variant'
import Game from '#models/game'
import { FirebaseVersionVariantFactory } from '#database/factories/firebase_version_variant_factory'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()

    const records = await Promise.sequence(games, async (game) =>
      Promise.sequence(
        ['std', ...new Array(2).fill(0).map((_, index) => `firebase_exp_${index}`)],
        async (experiment) =>
          Promise.sequence(
            ['std', ...new Array(2).fill(0).map((_, index) => index.toString())],
            async (variant) => {
              return await FirebaseVersionVariantFactory.merge({
                experiment,
                name: variant,
                gameId: game.id,
                id: `${game.id}_${experiment}_${variant}`,
              }).make()
            }
          )
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      FirebaseVersionVariant,
      records.flat(Number.MAX_SAFE_INTEGER) as FirebaseVersionVariant[]
    )
  }
}
