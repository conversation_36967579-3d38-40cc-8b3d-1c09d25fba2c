import { DateTime } from 'luxon'
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import { GameMetricFactory } from '#database/factories/game_metric_factory'
import Game from '#models/game'
import { LucidImportService } from '#services/lucid_import_service'
import GameMetric from '#models/game_metric'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.query()
    const records = await Promise.all(
      games.map((game) =>
        Promise.sequence(new Array(60).fill(0), async (_, index) => {
          return await GameMetricFactory.merge({
            storeId: game.storeId,
            date: DateTime.now().minus({ day: index }),
          }).make()
        })
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameMetric,
      records.flat(Number.MAX_SAFE_INTEGER) as GameMetric[]
    )
  }
}
