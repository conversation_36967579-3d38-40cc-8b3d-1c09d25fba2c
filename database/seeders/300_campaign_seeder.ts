import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { CampaignFactory } from '#database/factories/campaign_factory'
import Campaign from '#models/campaign'
import { LucidImportService } from '#services/lucid_import_service'
import Game from '#models/game'
import MediaSource from '#models/media_source'

export default class extends BaseSeeder {
  public async run() {
    const games = await Game.all()
    const mediaSources = await MediaSource.all()
    const records = await Promise.sequence(games, (game) =>
      Promise.sequence(mediaSources, (mediaSource) =>
        Promise.sequence(new Array(5).fill(0), async () => {
          return await CampaignFactory.merge({
            gameId: game.id,
            mediaSourceId: mediaSource.id,
          }).make()
        })
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(Campaign, records.flat(Number.MAX_SAFE_INTEGER) as Campaign[])
  }
}
