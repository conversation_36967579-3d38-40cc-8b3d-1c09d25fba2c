import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import GameCreativeMetric from '#models/game_creative_metric'
import Game from '#models/game'
import AdAgency from '#models/ad_agency'
import { GameCreativeMetricFactory } from '#database/factories/game_creative_metric_factory'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const agencies = await AdAgency.all()
    const today = DateTime.now()

    const records = (await Promise.sequence(games, async (game) =>
      Promise.sequence(agencies, async (agency) =>
        Promise.sequence(new Array(60).fill(0), async (_, i) => {
          return await GameCreativeMetricFactory.merge({
            gameId: game.id,
            agencyId: agency.id,
            date: today.minus({ days: i }),
          }).make()
        })
      )
    ).then((c) => c.flat(Infinity))) as GameCreativeMetric[]

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(GameCreativeMetric, records)
  }
}
