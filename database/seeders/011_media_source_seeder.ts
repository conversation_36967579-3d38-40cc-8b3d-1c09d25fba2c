import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import { MediaSourceFactory } from '#database/factories/media_source_factory'
import { LucidImportService } from '#services/lucid_import_service'
import MediaSource from '#models/media_source'
import AdAgency from '#models/ad_agency'

export default class extends BaseSeeder {
  async run() {
    const agencies = await AdAgency.all()
    const records = await Promise.sequence(agencies, async (agency) => {
      return await MediaSourceFactory.merge({
        agencyId: agency.id,
      }).make()
    })
    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(MediaSource, records)
  }
}
