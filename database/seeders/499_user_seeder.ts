import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import hash from '@adonisjs/core/services/hash'

import { UserFactory } from '#database/factories/user_factory'
import User, { ToolPermission } from '#models/user'
import { LucidImportService } from '#services/lucid_import_service'
import { UserKind } from '#graphql/main'
import { Permission, Tool } from '#config/enums'

export default class extends BaseSeeder {
  async run() {
    await UserFactory.merge({
      fullName: 'Admin',
      email: '<EMAIL>',
      password: '123456',
    }).create()

    await UserFactory.merge({
      fullName: 'Partner',
      email: '<EMAIL>',
      password: '123456',
      kind: UserKind.Partner,
      toolPermissions: [
        new ToolPermission().merge({ action: Permission.View, tool: Tool.Dashboard }),
      ],
    }).create()

    const records = await Promise.sequence(new Array(50).fill(0), async () => {
      return await UserFactory.merge({
        password: await hash.make('123456'),
      }).make()
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(User, records.flat(Number.MAX_SAFE_INTEGER) as User[])
  }
}
