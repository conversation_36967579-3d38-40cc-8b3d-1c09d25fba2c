import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { faker } from '@faker-js/faker'

import User from '#models/user'
import Workflow, { WorkflowStep } from '#models/workflow'

export default class extends BaseSeeder {
  async run() {
    const users = await User.all()

    await Workflow.create({
      name: 'Mintegral Budget Request',
      roleId: 'ua',
      steps: [
        new WorkflowStep().merge({
          name: 'UA Lead',
          action: 'Approve',
          alternateAction: 'Reject',
          assigneeId: faker.helpers.arrayElement(users).id,
        }),
        new WorkflowStep().merge({
          name: 'Action',
          action: 'Approve',
          alternateAction: 'Reject',
          assigneeId: faker.helpers.arrayElement(users).id,
        }),
        new WorkflowStep().merge({
          name: 'Manager',
          action: 'Approve',
          alternateAction: 'Reject',
          assigneeId: faker.helpers.arrayElement(users).id,
        }),
      ],
    })

    await Workflow.create({
      name: 'Tiktok Budget Request',
      roleId: 'ua',
      steps: [
        new WorkflowStep().merge({
          name: 'UA Lead',
          action: 'Approve',
          alternateAction: 'Reject',
          assigneeId: faker.helpers.arrayElement(users).id,
        }),
        new WorkflowStep().merge({
          name: 'Action',
          action: 'Approve',
          alternateAction: 'Reject',
          assigneeId: faker.helpers.arrayElement(users).id,
        }),
        new WorkflowStep().merge({
          name: 'Manager',
          action: 'Approve',
          alternateAction: 'Reject',
          assigneeId: faker.helpers.arrayElement(users).id,
        }),
      ],
    })
  }
}
