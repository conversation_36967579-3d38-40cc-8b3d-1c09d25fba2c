import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { GameReleaseProposalFactory } from '../factories/game_release_proposal_factory.js'
import GameReleaseProposal from '#models/game_release_proposal'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const records = await Promise.sequence(new Array(20).fill(0), async () => {
      return await GameReleaseProposalFactory.make()
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameReleaseProposal,
      records.flat(Number.MAX_SAFE_INTEGER) as GameReleaseProposal[]
    )
  }
}
