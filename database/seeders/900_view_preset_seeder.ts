import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import User from '#models/user'
import { ViewPresetFactory } from '#database/factories/view_preset_factory'
import { LucidImportService } from '#services/lucid_import_service'
import ViewPreset from '#models/view_preset'

export default class extends BaseSeeder {
  async run() {
    const users = await User.all()
    const registry = await app.container.make('cmap.registry')
    const viewPresetConfigMapCollection = registry.get('cmap.viewpreset')

    const records = await Promise.sequence(users, (user) => {
      return Promise.sequence(viewPresetConfigMapCollection, (vp) => {
        return ViewPresetFactory.merge({
          pageId: vp.pageId,
          userId: user.id,
        }).make()
      })
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      ViewPreset,
      records.flat(Number.MAX_SAFE_INTEGER) as ViewPreset[]
    )
  }
}
