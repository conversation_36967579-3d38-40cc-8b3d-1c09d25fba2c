import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'
import { faker } from '@faker-js/faker'
import { chunk } from 'lodash-es'

import Ad from '#models/ad'
import { LucidImportService } from '#services/lucid_import_service'
import AdMetric from '#models/ad_metric'
import { AdMetricFactory } from '#database/factories/ad_metric_factory'

export default class extends BaseSeeder {
  public async run() {
    const geos = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'KR', 'CN', 'RU', '__']

    const ads = await Ad.all()
    await Promise.sequence(chunk(ads, 10000), async (adsChunk) => {
      const records = await Promise.sequence(adsChunk, async (ad) =>
        Promise.sequence(new Array(60).fill(0), (_, dateIndex) =>
          Promise.sequence(
            faker.helpers.arrayElements(geos, { min: 0, max: 5 }),
            async (countryCode) => {
              return await AdMetricFactory.merge({
                adId: ad.id,
                campaignId: ad.campaignId,
                date: DateTime.now().minus({ day: dateIndex }),
                countryCode,
                adRevNthDayGrossAmounts: Object.fromEntries(
                  new Array(dateIndex).fill(0).map((_2, index) => {
                    return [
                      index.toString(),
                      faker.number.float({ min: 0, max: 20, multipleOf: 0.00001 }),
                    ]
                  })
                ),
              }).make()
            }
          )
        )
      )

      const lucidImportService = await app.container.make(LucidImportService)
      await lucidImportService.import(
        AdMetric,
        records.flat(Number.MAX_SAFE_INTEGER) as AdMetric[],
        {
          chunkSize: 2_000,
          excludedAttributes: [
            'roas',
            'roasNthDayRates',
            'retentionRate',
            'retentionNthDayRates',
            'dailyActiveUserCount',
            'cpi',
            'ctr',
            'cvr',
            'cpc',
            'ipm',
          ],
        }
      )
    })
  }
}
