import { BaseSeeder } from '@adonisjs/lucid/seeders'
import fastCartesian from 'fast-cartesian'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import AdNetwork from '#models/ad_agency'
import Game from '#models/game'
import { GameSpendFactory } from '#database/factories/game_spend_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameSpend from '#models/game_spend'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const adNetworks = await AdNetwork.query().withScopes((s) => s.default())

    const pairs = fastCartesian([games, adNetworks])

    const records = await Promise.sequence(pairs, ([game, adNetwork]) =>
      Promise.sequence(new Array(60).fill(0), async (_, index) => {
        return await GameSpendFactory.merge({
          storeId: game.storeId,
          networkId: adNetwork.id,
          date: DateTime.now().minus({ day: index }),
        }).make()
      })
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(GameSpend, records.flat(Number.MAX_SAFE_INTEGER) as GameSpend[])
  }
}
