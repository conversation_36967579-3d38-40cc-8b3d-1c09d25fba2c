import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { GameInstallationFactory } from '#database/factories/game_installation_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameInstallation from '#models/game_installation'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const records = await Promise.sequence(games, (game) =>
      Promise.sequence(new Array(60).fill(0), async (_, index) => {
        return await GameInstallationFactory.merge({
          gameId: game.id,
          date: DateTime.now().minus({ day: index }),
        }).make()
      })
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameInstallation,
      records.flat(Number.MAX_SAFE_INTEGER) as GameInstallation[]
    )
  }
}
