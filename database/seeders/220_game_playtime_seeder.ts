import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import { GamePlaytimeFactory } from '#database/factories/game_playtime_factory'
import Game from '#models/game'
import { LucidImportService } from '#services/lucid_import_service'
import GamePlaytime from '#models/game_playtime'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const records = await Promise.sequence(games, async (game) => {
      const firstInstallDate = DateTime.now().minus({ month: 1 })

      return await Promise.sequence(new Array(3).fill(0), async (_, versionIndex) => {
        return await Promise.sequence(new Array(5).fill(0), async (_2, installDateIndex) => {
          const installDate = firstInstallDate.plus({ days: installDateIndex })
          return await Promise.sequence(new Array(20).fill(0), async (_3, activeDateIndex) => {
            return await GamePlaytimeFactory.merge({
              activeDate: installDate.plus({ days: 2 + activeDateIndex }),
              installDate,
              version: `1.0.${versionIndex}`,
              gameId: game.id,
            }).make()
          })
        })
      })
    })

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GamePlaytime,
      records.flat(Number.MAX_SAFE_INTEGER) as GamePlaytime[]
    )
  }
}
