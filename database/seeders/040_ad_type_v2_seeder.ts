import { BaseSeeder } from '@adonisjs/lucid/seeders'

import AdType from '#models/v2/ad_type'
import { AdNetworkCategory, AdTypeCategory } from '#config/enums'

export default class extends BaseSeeder {
  async run() {
    await Promise.sequence(Object.values(AdNetworkCategory), async (networkCategory) => {
      await AdType.createMany(
        Object.values(AdTypeCategory).map((name) => ({
          id: `${networkCategory}_${name.replaceAll(' ', '_').toLowerCase()}`,
          name,
          category: name,
          networkCategory: networkCategory as AdNetworkCategory,
        }))
      )
    })
  }
}
