import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { GameRetentionRateFactory } from '#database/factories/game_retention_rate_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameRetentionRate from '#models/game_retention_rate'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.query()
    const records = await Promise.sequence(games, (game) =>
      Promise.sequence(new Array(3).fill(0), (_, versionIndex) =>
        Promise.sequence(new Array(60).fill(0), (_2, dateIndex) => {
          return GameRetentionRateFactory.merge({
            gameId: game.id,
            version: `1.0.${versionIndex}`,
            date: DateTime.now().minus({ days: dateIndex + 1 }),
          }).make()
        })
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameRetentionRate,
      records.flat(Number.MAX_SAFE_INTEGER) as GameRetentionRate[]
    )
  }
}
