import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import { GameNetworkRevenueFactory } from '#database/factories/game_network_revenue_factory'
import Game from '#models/game'
import GameRevenue from '#models/game_revenue'
import { LucidImportService } from '#services/lucid_import_service'
import GameNetworkRevenue from '#models/game_network_revenue'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const records = await Promise.all(
      games.map(async (game) => {
        const enabledNetworkIds = await GameRevenue.query()
          .where('storeId', game.id)
          .select('networkId')
          .groupBy('networkId')
          .then((rs) => rs.map((r) => r.networkId))
        return Promise.sequence(enabledNetworkIds, async (networkId) => {
          return Promise.sequence(new Array(60).fill(0), async (_, dateIndex) => {
            return await GameNetworkRevenueFactory.merge({
              gameId: game.id,
              networkId,
              date: DateTime.now().minus({ days: dateIndex }),
            }).make()
          })
        })
      })
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameNetworkRevenue,
      records.flat(Number.MAX_SAFE_INTEGER) as GameNetworkRevenue[]
    )
  }
}
