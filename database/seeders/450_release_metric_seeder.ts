import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { COUNTRIES } from '#database/utils/constants'
import { ReleaseMetricFactory } from '#database/factories/release_metric_factory'
import ReleaseMetric from '#models/release_metric'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    const countries = COUNTRIES

    const records = await Promise.all(
      games.map(async (game) =>
        Promise.sequence(countries, (countryCode) =>
          Promise.sequence(new Array(30).fill(0), (_, dateIndex) =>
            Promise.sequence(new Array(3).fill(0), async (_2, versionIndex) => {
              const date = DateTime.now().minus({ days: dateIndex })

              return await ReleaseMetricFactory.merge({
                gameId: game.id,
                countryCode,
                date,
                version: `1.${versionIndex}.0`,
              }).make()
            })
          )
        )
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      ReleaseMetric,
      records.flat(Number.MAX_SAFE_INTEGER) as ReleaseMetric[],
      {
        chunkSize: 2_000,
        excludedAttributes: [
          'retentionNthDayRates',
          'retentionRate',
          'sessionCountPerActiveUser',
          'dailyActiveUserCount',
          'lifetimeNthDayValues',
          'adRevGrossAmount',
          'adRevNthDayGrossAmounts',
          'adRevGrossAmountPerActiveUser',
          'impressionCount',
          'impressionCountPerActiveUser',
          'impressionNthDayCounts',
        ],
      }
    )
  }
}
