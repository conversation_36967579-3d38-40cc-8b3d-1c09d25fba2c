import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import app from '@adonisjs/core/services/app'

import Game from '#models/game'
import { GameFirebaseMetricFactory } from '#database/factories/game_firebase_metric_factory'
import { LucidImportService } from '#services/lucid_import_service'
import GameFirebaseMetric from '#models/game_firebase_metric'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()

    const records = await Promise.all(
      games.map((game) =>
        Promise.sequence(new Array(60).fill(0), async (_, index) => {
          return await GameFirebaseMetricFactory.merge({
            date: DateTime.now().minus({ day: index }),
            gameId: game.id,
          }).make()
        })
      )
    )

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameFirebaseMetric,
      records.flat(Number.MAX_SAFE_INTEGER) as GameFirebaseMetric[]
    )
  }
}
