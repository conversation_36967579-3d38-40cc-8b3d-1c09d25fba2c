import app from '@adonisjs/core/services/app'
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { faker } from '@faker-js/faker'

import GameReleaseProposal from '#models/game_release_proposal'
import { GameReleaseApprovalFactory } from '#database/factories/game_release_approval_factory'
import User from '#models/user'
import GameReleaseApproval from '#models/game_release_approval'
import { LucidImportService } from '#services/lucid_import_service'

export default class extends BaseSeeder {
  async run() {
    const proposals = await GameReleaseProposal.all()
    const reviewer = await User.all()
    const records = await Promise.sequence(proposals, (proposal) => {
      return Promise.sequence(new Array(10).fill(0), async () => {
        return await GameReleaseApprovalFactory.merge({ proposalId: proposal.id })
          .tap((approval) => {
            approval.reviewerId = faker.helpers.arrayElement(reviewer).id
          })
          .make()
      })
    })
    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      GameReleaseApproval,
      records.flat(Number.MAX_SAFE_INTEGER) as GameReleaseApproval[]
    )
  }
}
