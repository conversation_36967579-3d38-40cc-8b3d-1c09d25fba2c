import { BaseSeeder } from '@adonisjs/lucid/seeders'
import app from '@adonisjs/core/services/app'

import { AdAgencyFactory } from '#database/factories/ad_agency_factory'
import { AdNetworkInputType } from '#config/enums'
import { LucidImportService } from '#services/lucid_import_service'
import AdAgency from '#models/ad_agency'

export default class extends BaseSeeder {
  async run() {
    const manualAdNetworks = await AdAgencyFactory.merge({
      inputType: AdNetworkInputType.Manual,
    }).makeMany(2)

    const autoAdNetworks = await AdAgencyFactory.merge({
      inputType: AdNetworkInputType.Auto,
    }).makeMany(3)

    const allAdNetworks = [...manualAdNetworks, ...autoAdNetworks]

    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(AdAgency, allAdNetworks)
  }
}
