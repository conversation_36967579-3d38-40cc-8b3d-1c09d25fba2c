import { BaseSeeder } from '@adonisjs/lucid/seeders'

import { NetworkFactory } from '#database/factories/network_factory'
import { AdNetworkCategory } from '#config/enums'

export default class extends BaseSeeder {
  async run() {
    await Promise.sequence(Object.values(AdNetworkCategory), async (category) => {
      await NetworkFactory.merge({ category: category as AdNetworkCategory }).createMany(10)
    })
  }
}
