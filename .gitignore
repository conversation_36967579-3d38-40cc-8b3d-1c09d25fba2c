# Dependencies and AdonisJS build
node_modules
build
tmp

# Secrets
.env*
!.env.test
!.env*.example
.secret_npmrc

# Frontend assets compiled code
public/assets

# Build tools specific
npm-debug.log
yarn-error.log

# Editors specific
.fleet
.idea
.vscode/*
!.vscode/launch.json

# Platform specific
.DS_Store

# Docker
docker-compose.yml
compose.yml

# Log
log/*
!log/.gitkeep

vendor/

config/local*.yml

bastion/

.vite

*storybook.log

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

.cursorignore
