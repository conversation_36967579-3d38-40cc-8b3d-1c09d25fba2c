{"version": "0.2.0", "configurations": [{"type": "pwa-node", "request": "launch", "name": "Dev server", "program": "${workspaceFolder}/ace.js", "args": ["serve", "--hmr"], "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "sourceMaps": true, "cwd": "${workspaceFolder}"}, {"type": "pwa-node", "request": "launch", "name": "Unit Test", "program": "${workspaceFolder}/ace.js", "args": ["test", "unit", "--files", "${relativeFileDirname}/${fileBasename}"], "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "test"}}, {"type": "node", "request": "launch", "name": "Debug script", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "npx", "args": ["tsx", "${relativeFileDirname}/${fileBasename}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}"}]}