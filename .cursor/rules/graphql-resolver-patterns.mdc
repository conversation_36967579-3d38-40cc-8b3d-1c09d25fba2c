---
description: 
globs: 
alwaysApply: false
---
# GraphQL Resolver Patterns & Conventions

## 📁 Resolver File Structure

### Location
All GraphQL resolvers are located in `app/resolvers/` directory with snake_case naming:
- [app/resolvers/game_resolver.ts](mdc:app/resolvers/game_resolver.ts)
- [app/resolvers/user_resolver.ts](mdc:app/resolvers/user_resolver.ts)
- [app/resolvers/firebase_metric_resolver.ts](mdc:app/resolvers/firebase_metric_resolver.ts)
- [app/resolvers/release_metric_resolver.ts](mdc:app/resolvers/release_metric_resolver.ts)

### File Naming Convention
- Use **snake_case** for filenames: `game_resolver.ts`, `user_resolver.ts`
- Use **PascalCase** for class names: `GameResolver`, `UserResolver`
- Use **camelCase** for method names: `index`, `show`, `store`, `update`, `destroy`

## 🏗️ Resolver Class Structure

### Basic Resolver Template
```typescript
import { inject } from '@adonisjs/core'
import type { GraphQLHttpContext } from '@mirai-game-studio/adonis-sdk/types/graphql'
import vine from '@vinejs/vine'
import { z } from 'zod'
import { present } from '@mirai-game-studio/adonis-sdk/presenter'

import Model from '#models/model'
import ModelPolicy from '#policies/model_policy'

// Validation schemas
const indexValidator = z.object({
  where: z.object({
    // filter conditions
  }),
  offset: z.object({
    page: z.number().optional().default(1),
    perPage: z.number().optional().default(200),
  }),
})

export default class ModelResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer, auth },
      dataloaders,
    },
  }: GraphQLHttpContext<QueryModelArgs>) {
    // 1. Authorization
    await bouncer.with('ModelPolicy').authorize('index')
    
    // 2. Validation
    const validatedArgs = await indexValidator.parseAsync(args)
    
    // 3. Query building
    const query = Model.query()
      .where((q) => {
        // apply filters
      })
      .orderBy('created_at', 'desc')
    
    // 4. Policy scoping
    await bouncer.with('ModelQueryPolicy').authorize('scope', query, validatedArgs.where)
    
    // 5. Execute query
    const results = await query.paginate(validatedArgs.offset.page, validatedArgs.offset.perPage)
    
    // 6. Return serialized data
    return present(results)
  }
}
```

## 🎯 Common Resolver Methods

### Query Resolvers

#### Index (List) Resolver
```typescript
@inject()
async index({
  args,
  context: {
    http: { bouncer, auth },
    dataloaders,
  },
}: GraphQLHttpContext<QueryModelArgs>) {
  // Authorization
  await bouncer.with('ModelPolicy').authorize('index')
  
  // Validation
  const { where, offset } = await indexValidator.parseAsync(args)
  
  // Query building with filters
  const query = Model.query()
    .where((q) => {
      if (where.keyword) {
        q.whereILike('name', `%${where.keyword}%`)
      }
      if (where.status) {
        q.where('status', where.status)
      }
    })
    .orderBy('created_at', 'desc')
  
  // Policy scoping
  await bouncer.with('ModelQueryPolicy').authorize('scope', query, where)
  
  // Pagination
  const results = await query.paginate(offset.page, offset.perPage)
  
  return present(results)
}
```

#### Show (Single) Resolver
```typescript
@inject()
async show({
  args,
  context: {
    http: { bouncer },
  },
}: GraphQLHttpContext<QueryModelArgs>) {
  const model = await Model.findOrFail(args.id)
  await bouncer.with('ModelPolicy').authorize('show', model)
  return present(model)
}
```

### Mutation Resolvers

#### Store (Create) Resolver
```typescript
@inject()
async store({
  args,
  context: {
    http: { bouncer },
  },
}: GraphQLHttpContext<MutationCreateModelArgs>) {
  await bouncer.with('ModelPolicy').authorize('store')
  
  const { form } = await storeValidator.validate(args)
  
  const model = new Model().merge(form)
  await model.save()
  
  return present(model)
}
```

#### Update Resolver
```typescript
@inject()
async update({
  args,
  context: {
    http: { bouncer },
  },
}: GraphQLHttpContext<MutationUpdateModelArgs>) {
  await bouncer.with('ModelPolicy').authorize('update')
  
  const { where, form } = await updateValidator.validate(args)
  
  const model = await Model.findOrFail(where.id)
  model.merge(form)
  await model.save()
  
  return present(model)
}
```

#### Destroy Resolver
```typescript
@inject()
async destroy({
  args,
  context: {
    http: { bouncer },
  },
}: GraphQLHttpContext<MutationDeleteModelArgs>) {
  await bouncer.with('ModelPolicy').authorize('destroy')
  
  const { where } = await destroyValidator.validate(args)
  
  // Soft delete
  await Model.query().whereIn('id', where.ids).update({ deletedAt: new Date() })
  
  return { success: true }
}
```

### Field Resolvers (Relationships)
```typescript
@inject()
async relatedField({
  parent,
  context: {
    dataloaders,
    http: { bouncer },
  },
}: GraphQLHttpContext<{}, Model>) {
  // Use dataloader for N+1 prevention
  return await dataloaders.relatedField.load(parent.id)
}
```

## 🔧 Validation Patterns

### VineJS Validation (Recommended)
```typescript
const indexValidator = vine.compile(
  vine.object({
    where: vine.object({
      keyword: vine.string().trim().optional(),
      status: vine.enum(['active', 'inactive']).optional(),
      dateRange: vine.object({
        from: vine.date(),
        to: vine.date(),
      }).optional(),
    }),
    offset: vine.object({
      page: vine.number().min(1).optional().default(1),
      perPage: vine.number().min(1).max(1000).optional().default(200),
    }),
  })
)
```

### Zod Validation (Alternative)
```typescript
const indexValidator = z.object({
  where: z.object({
    keyword: z.string().trim().optional(),
    status: z.enum(['active', 'inactive']).optional(),
    dateRange: z.object({
      from: z.date(),
      to: z.date(),
    }).optional(),
  }),
  offset: z.object({
    page: z.number().int().min(1).optional().default(1),
    perPage: z.number().int().min(1).max(1000).optional().default(200),
  }),
})
```

## 🔒 Authorization Patterns

### Policy Authorization
```typescript
// Basic authorization
await bouncer.with('ModelPolicy').authorize('index')

// Authorization with resource
await bouncer.with('ModelPolicy').authorize('show', model)

// Multiple policy fallback
await bouncer
  .with('DashboardModelPolicy')
  .authorize('index')
  .catch(() => bouncer.with('PartnerModelPolicy').authorize('index'))
```

### Query Scoping
```typescript
// Apply policy scoping to query
await bouncer.with('ModelQueryPolicy').authorize('scope', query, where)

// Or use policy method directly
await modelPolicy.scope(auth.user!, query)
```

## 📊 Data Loading Patterns

### Dataloader Integration
```typescript
// In resolver method
async relatedData({
  parent,
  context: { dataloaders },
}: GraphQLHttpContext<{}, Model>) {
  return await dataloaders.relatedData.load(parent.id)
}

// Dataloader definition in app/dataloaders/
export default class RelatedDataLoader extends BaseLoader {
  async load(keys: string[]) {
    const data = await RelatedModel.query()
      .whereIn('parent_id', keys)
      .preload('nestedRelation')
    
    return keys.map(key => 
      data.filter(item => item.parentId === key)
    )
  }
}
```

### Eager Loading
```typescript
// Preload relationships in query
const models = await Model.query()
  .preload('relation1')
  .preload('relation2', (query) => {
    query.where('status', 'active')
  })
  .preload('relation3.relation4')
```

## 🎨 Response Patterns

### Using Presenter
```typescript
import { present } from '@mirai-game-studio/adonis-sdk/presenter'

// Present single model
return present(model)

// Present with serialization context
return present(model, serializationContext)

// Present paginated results
return present(paginatedResults)
```

### Custom Serialization
```typescript
// With serialization context
const serializationContext = await this.#buildSerializationContext(bouncer)
return present(model.withSerializationContext(serializationContext))

// Custom presenter method
private async #buildSerializationContext(bouncer: Bouncer) {
  return {
    includeSensitiveData: await bouncer.with('ModelPolicy').allows('viewSensitiveData'),
    includeInternalFields: await bouncer.with('ModelPolicy').allows('viewInternal'),
  }
}
```

## 🧪 Error Handling

### Validation Errors
```typescript
try {
  const validatedArgs = await validator.validate(args)
} catch (error) {
  if (error instanceof ValidationError) {
    throw new FieldValidationException('field', error.message)
  }
  throw error
}
```

### Authorization Errors
```typescript
try {
  await bouncer.with('ModelPolicy').authorize('action')
} catch (error) {
  if (error instanceof AuthorizationException) {
    throw new UnauthorizedException('Insufficient permissions')
  }
  throw error
}
```

### Custom Exceptions
```typescript
import FieldValidationException from '#exceptions/field_validation_exception'
import UnauthorizedException from '#exceptions/unauthorized_exception'

// Throw field-specific validation errors
throw new FieldValidationException('date', 'Date range must be less than 60 days')

// Throw authorization errors
throw new UnauthorizedException('Access denied')
```

## 📈 Performance Optimization

### Query Optimization
```typescript
// Use select to limit fields
const query = Model.query()
  .select('id', 'name', 'status', 'created_at')
  .where('status', 'active')

// Use indexes effectively
const query = Model.query()
  .where('status', 'active')
  .where('created_at', '>=', startDate)
  .orderBy('created_at', 'desc')

// Use pagination
const results = await query.paginate(page, perPage)
```

### Caching Strategies
```typescript
// Use dataloaders for caching
return await dataloaders.model.load(id)

// Cache expensive computations
const cachedResult = await cache.get(`model:${id}`)
if (!cachedResult) {
  const result = await expensiveOperation()
  await cache.set(`model:${id}`, result, '1h')
  return result
}
return cachedResult
```

## 🧪 Testing Resolvers

### Unit Test Pattern
```typescript
import { test } from '@japa/runner'
import { Database } from '@adonisjs/lucid/database'

test.group('ModelResolver', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
  })

  group.each.teardown(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('should return paginated models', async ({ client }) => {
    const user = await UserFactory.create()
    await ModelFactory.createMany(5)

    const response = await client
      .post('/graphql')
      .json({
        query: GET_MODELS,
        variables: { page: 1, perPage: 10 }
      })
      .loginAs(user)

    response.assertStatus(200)
    response.assertBodyContains({
      data: {
        models: {
          data: expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              name: expect.any(String)
            })
          ])
        }
      }
    })
  })
})
```

## 🎯 Best Practices

### Do's
- ✅ Always use `@inject()` decorator
- ✅ Always authorize with bouncer policies
- ✅ Validate input with VineJS or Zod
- ✅ Use dataloaders for relationships
- ✅ Return serialized data with `present()`
- ✅ Handle errors gracefully
- ✅ Use pagination for list queries
- ✅ Write tests for resolvers

### Don'ts
- ❌ Don't skip authorization
- ❌ Don't return raw model instances
- ❌ Don't use N+1 queries
- ❌ Don't ignore validation errors
- ❌ Don't hardcode business logic in resolvers
- ❌ Don't forget to handle edge cases

## 🔗 Related Files
- [graphql/context.ts](mdc:graphql/context.ts) - GraphQL context definition
- [app/dataloaders/](mdc:app/dataloaders) - Dataloader implementations
- [app/policies/](mdc:app/policies) - Authorization policies
- [app/models/](mdc:app/models) - Lucid ORM models
- [app/exceptions/](mdc:app/exceptions) - Custom exceptions
