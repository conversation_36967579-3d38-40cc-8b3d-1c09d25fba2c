---
description: 
globs: *.tsx,resources/js/next/**
alwaysApply: false
---
# Frontend Development Guidelines

## React + Inertia + Next.js Setup

### Project Structure
- **`resources/js/next/`** - Main React application
- **`resources/stories/`** - Storybook stories for component documentation
- **`resources/views/`** - Legacy Edge templates (avoid for new features)

### Component Organization
- **Built-in UI components**: `resources/js/next/components/ui/`
  - shadcn/ui components
  - Reusable form elements
  - Layout components
  
- **Custom domain components**: `resources/js/next/components/sdk/`
  - Game-specific components
  - Dashboard widgets
  - Business logic components

### Page Components
- Located in `resources/js/next/pages/`
- Follow Inertia.js patterns
- Use snake_case for file names: `release_metrics/index.tsx`
- Each page should be a complete route handler

### Key Files
- [resources/js/next/inertia.tsx](mdc:resources/js/next/inertia.tsx) - Inertia setup
- [resources/js/next/graphql.ts](mdc:resources/js/next/graphql.ts) - GraphQL client
- [resources/js/next/hooks/use-data-table.ts](mdc:resources/js/next/hooks/use-data-table.ts) - Data table hook

## Development Conventions

### Navigation
- **Use Inertia for navigation**: `router.visit('/dashboard')`
- **Never use Next.js router**: No `useRouter` or `Link` from Next.js
- **For external links**: Use `window.location.href` or `<a>` tags

### Styling
- **Tailwind CSS** for utility classes
- **shadcn/ui** for pre-built components
- **CSS modules** for complex custom styles
- Follow design system tokens

### Forms
- **react-hook-form** for form state management
- **zod** for form validation schemas
- Use controlled components pattern
- Implement proper error handling

### Data Fetching
- **GraphQL** for API communication
- Use generated types from GraphQL schema
- Implement proper loading states
- Handle errors gracefully

### Component Patterns

#### Data Table Component
```typescript
// Example from release_metrics table
export function ReleaseMetricTable({ collection, versions, slots }) {
  const columns = useMemo(() => [
    // Column definitions
  ], [versions, t]) // Include dependencies
  
  const { table } = useDataTable({
    columns,
    data: collection,
    pageCount: -1,
  })
  
  return (
    <TreeTable table={table} getNodeKeys={() => ['']} getNodeId={() => ''} sticky>
      <DataTableToolbar table={table} slots={slots} />
    </TreeTable>
  )
}
```

#### Form Component
```typescript
export function GameForm({ game, onSubmit }) {
  const form = useForm({
    resolver: zodResolver(gameSchema),
    defaultValues: game,
  })
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {/* Form fields */}
      </form>
    </Form>
  )
}
```

### Storybook Integration
- Every component should have a story
- Stories go in `resources/stories/components/`
- Use proper props and mock data
- Document component usage and variants

### Performance Considerations
- Use `useMemo` for expensive computations
- Implement proper dependency arrays
- Lazy load heavy components
- Optimize re-renders with React.memo when needed

### Error Handling
- Implement error boundaries for component trees
- Use try-catch for async operations
- Display user-friendly error messages
- Log errors for debugging

### Testing
- Unit tests for utility functions
- Component tests with React Testing Library
- Integration tests for complex workflows
- E2E tests for critical user journeys
