---
description: 
globs: 
alwaysApply: false
---
# AdonisJS Project Structure & Conventions

## Project Overview
This is a full-stack AdonisJS application with React frontend using Inertia.js. The project follows MVC pattern with TypeScript-first approach.

## Key Directories

### Backend (AdonisJS)
- **`app/`** - Main application logic
  - `controllers/` - HTTP request handlers organized by domain
  - `models/` - Lucid ORM models with relationships
  - `services/` - Business logic and external integrations
  - `resolvers/` - GraphQL resolvers
  - `policies/` - Authorization policies
  - `middleware/` - HTTP middleware
  - `validators/` - Input validation schemas
  - `exceptions/` - Custom exception handlers

- **`database/`** - Database management
  - `migrations/` - Database schema changes
  - `seeders/` - Sample data population
  - `factories/` - Model factories for testing

- **`config/`** - Application configuration files
- **`start/`** - Application bootstrap and startup files
- **`tests/`** - Test files (unit, functional, e2e)

### Frontend (React + Inertia)
- **`resources/js/next/`** - React application
  - `pages/` - Page components (Inertia pages)
  - `components/` - Reusable components
    - `ui/` - Built-in UI components (shadcn)
    - `sdk/` - Custom domain-specific components
  - `hooks/` - Custom React hooks
  - `utils/` - Utility functions

- **`resources/stories/`** - Storybook stories for components
- **`resources/views/`** - Edge templates (legacy)

## Key Files
- [adonisrc.ts](mdc:adonisrc.ts) - AdonisJS configuration
- [package.json](mdc:package.json) - Dependencies and scripts
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [resources/js/next/graphql.ts](mdc:resources/js/next/graphql.ts) - GraphQL client setup
- [start/routes.ts](mdc:start/routes.ts) - Application routes

## Conventions

### File Naming
- Use **snake_case** for filenames: `user_controller.ts`, `game_metric.ts`
- Use **camelCase** for code: `userController`, `gameMetric`
- Use **PascalCase** for classes: `UserController`, `GameMetric`

### Code Organization
- Controllers should be thin - delegate business logic to services
- Models should focus on relationships and basic CRUD
- Use dependency injection with `@inject()` decorator
- Policies handle authorization logic
- Validators ensure data integrity

### Frontend Conventions
- Use Inertia for navigation (not Next.js router)
- Built-in components go in `resources/js/next/components/ui/`
- Custom components go in `resources/js/next/components/sdk/`
- Each component should have a Storybook story
- Use Tailwind CSS + shadcn/ui for styling
- Use react-hook-form for form handling

### Database
- Always create migrations for schema changes
- Use seeders for sample data
- Use factories for test data
- Follow naming conventions: `table_names`, `column_names`

### Testing
- Unit tests in `tests/unit/`
- Functional tests in `tests/functional/`
- E2E tests in `tests/e2e/`
- Use factories and database transactions in tests

## Common Patterns

### Controller Pattern
```typescript
export default class UserController {
  @inject()
  async index({ bouncer }: HttpContext) {
    await bouncer.with('UserPolicy').authorize('index')
    // controller logic
  }
}
```

### Service Pattern
```typescript
export default class UserService {
  @inject()
  async createUser(data: CreateUserData) {
    // business logic
  }
}
```

### Model Pattern
```typescript
export default class User extends BaseModel {
  @column()
  declare email: string
  
  @hasMany(() => Post)
  declare posts: HasMany<typeof Post>
}
```

### Policy Pattern
```typescript
export default class UserPolicy extends BasePolicy {
  async index(user: User) {
    return user.role === 'admin'
  }
}
```
