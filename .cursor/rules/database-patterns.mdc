---
description: 
globs: database/**
alwaysApply: false
---
# Database Patterns & Conventions

## Lucid ORM Setup

### Key Files
- [database/migrations/](mdc:database/migrations) - Database schema migrations
- [database/seeders/](mdc:database/seeders) - Sample data population
- [database/factories/](mdc:database/factories) - Model factories for testing
- [app/models/](mdc:app/models) - Lucid ORM models


## Model Patterns

### Basic Model Structure
```typescript
import { BaseModel, column, hasMany, belongsTo } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class Game extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare status: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @hasMany(() => ReleaseMetric)
  declare releaseMetrics: HasMany<typeof ReleaseMetric>
}
```

### Relationship Patterns
```typescript
// One-to-Many
@hasMany(() => Post)
declare posts: HasMany<typeof Post>

// Many-to-One
@belongsTo(() => User)
declare user: BelongsTo<typeof User>

// Many-to-Many
@manyToMany(() => Tag)
declare tags: ManyToMany<typeof Tag>

// Has-One
@hasOne(() => Profile)
declare profile: HasOne<typeof Profile>
```

### Model Extensions
Located in `app/models/extensions/`:
- [app/models/extensions/acl.ts](mdc:app/models/extensions/acl.ts) - Access control
- [app/models/extensions/aggregation.ts](mdc:app/models/extensions/aggregation.ts) - Aggregation methods
- [app/models/extensions/filter.ts](mdc:app/models/extensions/filter.ts) - Filtering scopes

## Migration Patterns

### Migration Structure
```typescript
import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'games'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('name').notNullable()
      table.string('status').defaultTo('draft')
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
```

### Common Migration Patterns
```typescript
// Foreign key relationships
table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')

// Indexes for performance
table.index(['status', 'created_at'])

// JSON columns
table.json('metadata').nullable()

// Enum columns
table.enum('status', ['draft', 'published', 'archived']).defaultTo('draft')

// Timestamps
table.timestamp('created_at').notNullable()
table.timestamp('updated_at').notNullable()
```

## Seeder Patterns

### Basic Seeder
```typescript
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import Game from '#models/game'

export default class extends BaseSeeder {
  async run() {
    await Game.createMany([
      { name: 'Game 1', status: 'published' },
      { name: 'Game 2', status: 'draft' },
    ])
  }
}
```

### Complex Seeder with Relationships
```typescript
import { BaseSeeder } from '@adonisjs/lucid/seeders'
import Game from '#models/game'
import ReleaseMetric from '#models/release_metric'
import { ReleaseMetricFactory } from '#database/factories/release_metric_factory'

export default class extends BaseSeeder {
  async run() {
    const games = await Game.all()
    
    // Create related data using factories
    const records = await Promise.all(
      games.map(async (game) =>
        Promise.sequence(new Array(30).fill(0), (_, index) =>
          ReleaseMetricFactory.merge({
            gameId: game.id,
            date: DateTime.now().minus({ days: index }),
          }).make()
        )
      )
    )

    // Bulk insert for performance
    const lucidImportService = await app.container.make(LucidImportService)
    await lucidImportService.import(
      ReleaseMetric,
      records.flat(Number.MAX_SAFE_INTEGER),
      { chunkSize: 2_000 }
    )
  }
}
```

## Factory Patterns

### Basic Factory
```typescript
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { Factory } from '@adonisjs/lucid/factories'

export default class GameFactory extends Factory {
  protected model = () => Game

  definition() {
    return {
      name: this.faker.company.name(),
      status: this.faker.helpers.arrayElement(['draft', 'published', 'archived']),
    }
  }
}
```

### Factory with Relationships
```typescript
export default class ReleaseMetricFactory extends Factory {
  protected model = () => ReleaseMetric

  definition() {
    return {
      gameId: () => GameFactory.create(),
      date: this.faker.date.recent(),
      activeUserCount: this.faker.number.int({ min: 100, max: 10000 }),
      sessionCount: this.faker.number.int({ min: 500, max: 50000 }),
    }
  }

  // Custom states
  published() {
    return this.merge({ status: 'published' })
  }

  withHighUsage() {
    return this.merge({
      activeUserCount: this.faker.number.int({ min: 5000, max: 50000 }),
    })
  }
}
```

## Query Patterns

### Basic Queries
```typescript
// Find by ID
const game = await Game.findOrFail(id)

// Find with conditions
const publishedGames = await Game.query().where('status', 'published')

// Find with relationships
const gameWithMetrics = await Game.query()
  .where('id', gameId)
  .preload('releaseMetrics')
  .firstOrFail()
```

### Complex Queries with Scopes
```typescript
// Using model scopes
const metrics = await ReleaseMetric.query()
  .withScopes((s) => {
    s.filter({ date: { operator: 'between', values: [startDate, endDate] } })
    s.filter({ gameId: { operator: 'eq', values: [gameId] } })
  })
  .preload('game')
  .orderBy('date', 'desc')
```

### Aggregation Queries
```typescript
// Group by with aggregation
const dailyMetrics = await ReleaseMetric.query()
  .select('date')
  .selectRaw('SUM(active_user_count) as total_dau')
  .selectRaw('AVG(session_count) as avg_sessions')
  .where('game_id', gameId)
  .groupBy('date')
  .orderBy('date', 'desc')
```

### Transaction Patterns
```typescript
import db from '@adonisjs/lucid/services/db'

// Basic transaction
await db.transaction(async (trx) => {
  const game = await Game.create({ name: 'New Game' }, { client: trx })
  await ReleaseMetric.createMany(metrics, { client: trx })
})

// Transaction with error handling
try {
  await db.transaction(async (trx) => {
    // Database operations
  })
} catch (error) {
  // Handle transaction failure
  console.error('Transaction failed:', error)
}
```

## Performance Optimization

### Eager Loading
```typescript
// Avoid N+1 queries
const games = await Game.query()
  .preload('releaseMetrics', (query) => {
    query.where('date', '>=', startDate)
  })
  .preload('user')
```

### Batch Operations
```typescript
// Bulk insert
await ReleaseMetric.createMany(metrics)

// Bulk update
await ReleaseMetric.query()
  .whereIn('id', ids)
  .update({ status: 'archived' })

// Bulk delete
await ReleaseMetric.query()
  .where('date', '<', cutoffDate)
  .delete()
```

### Indexing Strategy
```typescript
// Add indexes for frequently queried columns
table.index(['game_id', 'date'])
table.index(['status', 'created_at'])
table.index(['user_id', 'status'])
```

## Testing Database

### Test Setup
```typescript
import { test } from '@japa/runner'
import { Database } from '@adonisjs/lucid/database'

test.group('Games', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
  })

  group.each.teardown(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('should create game', async () => {
    const game = await GameFactory.create()
    expect(game.id).toBeDefined()
  })
})
```

### Factory in Tests
```typescript
test('should load game with metrics', async () => {
  const game = await GameFactory.create()
  await ReleaseMetricFactory.merge({ gameId: game.id }).createMany(5)

  const loadedGame = await Game.query()
    .where('id', game.id)
    .preload('releaseMetrics')
    .firstOrFail()

  expect(loadedGame.releaseMetrics).toHaveLength(5)
})
```
