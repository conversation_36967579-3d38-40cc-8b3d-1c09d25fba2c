---
description: 
globs: *.tsx,*.ts,app/resolvers/**,app/resolvers/*
alwaysApply: false
---
# GraphQL Development Patterns

## Project GraphQL Setup

### Key Files
- [graphql/adonis.ts](mdc:graphql/adonis.ts) - AdonisJS GraphQL configuration
- [graphql/codegen.ts](mdc:graphql/codegen.ts) - Code generation setup
- [graphql/context.ts](mdc:graphql/context.ts) - GraphQL context definition
- [resources/js/next/graphql.ts](mdc:resources/js/next/graphql.ts) - Frontend GraphQL client

### Backend Resolvers
Located in `app/resolvers/` with domain-specific organization:

#### Resolver Structure
```typescript
export default class FirebaseMetricResolver {
  @inject()
  async index({
    args,
    context: {
      http: { bouncer, auth },
      dataloaders,
    },
  }: GraphQLHttpContext<QueryFirebaseMetricsArgs>) {
    // Authorization
    await bouncer.with('dash.game.fb_experiment').authorize('index', game)
    
    // Validation
    const validatedArgs = await validator.validate(args)
    
    // Business logic
    const metrics = await query.execute()
    
    // Return with proper serialization
    return {
      collection: metrics.map(m => m.serialize()),
      viewPreset: viewPreset.serialize(),
    }
  }
}
```

#### Common Patterns
- Use `@inject()` for dependency injection
- Always authorize with bouncer policies
- Validate input with VineJS schemas
- Use dataloaders for N+1 query prevention
- Return serialized data with proper context

### Frontend GraphQL Usage

#### Query Pattern
```typescript
import { useQuery } from '@apollo/client'
import { gql } from 'graphql-tag'

const GET_GAME_METRICS = gql`
  query GetGameMetrics($gameId: ID!, $dateRange: DateRangeInput!) {
    gameMetrics(gameId: $gameId, dateRange: $dateRange) {
      collection {
        id
        date
        activeUserCount
        sessionCount
      }
    }
  }
`

export function GameMetricsPage() {
  const { data, loading, error } = useQuery(GET_GAME_METRICS, {
    variables: { gameId: '123', dateRange: { from: '2024-01-01', to: '2024-01-31' } }
  })
  
  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />
  
  return <MetricsTable data={data.gameMetrics.collection} />
}
```

#### Mutation Pattern
```typescript
const UPDATE_GAME = gql`
  mutation UpdateGame($id: ID!, $input: UpdateGameInput!) {
    updateGame(id: $id, input: $input) {
      game {
        id
        name
        status
      }
      errors {
        field
        message
      }
    }
  }
`

export function GameForm({ game }) {
  const [updateGame, { loading }] = useMutation(UPDATE_GAME)
  
  const handleSubmit = async (formData) => {
    try {
      const result = await updateGame({
        variables: { id: game.id, input: formData }
      })
      
      if (result.data.updateGame.errors.length > 0) {
        // Handle validation errors
      } else {
        // Success - redirect or show message
        router.visit('/games')
      }
    } catch (error) {
      // Handle network/other errors
    }
  }
}
```

### Data Loading Patterns

#### Dataloader Integration
```typescript
// In resolver
async ads({ parent, context: { dataloaders } }) {
  return await dataloaders.firebaseAdMetric.load(parent)
}

// In dataloader definition
export default class FirebaseAdMetricLoader extends BaseLoader {
  async load(keys: string[]) {
    // Batch load related data
    const metrics = await FirebaseMetric.query()
      .whereIn('id', keys)
      .preload('ads')
    
    return keys.map(key => 
      metrics.find(m => m.id === key)?.ads || []
    )
  }
}
```

### Error Handling

#### Backend Error Patterns
```typescript
// Custom exceptions
throw new FieldValidationException('date', 'Date range must be less than 60 days')

// Authorization errors
await bouncer.with('GamePolicy').authorize('update', game)

// Validation errors
const validated = await validator.validate(args)
```

#### Frontend Error Handling
```typescript
function useGraphQLError() {
  return useCallback((error: ApolloError) => {
    if (error.graphQLErrors?.length > 0) {
      // Handle GraphQL errors (validation, auth, etc.)
      const gqlError = error.graphQLErrors[0]
      if (gqlError.extensions?.code === 'UNAUTHORIZED') {
        router.visit('/login')
      }
    } else if (error.networkError) {
      // Handle network errors
      toast.error('Network error. Please try again.')
    }
  }, [])
}
```

### Type Safety

#### Generated Types
```typescript
import { 
  QueryGameMetricsArgs, 
  GameMetricIndex_GameMetricAttributesFragment 
} from '@/graphql'

// Use generated types for type safety
function GameMetricsTable({ 
  collection 
}: { 
  collection: GameMetricIndex_GameMetricAttributesFragment[] 
}) {
  // TypeScript will ensure type safety
}
```

### Performance Optimization

#### Query Optimization
- Use field selection to limit data transfer
- Implement pagination for large datasets
- Use dataloaders to prevent N+1 queries
- Cache frequently accessed data

#### Frontend Optimization
- Use `useMemo` for expensive computations
- Implement proper loading states
- Use optimistic updates for better UX
- Cache query results appropriately

### Testing GraphQL

#### Backend Testing
```typescript
test('should return game metrics', async ({ client }) => {
  const response = await client
    .post('/graphql')
    .json({
      query: GET_GAME_METRICS,
      variables: { gameId: '123' }
    })
    .loginAs(user)
  
  response.assertStatus(200)
  response.assertBodyContains({
    data: {
      gameMetrics: {
        collection: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            activeUserCount: expect.any(Number)
          })
        ])
      }
    }
  })
})
```

#### Frontend Testing
```typescript
import { MockedProvider } from '@apollo/client/testing'

test('renders game metrics', async () => {
  const mocks = [
    {
      request: {
        query: GET_GAME_METRICS,
        variables: { gameId: '123' }
      },
      result: {
        data: {
          gameMetrics: {
            collection: [
              { id: '1', activeUserCount: 100 }
            ]
          }
        }
      }
    }
  ]
  
  render(
    <MockedProvider mocks={mocks}>
      <GameMetricsPage />
    </MockedProvider>
  )
  
  await waitFor(() => {
    expect(screen.getByText('100')).toBeInTheDocument()
  })
})
```
