---
description: 
globs: 
alwaysApply: true
---
# Memory Bank - Studio Toolkit Project

## 🏴‍☠️ Project Overview
**Mirai Studio Toolkit** - Full-stack gaming analytics platform built with AdonisJS + React + Inertia.js

### Core Architecture
- **Backend**: AdonisJS v6 + TypeScript + Lucid ORM + GraphQL
- **Frontend**: React 19 + Inertia.js + Tailwind CSS + shadcn/ui
- **Database**: PostgreSQL (multi-database: main + data warehouse + snapshot)
- **Package Manager**: pnpm with private GitHub packages

## 📁 Project Structure Quick Reference

### Backend Structure (`app/`)
```
app/
├── controllers/     # HTTP request handlers (snake_case)
├── models/         # Lucid ORM models with relationships
├── resolvers/      # GraphQL resolvers with dataloaders
├── services/       # Business logic and external integrations
├── policies/       # Authorization policies (bouncer)
├── dataloaders/    # GraphQL N+1 query prevention
├── validators/     # Input validation (VineJS)
├── middleware/     # HTTP middleware
├── exceptions/     # Custom exception handlers
├── configmaps/     # Configuration management
├── jobs/          # Background jobs and queues
└── utils/         # Utility functions
```

### Frontend Structure (`resources/js/next/`)
```
resources/js/next/
├── pages/         # Inertia page components (snake_case)
├── components/    # React components
│   ├── ui/       # shadcn/ui built-in components
│   ├── sdk/      # Custom domain-specific components
│   └── data-table/ # Table components
├── hooks/         # Custom React hooks
├── stores/        # State management (Zustand)
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
└── config/        # Frontend configuration
```

### Database Structure (`database/`)
```
database/
├── migrations/    # Schema changes (timestamped)
├── seeders/       # Sample data population
├── factories/     # Test data factories
└── utils/         # Database utilities
```

## 🎯 Key Conventions & Patterns

### File Naming Conventions
- **Filenames**: `snake_case` (e.g., `user_controller.ts`, `game_metric.ts`)
- **Code**: `camelCase` (e.g., `userController`, `gameMetric`)
- **Classes**: `PascalCase` (e.g., `UserController`, `GameMetric`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `MAX_FILE_SIZE`)

### Backend Patterns

#### Controller Pattern
```typescript
export default class UserController {
  @inject()
  async index({ bouncer, auth }: HttpContext) {
    await bouncer.with('UserPolicy').authorize('index')
    // controller logic
    return view.render('users/index', { users })
  }
}
```

#### Service Pattern
```typescript
export default class UserService {
  @inject()
  async createUser(data: CreateUserData) {
    // business logic
    return await User.create(data)
  }
}
```

#### Model Pattern
```typescript
export default class User extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare email: string

  @hasMany(() => Post)
  declare posts: HasMany<typeof Post>
}
```

#### Policy Pattern
```typescript
export default class UserPolicy extends BasePolicy {
  async index(user: User) {
    return user.role === 'admin'
  }
}
```

### Frontend Patterns

#### Page Component Pattern
```typescript
export default function UsersPage({ users }: { users: User[] }) {
  return (
    <div>
      <h1>Users</h1>
      <UserTable users={users} />
    </div>
  )
}
```

#### Data Table Pattern
```typescript
export function UserTable({ users }: { users: User[] }) {
  const columns = useMemo(() => [
    // column definitions
  ], [t]) // Include dependencies

  const { table } = useDataTable({
    columns,
    data: users,
    pageCount: -1,
  })

  return (
    <TreeTable table={table} getNodeKeys={() => ['']} getNodeId={() => ''} sticky>
      <DataTableToolbar table={table} slots={slots} />
    </TreeTable>
  )
}
```

#### Form Pattern
```typescript
export function UserForm({ user, onSubmit }: UserFormProps) {
  const form = useForm({
    resolver: zodResolver(userSchema),
    defaultValues: user,
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {/* form fields */}
      </form>
    </Form>
  )
}
```

### GraphQL Patterns

#### Resolver Pattern
```typescript
export default class UserResolver {
  @inject()
  async index({
    args,
    context: { http: { bouncer }, dataloaders },
  }: GraphQLHttpContext<QueryUsersArgs>) {
    await bouncer.with('UserPolicy').authorize('index')
    const validatedArgs = await validator.validate(args)
    
    const users = await User.query().withScopes((s) => {
      s.filter(validatedArgs.where)
    })

    return {
      collection: users.map(u => u.serialize()),
    }
  }
}
```

#### Dataloader Pattern
```typescript
export default class UserLoader extends BaseLoader {
  async load(keys: string[]) {
    const users = await User.query().whereIn('id', keys)
    return keys.map(key => users.find(u => u.id === key))
  }
}
```

## 🔧 Development Workflow

### Setup Commands
```bash
# Install dependencies
pnpm install

# Database setup
node ace migration:run
node ace migration:run -c dataWarehouse
node ace migration:run -c dataWarehouseSnapshot

# Seed data
node ace db:seed

# Development server
npm run dev

# Storybook
npm run storybook
```

### Code Generation
```bash
# Create controller
node ace make:controller users

# Create model
node ace make:model user

# Create migration
node ace make:migration create_users_table

# Create seeder
node ace make:seeder users

# Create factory
node ace make:factory user
```

### Testing
```bash
# Run all tests
npm run test

# Run specific test suite
node ace test --suite=unit
node ace test --suite=functional
```

## 🎨 UI/UX Guidelines

### Component Organization
- **Built-in UI**: `resources/js/next/components/ui/` (shadcn/ui)
- **Custom Components**: `resources/js/next/components/sdk/` (domain-specific)
- **Data Tables**: `resources/js/next/components/data-table/`
- **Stories**: `resources/stories/components/` (Storybook)

### Styling
- **Primary**: Tailwind CSS utility classes
- **Components**: shadcn/ui for consistent design
- **Custom CSS**: CSS modules for complex styles
- **Themes**: next-themes for dark/light mode

### Navigation
- **Use Inertia**: `router.visit('/dashboard')`
- **Never Next.js router**: No `useRouter` or `Link`
- **External links**: `window.location.href` or `<a>` tags

## 🗄️ Database Patterns

### Migration Structure
```typescript
export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('email').notNullable().unique()
      table.string('status').defaultTo('active')
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()
    })
  }
}
```

### Seeder Pattern
```typescript
export default class extends BaseSeeder {
  async run() {
    await User.createMany([
      { email: '<EMAIL>', role: 'admin' },
      { email: '<EMAIL>', role: 'user' },
    ])
  }
}
```

### Factory Pattern
```typescript
export default class UserFactory extends Factory {
  protected model = () => User

  definition() {
    return {
      email: this.faker.internet.email(),
      role: this.faker.helpers.arrayElement(['admin', 'user']),
    }
  }
}
```

## 🔒 Security & Authorization

### Authentication
- **Session-based**: AdonisJS session middleware
- **API tokens**: For external integrations
- **OAuth**: Google, GitHub integration

### Authorization
- **Policies**: Bouncer policies for resource access
- **ACL**: Role-based access control
- **Middleware**: Route-level protection

### Validation
- **Input**: VineJS schemas
- **Output**: Model serialization
- **Errors**: Custom exception handlers

## 📊 Performance Optimization

### Backend
- **Dataloaders**: Prevent N+1 GraphQL queries
- **Eager Loading**: Preload relationships
- **Caching**: Redis for sessions and data
- **Indexing**: Database indexes for frequent queries

### Frontend
- **Memoization**: useMemo for expensive computations
- **Lazy Loading**: Code splitting and dynamic imports
- **Optimistic Updates**: Better UX for mutations
- **Virtual Scrolling**: For large data tables

## 🧪 Testing Strategy

### Test Types
- **Unit**: Individual functions and methods
- **Functional**: API endpoints and GraphQL resolvers
- **E2E**: Complete user workflows
- **Component**: React component testing

### Test Patterns
```typescript
test.group('Users', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
  })

  group.each.teardown(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('should create user', async () => {
    const user = await UserFactory.create()
    expect(user.id).toBeDefined()
  })
})
```

## 🚀 Deployment & DevOps

### Environment Setup
- **Development**: Local PostgreSQL + Redis
- **Staging**: Cloud database with test data
- **Production**: Managed database services

### Configuration
- **Environment**: `.env` files for secrets
- **Config Maps**: `config/` directory for app settings
- **Database**: Multiple connection configs

### Monitoring
- **Logging**: Pino logger with structured logs
- **Error Tracking**: Sentry integration
- **Health Checks**: Readiness and liveness endpoints

## 📚 Key Dependencies

### Backend
- `@adonisjs/core`: Framework core
- `@adonisjs/lucid`: ORM and database
- `@adonisjs/auth`: Authentication
- `@adonisjs/bouncer`: Authorization
- `@adonisjs/inertia`: Frontend integration
- `@apollo/server`: GraphQL server

### Frontend
- `react`: UI library
- `@inertiajs/react`: Inertia integration
- `@tanstack/react-table`: Data tables
- `@apollo/client`: GraphQL client
- `tailwindcss`: Styling
- `shadcn/ui`: Component library

### Development
- `typescript`: Type safety
- `eslint`: Code linting
- `prettier`: Code formatting
- `@japa/runner`: Testing framework
- `storybook`: Component documentation

## 🎯 Common Tasks Quick Reference

### Creating New Feature
1. Create migration: `node ace make:migration create_feature_table`
2. Create model: `node ace make:model feature`
3. Create controller: `node ace make:controller features`
4. Create policy: `node ace make:policy feature`
5. Create resolver: `node ace make:resolver feature`
6. Create frontend page: `resources/js/next/pages/features/index.tsx`
7. Create component: `resources/js/next/components/sdk/feature_table.tsx`
8. Create story: `resources/stories/components/feature_table.stories.tsx`

### Database Operations
- **Migration**: `node ace migration:run`
- **Rollback**: `node ace migration:rollback`
- **Seed**: `node ace db:seed`
- **Factory**: `UserFactory.create()` or `UserFactory.createMany()`

### GraphQL Development
- **Generate types**: `npm run graphgen`
- **Test query**: Use GraphiQL at `/graphiql`
- **Dataloader**: Create in `app/dataloaders/`
- **Resolver**: Create in `app/resolvers/`

### Frontend Development
- **New page**: Create in `resources/js/next/pages/`
- **New component**: Create in `resources/js/next/components/sdk/`
- **Storybook**: `npm run storybook`
- **Build**: `npm run build`

This memory bank serves as the ultimate reference for maintaining consistency and following established patterns across the entire codebase! 🚢⚓
