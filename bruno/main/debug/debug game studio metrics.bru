meta {
  name: debug game studio metrics
  type: graphql
  seq: 1
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

headers {
  Cookie: adonis-session=s%3AeyJtZXNzYWdlIjoidHphN3R2cmt4NXBkYzZ6aGwxZXVmc2p3IiwicHVycG9zZSI6ImFkb25pcy1zZXNzaW9uIn0.PbM5Oh3JhRFSxNuBKwG01khrVPPRSOkSDBwchpN07gU; tza7tvrkx5pdc6zhl1eufsjw=e%3AyJexAUOCV-6GP8G-qVDCc0E3Sl9sbvdGqSNtE1rVfpYfzJNpykmL0vZRVxsvJUkm1mr_5ql9cPrWIAets6tWHstJpr2bzfzc9HckJT4hjdDve_qwt9hFoal3gPxAxtlseTbz6QsMasVVfAl_n4Nt84IKZw30_AvxsM_Udk6Vu7AV2L9NOWcs6gx_8Z_CgosFu6F_xGyWAv2E7k1rcu4yqewNjZZXAeKa6LFJ8H4OJXE.d3VnUkhzOUh2a3c0UDQ3QQ.FWFESMuMFwONHsg8b_H7oA6Mk87LP9Kh8nYUE-1COjA
  Host: toolkit.miraistudio.games
}

auth:basic {
  username: mirai
  password: MiraiStudio
}

body:graphql {
  query GameStudioMetricsIndex_GetMetrics(
    $studioId: Int!
    $dateFrom: Date!
    $dateTo: Date!
  ) {
    metrics: gameStudioMetrics(
      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        ...GameStudioMetricAttributes
        __typename
      }
      __typename
    }
  }
  
  fragment GameStudioMetricAttributes on GameStudioMetric {
    gameId
    totalInstalls
    mmpCostAmount
    totalAgencyCost
    revenue
    profit
    __typename
  }
  
}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
