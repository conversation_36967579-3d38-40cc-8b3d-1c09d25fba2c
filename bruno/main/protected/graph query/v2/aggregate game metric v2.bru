meta {
  name: aggregate game metric v2
  type: graphql
  seq: 2
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($gameId: ID!) {
    v2 {
      aggregateGameMetrics(where: { gameId: $gameId }) {
        paidInstalls
        organicInstalls
        organicPercentage
        totalInstalls
        cost
        cpi
        roas
        revenue
        profit
        retentionRateDay1
        retentionRateDay3
        retentionRateDay7
        sessions
        playtime
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "16bf20f6-7bf0-441f-908b-223dae8100c4"
  }
}
