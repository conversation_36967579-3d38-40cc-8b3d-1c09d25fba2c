meta {
  name: game metric v2
  type: graphql
  seq: 1
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($gameId: ID!) {
    v2 {
      gameMetrics(
        where: { gameId: $gameId }
        offset: { page: 1, perPage: 100 }
        source: SNAPSHOT
      ) {
        collection {
          date
          gameId
          paidInstalls
          organicInstalls
          organicPercentage
          totalInstalls
          cost
          cpi
          roas
          revenue
          profit
          retentionRateDay1
          retentionRateDay3
          retentionRateDay7
          sessions
          playtime
          adPerformances {
            ecpm
            arpDau
            impsDau
          }
        }
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "4e95d831-6122-4505-b53e-ab6ef8114791"
  }
}
