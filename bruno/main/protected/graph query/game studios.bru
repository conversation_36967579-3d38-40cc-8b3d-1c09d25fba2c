meta {
  name: game studios
  type: graphql
  seq: 19
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    gameStudios {
      collection {
        id
        name
      }
    }
  }
  
}

body:graphql:vars {
  {
    "dateFrom": "2024-12-01",
    "dateTo": "2025-02-12",
    "gameId": "53cf5763-12cc-44b6-bb0b-ec85e610084d"
  }
}
