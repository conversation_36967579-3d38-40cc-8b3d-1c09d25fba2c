meta {
  name: firebase experiments
  type: graphql
  seq: 25
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    firebaseExperiments(
      where: {
        gameId: "cfb391b1-2f87-4cc7-bfc8-bce2f4e15493"
      }
    ) {
      collection {
        name
      }
    }
  }
  
}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
