meta {
  name: admob metrics
  type: graphql
  seq: 24
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    admobMetrics(
      where: {
        gameId: "ca-app-pub-9561563625318152~1732726019"
        dateFrom: "2025-04-16"
        dateTo: "2025-04-16"
        version: "3.0.14"
      }
    ) {
      mediation
      network
    }
  }
  
}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
