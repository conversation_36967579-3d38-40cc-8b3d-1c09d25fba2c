meta {
  name: ad metric view preset
  type: graphql
  seq: 6
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    viewPresets(where: { pageId: "dashboard.games.campaign_metrics.index" }) {
      collection {
        id
        name
        attributes {
          name
          isCohort
        }
      }
    }
  }
  
}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
