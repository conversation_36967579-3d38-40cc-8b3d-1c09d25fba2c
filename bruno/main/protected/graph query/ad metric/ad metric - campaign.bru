meta {
  name: ad metric / campaign
  type: graphql
  seq: 2
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    adMetrics(
      where: {
        gameId: "23d2e3eb-68f6-4ce6-a518-08aa2cff08f7"
        date: { operator: BETWEEN, values: ["2025-03-01", "2025-03-27"] }
      }
      group: { fields: ["campaignId"] }
      preset: { viewPresetId: 27 }
    ) {
      collection {
        date
        adId
        groupId
        campaignId
        countryCode
        adRevGrossAmount
        adCostNonTaxAmount
        impressionCount
        clickCount
        installCount
        agencyId
        adRevNthDayGrossAmounts
        cpi
        ctr
        cvr
        cpc
        ipm
        roas
      }
  
      ads {
        id
        name
      }
  
      adGroups {
        id
        name
      }
  
      campaigns {
        id
        name
      }
  
      agencies {
        id
        name
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "com.mirai.funnyjoke.pranksounds.spranky"
  }
}
