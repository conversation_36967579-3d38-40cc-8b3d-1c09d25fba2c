meta {
  name: ad metric / date-campaign
  type: graphql
  seq: 1
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    adMetrics(
      where: {
        gameId: "cfb391b1-2f87-4cc7-bfc8-bce2f4e15493"
        date: { operator: BETWEEN, values: ["2025-03-24", "2025-03-31"] }
        cpi: { operator: GTE, values: [0.01] }
      }
      group: { fields: ["date", "campaignId"] }
    ) {
      collection {
        date
        adId
        groupId
        campaignId
        countryCode
        adRevGrossAmount
        cvr
      }
  
      ads {
        id
        name
      }
  
      adGroups {
        id
        name
      }
  
      campaigns {
        id
        name
      }
  
      agencies {
        id
        name
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "cfb391b1-2f87-4cc7-bfc8-bce2f4e15493"
  }
}
