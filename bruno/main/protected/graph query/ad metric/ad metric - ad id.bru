meta {
  name: ad metric / ad id
  type: graphql
  seq: 4
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    adMetrics(
      where: {
        gameId: "17c56221-82bb-473d-8ca1-2c358156252a"
        date: { operator: BETWEEN, values: ["2025-03-01", "2025-03-27"] }
        campaignId: {
          operator: EQ
          values: ["2fea1961-6d6f-4ae6-840e-8583fb90b27a"]
        }
        groupId: {
          operator: EQ
          values: ["74c56c86-b60e-4a67-814d-8deb0e00e36c"]
        }
      }
      group: { fields: ["campaignId", "groupId", "adId"] }
      preset: { viewPresetId: 20 }
    ) {
      collection {
        date
        adId
        groupId
        campaignId
        countryCode
        adRevGrossAmount
        adCostNonTaxAmount
        impressionCount
        clickCount
        installCount
        agencyId
        adRevNthDayGrossAmounts
        cpi
        ctr
        cvr
        cpc
        ipm
        roas
      }
  
      ads {
        id
        name
      }
  
      adGroups {
        id
        name
      }
  
      campaigns {
        id
        name
      }
  
      agencies {
        id
        name
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "com.mirai.funnyjoke.pranksounds.spranky"
  }
}
