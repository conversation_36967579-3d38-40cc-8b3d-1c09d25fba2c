meta {
  name: budget requests
  type: graphql
  seq: 22
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    budgetRequests(
      where: { createdAtTo: "2025-02-25", createdAtFrom: "2025-01-01" }
    ) {
      _debug
    }
  }
  
}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
