meta {
  name: firebase metrics
  type: graphql
  seq: 28
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    firebaseMetrics(
      where: {
        gameId: "cfb391b1-2f87-4cc7-bfc8-bce2f4e15493"
        experiment: { operator: EQ, values: ["firebase_exp_0"] }
        date: { operator: BETWEEN, values: ["2025-05-14", "2025-05-19"] }
      }
      group:{
        fields: ["variantId"]
      }
    ) {
      collection {
        sessionCount
        activeUserCount
        variantId
      }
    }
  }
  
}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
