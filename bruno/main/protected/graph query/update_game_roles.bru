meta {
  name: update game roles
  type: graphql
  seq: 1
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  mutation UpdateGameRoles($where: UpdateGameRolesWhere!, $form: UpdateGameRolesForm!) {
    updateGameRoles(where: $where, form: $form) {
      id
      roleId
      storeId
      users {
        email
      }
    }
  }
}

body:graphql:vars {
  {
    "where": {
      "gameId": "game-id-here"
    },
    "form": {
      "_csrf": "{{CSRF_TOKEN}}",
      "roles": [
        {
          "id": "outside-manager",
          "users": ["<EMAIL>"]
        },
        {
          "id": "creative",
          "users": []
        }
      ]
    }
  }
}
