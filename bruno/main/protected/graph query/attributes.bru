meta {
  name: attributes
  type: graphql
  seq: 3
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($gameId: ID!) {
    attributes(where: { gameId: $gameId, modelName: "GameMetric" }) {
      collection {
        name
        displayName
        permission
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "16bf20f6-7bf0-441f-908b-223dae8100c4"
  }
}
