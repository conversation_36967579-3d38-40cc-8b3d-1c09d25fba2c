meta {
  name: game studio metrics
  type: graphql
  seq: 20
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query GameStudioMetricsIndex_GetMetrics(
    $studioId: Int!
    $dateFrom: Date!
    $dateTo: Date!
  ) {
    metrics: gameStudioMetrics(
      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        ...GameStudioMetricAttributes
        __typename
      }
      __typename
    }
  }

  fragment GameAttributes on Game {
    id
    name
    packageName
    platform
    isInhouse
    isActive
    __typename
  }

  fragment GameStudioMetricAttributes on GameStudioMetric {
    game {
      ...GameAttributes
      __typename
    }
    totalInstalls
    mmpCostAmount
    totalAgencyCost
    revenue
    profit
    __typename
  }

}

body:graphql:vars {
  {
    "studioId": 0,
    "dateFrom": "2025-02-01",
    "dateTo": "2025-02-19"
  }
}
