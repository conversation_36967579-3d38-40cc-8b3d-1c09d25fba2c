meta {
  name: aggregate playtime
  type: graphql
  seq: 8
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    aggregatePlaytime(
      where: {
        activeDateFrom: "2024-09-20"
        activeDateTo: "2024-10-05"
        versions: ["1.0.0"]
        gameId: "aa88c9e6-b222-4aba-852a-8254747d04f0",
        installDateFrom: "2024-10-01",
        installDateTo: "2024-10-03"
      }
    ) {
      engagementSessionCount
      playtimeSec
      versions
    }
  }
  
}
