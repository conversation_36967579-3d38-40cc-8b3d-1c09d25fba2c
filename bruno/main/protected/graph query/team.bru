meta {
  name: team
  type: graphql
  seq: 33
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

headers {
  Cookie: session=s%3AeyJtZXNzYWdlIjoiYTg5eXJ3cHF4YndsZGk1Y3Y4dXp2dXpiIiwicHVycG9zZSI6ImFkb25pcy1zZXNzaW9uIn0.JXpBI_h_hDL5mRFOdrpsvLl3Q46xrylmC1ZWhzyBeUk; XSRF-TOKEN=e%3AuVfwm95h5O94DvydmKntWbPX6ImEQk23UhO4elJ-J1Fxr2uZd-SbVD0pE8XZkyoINDaomrjBYLkIeMp-qxWwp0FIiLfvkwt7npAe8befzW4.enV0YXY1WDhnZDJvRkZmUg.wogZ3Qg56rQS_M6ZCAkz7gahQNHUjyvWeqxsIdJ41T8; a89yrwpqxbwldi5cv8uzvuzb=e%3AZD9YTCvTIKcWoQGCED4Mby5F5PfoRkw-4TFuPrSNeW0crxLnCDI4B0WYrYvbTNz2iIEa0Yq3beveZemHobdJv-Yw7r0HUbFZvHV-E6jzK3qAMrd96AqICTicqV4FvIGKbTvqDr42BkNXIRNeH03YhqgKXv__0fEGce20SUWH59ETNmGf2ITci2gmvpLm3iXSfDlCfMY9BjT_hPLDWDSVUjpH_02iENR9ms85HfWf--C_yufFQcgeKRO70PgOOoBa_QSDSX9KAb8a4THt1s9e0Z_MC_FF_gQ70V0Plzm0Y3g.MWU1NWtBNVk4SGZEbXNuYw.toiU5JFx4fqqa_HAPST44KZpBSzdDiodaXqDktYb77Y
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query {
    teams {
        id
        name
        roleId
        leader {
          id
          email
          fullName
        }
      }
    }
  
}
