meta {
  name: get release intel
  type: graphql
  seq: 36
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query GetReleaseIntels {
    releaseMetrics(
      where: {
        gameId: "f4020d7d-0231-4de4-915a-0470207010b0"
        date: {
          operator: BETWEEN
          values: ["2025-06-10", "2025-06-17"]
        }
        version:{
          operator:IN
          values:["1.0.0", "1.2.0"]
        }
      }
      group: {
        fields: ["version"]
      }
      preset: {
        viewPresetId: 161
      }
    ) {
      collection {
        date,
        countryCode,
        retentionRate
        version,
        sessionCount,
        activeUserCount,
        activeUserNthDayCounts
        sessionNthDayCounts
        playtimeMsec,
        installCount
        ads{
          adTypeCategory
          adRevGrossAmount
          impressionCount
          adRevNthDayGrossAmounts
        }
      }
      viewPreset {
        id
        attributes{
          name
          isCohort
          cohortDays
        }
      }
    }
  }
}
