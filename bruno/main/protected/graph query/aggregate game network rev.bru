meta {
  name: aggregate game network rev
  type: graphql
  seq: 14
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {
    aggregateGameNetworkRevenue(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        networkId
        mediationRevenue
        varianceRate
        revenue
        network {
          name
        }
      }
    }
  }
  
}

body:graphql:vars {
  {
    "dateFrom": "2024-12-01",
    "dateTo": "2024-12-27",
    "gameId": "bd74e09a-6c52-4a09-808a-53e977040367"
  }
}
