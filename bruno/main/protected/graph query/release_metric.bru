meta {
  name: release_metric
  type: graphql
  seq: 35
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query GetReleaseIntels {
    releaseMetrics(
      where: {
        gameId: "f4020d7d-0231-4de4-915a-0470207010b0"
        date: {
          operator: BETWEEN
          values: ["2025-06-10", "2025-06-17"]
        }
      }
      group: {
        fields: ["version"]
      }
      preset: {
      }
    ) {
      collection {
        date,
        countryCode,
        version,
        sessionCount,
        activeUserCount,
        playtimeMsec,
        installCount
        ads{
          adTypeCategory
          adRevGrossAmount
          impressionCount
        }
      }
      viewPreset {
        id
        attributes{
          name
          isCohort
          cohortDays
        }
      }
    }
  }
}
