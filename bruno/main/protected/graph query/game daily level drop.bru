meta {
  name: game daily level drop
  type: graphql
  seq: 11
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query data(
    $dateFrom: Date!
    $dateTo: Date!
    $gameId: String!
    $versions: [String!]!
  ) {
    gameDailyLevelDrops(
      where: {
        dateFrom: $dateFrom
        dateTo: $dateTo
        gameId: $gameId
        versions: $versions
      }
    ) {
      collection {
        version
        world
        level
        activeUserCount
        attemptCount
        skipCount
        winCount
        totalPlaytimeSec
      }
    }
  }
  
}

body:graphql:vars {
  {
    "groupByLevel": true,
    "groupByLocation": false,
    "dateFrom": "2024-09-21",
    "dateTo": "2024-10-21",
    "gameId": "0d61c50d-ddf0-4341-a5ef-7671c95ab48e",
    "group": [
      "location"
    ],
    "order": "ASC",
    "versions": [
      "1.0.2",
      "1.0.1"
    ]
  }
}
