meta {
  name: update team
  type: graphql
  seq: 34
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  mutation UpdateTeam($where: UpdateTeamWhere!, $form: UpdateTeamForm!) {
    updateTeam(where: $where, form: $form) {
      id
      name
      roleId
      leader {
        id
        email
        fullName
      }
      members {
        id
        email
        fullName
      }
    }
  }
}

body:graphql:vars {
  {
    "where": {
      "id": 31
    },
    "form": {
      "name": "Updated Team Name",
      "memberEmails": [
      ],
      "leaderEmail": "<EMAIL>"
    }
  }
}
