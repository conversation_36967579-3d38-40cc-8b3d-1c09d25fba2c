meta {
  name: game creative metric
  type: graphql
  seq: 17
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {
    gameCreativeMetrics(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
      offset: { page: 1, perPage: 200 }
      order: { direction: DESC }
    ) {
      collection {
        date
        adGroup
        editor
        campaign
        adGroupStartDate
        targetRoasRate
        roasRate
      }
    }
  }
  
}

body:graphql:vars {
  {
    "dateFrom": "2024-12-01",
    "dateTo": "2025-02-12",
    "gameId": "53cf5763-12cc-44b6-bb0b-ec85e610084d"
  }
}
