meta {
  name: game network rev
  type: graphql
  seq: 13
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {
    gameNetworkRevenues(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
      offset: { page: 1, perPage: 5 }
    ) {
      collection {
        date
        networkId
        revenue
        mediationRevenue
        varianceRate
      }
    }
  }
  
}

body:graphql:vars {
  {
    "dateFrom": "2024-12-01",
    "dateTo": "2024-12-27",
    "gameId": "bd74e09a-6c52-4a09-808a-53e977040367"
  }
}
