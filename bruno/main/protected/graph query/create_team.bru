meta {
  name: Create Team
  type: graphql
  seq: 1
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  mutation CreateTeam($input: CreateTeamInput!) {
    createTeam(input: $input) {
      id
      name
      roleId
      leader {
        id
        email
        fullName
      }
      members {
        id
        email
        fullName
      }
    }
  }
}

body:graphql:vars {
  {
    "form": {
      "name": "New Team",
      "roleId": "mkt-manager",
      "leaderEmail": "<EMAIL>",
      "memberEmails": [
        "<PERSON><PERSON>@yahoo.com"
      ]
    }
  }
}
