meta {
  name: aggregate game agency cost
  type: graphql
  seq: 16
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {
    aggregateGameAgencyCost(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        agencyId
        cost
        mediationCost
        mediationCost
        varianceRate
        agency {
          name
        }
      }
    }
  }
  
}

body:graphql:vars {
  {
    "dateFrom": "2024-12-01",
    "dateTo": "2024-12-27",
    "gameId": "bd74e09a-6c52-4a09-808a-53e977040367"
  }
}
