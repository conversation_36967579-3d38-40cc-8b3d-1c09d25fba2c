meta {
  name: get access control
  type: graphql
  seq: 4
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($subject: String!) {
    accessControl(where: { subject: $subject }) {
      roles {
        roleId
        subject
        permits
      }
    }
  }
  
}

body:graphql:vars {
  {
    "subject": "game_metric_attributes_write"
  }
}
