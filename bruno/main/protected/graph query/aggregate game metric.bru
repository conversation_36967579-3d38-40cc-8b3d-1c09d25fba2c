meta {
  name: aggregate game metric
  type: graphql
  seq: 2
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($gameId: ID!) {
    aggregateGameMetric(where: { gameId: $gameId }) {
      paidInstalls
      organicInstalls
      organicPercentage
      totalInstalls
      cost
      cpi
      roas
      revenue
      profit
      retentionRateDay1
      retentionRateDay3
      retentionRateDay7
      bannerImpsDau
      interImpsDau
      rewardImpsDau
      aoaImpsDau
      mrecImpsDau
      aoaAdmobImpsDau
      collapseAdmobImpsDau
      nativeAdmobImpsDau
      adaptiveAdmobImpsDau
      mrecAdmobImpsDau
      averageSession
      playtime
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "2b8c8277-82b7-483f-9480-8126d636e918"
  }
}
