meta {
  name: game reward usage
  type: graphql
  seq: 9
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ProductMetricsIndex_VersionData(
    $dateFrom: Date!
    $dateTo: Date!
    $gameId: String!
    $versions: [String!]!
    $groupByLevel: Boolean!
    $groupByLocation: Boolean!
    $group: [String!]!
    $order: OrderDirection!
  ) {
    gameRewardUsages(
      where: {
        dateFrom: $dateFrom
        dateTo: $dateTo
        gameId: $gameId
        versions: $versions
      }
      group: { fields: $group }
      order: { direction: $order }
    ) {
      collection {
        version
        location @include(if: $groupByLocation)
        level @include(if: $groupByLevel)
        world @include(if: $groupByLevel)
        useCount
      }
    }
  }
  
}

body:graphql:vars {
  {
    "groupByLevel": true,
    "groupByLocation": false,
    "dateFrom": "2024-09-21",
    "dateTo": "2024-10-21",
    "gameId": "0d61c50d-ddf0-4341-a5ef-7671c95ab48e",
    "group": [
      "location"
    ],
    "order": "ASC",
    "versions": [
      "1.0.2",
      "1.0.1"
    ]
  }
}
