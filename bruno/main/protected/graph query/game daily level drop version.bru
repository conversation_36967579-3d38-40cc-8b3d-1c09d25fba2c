meta {
  name: game daily level drop version
  type: graphql
  seq: 10
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ProductMetricsIndex_VersionData(
    $dateFrom: Date!
    $dateTo: Date!
    $gameId: String!
  ) {
    gameDailyLevelDropVersions(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection
    }
  }
  
}

body:graphql:vars {
  {
    "dateFrom": "2024-09-21",
    "dateTo": "2024-10-21",
    "gameId": "0d61c50d-ddf0-4341-a5ef-7671c95ab48e"
  }
}
