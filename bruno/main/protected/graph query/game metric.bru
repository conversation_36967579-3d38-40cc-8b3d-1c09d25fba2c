meta {
  name: game metric
  type: graphql
  seq: 1
}

post {
  url: {{BASE_URL}}/graphql
  body: graphql
  auth: basic
}

auth:basic {
  username: {{EMAIL}}
  password: {{PASSWORD}}
}

body:graphql {
  query ($gameId: ID!) {
    gameMetrics(where: { gameId: $gameId }) {
      collection {
        date
        paidInstalls
        organicInstalls
        organicPercentage
        totalInstalls
        cost
        cpi
        roas
        revenue
        profit
        retentionRateDay1
        retentionRateDay3
        retentionRateDay7
        bannerImpsDau
        interImpsDau
        metadata {
          id
          note
          uaNote
          monetNote
          productNote
          versionNote
        }
      }
      pageInfo {
        total
        lastPage
        currentPage
      }
    }
  }
  
}

body:graphql:vars {
  {
    "gameId": "2b8c8277-82b7-483f-9480-8126d636e918"
  }
}
