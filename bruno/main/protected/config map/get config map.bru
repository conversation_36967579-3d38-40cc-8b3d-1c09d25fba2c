meta {
  name: get config map
  type: http
  seq: 1
}

get {
  url: {{BASE_URL}}/configmaps/:id?_format=json
  body: none
  auth: none
}

params:query {
  _format: json
}

params:path {
  id: cmap.dash.user
}

headers {
  Cookie: adonis-session=s%3AeyJtZXNzYWdlIjoianI1cmN2enI1NGZjNXg1dzk2ZnMyeGs1IiwicHVycG9zZSI6ImFkb25pcy1zZXNzaW9uIn0.5mXzIYGfIKcw6uvmn5iJJX1-AOOUMj2i4AiTfg1kAUk; Max-Age=259200; Path=/; HttpOnly; SameSite=Laxjr5rcvzr54fc5x5w96fs2xk5=e%3APOKBlVU0dxDT0YMlnY_AvEdW_Uon00vsvcvLBzDLI3lJHXN4SJKNqwH6c-9YqOgb3xZLfAY6Eo_p9L6Mr20AHKnxPGkvcccNHiUbEDBoV0-z4cgn-RH0MsvRMxEIb39FfgX0FKM8HDS2n4qadymXGLq1RUexdmO19ID3VAAfkdkzdH3Np3Jw9dpP4nBwBZUNEuuh7aRwH_j82He_bG4i3Q.SWY2M3hEY2pGWVRxczlIbA.EikRQKMWmhY9aDHB43xDDqGka-uCp4qv5xBdQkDLxn8; Max-Age=259200; Path=/; HttpOnly; SameSite=Lax
}
