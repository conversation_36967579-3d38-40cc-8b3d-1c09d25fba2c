meta {
  name: test
  type: http
  seq: 3
}

post {
  url: http://localhost:3333/sessions
  body: formUrlEncoded
  auth: none
}

headers {
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Language: en-US,en;q=0.9,vi;q=0.8
  Cache-Control: max-age=0
  Connection: keep-alive
  Content-Type: application/x-www-form-urlencoded
  Cookie: wp-settings-time-1=1726678428; adonis-session=s%3AeyJtZXNzYWdlIjoicjhraTl5YmJyM3Z0dXY3cHM0bmVnejNuIiwicHVycG9zZSI6ImFkb25pcy1zZXNzaW9uIn0.YuDKp7PwDlYvFzM16Bue_ZWaUuIz03Cdfm3fCDf1Wnw; r8ki9ybbr3vtuv7ps4negz3n=e%3AGKrmP570OY8p1NFIEidLflO89VKzCav8Dn31nLI5EYMWyA3ka06yA9nG78nmcR6vqDJzv73btoXVPQPJOqexPK9gizNEjCa9CyvU5wHSC5uLYOYawZ6oii2cC4WIEnFBAL-0qvYAFGk4sRABW7UG8Q.cERaRi1xRGJXV1g1R08tSA.ooMEOBaAHyNniT6-aBnh_jyJ5loAILfVcm5a3tHfPfg
  Origin: http://localhost:3333
  Referer: http://localhost:3333/sessions/create
  Sec-Fetch-Dest: document
  Sec-Fetch-Mode: navigate
  Sec-Fetch-Site: same-origin
  Sec-Fetch-User: ?1
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  sec-ch-ua: "Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"
  sec-ch-ua-mobile: ?0
  sec-ch-ua-platform: "macOS"
}

body:form-urlencoded {
  _csrf: 6MnfNvJ5-_yS1MxscQNAVv92xzOvl3X_RUOI
  email: <EMAIL>
  password: 123456
}
