meta {
  name: test login
  type: http
  seq: 4
}

post {
  url: http://localhost:3333/sessions
  body: formUrlEncoded
  auth: none
}

headers {
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Language: en-US,en;q=0.9,vi;q=0.8
  Cache-Control: max-age=0
  Connection: keep-alive
  Content-Type: application/x-www-form-urlencoded
  Cookie: wp-settings-time-1=1726678428; adonis-session=s%3AeyJtZXNzYWdlIjoibzNvaWZ2aTNudzM2a2FmN3pxNGI2OHJuIiwicHVycG9zZSI6ImFkb25pcy1zZXNzaW9uIn0.TS9T1I5ysFQR8EN8b3lcJcF5ZQ8ayq6eWVEOda0iOl8; o3oifvi3nw36kaf7zq4b68rn=e%3ALWKixJA6oRuSE0YR0Iq24YwGuILo92B_bk3bTSUHmPYrtvFE1siMdSR7wa4essEm-kaeo-0qjX1LMacL7mczcv04PsaSCxiMgHfDoxyzHJD9HNac0UH2_SQVe8x1TpZdI7hDyHtvZi6tFWdBfO_uyw.clhJZExmUjhLb3FnQ2Z0aw.gt6L6NpZueUujWu7-kNVLLl506c5QHjGtzgE5b6uB1g
  Origin: http://localhost:3333
  Referer: http://localhost:3333/sessions/create
  Sec-Fetch-Dest: document
  Sec-Fetch-Mode: navigate
  Sec-Fetch-Site: same-origin
  Sec-Fetch-User: ?1
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  sec-ch-ua: "Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"
  sec-ch-ua-mobile: ?0
  sec-ch-ua-platform: "macOS"
}

body:form-urlencoded {
  _csrf: RWHbGY21-irU_nZ6e4pO0EzWfoDEfwvPRh5w
  email: <EMAIL>
  password: 123456
}
