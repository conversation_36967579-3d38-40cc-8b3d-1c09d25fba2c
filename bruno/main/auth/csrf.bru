meta {
  name: csrf
  type: http
  seq: 1
}

get {
  url: {{BASE_URL}}/sessions/create
  body: multipartForm
  auth: none
}

headers {
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "123456",
    "_csrf": "6MnfNvJ5-_yS1MxscQNAVv92xzOvl3X_RUOI"
  }
}

body:multipart-form {
  email: <EMAIL>
  password: 123456
  _csrf: 6MnfNvJ5-_yS1MxscQNAVv92xzOvl3X_RUOI
}

script:post-response {
  const csrfToken = res.body.match(/name='_csrf' value='(.*)'>/)[1]
  bru.setEnvVar("CSRF_TOKEN", csrfToken)
}
