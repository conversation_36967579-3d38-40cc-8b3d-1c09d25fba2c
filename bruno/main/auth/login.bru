meta {
  name: login
  type: http
  seq: 2
}

post {
  url: {{BASE_URL}}/sessions
  body: multipartForm
  auth: none
}

headers {
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "123456",
    "_csrf": "6MnfNvJ5-_yS1MxscQNAVv92xzOvl3X_RUOI"
  }
}

body:multipart-form {
  email: {{EMAIL}}
  password: {{PASSWORD}}
  _csrf: {{CSRF_TOKEN}}
}
