# Mirai Studio Toolkit

## Development

#### Prerequisite

- NodeJS (v22), pnpm

- Typescript

- React

- AdonisJS, Inertia

#### Installation

- npm packages has private github packages, so we need to create a PAT first
  - Go to Tokens (Classic) page: https://github.com/settings/tokens

  - Click Generate new token (classic)
    ![Screenshot 2025-03-11 at 15 15 59](https://github.com/user-attachments/assets/35dfda76-80e1-4097-9111-51099b19ca85)

  - Choose expiration time of `No Expiration`, and `write:packages` permission

  - Press Generate Token
  - Save the token securely

  - Login to `@mirai-game-studio`

  ```bash
  npm login --scope=@mirai-game-studio --auth-type=legacy --
  ```

- Install packages

```bash
pnpm install
```

- Create and update `config/local.yml`

```yaml
config-map:
  driver: local

db:
  primary:
    host: localhost
    port: 5432
  username: postgres
  password: secret
  database: mirai_studio_toolkit_development
  tls: false

dataWarehouseSnapshot:
  primary:
    host: localhost
    port: 5432
  username: postgres
  password: secret
  database: mirai_data_warehouse_development_snapshot
  tls: false

dataWarehouse:
  primary:
    host: localhost
    port: 5432
  username: postgres
  password: secret
  database: mirai_data_warehouse_development
  tls: false

redis:
  cluster: false
  host: localhost
  port: 6379
  password: ''
  tls: false
  db: 0
logger:
  level: debug
  pretty: true
```

- Create the following databases (postgresql)
  - mirai_studio_toolkit_development
  - mirai_data_warehouse_development_snapshot
  - mirai_data_warehouse_development

- Migrate data

```bash
node ace migration:run
node ace migration:run -c dataWarehouse
node ace migration:run -c dataWarehouseSnapshot
```

- Seed data

```bash
node ace db:seed
```

- Run the development server

```bash
npm run dev
```
