{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", "github>mirai-game-studio/renovate"], "commitMessagePrefix": "chore(nodeploy):", "reviewers": ["bichle-mechmaster"], "enabledManagers": ["npm", "dockerfile", "helm-values", "helmfile"], "labels": ["dependencies", "no-stale", "no-test"], "packageRules": [{"matchCategories": ["js"], "groupName": "js"}, {"matchCategories": ["helmfile", "dockerfile"], "groupName": "helmfile"}, {"matchPackageNames": ["@types/config", "typescript", "prettier-edgejs"], "enabled": false}, {"matchUpdateTypes": ["major"], "matchPackageNames": ["@mui/*", "react-day-picker", "googlea<PERSON>", "google-auth-library", "@googleapis/*", "recharts"], "enabled": false}, {"matchPackageNames": ["vite"], "matchUpdateTypes": ["major"], "enabled": false}]}