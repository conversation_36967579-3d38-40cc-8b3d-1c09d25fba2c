# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Deploy production application to Kubernetes

on:
  push:
    branches:
      - production
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy:
    uses: mirai-game-studio/sdk/.github/workflows/k8s-deploy-v2.yml@runner
    secrets: inherit
    with:
      environment: Production
      initial-version: '1.0.0'
      bump: |
        yq -i '.server.image.tag = strenv(TAG)' charts/prod/api/values.yaml
      prebuild: |
        echo -e "//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN\n@mirai-game-studio:registry=https://npm.pkg.github.com/" > .secret_npmrc
      build-args: |
        STAGE=production
        REPO_COMMIT_ID=${{ github.sha }}
        NODE_CONFIG_ENV=production
      build-secret-files: |
        "npmrc=./.secret_npmrc"
      noti-group: '-1002242360148'
      noti-topic: '73'
      noti-success-columns: |
        |W| full |W| Website |W| https://toolkit.miraistudio.games
