name: Check before merge

on:
  pull_request:
    branches:
      - develop

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: read

jobs:
  lint:
    runs-on: linux
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'no-test') }}
    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Private registry
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo -e "//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN\n@mirai-game-studio:registry=https://npm.pkg.github.com/" > .npmrc

      - name: Setup runtime
        uses: mirai-game-studio/sdk/.github/actions/pnpm-setup@runner
        with:
          node-version: 22.14.0
          pnpm-version: 10.8.0

      - name: Run lint
        run: npm run lint

      - name: Static type check
        run: npm run typecheck

      - name: Check lint
        run: npm exec prettier . -- --check

  build:
    runs-on: linux
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'no-test') }}
    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Private registry
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo -e "//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN\n@mirai-game-studio:registry=https://npm.pkg.github.com/" > .npmrc

      - name: Setup runtime
        uses: mirai-game-studio/sdk/.github/actions/pnpm-setup@runner
        with:
          node-version: 22.14.0
          pnpm-version: 10.8.0

      - name: Try to build production bundle
        run: npm run build
