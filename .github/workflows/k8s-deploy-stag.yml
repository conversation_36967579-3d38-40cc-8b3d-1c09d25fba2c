# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Deploy staging application to Kubernetes

on:
  push:
    branches:
      - staging
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy:
    uses: mirai-game-studio/sdk/.github/workflows/k8s-deploy-v2.yml@runner
    secrets: inherit
    with:
      environment: Staging
      initial-version: '0.1.0'
      bump: |
        yq -i '.server.image.tag = strenv(TAG)' charts/stag/api/values.yaml
      prebuild: |
        echo -e "//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN\n@mirai-game-studio:registry=https://npm.pkg.github.com/" > .secret_npmrc
      build-args: |
        STAGE=production
        REPO_COMMIT_ID=${{ github.sha }}
        NODE_CONFIG_ENV=staging
      build-secret-files: |
        "npmrc=./.secret_npmrc"
      noti-group: '-1002242360148'
      noti-topic: '73'
      noti-success-columns: |
        |W| full |W| Website |W| https://toolkit-stag-10-241-90-160.local.miraistudio.games
