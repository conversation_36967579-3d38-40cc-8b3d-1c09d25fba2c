## Coding convention

- Standard coding convention: https://github.com/airbnb/javascript with some minor custom rules
- The code should be linted with prettier

## Pull request

- Pull request name should follows conventional commit https://www.conventionalcommits.org/
  - Use lower case
  - The message should be short and informative
    eg:

```txt
feat(core): integrate apollo server for graphql
feat(budget request): add filters
fix(game list): the table height is overflow
```

- Pull Request description should contains the task information, it can be
  - link to the task: https://task.miraistudio.games/wp/28361 or https://task.miraistudio.games/projects/dashboard/work_packages/28361

  - shortcut (OpenProject builtin GitHub integration): OP#28361

## Frontend

- Essential UI elements should be placed in `components` folder (inspired by shadcn ui)

- Screen/Page composes UI elements to build a page, it should not be customized too much at screen-page level, prefer using pre-defined style

- Every UI elements should have its own storybook

- Type-safe:
  - Component and function arguments and return value must always be typed

  ```tsx
  // bad

  function sum(x: any, y: any): any {
    return x + y
  }

  function Button(props: { onClick: any }) {
    return <button {...props} />
  }
  ```

  ```tsx
  // good

  function sum(x: number, y: number) {
    return x + y // the return type is auto-detected
  }

  function Button(props: { onClick: MuiButton['onClick'] }) {
    return <MuiButton {...props} />
  }
  ```

  - When using the function/components, the prop can be use as any

  ```tsx
  sum(x as any, y as any) // => number
  ```

## Backend

- Prefer GraphQL over RestAPI

- To prevent n+1 query problem, we use dataloader pattern: https://www.apollographql.com/tutorials/dataloaders-typescript/04-using-a-dataloader
