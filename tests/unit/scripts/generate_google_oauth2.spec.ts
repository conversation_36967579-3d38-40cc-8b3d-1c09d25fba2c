import { createInterface } from 'node:readline/promises'

import { test } from '@japa/runner'
import { OAuth2Client } from 'google-auth-library'

test.group('Generate google oauth 2', () => {
  test('generate tokens', async () => {
    const clientId = ''
    const clientSecret = ''
    const auth = new OAuth2Client({
      clientId,
      clientSecret,
      redirectUri: 'http://localhost',
    })
    const url = auth.generateAuthUrl({
      access_type: 'offline',
      scope: 'https://www.googleapis.com/auth/firebase.readonly',
      prompt: 'consent',
    })
    console.log('Open the following URL in your browser:', url)
    const code = await createInterface({
      input: process.stdin,
      output: process.stdout,
    }).question('Enter the code: ')
    const tokens = await auth.getToken(code)
    console.log(tokens)
  }).skip(true, 'runs local only')
})
