import { test } from '@japa/runner'
import app from '@adonisjs/core/services/app'
import { ConfigMapRegistry } from '@munkit/main'

import { AdMetricFactory } from '#database/factories/ad_metric_factory'
import AdMetric from '#models/ad_metric'
import { ViewPresetAttribute } from '#models/view_preset'
import ViewPresetConfigMap from '#configmaps/dashboard/view_preset'

const pageId = 'dashboard.games.campaign_metrics.index'

test.group('AdMetric.selectPreset', (group) => {
  let registry: ConfigMapRegistry
  let viewPresetConfigMap: ViewPresetConfigMap

  group.setup(async () => {
    registry = await app.container.make('cmap.registry')
    viewPresetConfigMap = registry.get('cmap.viewpreset').get(pageId)

    await AdMetricFactory.merge({ countryCode: 'VN' }).createMany(5)
  })

  const createViewPresetAttribute = (name: string) => {
    const viewPresetAttributeConfigMap = viewPresetConfigMap.attributes.get(name)
    return [
      new ViewPresetAttribute().merge({
        name,
        isCohort: viewPresetAttributeConfigMap.isCohort,
        cohortDays: viewPresetAttributeConfigMap.isCohort ? [1, 2, 3] : [],
      }),
      viewPresetAttributeConfigMap,
    ] as const
  }

  test('can query columns', async ({ expect }) => {
    const collection = await AdMetric.query().withScopes((s) => {
      s.selectPreset(...createViewPresetAttribute('adRevGrossAmount'))
      s.selectPreset(...createViewPresetAttribute('impressionCount'))
      s.selectPreset(...createViewPresetAttribute('clickCount'))
    })

    expect(collection).toBeArrayOfSize(5)
  })

  test('can query computed columns', async ({ expect }) => {
    const collection = await AdMetric.query().withScopes((s) => {
      s.selectPreset(...createViewPresetAttribute('cpi'))
    })

    expect(collection).toBeArrayOfSize(5)
  })

  test('can query cohort columns', async ({ expect }) => {
    const collection = await AdMetric.query().withScopes((s) => {
      s.selectPreset(...createViewPresetAttribute('adRevNthDayGrossAmounts'))
    })

    expect(collection).toBeArrayOfSize(5)
  })

  test('can query computed cohort columns', async ({ expect }) => {
    const collection = await AdMetric.query().withScopes((s) => {
      s.selectPreset(...createViewPresetAttribute('retentionNthDayRates'))
    })

    expect(collection).toBeArrayOfSize(5)
  })

  test('can query all attribute types together', async ({ expect }) => {
    const collection = await AdMetric.query().withScopes((s) => {
      // Regular columns
      s.selectPreset(...createViewPresetAttribute('adRevGrossAmount'))
      s.selectPreset(...createViewPresetAttribute('impressionCount'))
      s.selectPreset(...createViewPresetAttribute('clickCount'))

      // Computed columns
      s.selectPreset(...createViewPresetAttribute('cpi'))

      // Cohort columns
      s.selectPreset(...createViewPresetAttribute('adRevNthDayGrossAmounts'))

      // Computed cohort columns
      s.selectPreset(...createViewPresetAttribute('retentionNthDayRates'))
    })

    expect(collection).toBeArrayOfSize(5)
  })

  test('can query aggregated fields', async ({ expect }) => {
    const collection = await AdMetric.query()
      .groupBy('countryCode')
      .select('countryCode')
      .withScopes((s) => {
        s.selectPreset(...createViewPresetAttribute('adRevGrossAmount'))
      })
    expect(collection).toBeArrayOfSize(1)
  })
})
