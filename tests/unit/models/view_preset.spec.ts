import { test } from '@japa/runner'

import ViewPreset, { ViewPresetAttribute, ViewPresetSchema } from '#models/view_preset'
import { UserFactory } from '#database/factories/user_factory'

test.group('Models ViewPreset', () => {
  test('serialization', async ({ expect }) => {
    const user = await UserFactory.create()

    let viewPreset = new ViewPreset().merge({
      userId: user.id,
      pageId: 'test',
      schema: new ViewPresetSchema().merge({
        attributes: [
          new ViewPresetAttribute().merge({
            name: 'adRevGrossAmount',
            isCohort: false,
          }),
          new ViewPresetAttribute().merge({
            name: 'adRevNthDayGrossAmounts',
            isCohort: true,
            cohortDays: [1, 2, 3],
          }),
        ],
      }),
    })

    await viewPreset.save()

    viewPreset = await ViewPreset.findByOrFail({ userId: user.id, pageId: 'test' })
    expect(viewPreset.schema).toBeInstanceOf(ViewPresetSchema)
    expect(viewPreset.schema.attributes).toBeArrayOfSize(2)
  })
})
