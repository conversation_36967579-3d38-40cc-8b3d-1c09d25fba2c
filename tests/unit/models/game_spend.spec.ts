import { test } from '@japa/runner'

import { GameSpendFactory } from '#database/factories/game_spend_factory'
import GameSpend from '#models/game_spend'

test.group('Models game spend', () => {
  test('withOverride', async () => {
    const storeId = 'xxx'
    await GameSpendFactory.merge({ storeId, preTaxAmount: 0 }).createMany(10)

    const list = await GameSpend.query().withScopes((q) => q.withOverride())
    console.log(list[0])
    await GameSpend.query()
      .withScopes((q) => q.withAggregation())
      .where('storeId', 'xxx')
  })
})
