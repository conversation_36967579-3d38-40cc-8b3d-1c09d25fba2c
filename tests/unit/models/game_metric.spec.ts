import { test } from '@japa/runner'

import { GameFactory } from '#database/factories/game_factory'
import { GameMetricFactory } from '#database/factories/game_metric_factory'
import { GameSpendFactory } from '#database/factories/game_spend_factory'
import GameMetric from '#models/game_metric'
import GameSpend from '#models/game_spend'

test.group('Game Metric model', () => {
  test('default', async () => {
    const game = await GameFactory.create()

    const metrics = await GameMetricFactory.merge({ storeId: game.storeId }).createMany(10)

    for (const metric of metrics) {
      await GameSpendFactory.merge({ storeId: game.storeId, date: metric.date }).create()
    }

    await GameMetric.query().withScopes((s) => s.default())
    await GameSpend.query().withScopes((s) => s.withAggregation())
  })
})
