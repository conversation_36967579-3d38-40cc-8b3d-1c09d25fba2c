import { test } from '@japa/runner'

import { isUrlMatchPattern } from '#utils/route'

test.group('Utils route', () => {
  test('multiple placeholders', async ({ expect }) => {
    const url = `/dash/game_metrics/application.package.id/revenues/2024-01-10`
    const pattern = `/dash/game_metrics/{0}/revenues/{1}`
    expect(isUrlMatchPattern(url, pattern).placeholders).toEqual([
      'application.package.id',
      '2024-01-10',
    ])
  })

  test('single placeholders', async ({ expect }) => {
    const url = `/dash/game_metrics/application.package.id`
    const pattern = `/dash/game_metrics/{0}`
    expect(isUrlMatchPattern(url, pattern).placeholders).toEqual(['application.package.id'])
  })

  test('no placeholders', async ({ expect }) => {
    const url = `/dash/game_metrics`
    const pattern = `/dash/game_metrics`
    expect(isUrlMatchPattern(url, pattern).match).toBeTrue()
  })
})
