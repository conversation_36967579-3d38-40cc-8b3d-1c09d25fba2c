import { test } from '@japa/runner'

import { compareLevel } from '#utils/level'

test.group('Utils compare level', () => {
  test('diff world', async ({ assert }) => {
    const a = { level: 1, world: 1 }
    const b = { level: 1, world: 2 }

    assert.equal(compareLevel(a, b), -1)
    assert.equal(compareLevel(b, a), 1)
  })

  test('same world, diff level', async ({ assert }) => {
    const a = { level: 1, world: 1 }
    const b = { level: 2, world: 1 }

    assert.equal(compareLevel(a, b), -1)
    assert.equal(compareLevel(b, a), 1)
  })

  test('equals', async ({ assert }) => {
    const a = { level: 1, world: 1 }
    const b = { level: 1, world: 1 }

    assert.equal(compareLevel(a, b), 0)
  })
})
