import { test } from '@japa/runner'

test.group('Url', () => {
  test('generate game release proposal url', async () => {
    const url = new URL('http://localhost:3333/game_release_proposals/create')

    const query = {
      repository: 'mirai-game-studio/studio-toolkit',
      revision: 'e7ca011',
      semver: '1.0',
      platform: 'android',
      installableDownloadUrl:
        'https://drive.google.com/file/d/1IfGecOiDABEvVrls4eWTpFrU51sjSEqB/view',
      symbolsDownloadUrl: 'https://drive.google.com/file/d/17wv5K59sqXMQUiIEexMmOLQ8zDHFYCiV/view',
    }

    Object.entries(query).forEach(([key, value]) => {
      url.searchParams.append(key, value)
    })

    console.log(url.toString())
  })
})
