import app from '@adonisjs/core/services/app'
import { test } from '@japa/runner'
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  TextChannel,
  ThreadChannel,
} from 'discord.js'

test.group('Discord', () => {
  test('send message to thread', async () => {
    const bot = await app.container.make('discord.bot')

    await bot.useClient(async (client) => {
      const channel = (await client.channels.fetch('1227205958669963355')) as
        | TextChannel
        | ThreadChannel

      await channel.send({
        content: 'Hello world',
        embeds: [
          new EmbedBuilder().setColor('Aqua').addFields([
            {
              name: 'Message',
              value: 'Hello world',
            },
          ]),
        ],
        components: [
          new ActionRowBuilder<ButtonBuilder>().addComponents(
            new ButtonBuilder()
              .setLabel('Click me!')
              .setStyle(ButtonStyle.Link)
              .setURL('https://toolkit.miraistudio.games')
          ),
        ],
      })
    })
  }).skip(true)
})
