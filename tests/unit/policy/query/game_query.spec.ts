import { test } from '@japa/runner'
import { Bouncer } from '@adonisjs/bouncer'
import { testcase } from '@mirai-game-studio/adonis-testing-sdk'
import app from '@adonisjs/core/services/app'

import { GameFactory } from '#database/factories/game_factory'
import Game from '#models/game'
import { UserFactory } from '#database/factories/user_factory'
import User from '#models/user'
import * as abilities from '#abilities/main'
import { policies } from '#policies/main'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import DashboardRoleMembershipUser from '#models/dashboard_role_membership_user'

test.group('GameQueryPolicy#scope', (group) => {
  type GameQueryPolicyTestContext = {
    inhouseGames: Game[]
    partnerGames: Game[]
    user: () => Promise<User>
    expectedGameIds: Game['id'][]
  }

  let context = {} as GameQueryPolicyTestContext

  group.each.setup(async () => {
    context.inhouseGames = await GameFactory.createMany(2)
    context.partnerGames = await GameFactory.createMany(2)
  })

  const includeTest = testcase<GameQueryPolicyTestContext>(async (ctx, { expect }) => {
    const bouncer = await createBouncer(await context.user())
    const gamesQuery = Game.query()
    await bouncer.with('GameQueryPolicy').authorize('scope', gamesQuery)
    const games = await gamesQuery
    const actualGameIds = games.map((g) => g.id)
    expect(actualGameIds).toIncludeAllMembers(ctx.expectedGameIds)
    expect(actualGameIds).toHaveLength(ctx.expectedGameIds.length)
  })

  includeTest({
    name: 'manager can view all games',
    context: async () => {
      context.user = () => UserFactory.apply('all:manage').create()
      context.expectedGameIds = [
        ...context.inhouseGames.map((g) => g.id),
        ...context.partnerGames.map((g) => g.id),
      ]
      return context
    },
  })

  includeTest({
    name: 'partner can view assigned games',
    context: async () => {
      context.user = async () => {
        const user = await UserFactory.apply('dashboard:view')
          .apply('partner')
          .with('team')
          .create()
        await DashboardRoleMembership.create({
          roleId: user.team.roleId,
          storeId: context.partnerGames[0].id,
          users: [new DashboardRoleMembershipUser().merge({ email: user.email })],
        })

        return user
      }

      context.expectedGameIds = [context.partnerGames[0].id]
      return context
    },
  })

  includeTest({
    name: 'normal user can view assigned games',
    context: async () => {
      context.user = async () => {
        const user = await UserFactory.apply('dashboard:view').with('team').create()
        await DashboardRoleMembership.create({
          roleId: user.team.roleId,
          storeId: context.inhouseGames[0].id,
          users: [new DashboardRoleMembershipUser().merge({ email: user.email })],
        })

        return user
      }

      context.expectedGameIds = [context.inhouseGames[0].id]
      return context
    },
  })

  includeTest({
    name: 'leader can view member games, and assigned games',
    context: async () => {
      context.user = async () => {
        const leader = await UserFactory.apply('dashboard:view').with('team').create()
        await leader.team.merge({ leaderId: leader.id }).save()
        const member = await UserFactory.apply('dashboard:view')
          .tap((user) => {
            user.teamId = leader.team.id
          })
          .create()
        await DashboardRoleMembership.create({
          roleId: leader.team.roleId,
          storeId: context.inhouseGames[0].id,
          users: [new DashboardRoleMembershipUser().merge({ email: member.email })],
        })

        await DashboardRoleMembership.create({
          roleId: leader.team.roleId,
          storeId: context.inhouseGames[1].id,
          users: [new DashboardRoleMembershipUser().merge({ email: leader.email })],
        })

        return leader
      }

      context.expectedGameIds = [context.inhouseGames[0].id, context.inhouseGames[1].id]
      return context
    },
  })

  /**
   * Helpers
   */

  async function createBouncer(user: User) {
    const bouncer = new Bouncer(user, abilities, policies).setContainerResolver(
      app.container.createResolver()
    )
    return bouncer
  }
})
