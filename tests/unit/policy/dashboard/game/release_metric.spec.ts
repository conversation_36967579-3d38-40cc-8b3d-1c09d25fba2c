import { test } from '@japa/runner'
import { testcase } from '@mirai-game-studio/adonis-testing-sdk'

import { GameFactory } from '#database/factories/game_factory'
import User from '#models/user'
import { createBouncer } from '#tests/supports/create_bouncer'
import Game from '#models/game'
import { UserFactory } from '#database/factories/user_factory'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import DashboardRoleMembershipUser from '#models/dashboard_role_membership_user'
import ACL from '#models/acl'
import { AclSubject } from '#config/enums'
import Team from '#models/team'

type ReleaseMetricPolicyTestContext = {
  user: (game: Game) => Promise<User>
  authorized: boolean
}

test.group('Policy dashboard game release metric', () => {
  const includeTest = testcase<ReleaseMetricPolicyTestContext>(async (ctx, { expect }) => {
    const game = await GameFactory.create()
    const bouncer = await createBouncer(await ctx.user(game))
    const isAllowed = await bouncer.with('dash.game.release_metric').allows('index', game)
    expect(isAllowed).toBe(ctx.authorized)
  })

  includeTest({
    name: 'allows manager access',
    context: async () => {
      return {
        authorized: true,
        user: async (_game) => {
          const user = await UserFactory.with('team').apply('dashboard:manage').create()
          return user
        },
      }
    },
  })

  includeTest({
    name: 'allows leader access member games',
    context: async () => {
      return {
        authorized: true,
        user: async (game) => {
          const leader = await UserFactory.with('team').apply('dashboard:manage').create()
          await leader.team
            .merge({
              leaderId: leader.id,
            })
            .save()
          const member = await UserFactory.merge({
            teamId: leader.teamId,
          })
            .apply('dashboard:view')
            .create()
          await member.load('team')
          await addToGame(game, member)
          await allowAccessToRoute(leader.team)
          return leader
        },
      }
    },
  })

  includeTest({
    name: 'user is in project but ACL block access',
    context: async () => {
      return {
        authorized: false,
        user: async (game) => {
          const user = await UserFactory.with('team').apply('dashboard:view').create()
          await addToGame(game, user)
          return user
        },
      }
    },
  })

  includeTest({
    name: 'user is in project and ACL allow access',
    context: async () => {
      return {
        authorized: true,
        user: async (game) => {
          const user = await UserFactory.with('team').apply('dashboard:view').create()
          await addToGame(game, user)
          await allowAccessToRoute(user.team)
          return user
        },
      }
    },
  })

  includeTest({
    name: 'user not in project, ACL allow access',
    context: async () => {
      return {
        authorized: false,
        user: async (_game) => {
          const user = await UserFactory.with('team').apply('dashboard:view').create()
          await allowAccessToRoute(user.team)
          return user
        },
      }
    },
  })

  includeTest({
    name: 'block partner access',
    context: async () => {
      return {
        authorized: false,
        user: async (game) => {
          const user = await UserFactory.with('team')
            .apply('partner')
            .apply('dashboard:view')
            .create()
          await addToGame(game, user)
          await allowAccessToRoute(user.team)
          return user
        },
      }
    },
  })

  async function addToGame(game: Game, user: User) {
    const membership = await DashboardRoleMembership.firstOrNew(
      {
        roleId: user.team.roleId,
        storeId: game.id,
      },
      {
        users: [],
      }
    )
    membership.users.push(new DashboardRoleMembershipUser().merge({ email: user.email }))
    await membership.save()
  }

  async function allowAccessToRoute(team: Team) {
    await ACL.updateOrCreate(
      {
        roleId: team.roleId,
        subject: AclSubject.Route,
      },
      {
        permits: ['dashboard.games.release_metrics.index'],
      }
    )
  }
})
