import app from '@adonisjs/core/services/app'
import { test } from '@japa/runner'

import { GameFactory } from '#database/factories/game_factory'
import { UserFactory } from '#database/factories/user_factory'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import DashboardRoleMembershipUser from '#models/dashboard_role_membership_user'
import GameMetricPolicy from '#policies/dashboard/game/game_metric_policy'

test.group('Policy game metric', () => {
  test('cherryPick', async () => {
    const game = await GameFactory.create()

    await DashboardRoleMembership.createMany([
      {
        storeId: game.storeId,
        roleId: 'ua',
        users: [
          new DashboardRoleMembershipUser().merge({ email: '<EMAIL>' }),
          new DashboardRoleMembershipUser().merge({ email: '<EMAIL>' }),
        ],
      },
      {
        storeId: game.storeId,
        roleId: 'mkt-manager',
        users: [new DashboardRoleMembershipUser().merge({ email: '<EMAIL>' })],
      },
    ])

    const policy = await app.container.make(GameMetricPolicy)
    const user = await UserFactory.merge({ email: '<EMAIL>' }).make()
    await policy.cherryPick(user, game)
  })
})
