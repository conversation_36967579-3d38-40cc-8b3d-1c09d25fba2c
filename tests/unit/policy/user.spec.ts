import { test } from '@japa/runner'
import app from '@adonisjs/core/services/app'

import UserPolicy from '#policies/user_policy'
import { UserFactory } from '#database/factories/user_factory'

test.group('UserPolicy.simulate', (group) => {
  let policy: UserPolicy
  let subject: UserPolicy['simulate']

  group.each.setup(async () => {
    policy = await app.container.make(UserPolicy)
    subject = policy.simulate.bind(policy)
  })

  test('allows if global manager', async ({ expect }) => {
    const user = await UserFactory.apply('all:manage').make()
    const resource = await UserFactory.apply('all:manage').make()
    expect(await subject(user, resource)).toBeTrue()
  })

  test('allows if they can manage all of resource tools', async ({ expect }) => {
    const user = await UserFactory.apply('dashboard:manage').make()
    const manager = await UserFactory.apply('dashboard:manage').make()
    const viewer = await UserFactory.apply('dashboard:view').make()
    expect(await subject(user, manager)).toBeTrue()
    expect(await subject(user, viewer)).toBeTrue()
  })

  test('reject if the tool is not managed by them', async ({ expect }) => {
    const user = await UserFactory.apply('dashboard:manage').make()
    const viewer = await UserFactory.apply('all:view').make()
    const manager = await UserFactory.apply('all:manage').make()
    expect(await subject(user, viewer)).toBeFalse()
    expect(await subject(user, manager)).toBeFalse()
  })
})
