import { test } from '@japa/runner'
import firebase from '@googleapis/firebase'
import { OAuth2Client } from 'google-auth-library'
import app from '@adonisjs/core/services/app'
import config from 'config'

import { VaultKeyClient } from '#services/vaultkey_client'

test.group('Services firebase', () => {
  test('example test', async () => {
    const vkClient = await app.container.make(VaultKeyClient)
    const creds = await vkClient.getOAuth2Credentials(config.get('googleapi').vaultkeyId)
    const oauthClient = new OAuth2Client({
      client_id: creds.clientId,
      client_secret: creds.clientSecret,
      redirectUri: creds.redirectUri,
    })
    oauthClient.setCredentials({
      access_token: creds.accessToken,
      refresh_token: creds.refreshToken,
      scope: creds.scope,
      token_type: 'Bearer',
    })
    const client = new firebase.firebase_v1beta1.Firebase({
      auth: oauthClient,
    })
    const project = await client.projects.get({
      name: 'projects/bad-student-school-prank',
    })
    const analyticsDetails = await client.projects.getAnalyticsDetails({
      name: 'projects/bad-student-school-prank/analyticsDetails',
    })
    console.log(project.data, analyticsDetails.data.analyticsProperty)
  })
})
