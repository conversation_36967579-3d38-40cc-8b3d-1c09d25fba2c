import fs from 'node:fs/promises'
import rl from 'node:readline'

import { test } from '@japa/runner'
import admob from '@googleapis/admob'
import { OAuth2Client } from 'google-auth-library'

test.group('Services admob', () => {
  test('create admob client from client secrets', async ({ expect }) => {
    // Define required scopes for AdMob API
    const scopes = [
      'https://www.googleapis.com/auth/admob.readonly',
      'https://www.googleapis.com/auth/admob.report',
    ]

    // Load client secrets from file
    const clientSecrets = await fs
      .readFile('tmp/admob/client_secrets.json')
      .then((b) => JSON.parse(b.toString()))
      .catch((err) => {
        console.error('Failed to read client secrets file:', err)
        throw new Error('Client secrets file not found or invalid')
      })

    // Create auth client from secrets
    const auth = new OAuth2Client(
      clientSecrets.installed.client_id,
      clientSecrets.installed.client_secret,
      clientSecrets.installed.redirect_uris[0]
    )

    // Generate auth URL (if needed for manual authorization)
    const url = auth.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
    })

    // For testing, you can use the URL to manually get an authorization code
    console.log('Authorize this app by visiting this URL:', url)

    // Wait for user to input the authorization code
    const readline = rl.createInterface({
      input: process.stdin,
      output: process.stdout,
    })

    const authCode = await new Promise<string>((resolve) => {
      readline.question('Enter the authorization code from that page: ', (code: string) => {
        readline.close()
        resolve(code.trim())
      })
    })

    // Exchange the authorization code for tokens
    const { tokens } = await auth.getToken(authCode)
    console.log(JSON.stringify(tokens))
    console.log('Access token obtained successfully!')

    // Set the obtained credentials for the client
    auth.setCredentials(tokens)

    // Initialize the AdMob client
    const client = admob.admob({
      version: 'v1beta',
      auth: auth as any,
    })

    // Test that the client was created successfully
    expect(client).toBeDefined()
    expect(client.accounts).toBeDefined()

    // Comment out actual API call for testing unless you have valid tokens
    // const accounts = await client.accounts.list()
    // console.log(accounts)
  }).timeout(60_000)

  test('report', async ({ expect }) => {
    const tokens = {
      access_token:
        '******************************************************************************************************************************************************************************************************************************',
      refresh_token:
        '1//05i6niKaD39C_CgYIARAAGAUSNwF-L9IrsJuUKViWllYriV8pU7rmXNVqwCE_fe-7jv-7RI2l-ar3-UYYUQK9NPRCyXIZCsYP_Zw',
      scope:
        'https://www.googleapis.com/auth/admob.report https://www.googleapis.com/auth/admob.readonly',
      token_type: 'Bearer',
      expiry_date: *************,
    }

    // Create OAuth client
    const auth = new OAuth2Client()

    // Set credentials from the stored tokens
    auth.setCredentials(tokens)

    // Initialize the AdMob client
    const client = admob.admob({
      version: 'v1beta',
      auth: auth as any,
    })

    // Test that the client was created successfully
    expect(client).toBeDefined()
    expect(client.accounts).toBeDefined()

    // Get the publisher accounts
    const accounts = await client.accounts.list()

    // Ensure we got a valid response
    expect(accounts.status).toBe(200)
    expect(accounts.data.account).toBeDefined()

    const account = accounts.data.account![0]
    console.log(account)

    const apps = await client.accounts.apps.list({
      parent: account.name!,
      pageSize: 1000,
    })

    await fs.writeFile('apps.json', JSON.stringify(apps.data))

    // try {
    //   const report = await client.accounts.networkReport.generate({
    //     parent: account.name!,
    //     requestBody: {
    //       reportSpec: {
    //         dimensions: [
    //           'APP',
    //           'DATE',
    //           'AD_UNIT',
    //           //'COUNTRY',
    //           // 'APP_VERSION_NAME',
    //           'FORMAT',
    //         ],
    //         dimensionFilters: [
    //           {
    //             dimension: 'APP',
    //             matchesAny: {
    //               values: ['ca-app-pub-****************~**********'],
    //             },
    //           },
    //         ],
    //         dateRange: {
    //           startDate: {
    //             day: 16,
    //             month: 4,
    //             year: 2025,
    //           },
    //           endDate: {
    //             day: 16,
    //             month: 4,
    //             year: 2025,
    //           },
    //         },
    //         metrics: [
    //           'CLICKS',
    //           'ESTIMATED_EARNINGS',
    //           'IMPRESSIONS',
    //           'IMPRESSION_CTR',
    //           'MATCHED_REQUESTS',
    //           'SHOW_RATE',
    //           // 'OBSERVED_ECPM',
    //         ],
    //       },
    //     },
    //   })
    //   await fs.writeFile('network.json', JSON.stringify(report.data))
    // } catch (err) {
    //   console.error('Error generating report:', JSON.stringify(err))
    //   expect(err).toBeNull()
    // }
  })
    .timeout(60_000)
    .skip()
})
