import app from '@adonisjs/core/services/app'
import { test } from '@japa/runner'

import GoogleDriveDownloader from '#services/game_release/google_drive_downloader'

test.group('Services game release google drive downloader', () => {
  test('example test', async () => {
    const downloader = await app.container.make(GoogleDriveDownloader)
    await downloader.downloadAsFile(
      'https://drive.google.com/file/d/17XLvm5IMQUEfnruWtrOkvQj1-wIEs7b8/view'
    )
  })
    .timeout(300_000)
    .skip(true)
})
