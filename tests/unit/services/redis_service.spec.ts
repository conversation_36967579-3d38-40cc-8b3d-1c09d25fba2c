import app from '@adonisjs/core/services/app'
import { test } from '@japa/runner'
import { BaseModel, column } from '@adonisjs/lucid/orm'

import { RedisService, RedisStringKey } from '#services/redis/redis_service'

class Student extends BaseModel {
  @column({ columnName: 'date_of_birth' })
  declare dateOfBirth: string
}

test.group('Services redis service', () => {
  test('single', async ({ expect }) => {
    const redis = await app.container.make(RedisService)
    await redis.save(
      new RedisStringKey('bichls'),
      new Student().merge({
        dateOfBirth: '1990-01-01',
      })
    )
    const student = await redis.get(new RedisStringKey('bichls'), Student)
    expect(student!.dateOfBirth).toBe('1990-01-01')
  })

  test('many', async ({ expect }) => {
    const redis = await app.container.make(RedisService)
    await redis.save(new RedisStringKey('bichls'), [
      new Student().merge({
        dateOfBirth: '1990-01-01',
      }),
      new Student().merge({
        dateOfBirth: '1990-01-02',
      }),
    ])
    const students = await redis.getMany(new RedisStringKey('bichls'), Student)
    expect(students.length).toBe(2)
    expect(students[0].dateOfBirth).toBe('1990-01-01')
    expect(students[1].dateOfBirth).toBe('1990-01-02')
  })
})
