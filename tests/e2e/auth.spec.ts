import { test, expect } from '@playwright/test'

test.beforeEach(async ({ page }) => {
  await page.goto('http://localhost:3333')
})

test.describe('Authentication Tests', () => {
  test('should allow user to log in', async ({ page }) => {
    const username = page.getByTestId('login_email')
    const password = page.getByTestId('login_password')
    const loginButton = page.getByTestId('login_submit')

    await username.fill('<EMAIL>')
    await password.fill('123456')
    await loginButton.click()

    await expect(page.getByTestId('welcome')).toHaveText('Welcome')
  })
})
