import { test, expect } from '@playwright/test'

test.beforeEach(async ({ page }) => {
  await page.goto('http://localhost:3333')
  const username = page.getByTestId('login_email')
  const password = page.getByTestId('login_password')
  const loginButton = page.getByTestId('login_submit')

  await username.fill('<EMAIL>')
  await password.fill('123456')
  await loginButton.click()

  await expect(page.getByTestId('welcome')).toHaveText('Welcome')
})

test.describe('Game Tests', () => {
  test('should handle navigation correctly', async ({ page }) => {
    await page.goto('http://localhost:3333/dash/games')
    await expect(page.getByTestId('breadcrumb_item_last')).toHaveText('All Games')
    await expect(page.getByTestId('query_state_loading')).not.toBeVisible()
    await expect(page.getByTestId('game_list_table')).toBeVisible()
    expect(await page.locator('[name=game_link]').count()).toBeGreaterThan(1)

    await page.getByTestId('manage_game_roles_0').click()
    await expect(page.getByTestId('manage_roles_dialog_title')).toBeVisible()
  })
})
