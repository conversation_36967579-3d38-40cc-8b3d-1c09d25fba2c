import app from '@adonisjs/core/services/app'
import { test } from '@japa/runner'

import { GamePlatform, GameReleaseProposalStatus, GameReleaseStatus } from '#config/enums'
import { UserFactory } from '#database/factories/user_factory'
import GameReleaseApproval from '#models/game_release_approval'
import GameReleaseProposal, { AndroidExtra } from '#models/game_release_proposal'

test.group('Release game job', () => {
  test('Release game job success', async () => {
    const { default: Job } = await import('#jobs/release_game_job')
    const job = await app.container.make(Job, [{}])
    const proposal = await GameReleaseProposal.updateOrCreate(
      {
        repository: 'mirai-unity-template',
      },
      {
        changelog: 'unit test',
        installableDownloadUrl:
          'https://drive.google.com/file/d/17XLvm5IMQUEfnruWtrOkvQj1-wIEs7b8/view',
        platform: GamePlatform.Android,
        semver: '1.0',
        status: GameReleaseProposalStatus.Pending,
        revision: '17f06c0',
        extra: new AndroidExtra().merge({
          symbolsDownloadUrl:
            'https://drive.google.com/file/d/15k9HxzTVJ_79K-QJSxclyiozhYqQhKx6/view',
          versionCode: 13,
        }),
      }
    )

    const reviewer = await UserFactory.create()

    const approval = await GameReleaseApproval.create({
      proposalId: proposal.id,
      publisherId: 'GP Mirai Games',
      releaseStatus: GameReleaseStatus.Pending,
      jobId: '1',
      reviewerId: reviewer.id,
    })

    await job.handle({
      approvalId: approval.id,
    })
  }).timeout(300_000)
})
