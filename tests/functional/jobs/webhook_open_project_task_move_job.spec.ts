import app from '@adonisjs/core/services/app'
import { test } from '@japa/runner'

import pullRequestOpenedEvent from '#fixtures/pull_request_opened.json' assert { type: 'json' }
import OpenProjectService from '#services/open_project_service'

test.group('OpenProjectService#getWorkPackageIdsFromIntegration', (group) => {
  let service: OpenProjectService

  group.setup(async () => {
    service = await app.container.make(OpenProjectService, ['apiKey'])
  })

  test('OP#taskId', ({ expect }) => {
    const result = service.getWorkPackageIdsFromIntegration('OP#100 OP#101 OP#102')
    expect(result).toEqual(['100', '101', '102'])
  })

  test('task url', ({ expect }) => {
    const result = service.getWorkPackageIdsFromIntegration(
      'https://task.miraistudio.games/projects/infrastructure/work_packages/2524/activity'
    )
    expect(result).toEqual(['2524'])
  })

  test('multiple url', ({ expect }) => {
    const result = service.getWorkPackageIdsFromIntegration(
      `https://task.miraistudio.games/projects/infrastructure/work_packages/2524/activity

      https://task.miraistudio.games/projects/infrastructure/work_packages/2525/activity
      `
    )
    expect(result).toEqual(['2524', '2525'])
  })

  test('mixed url', ({ expect }) => {
    const result = service.getWorkPackageIdsFromIntegration(
      `https://task.miraistudio.games/projects/infrastructure/work_packages/2524/activity
      OP#100
      https://task.miraistudio.games/projects/infrastructure/work_packages/2525/activity
      OP#101OP#102
      `
    )
    expect(result).toEqual(['2524', '100', '2525', '101', '102'])
  })
})

test.group('Webhook open project task move job', () => {
  test('Webhook open project task move job success', async ({ expect }) => {
    const { default: Job } = await import('#jobs/webhook_open_project_task_move_job')
    const job = await app.container.make(Job, [{}])
    expect(() =>
      job.handle({
        event: pullRequestOpenedEvent as any,
        actionId: '3',
        webhookId: 'Notify and Task Auto',
      })
    ).not.toThrowError()
  })
})
