import { test } from '@japa/runner'

import { UserFactory } from '#database/factories/user_factory'
import { ViewPresetFactory } from '#database/factories/view_preset_factory'
import ViewPreset from '#models/view_preset'

const pageId = 'dashboard.games.campaign_metrics.index'

// TODO: Add authorization tests
test.group('ViewPresetResolver.index', () => {
  test('return view preset list of the user', async ({ expect, graph }) => {
    const manager = await UserFactory.apply('all:manage').create()

    await ViewPresetFactory.merge({ pageId }).with('user').createMany(2)
    await ViewPresetFactory.merge({ pageId, userId: manager.id }).create()

    const res = await graph
      .loginAs(manager)
      .query(
        `query q($pageId: String!) {
          viewPresets(where: { pageId: $pageId }) {
            collection {
              id
              name
              attributes {
                name
                isCohort
                cohortDays
              }
            }
          }
        }`
      )
      .vars({
        pageId,
      })
      .send()

    expect(res).toBeSuccessfulGraphQLOperation()
    expect(res.body().data.viewPresets).toMatchSchema({
      type: 'object',
      properties: {
        collection: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'number',
              },
              name: {
                type: 'string',
              },
              attributes: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: {
                      type: 'string',
                    },
                    isCohort: {
                      type: 'boolean',
                    },
                    cohortDays: {
                      type: 'array',
                      items: {
                        type: 'number',
                      },
                    },
                  },
                  required: ['name', 'isCohort', 'cohortDays'],
                },
              },
            },
            required: ['id', 'name', 'attributes'],
          },
        },
      },
      required: ['collection'],
    })
  })
})

test.group('ViewPresetResolver.store', () => {
  test('create a new view preset', async ({ expect, graph }) => {
    const manager = await UserFactory.apply('all:manage').create()

    const res = await graph
      .loginAs(manager)
      .mutate(
        `mutation m($form: CreateViewPresetForm!) {
          createViewPreset(form: $form) {
            id
            name
            attributes {
              name
              isCohort
              cohortDays
            }
          }
        }`
      )
      .vars({
        form: {
          name: 'My preset',
          pageId,
          attributes: [
            {
              name: 'adRevGrossAmount',
              cohortDays: [],
            },
            {
              name: 'adRevNthDayGrossAmounts',
              cohortDays: [1, 2, 3],
            },
          ],
        },
      })
      .send()

    expect(res).toBeSuccessfulGraphQLOperation()
    expect(res.body().data.createViewPreset).toMatchSchema({
      type: 'object',
      properties: {
        id: {
          type: 'number',
        },
        name: {
          type: 'string',
        },
        attributes: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
              },
              isCohort: {
                type: 'boolean',
              },
              cohortDays: {
                type: 'array',
                items: {
                  type: 'number',
                },
              },
            },
            required: ['name', 'isCohort', 'cohortDays'],
          },
        },
      },
      required: ['id', 'name', 'attributes'],
    })

    expect(
      await ViewPreset.query().where('userId', manager.id).where('pageId', pageId).select('id')
    ).toBeArrayOfSize(1)
  })
})

test.group('ViewPresetResolver.destroy', () => {
  test('delete single or multiple view presets', async ({ expect, graph }) => {
    const manager = await UserFactory.apply('all:manage').create()

    const viewPresets = await ViewPresetFactory.merge({ userId: manager.id, pageId }).createMany(3)
    await ViewPresetFactory.merge({ pageId }).with('user').createMany(2)

    const res = await graph
      .loginAs(manager)
      .mutate(
        `mutation m($where: DeleteViewPresetsWhere!) {
          deleteViewPresets(where: $where)
        }`
      )
      .vars({
        where: {
          ids: viewPresets.slice(0, 2).map((vp) => vp.id),
        },
      })
      .send()

    expect(res).toBeSuccessfulGraphQLOperation()

    expect(
      await ViewPreset.query().where('userId', manager.id).where('pageId', pageId).select('id')
    ).toBeArrayOfSize(1)
  })
})
