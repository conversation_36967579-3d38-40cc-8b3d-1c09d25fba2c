import { test } from '@japa/runner'

import { NetworkFactory } from '#database/factories/network_factory'
import User from '#models/user'
import { UserFactory } from '#database/factories/user_factory'

test.group('NetworkResolver.index', (group) => {
  let manager: User

  group.each.setup(async () => {
    await NetworkFactory.createMany(3)
    manager = await UserFactory.apply('all:manage').create()
  })

  test('query success', async ({ expect, graph }) => {
    const res = await graph
      .loginAs(manager)
      .query(
        `query {
          networks {
            collection {
              id
              name
            }
          }
        }`
      )
      .send()

    expect(res).toBeSuccessfulGraphQLOperation()
  })
})
