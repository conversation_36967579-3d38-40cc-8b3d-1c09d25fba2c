import { test } from '@japa/runner'

import { FirebaseVersionVariantFactory } from '#database/factories/firebase_version_variant_factory'
import User from '#models/user'
import { UserFactory } from '#database/factories/user_factory'
import Game from '#models/game'
import { GameFactory } from '#database/factories/game_factory'

test.group('FirebaseExperimentResolver.index', (group) => {
  let manager: User
  let game: Game

  group.each.setup(async () => {
    game = await GameFactory.create()
    await FirebaseVersionVariantFactory.merge({ gameId: game.id }).createMany(3)
    manager = await UserFactory.apply('all:manage').create()
  })

  test('query success', async ({ expect, graph }) => {
    const res = await graph
      .loginAs(manager)
      .query(
        `query($gameId: ID!) {
          firebaseExperiments(where: { gameId: $gameId }) {
            collection {
              name
            }
          }
        }`
      )
      .vars({
        gameId: game.id,
      })
      .send()

    expect(res).toBeSuccessfulGraphQLOperation()
  })
})
