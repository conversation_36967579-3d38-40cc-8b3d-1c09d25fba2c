import { test } from '@japa/runner'

import User from '#models/user'
import { AdAgencyFactory } from '#database/factories/ad_agency_factory'
import AdAgency from '#models/ad_agency'
import { UserFactory } from '#database/factories/user_factory'
import { AdNetworkInputType } from '#config/enums'
import { GameSpendFactory } from '#database/factories/game_spend_factory'

test.group('Resolvers game cost', (group) => {
  let manager: User
  let autoAgency: AdAgency
  let manualAgency: AdAgency

  group.each.setup(async () => {
    manager = await UserFactory.apply('all:manage').create()
    autoAgency = await AdAgencyFactory.merge({ inputType: AdNetworkInputType.Auto }).create()
    manualAgency = await AdAgencyFactory.merge({ inputType: AdNetworkInputType.Manual }).create()
  })

  test('reject auto agency update', async ({ expect, graph }) => {
    const cost = await GameSpendFactory.with('game')
      .merge({
        networkId: autoAgency.id,
      })
      .create()

    const res = await graph
      .loginAs(manager)
      .mutate(
        `mutation($id: ID!) {
          updateGameCost(where: { id: $id }, form: { preTaxAmount: 100 }) {
            id
          }
        }`
      )
      .vars({ id: cost.id })
      .send()

    expect(res.status).toBe(403)
  })

  test('update cost for manual network', async ({ expect, graph }) => {
    const cost = await GameSpendFactory.with('game')
      .merge({
        networkId: manualAgency.id,
      })
      .create()

    const res = await graph
      .loginAs(manager)
      .mutate(
        `mutation($id: ID!) {
          updateGameCost(where: { id: $id }, form: { preTaxAmount: 100 }) {
            id
          }
        }`
      )
      .vars({ id: cost.id })
      .send()

    expect(res.status).toBe(200)
  })
})
