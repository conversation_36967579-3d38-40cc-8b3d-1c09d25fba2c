import { test } from '@japa/runner'

import { MediationFactory } from '#database/factories/mediation_factory'
import User from '#models/user'
import { UserFactory } from '#database/factories/user_factory'

test.group('MediationResolver.index', (group) => {
  let manager: User

  group.each.setup(async () => {
    await MediationFactory.createMany(3)
    manager = await UserFactory.apply('all:manage').create()
  })

  test('query success', async ({ expect, graph }) => {
    const res = await graph
      .loginAs(manager)
      .query(
        `query {
          mediations {
            collection {
              id
              name
            }
          }
        }`
      )
      .send()

    expect(res).toBeSuccessfulGraphQLOperation()
  })
})
