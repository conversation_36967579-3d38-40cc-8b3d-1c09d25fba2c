import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

import { test } from '@japa/runner'

test.group('Webhooks github', () => {
  test('pull_request_opened', async ({ expect, client }) => {
    const response = await client
      .post('/webhooks/github')
      .json(
        JSON.parse(
          fs
            .readFileSync(
              path.join(
                path.dirname(fileURLToPath(import.meta.url)),
                'fixtures',
                'pull_request_opened.json'
              )
            )
            .toString()
        )
      )

    expect(response).toHaveHttpStatus(200)
  }).skip()

  test('pull_request_review_requested', async ({ expect, client }) => {
    const response = await client
      .post('/webhooks/github')
      .json(
        JSON.parse(
          fs
            .readFileSync(
              path.join(
                path.dirname(fileURLToPath(import.meta.url)),
                'fixtures',
                'pull_request_review_requested.json'
              )
            )
            .toString()
        )
      )

    expect(response).toHaveHttpStatus(200)
  }).skip()
})
