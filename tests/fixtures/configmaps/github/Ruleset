Ruleset Id,Available Merge Strategy,Default Branch,Environment,Branch,Rule,Rule Value
Main Trunk Based,SQUASH,main,Production,main,APPROVAL_REQUIRED,0
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,deploy,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
Develop Trunk Based,SQUASH,develop,Production,develop,APPROVAL_REQUIRED,0
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,deploy,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
2-Stage Trunk Based,SQUASH,develop,Production,main,APPROVAL_REQUIRED,5
,,,Test,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop,APPROVAL_REQUIRED,0
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,deploy,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
3-Stage Trunk Based,SQUASH,develop,Production,main,APPROVAL_REQUIRED,5
,,,Staging,,APPROVAL_DISMISS,TRUE
,,,Test,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,staging,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop,APPROVAL_REQUIRED,0
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,deploy,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
Unity Trunk Based,SQUASH,develop,Production,develop,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop-ios,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop_refactor,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop_mode_1,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop_mode_2,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop_mode_3,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop_mode_4,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
2-Stage Trunk Based Review Required,SQUASH,develop,Production,main,APPROVAL_REQUIRED,5
,,,Test,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
3-Stage Trunk Based Review Required,SQUASH,develop,Production,main,APPROVAL_REQUIRED,5
,,,Staging,,APPROVAL_DISMISS,TRUE
,,,Test,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,staging,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,develop,APPROVAL_REQUIRED,1
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,deploy,APPROVAL_REQUIRED,5
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
Config,SQUASH,develop,,builder,APPROVAL_REQUIRED,0
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE
,,,,release/*,APPROVAL_REQUIRED,0
,,,,,APPROVAL_DISMISS,TRUE
,,,,,CODE_OWNERS,TRUE
,,,,,ADMIN_BYPASS,TRUE