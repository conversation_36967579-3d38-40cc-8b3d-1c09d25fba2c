Member Id,Permission,Role Id,Team Id,Protected Branch Rule Set Id,Webhook Action Type
Chu <PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,base-common,2-Stage Trunk Based,DISCORD_NOTIFY
Đỗ Đình Tuân,Read,Base,common-team,2-Stage Trunk Based Review Required,OP_TASK_MOVE
Lê <PERSON>,Write,Common Team All,github-actions,3-Stage Trunk Based,
<PERSON><PERSON><PERSON>,Maintain,GitHub Actions Maintainer,Hyper - All in Team,3-Stage Trunk Based Review Required,
Vũ Văn <PERSON>,Admin,Hyper - All in Team,infra,Config,
Nguyễ<PERSON>,,Infra,Lead,Develop Trunk Based,
Nguyễn <PERSON>,,Intern,mirai,Main Trunk Based,
<PERSON>uy<PERSON>n <PERSON>,,Lead,mkt-creative,Unity Trunk Based,
Trần Văn <PERSON>,,Legacy Projects,mkt-trending,,
<PERSON>r<PERSON><PERSON>,,Marketing Creative All,Moolah Team,,
<PERSON><PERSON>,,<PERSON><PERSON>,old-game,,
<PERSON><PERSON><PERSON>,,<PERSON><PERSON><PERSON>,server,,
<PERSON><PERSON>,,Moolah Team,studio,,
<PERSON>uy<PERSON><PERSON>,,Old Game All,tool<PERSON><PERSON>,,
<PERSON><PERSON>,,<PERSON>,training-tr2,,
Đỗ Th<PERSON> Tú Uy<PERSON>n,,Studio All,unity-intern,,
<PERSON>uy<PERSON>n <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>,,<PERSON><PERSON><PERSON>,,,
<PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON>n,,<PERSON> <PERSON><PERSON>-2,,,
<PERSON>ù<PERSON> <PERSON>,,,,,
<PERSON>uy<PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>,,,,,
<PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>,,,,,
<PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>,,,,,
Ngố Tiến Dũng,,,,,
Dương Tiến Cường,,,,,
Hoàng Xuân Vinh,,,,,
Lương Việt Thắng,,,,,
Phạm Quý Tuấn Anh,,,,,
Phạm Gia Phong,,,,,
Lê Quang Huy,,,,,