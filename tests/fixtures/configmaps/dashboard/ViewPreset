Page Id,Attribute,Substitute Attribute Template,Cohor<PERSON>,Cohort Day,Section
dashboard.games.campaign_metrics.index,adRevGrossAmount,,FALSE,,ua
,adRevNthDayGrossAmounts,adRevGrossAmountDay{0},TRUE,1-365,ua
,adCostNonTaxAmount,,FALSE,,ua
,impressionCount,,FALSE,,ua
,clickCount,,FALSE,,ua
,installCount,,FALSE,,ua
,roas,,FALSE,,ua
,roasNthDayRates,roasDay{0},TRUE,1-365,ua
,cpi,,FALSE,,ua
,ctr,,FALSE,,ua
,cvr,,FALSE,,ua
,cpc,,FALSE,,ua
,ipm,,FALSE,,ua
,sessionCount,,FALSE,,product
,sessionNthDayCounts,sessionCountDay{0},TRUE,1-180,product
,dailyActiveUserCount,,FALSE,,product
,activeUserNthDayCounts,activeUserDay{0},TRUE,1-180,product
,retentionRate,,FALSE,,product
,retentionNthDayRates,retentionRateDay{0},TRUE,1-180,product
dashboard.games.firebase_experiments.index,dailyActiveUserCount,,FALSE,,general
,activeUserNthDayCounts,activeUserDay{0},TRUE,1-365,general
,sessionCount,,FALSE,,general
,sessionNthDayCounts,sessionCountDay{0},TRUE,1-365,general
,playtimeMsec,,FALSE,,general
,playtimeNthDayMsecs,playtimeMsecDay{0},TRUE,1-365,general
,installCount,,FALSE,,general
,sessionCountPerActiveUser,,FALSE,,general
,retentionRate,,FALSE,,general
,retentionNthDayRates,retentionRateDay{0},TRUE,1-365,general
,impressionNthDayCounts,impressionCountDay{0},TRUE,1-365,general
,adRevNthDayGrossAmounts,adRevGrossAmountDay{0},TRUE,1-365,general
,ad_adRevGrossAmount,,FALSE,,general
,ad_impressionCount,,FALSE,,general
,ad_adRevGrossAmountPerActiveUser,,FALSE,,general
,ad_impressionCountPerActiveUser,,FALSE,,general
dashboard.games.release_metrics.index,installCount,,FALSE,,general
,dailyActiveUserCount,,FALSE,,general
,activeUserNthDayCounts,activeUserDay{0},TRUE,1-365,general
,sessionCountPerActiveUser,,FALSE,,general
,sessionNthDayCounts,sessionCountDay{0},TRUE,1-365,general
,playtimeMsec,,FALSE,,general
,playtimeNthDayMsecs,playtimeMsecDay{0},TRUE,1-365,general
,retentionNthDayRates,retentionRateDay{0},TRUE,1-365,general
,adRevGrossAmountPerActiveUser,,FALSE,,general
,adRevGrossAmount,,FALSE,,general
,adRevNthDayGrossAmounts,adRevGrossAmountDay{0},TRUE,1-365,general
,lifetimeNthDayValues,lifetimeValueDay{0},TRUE,1-365,general
,impressionCountPerActiveUser,,FALSE,,general
,impressionCount,,FALSE,,general
,impressionNthDayCounts,impressionCountDay{0},TRUE,1-365,general
,ad_adRevGrossAmountPerActiveUser,,FALSE,,general
,ad_adRevGrossAmount,,FALSE,,general
,ad_impressionCount,,FALSE,,general
,ad_impressionCountPerActiveUser,,FALSE,,general