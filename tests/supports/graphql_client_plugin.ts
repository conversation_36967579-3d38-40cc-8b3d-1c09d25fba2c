import { TestContext } from '@japa/runner/core'
import { PluginFn } from '@japa/runner/types'
import { ApiClient } from '@mirai-game-studio/adonis-testing-sdk'

import User from '#models/user'

export function graphqlClient(): PluginFn {
  return function () {
    TestContext.getter('graph', function (this: TestContext) {
      return new GraphqlClient(this.client, this.expect)
    })
  }
}

class GraphqlClient {
  #query: string | undefined
  #vars: any = {}
  #user: User = null!

  constructor(
    private client: ApiClient,
    private expect: TestContext['expect']
  ) {}

  query(query: string) {
    this.#query = query
    return this
  }

  mutate(mutation: string) {
    this.#query = mutation
    return this
  }

  vars(vars: any) {
    this.#vars = vars
    return this
  }

  loginAs(user: User) {
    this.#user = user
    return this
  }

  async send() {
    this.expect(this.#user).not.toBeNull()
    this.expect(this.#query).not.toBeEmpty()

    const res = await this.client.post('/graphql').withGuard('web').loginAs(this.#user).json({
      query: this.#query,
      variables: this.#vars,
    })

    return res
  }
}

declare module '@japa/runner/core' {
  interface TestContext {
    graph: GraphqlClient
  }
}
