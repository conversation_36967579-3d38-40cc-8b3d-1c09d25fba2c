import app from '@adonisjs/core/services/app'
import testUtils from '@adonisjs/core/services/test_utils'
import { authApiClient } from '@adonisjs/auth/plugins/api_client'
import { sessionApiClient } from '@adonisjs/session/plugins/api_client'
import { shieldApiClient } from '@adonisjs/shield/plugins/api_client'
import {
  expect,
  assert,
  Config,
  pluginAdonisJS,
  apiClient,
} from '@mirai-game-studio/adonis-testing-sdk'

import { graphqlClient } from './supports/graphql_client_plugin.js'

export const plugins: Config['plugins'] = [
  assert(),
  expect(),
  pluginAdonisJS(app),
  apiClient(),
  sessionApiClient(app),
  authApiClient(app),
  shieldApiClient(),
  graphqlClient(),
]

export const runnerHooks: Required<Pick<Config, 'setup' | 'teardown'>> = {
  setup: [
    () => testUtils.db('postgres').truncate(),
    () => testUtils.db('dataWarehouse').truncate(),
  ],
  teardown: [
    async () => {
      const queue = await app.container.make('queue')
      await Promise.all(queue.list().map((q) => q.disconnect())).catch(() => {})
    },
  ],
}

export const configureSuite: Config['configureSuite'] = (suite) => {
  if (['browser', 'functional', 'e2e'].includes(suite.name)) {
    return suite.setup(() => testUtils.httpServer().start())
  }
}
