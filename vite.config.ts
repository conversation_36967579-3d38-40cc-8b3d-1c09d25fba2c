import { defineConfig } from 'vite'
import adonisjs from '@adonisjs/vite/client'
import react from '@vitejs/plugin-react'
import { globSync } from 'glob'
import inertia from '@adonisjs/inertia/client'
import tailwindcss from '@tailwindcss/vite'
import config from 'config'

export default defineConfig(({ mode }) => ({
  plugins: [
    adonisjs({
      entrypoints: [
        'resources/js/inertia.tsx',
        'resources/js/next/inertia.tsx',
        'resources/js/app.js',
        ...globSync('resources/js/pages/**/*.tsx', {
          nodir: true,
          ignore: ['resources/js/pages/**/_*'],
        }),
      ],

      reload: ['resources/views/**/*.edge'],

      assetsUrl: config.get('static').cdn.url,
    }),
    react(),
    tailwindcss(),
    inertia({
      ssr: {
        enabled: false,
      },
    }),
  ],
  server: {},
  resolve: {
    alias: [
      { find: '@', replacement: '/resources/js' },
      { find: '@/next', replacement: '/resources/js/next' },
      { find: '#stories', replacement: '/resources/stories' },
    ],
  },
  esbuild: {
    drop: mode === 'production' ? ['console', 'debugger'] : [],
  },
}))
