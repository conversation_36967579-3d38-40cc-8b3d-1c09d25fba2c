import { COLORS } from '@/components/color'
import { DateRange, FilterActions, FilterContainer, FilterGroup, Select } from '@/components/form'
import {
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { PartnerLayout } from '@/components/layout/partner_layout'
import { isLoading, Loading } from '@/components/loading'
import { createParamsStore, routes, useNavigate } from '@/components/location'
import { DataGrid } from '@/components/table'
import { useGraphql } from '@/components/toolkit-api'
import { graphql } from '@/gql'
import { InferPageProps } from '@adonisjs/inertia/types'
import { Box } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import dayjs from 'dayjs'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { mixed, number, object, string } from 'yup'
import type PartnerGameMetricsController from '#controllers/partner/game_metrics_controller'
import { useServerProp } from '@/components/ssr'
import { Head } from '@inertiajs/react'
import { SwitchButton } from '@/components/icons/switch_button'
import {
  PageInfoAttributesFragment,
  PartnerGameMetricAggregationAttributesFragment,
  PartnerGameMetricAttributesFragment,
} from '@/graphql'
import { formatter } from '#utils/formatter'
import { formatDuration, formatNumber, formatPercentage } from '@/components/typography'
import { isNil, sumBy } from 'lodash-es'
import { safeDivide } from '#utils/math'
import { DataSource } from '#graphql/main'

const { useParam, usePagination, useSetParams } = createParamsStore(
  routes.partner.games.metrics(':gameId'),
  {
    parse: object({
      source: string().oneOf(Object.values(DataSource)).default(DataSource.Snapshot),
      gameId: string().required(),
      from: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs().subtract(1, 'month') }),
      to: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs() }),
      page: number().default(1),
      perPage: number().default(200),
    }),
    stringify: object({
      from: mixed().stringifyDate(),
      to: mixed().stringifyDate(),
      page: number(),
      perPage: number(),
      source: string(),
    }),
  }
)

graphql(`
  fragment PartnerGameMetricAttributes on V2GameMetric {
    date
    totalInstalls
    cost
    roas
    revenue
    profit
    retentionRateDay1
    retentionRateDay3
    retentionRateDay7
    sessions
    playtime

    adPerformances {
      adType
      impressionCount
      dailyActiveUserCount
    }
  }

  fragment PartnerGameMetricAggregationAttributes on V2GameMetric {
    totalInstalls
    cost
    roas
    revenue
    profit
  }
`)

const query = graphql(`
  query PartnerGameMetricIndex_GetInitialData(
    $gameId: ID!
    $from: Date!
    $to: Date!
    $perPage: Int!
    $page: Int!
    $source: DataSource
  ) {
    v2 {
      gameMetrics(
        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }
        offset: { perPage: $perPage, page: $page }
        source: $source
      ) {
        collection {
          ...PartnerGameMetricAttributes
        }
        pageInfo {
          ...PageInfoAttributes
        }
      }

      total: aggregateGameMetrics(
        id: "total"
        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }
        source: SNAPSHOT
      ) {
        ...PartnerGameMetricAggregationAttributes
      }
    }
  }
`)

type PageProps = InferPageProps<PartnerGameMetricsController, 'index'>

export default function Page() {
  const game = useServerProp<PageProps, PageProps['game']>((s) => s.game)
  const dateFrom = useParam((p) => p.from!)
  const dateTo = useParam((p) => p.to!)
  const source = useParam((p) => p.source)
  const page = useParam((p) => p.page)
  const perPage = useParam((p) => p.perPage)
  const canViewGameMetricV2 = useServerProp<PageProps, PageProps['canViewGameMetricV2']>(
    (s) => s.canViewGameMetricV2
  )
  const gameId = game.id

  const getInitialData = useGraphql(query, {
    variables: {
      from: dateFrom.format('YYYY-MM-DD'),
      to: dateTo.format('YYYY-MM-DD'),
      gameId: game.id,
      page,
      perPage,
      source,
    },
  })
  const setParams = useSetParams()
  const { refresh, navigate } = useNavigate()

  useBreadcrumbs(
    [
      { label: 'All Games', url: routes.partner.games.index() },
      {
        label: game.name,
        url: routes.partner.games.metrics(gameId),
      },
      'Game Metrics',
    ],
    'default'
  )

  const initialLoading = isLoading([getInitialData.error, getInitialData.loading])

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [filterRef, setFilterSize, filterSize] = useLayoutComponentRegistration('filter')
  const layoutHeight = useLayoutHeight(navbarSize, breadcrumbsSize, notificationSize, filterSize)
  // const layoutHeight = 200

  const form = useForm({
    defaultValues: {
      dateRange: [dateFrom, dateTo],
      source,
    },
  })

  useEffect(() => {
    setFilterSize({
      height: filterRef.current?.clientHeight ?? 0,
    })
  }, [filterRef, setFilterSize])

  return (
    <PartnerLayout>
      <Head title={`Game Metrics | ${game.name}`} />

      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <FilterContainer sx={{ pb: 2 }} ref={filterRef}>
          <FilterGroup label="DATE">
            <DateRange
              defaultValue={[dateFrom, dateTo]}
              onChange={(dates) => {
                if (dates[0] && dates[1]) {
                  form.setValue('dateRange', [dates[0], dates[1]])
                }
              }}
            />
          </FilterGroup>

          {canViewGameMetricV2 && (
            <FilterGroup label="DATA SOURCE">
              <Select
                defaultValue={source}
                options={[
                  { value: DataSource.Origin, label: 'INHOUSE' },
                  { value: DataSource.Snapshot, label: 'PARTNER' },
                ]}
                {...form.register('source')}
              />
            </FilterGroup>
          )}

          <FilterActions
            onReset={() => refresh({ query: {} })}
            onSubmit={form.handleSubmit((data) => {
              setParams({
                from: data.dateRange[0]!,
                to: data.dateRange[1]!,
                source: data.source,
              })
            })}
          />
        </FilterContainer>

        {canViewGameMetricV2 && (
          <SwitchButton
            tooltip="Switch to Inhouse view"
            onClick={() => navigate(routes.dash.gameMetricsNext.index(game.id))}
          />
        )}
      </Box>

      <Loading visible={initialLoading}>
        <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem` }}>
          <Table
            metrics={getInitialData.data?.v2?.gameMetrics?.collection!}
            total={getInitialData.data?.v2?.total!}
            pageInfo={getInitialData.data?.v2?.gameMetrics?.pageInfo!}
          />
        </Box>
      </Loading>
    </PartnerLayout>
  )
}

type Metric = PartnerGameMetricAttributesFragment

const impsDauGroups = [
  {
    adTypes: [3],
    header: 'Banner',
  },
  {
    adTypes: [4],
    header: 'Inter',
  },
  {
    adTypes: [1],
    header: 'Rewarded',
  },
  {
    adTypes: [2, 6],
    header: 'AOA',
  },
  {
    adTypes: [5, 10],
    header: 'MREC',
  },
  {
    adTypes: [14, 8, 12],
    header: 'Native',
  },
  {
    adTypes: [7],
    header: 'Collapsible',
  },

  {
    adTypes: [9],
    header: 'Adaptive Banner',
  },
  {
    adTypes: [11],
    header: 'Audio',
  },
]

function Table({
  metrics,
  total,
  pageInfo,
}: {
  metrics: Metric[]
  total: PartnerGameMetricAggregationAttributesFragment
  pageInfo: PageInfoAttributesFragment
}) {
  const pagination = usePagination(pageInfo)
  const rows = useMemo(() => [{ ...total, date: 'Total' } as Metric, ...metrics], [metrics, total])
  const columns = useMemo<GridColDef<Metric>[]>(() => {
    return [
      { field: 'date', headerName: 'Date' },
      {
        field: 'day',
        headerName: 'Day',
        valueGetter: (_, row) => row.date,
        valueFormatter: (value) => (dayjs(value).isValid() ? formatter.day(value) : ''),
      },
      {
        field: 'totalInstalls',
        headerName: 'Installs',
        headerClassName: 'bg-cyan-300',
        valueFormatter: formatNumber,
      },
      {
        field: 'cost',
        headerName: 'Cost',
        headerClassName: 'bg-cyan-300',
        valueFormatter: (value) => formatNumber(value, 4),
      },
      {
        field: 'roas',
        headerName: 'ROAS',
        headerClassName: 'bg-green-400',
        valueFormatter: formatPercentage,
      },
      {
        field: 'revenue',
        headerName: 'Revenue',
        headerClassName: 'bg-green-400',
        valueFormatter: formatNumber,
      },
      {
        field: 'profit',
        headerName: 'Profit',
        headerClassName: 'bg-green-400',
        valueFormatter: formatNumber,
      },
      ...impsDauGroups.map(
        ({ adTypes, header }): GridColDef<Metric> => ({
          field: `${header}.impsDau`,
          headerName: header,
          headerClassName: 'bg-orange-400',
          valueGetter: (_, row) => {
            const adPerformances = row.adPerformances?.filter((a) => adTypes.includes(a.adType))
            return (
              adPerformances &&
              safeDivide(
                sumBy(adPerformances, 'impressionCount'),
                sumBy(adPerformances, 'dailyActiveUserCount')
              )
            )
          },
          valueFormatter: (value) => (isNil(value) ? '' : formatNumber(value, 2)),
        })
      ),
      {
        field: 'retentionRateDay1',
        headerName: 'RR D-1',
        headerClassName: 'bg-orange-400',
        valueFormatter: (value) => (isNil(value) ? '' : formatPercentage(value)),
      },
      {
        field: 'retentionRateDay3',
        headerName: 'RR D-3',
        headerClassName: 'bg-orange-400',
        valueFormatter: (value) => (isNil(value) ? '' : formatPercentage(value)),
      },
      {
        field: 'retentionRateDay7',
        headerName: 'RR D-7',
        headerClassName: 'bg-orange-400',
        valueFormatter: (value) => (isNil(value) ? '' : formatPercentage(value)),
      },
      {
        field: 'sessions',
        headerName: 'Avg. Sessions',
        headerClassName: 'bg-orange-400',
        valueFormatter: (value) => (isNil(value) ? '' : formatNumber(value, 2)),
      },
      {
        field: 'playtime',
        headerName: 'Playtime (minutes)',
        headerClassName: 'bg-orange-400',
        valueFormatter: (value) => (isNil(value) ? '' : formatDuration(value)),
      },
    ]
  }, [])

  return (
    <DataGrid
      rows={rows}
      getRowId={(row) => row.date}
      pinnedRows={{
        top: [rows[0]],
      }}
      pinnedColumns={{
        left: ['date', 'day'],
      }}
      columns={columns}
      actionColumn={false}
      editable={false}
      slots={{ toolbar: null }}
      sx={{ ...COLORS }}
      pagination={pagination}
    />
  )
}
