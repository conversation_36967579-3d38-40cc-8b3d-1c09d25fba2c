import { useBreadcrumbs, useLayoutHeight, useLayoutSize } from '@/components/layout'
import { PartnerLayout } from '@/components/layout/partner_layout'
import { isLoading, Loading } from '@/components/loading'
import { DataGrid } from '@/components/table'
import { useGraphql } from '@/components/toolkit-api'
import { graphql } from '@/gql'
import { GameAttributesFragment } from '@/graphql'
import { Box, Link } from '@mui/material'
import { useMemo } from 'react'
import { GridColDef } from '@mui/x-data-grid-pro'
import { routes } from '@/components/location'
import { Platform } from '@/components/platform'

const query = graphql(`
  query PartnerGameIndex_GetInitData {
    games(where: { keyword: "" }, offset: { page: 1, perPage: 9999 }) {
      collection {
        ...GameAttributes
      }
    }
  }
`)

export default function Page() {
  const getInitData = useGraphql(query)
  const initLoading = isLoading([getInitData.loading, getInitData.error])

  useBreadcrumbs(['All Game'], 'default')

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const layoutHeight = useLayoutHeight(navbarSize, breadcrumbsSize, notificationSize)

  return (
    <PartnerLayout>
      <Loading visible={initLoading}>
        <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem` }}>
          <Table games={getInitData.data?.games?.collection!} />
        </Box>
      </Loading>
    </PartnerLayout>
  )
}

function Table({ games }: { games: GameAttributesFragment[] }) {
  const columns = useMemo<GridColDef<GameAttributesFragment>[]>(() => {
    return [
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
        align: 'left',
        renderCell: ({ row, value }) => {
          return (
            <Link
              sx={{ display: 'block' }}
              underline="hover"
              href={routes.partner.games.metrics(row.id)}
            >
              <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
                <Platform value={row.platform} />
                <Box component="span" sx={{ pl: 1 }}>
                  {value}
                </Box>
              </Box>
            </Link>
          )
        },
      },
    ]
  }, [])

  return (
    <DataGrid
      rows={games}
      getRowId={(g) => g.id}
      columns={columns}
      editable={false}
      actionColumn={false}
      slots={{ toolbar: null }}
      hideFooter
      sx={{
        [`& .MuiDataGrid-cell, & .MuiDataGrid-columnHeaderTitle`]: { textAlign: 'left!important' },
        [`& .MuiDataGrid-columnHeaderTitle`]: { paddingY: 2 },
      }}
    />
  )
}
