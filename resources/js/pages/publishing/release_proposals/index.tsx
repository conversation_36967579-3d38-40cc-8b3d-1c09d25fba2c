import { useTranslation } from 'react-i18next'
import { thru } from 'lodash-es'
import { useEffect, useMemo, useState } from 'react'
import { GridActionsCellItem, GridColDef } from '@mui/x-data-grid-pro'
import { Badge, Box, ButtonBase, Chip, colors, MenuItem, Select, Typography } from '@mui/material'
import OpenInNewIcon from '@mui/icons-material/OpenInNew'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import classNames from 'classnames'

import { AdminLayout, useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import { useConfigMaps } from '@/components/configmaps'
import { Loading } from '@/components/loading'
import { useApproveReleaseProposal, useListReleaseProposal } from '@/components/toolkit-api'
import { PaginationState, usePagination } from '@/components/pagination'
import { routes, useParams } from '@/components/location'
import { GamePlatform, GameReleaseProposalStatus, GameStore } from '#config/enums'
import PublisherConfigMap from '#configmaps/publishing/publisher'
import GameReleaseProposal from '#models/game_release_proposal'
import { CenterCell, DataGrid } from '@/components/table'
import { Platform } from '@/components/platform'
import { Modal, ModalBody, ModalHeader, useModal } from '@/components/modal'
import { useNavigate } from '@/components/ssr'
import { enumValues } from '#utils/pure'
import {
  FilterActions,
  FilterContainer,
  FilterGroup,
  Select as FormSelect,
} from '@/components/form'

render(PageWithLayout)

function PageWithLayout() {
  const { t } = useTranslation()

  useBreadcrumbs([t('releaseProposals.listTitle')])

  return (
    <AdminLayout>
      <Page />
    </AdminLayout>
  )
}

function Page() {
  const { t } = useTranslation()

  const pagination = usePagination()
  const params = useParams<{ status: GameReleaseProposalStatus[] }>()
  const { navigate } = useNavigate()

  const statusIn = thru(params.status, (v) => [v].flat() || [GameReleaseProposalStatus.Pending])

  const form = useForm({ defaultValues: { status: statusIn } })

  const publisherConfigMapCollection = useConfigMaps('cmap.pl.publisher')
  const listProposal = useListReleaseProposal({
    page: pagination.page,
    perPage: pagination.perPage,
    status: statusIn,
  })

  useEffect(() => {
    if (listProposal.data) {
      pagination.setFromMeta(listProposal.data!.meta)
    }
  }, [listProposal.data])

  if (publisherConfigMapCollection.isLoading || listProposal.isLoading) {
    return <Loading visible />
  }

  return (
    <Box sx={{ height: `calc(100vh - 150px)` }}>
      <FilterContainer
        sx={{ mb: 1 }}
        component="form"
        onSubmit={form.handleSubmit((values) => {
          navigate('', { status: values.status }, true)
        })}
      >
        <FilterGroup label="STATUS">
          <FormSelect
            options={enumValues(GameReleaseProposalStatus).map((v) => ({
              value: v.toString(),
              label: t(`releaseProposals.status.${v}`),
            }))}
            multiple
            defaultValue={statusIn}
            hint="Select status"
            {...form.register('status')}
          />
        </FilterGroup>
        <FilterActions
          onReset={() => {
            navigate('', {}, false)
          }}
        />
      </FilterContainer>

      <GameReleaseProposalTable
        pagination={pagination}
        proposals={listProposal.data!.data}
        publishers={publisherConfigMapCollection.data!}
      />
    </Box>
  )
}

function GameReleaseProposalTable({
  publishers,
  proposals,
  pagination,
}: {
  publishers: PublisherConfigMap[]
  proposals: GameReleaseProposal[]
  pagination: PaginationState
}) {
  const { t } = useTranslation()
  const { navigate } = useNavigate()
  const control = useModal({ opened: false })
  const [proposal, setProposal] = useState<GameReleaseProposal | null>(null)
  const approve = useApproveReleaseProposal()
  const form = useForm<{ proposalToPublisherId: Record<string, GameStore> }>({
    defaultValues: { proposalToPublisherId: {} },
  })

  const columns = useMemo<GridColDef<GameReleaseProposal>[]>(() => {
    return [
      {
        field: 'repository',
        headerName: t('releaseProposals.table.repository'),
        width: 250,
        renderCell: ({ row }) => (
          <CenterCell
            alignItems="center"
            onClick={() => navigate(routes.publising.approvals.index(row.id))}
          >
            <Platform value={row.platform} />
            <Box>{row.repository}</Box>
          </CenterCell>
        ),
      },
      {
        field: 'publisher',
        headerName: 'Publisher',
        width: 250,
        renderCell: ({ row }) => (
          <Select
            defaultValue={publishers[0]?.id}
            fullWidth
            size="small"
            variant="standard"
            sx={{ my: 1 }}
            {...form.register(`proposalToPublisherId.${row.id}`)}
          >
            {publishers.map((p) => (
              <MenuItem key={p.id} value={p.id}>
                {p.name}
              </MenuItem>
            ))}
          </Select>
        ),
      },
      {
        field: 'version',
        headerName: 'Version',
        width: 200,
        renderCell: ({ row }) => (
          <CenterCell>
            <ButtonBase
              onClick={(e) => {
                setProposal(row)
                control.onOpen(e)
              }}
            >
              <Badge
                variant="standard"
                badgeContent={<OpenInNewIcon sx={{ fontSize: '1rem', ml: 2, mt: 1 }} />}
              >
                {row.extra.platform === GamePlatform.Android ? (
                  <Typography>
                    {row.semver} ({row.extra.versionCode})
                  </Typography>
                ) : null}
              </Badge>
            </ButtonBase>
          </CenterCell>
        ),
      },
      {
        field: 'status',
        headerName: 'Last Status',
        width: 150,
        valueFormatter: (value: number) => t(`releaseProposals.status.${value}`),
        renderCell: ({ formattedValue, value }) => {
          return (
            <CenterCell>
              <Chip
                label={formattedValue}
                color={
                  (classNames({
                    error: value === GameReleaseProposalStatus.Rejected,
                    success: value === GameReleaseProposalStatus.Approved,
                  }) as any) || undefined
                }
                size="small"
              />
            </CenterCell>
          )
        },
      },
    ]
  }, [t])

  return (
    <>
      <DataGrid
        rows={proposals}
        pagination={pagination}
        editable={false}
        getRowHeight={() => 'auto'}
        columns={columns}
        actionColumn={{
          headerName: 'Approve / Reject',
          width: 200,
          getActions: ({ row }) => {
            return [
              <GridActionsCellItem
                icon={<CheckIcon />}
                label="Approve"
                sx={{ color: colors.green[400] }}
                onClick={() =>
                  approve.mutateAsync({
                    proposalId: row.id,
                    publisherId: form.getValues().proposalToPublisherId[row.id.toString()],
                    status: GameReleaseProposalStatus.Approved,
                  })
                }
              />,
              <GridActionsCellItem
                icon={<CloseIcon />}
                label="Reject"
                sx={{ color: colors.red[600] }}
                onClick={() =>
                  approve.mutateAsync({
                    proposalId: row.id,
                    publisherId: form.getValues().proposalToPublisherId[row.id.toString()],
                    status: GameReleaseProposalStatus.Rejected,
                  })
                }
              />,
            ]
          },
        }}
      />

      {proposal && (
        <Modal control={control}>
          <ModalHeader>Changelog</ModalHeader>
          <ModalBody>
            <Typography>{proposal.changelog}</Typography>
          </ModalBody>
        </Modal>
      )}
    </>
  )
}
