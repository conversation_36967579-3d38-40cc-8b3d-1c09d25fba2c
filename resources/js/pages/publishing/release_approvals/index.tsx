import { Box, colors } from '@mui/material'
import { useEffect, useMemo } from 'react'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useTranslation } from 'react-i18next'
import { ConfigMapCollection } from '@munkit/main'
import classNames from 'classnames'

import { AdminLayout, useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import { routes, useParams } from '@/components/location'
import { useListReleaseApproval } from '@/components/toolkit-api'
import { PaginationState, usePagination } from '@/components/pagination'
import { Loading } from '@/components/loading'
import { CenterCell, DataGrid } from '@/components/table'
import GameReleaseApproval from '#models/game_release_approval'
import GameReleaseProposal from '#models/game_release_proposal'
import { useConfigMaps } from '@/components/configmaps'
import PublisherConfigMap from '#configmaps/publishing/publisher'
import { GameReleaseStatus } from '#config/enums'

render(PageWithLayout)

function PageWithLayout() {
  const { proposalId } = useParams<{ proposalId: number }>(
    routes.publising.approvals.index(':proposalId')
  )
  const pagination = usePagination()
  const listApproval = useListReleaseApproval(proposalId, pagination)

  const publisherConfigMapCollection = useConfigMaps('cmap.pl.publisher')

  useEffect(() => {
    if (listApproval.data) {
      pagination.setFromMeta(listApproval.data.meta)
    }
  }, [listApproval.data])

  const isLoading = listApproval.isLoading || publisherConfigMapCollection.isLoading

  return (
    <AdminLayout>
      {isLoading ? (
        <Loading visible />
      ) : (
        <Page
          approvals={listApproval.data!.data}
          proposal={listApproval.data!.meta.proposal}
          pagination={pagination}
          publisherConfigMapCollection={publisherConfigMapCollection.data!}
        />
      )}
    </AdminLayout>
  )
}

function Page({
  approvals,
  proposal,
  pagination,
  publisherConfigMapCollection,
}: {
  approvals: GameReleaseApproval[]
  proposal: GameReleaseProposal
  pagination: PaginationState
  publisherConfigMapCollection: ConfigMapCollection<PublisherConfigMap>
}) {
  const { t } = useTranslation()

  useBreadcrumbs([
    {
      url: routes.publising.proposals.index(),
      label: `Publish Requests`,
    },
    `${proposal.repository} Approval History`,
  ])

  const columns = useMemo<GridColDef<GameReleaseApproval>[]>(() => {
    return [
      {
        field: 'repository',
        headerName: 'Repository',
        valueGetter: () => proposal.repository,
      },
      {
        field: 'publisher.name',
        headerName: 'Publisher',
        width: 200,
        valueGetter: (_, row) =>
          publisherConfigMapCollection.find((e) => e.id === row.publisherId)!.name,
      },
      {
        field: 'releaseStatus',
        headerName: 'Job Progress',
        valueFormatter: (value: number) => t(`releaseApproval.releaseStatus.${value}`),
        renderCell: ({ formattedValue, value }) => (
          <Box
            sx={{
              color:
                classNames({
                  [colors.red[400]]: value === GameReleaseStatus.Error,
                  [colors.green[600]]: value === GameReleaseStatus.Completed,
                  [colors.blue[600]]: value === GameReleaseStatus.InProgress,
                }) || undefined,
            }}
          >
            {formattedValue}
          </Box>
        ),
      },
      {
        field: 'reviewer.fullName',
        headerName: 'Reviewer',
        width: 200,
        valueGetter: (_, row) => row.reviewer.fullName,
      },
      {
        field: 'build',
        headerName: 'Build',
        width: 150,
        valueGetter: (_, row) => row.jobId,
        renderCell: ({ row, formattedValue }) => (
          <CenterCell>
            <a href={routes.publising.uploads.show(row.jobId)} target="_blank" rel="noopen">
              {formattedValue}
            </a>
          </CenterCell>
        ),
      },
      {
        field: 'log',
        headerName: 'Last logs',
        width: 300,
        valueGetter: (_, row) =>
          row.releaseStatus === GameReleaseStatus.Completed ? 'Success' : 'TBA',
      },
    ]
  }, [proposal, t, publisherConfigMapCollection])

  return (
    <>
      <Box sx={{ height: 'calc(100vh - 155px)' }}>
        <DataGrid
          rows={approvals}
          columns={columns}
          pagination={pagination}
          actionColumn={false}
          editable={false}
        />
      </Box>
    </>
  )
}
