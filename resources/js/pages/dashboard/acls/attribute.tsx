import { useMutation } from '@tanstack/react-query'
import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import { Box, Button, FormLabel } from '@mui/material'
import { useTranslation } from 'react-i18next'
import * as changeCase from 'case-anything'

import { useCsrf } from '@/components/csrf/index.js'
import { toolkitApi, useGraphql } from '@/components/toolkit-api/index.js'
import { render } from '@/react.jsx'
import { DashboardLayout, useBreadcrumbs } from '@/components/layout'
import { isLoading, Loading } from '@/components/loading'
import { useMutationToast } from '@/components/toast'
import { AclSubject } from '#config/enums'
import { DataGrid } from '@/components/table'
import { Select } from '@/components/form'
import { graphql } from '@/gql'
import { ModelAttributeFragment } from '@/graphql'
import { aclAttributeSubject } from '#utils/acl'
import { createParamsStore, routes } from '@/components/location'

import { AclNavTabs } from './_components/_acl_nav_tabs'
import {
  useConfigMapCollectionsBatchProcessor,
  useConfigMapCollections,
} from '@/components/configmaps'
import { object, string } from 'yup'

export default function PageWithLayout() {
  useBreadcrumbs(['Game Metric Attributes ACL'], 'dashboard')

  return (
    <DashboardLayout>
      <AclNavTabs sx={{ mb: 2 }} />

      <Page />
    </DashboardLayout>
  )
}

const getInitDataQuery = graphql(`
  query Acl_GameMetricAttributes(
    $readSubject: String!
    $writeSubject: String!
    $modelName: String!
  ) {
    attributes(where: { gameId: null, modelName: $modelName }) {
      collection {
        ...ModelAttribute
      }
    }

    read: accessControl(where: { subject: $readSubject }) {
      ...AccessControlAttributes
    }

    write: accessControl(where: { subject: $writeSubject }) {
      ...AccessControlAttributes
    }
  }
`)

const { useParam } = createParamsStore(routes.dash.acls.show(':subject'), {
  parse: object({
    subject: string().required(),
  }),
})

function Page() {
  useConfigMapCollectionsBatchProcessor()

  const getConfigMaps = useConfigMapCollections(useMemo(() => ['cmap.dash.role'], []))
  const { t } = useTranslation()
  const subjectParam = useParam((p) => p.subject)
  const [roleConfigMapCollection] = getConfigMaps.data

  const getInitData = useGraphql(getInitDataQuery, {
    variables: {
      readSubject: aclAttributeSubject(AclSubject.AttributeRead, subjectParam),
      writeSubject: aclAttributeSubject(AclSubject.AttributeWrite, subjectParam),
      modelName: subjectParam,
    },
  })

  const form = useForm<{ roles: Record<string, Record<string, boolean>> }>({
    defaultValues: { roles: {} },
  })

  const columns = useMemo<GridColDef<ModelAttributeFragment>[]>(() => {
    if (getConfigMaps.loading || !getInitData.data) {
      return []
    }

    const intlScope = changeCase.camelCase(subjectParam)

    return [
      {
        field: 'name',
        headerName: '',
        width: 200,
        valueGetter: (_, row) => t(`${intlScope}.${row.name}`),
        cellClassName: 'centerCell',
        headerClassName: 'centerCell',
      },
      ...roleConfigMapCollection.map(
        (roleConfigMap): GridColDef<ModelAttributeFragment> => ({
          field: roleConfigMap.id,
          headerName: roleConfigMap.name,
          width: 150,
          renderCell: ({ row }) => {
            const readAcls = getInitData.data!.read.roles
            const writeAcls = getInitData.data!.write.roles
            const roleReadAcl = readAcls.find((a) => a.roleId === roleConfigMap.id)!
            const roleWriteAcl = writeAcls.find((a) => a.roleId === roleConfigMap.id)!

            const permission = roleWriteAcl.permits.includes(row.name)
              ? 'rw'
              : roleReadAcl.permits.includes(row.name)
                ? 'r'
                : 'none'

            return (
              <FormLabel sx={{ display: 'block', width: '100%' }}>
                <Select
                  variant="standard"
                  options={[
                    { label: '-', value: 'none' },
                    { label: 'Read', value: 'r' },
                    { label: 'Read & Write', value: 'rw' },
                  ]}
                  sx={{
                    '::before': {
                      display: 'none !important',
                    },
                  }}
                  defaultValue={permission}
                  {...form.register(`roles.${roleConfigMap.id}.${row.name}`)}
                />
              </FormLabel>
            )
          },
          cellClassName: 'centerCell',
        })
      ),
    ]
  }, [getConfigMaps.data, getInitData.data, form.register, t, getConfigMaps.loading])

  const columnGrouping = useMemo<GridColumnGroupingModel>(() => {
    if (getConfigMaps.loading) {
      return []
    }

    const groups = Array.from(new Set(roleConfigMapCollection.map((r) => r.group)))
    return groups.map((group) => ({
      groupId: group,
      headerName: group,
      children: roleConfigMapCollection
        .filter((roleConfigMap) => roleConfigMap.group === group)
        .map((roleConfigMap) => ({
          field: roleConfigMap.id,
        })),
    }))
  }, [getConfigMaps.data, getConfigMaps.loading])

  const csrf = useCsrf()

  const updateAcl = useMutation({
    mutationKey: ['dash', 'game_metric', 'acl', 'update'],
    mutationFn: async (values: any) => {
      console.log(values)
      await Promise.all([
        toolkitApi.updateGameMetricAcl(
          aclAttributeSubject(AclSubject.AttributeRead, subjectParam),
          {
            roles: Object.entries(values.roles).map(([roleId, acls]) => {
              return {
                id: roleId,
                attrs: Object.entries(acls as any)
                  .filter(([_, permission]) => permission === 'r' || permission === 'rw')
                  .map(([attr]) => attr),
              }
            }),
            ...csrf.getField(),
          }
        ),
        toolkitApi.updateGameMetricAcl(
          aclAttributeSubject(AclSubject.AttributeWrite, subjectParam),
          {
            roles: Object.entries(values.roles).map(([roleId, acls]) => {
              return {
                id: roleId,
                attrs: Object.entries(acls as any)
                  .filter(([_, permission]) => permission === 'rw')
                  .map(([attr]) => attr),
              }
            }),
            ...csrf.getField(),
          }
        ),
      ])
    },
  })

  useMutationToast(updateAcl, {
    successMessage: 'ACL saved',
  })

  const onSubmit = (values: any) => {
    updateAcl.mutateAsync(values)
  }

  const loading = isLoading([
    getInitData.loading,
    getInitData.error,
    getConfigMaps.error,
    getConfigMaps.loading,
  ])

  if (loading) {
    return <Loading visible />
  }

  return (
    <Box component="form" onSubmit={form.handleSubmit(onSubmit)}>
      <Button variant="contained" type="submit" sx={{ mb: 2 }}>
        Save
      </Button>

      <Box sx={{ height: 500 }}>
        <DataGrid
          rows={getInitData.data!.attributes.collection}
          getRowId={(row) => row.name}
          columns={columns}
          actionColumn={false}
          editable={false}
          columnGroupingModel={columnGrouping}
          hideFooter
          slots={{
            toolbar: null,
          }}
          sx={{
            '& .centerCell': {
              textAlign: 'center !important',
            },
            '& .MuiDataGrid-cell': {
              borderRight: 1,
              borderTop: 1,
            },
            '& .MuiDataGrid-columnHeader': {
              borderRight: 1,
              borderTop: 1,
            },
          }}
          disableVirtualization
        />
      </Box>

      <Button variant="contained" type="submit" sx={{ mt: 2 }}>
        Save
      </Button>
    </Box>
  )
}
