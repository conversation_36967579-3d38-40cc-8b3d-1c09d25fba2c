import { BoxProps } from '@mui/material'

import { AclSubject } from '#config/enums'
import { NavTabs } from '@/components/layout'
import { routes } from '@/components/location'

export function AclNavTabs(props: BoxProps) {
  return (
    <NavTabs
      items={[
        {
          label: 'GAME METRICS ACL',
          href: routes.dash.acls.show('GameMetric'),
        },
        {
          label: 'ROUTE ACL',
          href: routes.dash.acls.show(AclSubject.Route),
        },
        {
          label: 'NEW GAME METRICS ACL',
          href: routes.dash.acls.show('GameMetricV2'),
        },
        {
          label: 'REVENUE ACL',
          href: routes.dash.acls.show('GameRevenue'),
        },
      ]}
      {...props}
    />
  )
}
