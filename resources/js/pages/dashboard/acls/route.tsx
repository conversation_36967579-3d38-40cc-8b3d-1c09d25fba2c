import { useMutation, useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import { Box, Button, Checkbox, FormLabel } from '@mui/material'

import { useConfigMaps } from '@/components/configmaps/index.js'
import { useCsrf } from '@/components/csrf/index.js'
import { toolkitApi } from '@/components/toolkit-api/index.js'
import { ModelAttributePresenter } from '#controllers/dashboard/game/model_attribute_presenter'
import { DashboardLayout, useBreadcrumbs } from '@/components/layout'
import { Loading } from '@/components/loading'
import { useMutationToast, useQueryToast } from '@/components/toast'
import { DataGrid } from '@/components/table'
import { AclSubject } from '#config/enums'

import { AclNavTabs } from './_components/_acl_nav_tabs'

export default function PageWithLayout() {
  useBreadcrumbs(['Route ACL'], 'dashboard')

  return (
    <DashboardLayout>
      <AclNavTabs sx={{ mb: 2 }} />

      <Page />
    </DashboardLayout>
  )
}

function Page() {
  const subject = AclSubject.Route

  const roleConfigMapQuery = useConfigMaps('cmap.dash.role')

  const listAclQuery = useQuery({
    queryKey: ['dash', 'game_metric', 'acl'],
    queryFn: () => toolkitApi.listGameMetricAcl(subject).then((r) => r.data),
  })
  useQueryToast(listAclQuery)

  const form = useForm<{ roles: Record<string, Record<string, boolean>> }>({
    defaultValues: { roles: {} },
  })

  const columns = useMemo<GridColDef<ModelAttributePresenter>[]>(() => {
    if (!roleConfigMapQuery.data || !listAclQuery.data) {
      return []
    }

    return [
      {
        field: 'name',
        headerName: '',
        width: 200,
        valueGetter: (_, row) => row.displayName,
        cellClassName: 'centerCell',
        headerClassName: 'centerCell',
      },
      ...roleConfigMapQuery.data.map(
        (roleConfigMap): GridColDef<ModelAttributePresenter> => ({
          field: roleConfigMap.id,
          headerName: roleConfigMap.name,
          width: 150,
          renderCell: ({ row }) => {
            const acls = listAclQuery.data
            const roleAttrs = acls.find((a) => a.roleId === roleConfigMap.id)?.attrs ?? []

            return (
              <FormLabel sx={{ display: 'block', width: '100%' }}>
                <Checkbox
                  {...form.register(`roles.${roleConfigMap.id}.${row.name.replace(/\./g, '#')}`)}
                  defaultChecked={roleAttrs.includes(row.name)}
                />
              </FormLabel>
            )
          },
          cellClassName: 'centerCell',
        })
      ),
    ]
  }, [roleConfigMapQuery.data, listAclQuery.data, form.register])

  const columnGrouping = useMemo<GridColumnGroupingModel>(() => {
    if (!roleConfigMapQuery.data) {
      return []
    }

    const groups = Array.from(new Set(roleConfigMapQuery.data.map((r) => r.group)))
    return groups.map((group) => ({
      groupId: group,
      headerName: group,
      children: roleConfigMapQuery.data
        .filter((roleConfigMap) => roleConfigMap.group === group)
        .map((roleConfigMap) => ({
          field: roleConfigMap.id,
        })),
    }))
  }, [roleConfigMapQuery.data])

  const csrf = useCsrf()

  const updateAcl = useMutation({
    mutationKey: ['dash', 'game_metric', 'acl', 'update'],
    mutationFn: async (values: any) => {
      await toolkitApi.updateGameMetricAcl(subject, {
        roles: Object.entries(values.roles).map(([roleId, attrsToEnabled]) => {
          return {
            id: roleId,
            attrs: Object.entries(attrsToEnabled as any)
              .filter(([_, enabled]) => enabled)
              .map(([attr]) => attr.replace(/#/g, '.')),
          }
        }),
        ...csrf.getField(),
      })
    },
  })

  useMutationToast(updateAcl, {
    successMessage: 'ACL saved',
  })

  const onSubmit = (values: any) => {
    updateAcl.mutateAsync(values)
  }

  const settingConfigMapCollectionQuery = useConfigMaps('cmap.dash.setting')

  if (
    listAclQuery.isLoading ||
    roleConfigMapQuery.isLoading ||
    settingConfigMapCollectionQuery.isLoading
  ) {
    return <Loading visible />
  }

  const attrs = settingConfigMapCollectionQuery.data![0]!.aclAccessibleRoutes.map(
    (route) =>
      ({
        name: route.id,
        displayName: route.name,
      }) as ModelAttributePresenter
  )

  return (
    <Box component="form" onSubmit={form.handleSubmit(onSubmit)}>
      <Button variant="contained" type="submit" sx={{ mb: 2 }}>
        Save
      </Button>

      <DataGrid
        rows={attrs}
        getRowId={(row) => row.name}
        columns={columns}
        actionColumn={false}
        editable={false}
        columnGroupingModel={columnGrouping}
        hideFooter
        slots={{
          toolbar: null,
        }}
        sx={{
          '& .centerCell': {
            textAlign: 'center !important',
          },
          '& .MuiDataGrid-cell': {
            borderRight: 1,
            borderTop: 1,
          },
          '& .MuiDataGrid-columnHeader': {
            borderRight: 1,
            borderTop: 1,
          },
        }}
      />

      <Button variant="contained" type="submit" sx={{ mt: 2 }}>
        Save
      </Button>
    </Box>
  )
}
