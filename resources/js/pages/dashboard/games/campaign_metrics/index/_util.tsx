import { createParamsStore, routes } from '@/components/location'
import dayjs from 'dayjs'
import { array, mixed, number, object, string } from 'yup'
import { graphql } from '@/gql'
import { FilterOperator } from '#graphql/main'
import { AdMetric, colectionItem, GroupItem } from './_type'

graphql(`
  fragment AdMetricAttributes on AdMetric {
    date
    adId
    groupId
    campaignId
    countryCode
    agencyId
    adRevGrossAmount
    adCostNonTaxAmount
    impressionCount
    roasNthDayRates
    retentionNthDayRates
    activeUserNthDayCounts
    sessionNthDayCounts
    sessionCount
    dailyActiveUserCount
    retentionRate
    clickCount
    installCount
    adRevNthDayGrossAmounts
    cpi
    ctr
    cvr
    cpc
    ipm
    roas
  }
`)

graphql(`
  fragment AdAttributes on Ad {
    id
    name
    campaignId
    groupId
  }
`)

graphql(`
  fragment AdGroupAttributes on AdGroup {
    id
    name
  }
`)

graphql(`
  fragment CampaignAttributes on Campaign {
    id
    name
  }
`)

graphql(`
  fragment AdAgencyAttributes on AdAgency {
    id
    name
  }
`)

graphql(`
  fragment ViewPresetAttributes on ViewPreset {
    id
    name
    attributes {
      name
      isCohort
      cohortDays
    }
  }
`)

export const adMetricsQuery = graphql(`
  query AdMetricsQuery(
    $gameId: ID!
    $dateFrom: FilterValue!
    $dateTo: FilterValue!
    $groupByFields: [String!]!
    $campaignId: Filter
    $agencyId: Filter
    $groupId: Filter
    $adId: Filter
    $countryCode: Filter
    $cvr: Filter
    $cpi: Filter
    $ctr: Filter
    $roas: Filter
    $adRevGrossAmount: Filter
    $adCostNonTaxAmount: Filter
    $impressionCount: Filter
    $clickCount: Filter
    $installCount: Filter
    $cpc: Filter
    $ipm: Filter
    $preset: Int
  ) {
    adMetrics(
      where: {
        gameId: $gameId
        date: { operator: BETWEEN, values: [$dateFrom, $dateTo] }
        campaignId: $campaignId
        groupId: $groupId
        adId: $adId
        agencyId: $agencyId
        countryCode: $countryCode
        cvr: $cvr
        cpi: $cpi
        ctr: $ctr
        roas: $roas
        adRevGrossAmount: $adRevGrossAmount
        adCostNonTaxAmount: $adCostNonTaxAmount
        impressionCount: $impressionCount
        clickCount: $clickCount
        installCount: $installCount
        cpc: $cpc
        ipm: $ipm
      }
      group: { fields: $groupByFields }
      preset: { viewPresetId: $preset }
    ) {
      collection {
        ...AdMetricAttributes
      }
      ads {
        ...AdAttributes
      }
      adGroups {
        ...AdGroupAttributes
      }
      campaigns {
        ...CampaignAttributes
      }
      agencies {
        ...AdAgencyAttributes
      }
    }
  }
`)

export const viewPresetsQuery = graphql(`
  query GetViewPresets($pageId: String!) {
    viewPresets(where: { pageId: $pageId }) {
      collection {
        ...ViewPresetAttributes
      }
    }
  }
`)

export const createViewPresetMutation = graphql(`
  mutation CreateViewPreset($form: CreateViewPresetForm!) {
    createViewPreset(form: $form) {
      ...ViewPresetAttributes
    }
  }
`)

export const deleteViewPresetsMutation = graphql(`
  mutation DeleteViewPresets($ids: [Int!]!) {
    deleteViewPresets(where: { ids: $ids })
  }
`)

export const { useParam, useSetParams, usePagination, useParams } = createParamsStore(
  routes.dash.games.revenues(':gameId'),
  {
    parse: object({
      gameId: string().required(),
      from: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs().subtract(7, 'day') }),
      to: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs() }),
      direction: string().oneOf(['DESC', 'ASC']).default('ASC') as any,
      page: number().default(1),
      perPage: number().default(10),
      query_id: number().default(0),
      filter: array(string()),
    }),
    stringify: object({
      from: mixed().stringifyDate(),
      to: mixed().stringifyDate(),
      query_id: string(),
      filter: array(string()),
    }),
  }
)

export const getOperator = (value: string | number) => {
  return { operator: FilterOperator.Eq, values: [value] }
}

function transformAdMetrics(adMetrics: AdMetric[]) {
  return adMetrics.map((item: AdMetric) => {
    const newItem = { ...item }

    // Transform adRevNthDayGrossAmounts
    if (item.adRevNthDayGrossAmounts) {
      Object.keys(item.adRevNthDayGrossAmounts).forEach((day) => {
        newItem[`adRevNthDayGrossAmountsD${day}`] = item.adRevNthDayGrossAmounts?.[day]
      })
    }

    // Transform roasNthDayRates
    if (item.roasNthDayRates) {
      Object.keys(item.roasNthDayRates).forEach((day) => {
        newItem[`roasNthDayRatesD${day}`] = item.roasNthDayRates?.[day]
      })
    }

    // Transform retentionNthDayRates
    if (item.retentionNthDayRates) {
      Object.keys(item.retentionNthDayRates).forEach((day) => {
        newItem[`retentionNthDayRatesD${day}`] = item.retentionNthDayRates?.[day]
      })
    }

    // Transform activeUserNthDayCounts
    if (item.activeUserNthDayCounts) {
      Object.keys(item.activeUserNthDayCounts).forEach((day) => {
        newItem[`activeUserNthDayCountsD${day}`] = item.activeUserNthDayCounts?.[day]
      })
    }

    // Transform sessionNthDayCounts
    if (item.sessionNthDayCounts) {
      Object.keys(item.sessionNthDayCounts).forEach((day) => {
        newItem[`sessionNthDayCountsD${day}`] = item.sessionNthDayCounts?.[day]
      })
    }

    return newItem
  })
}

export function addAgencyNameToMetrics(
  agencies: GroupItem[],
  campaigns: GroupItem[],
  adGroups: GroupItem[],
  ads: GroupItem[],
  metrics: colectionItem[]
) {
  const agencyMap = new Map(agencies.map((agency: GroupItem) => [agency.id, agency.name]))
  const campaignMap = new Map(campaigns.map((campaign: GroupItem) => [campaign.id, campaign.name]))
  const adGroupMap = new Map(adGroups.map((adGroup: GroupItem) => [adGroup.id, adGroup.name]))
  const adMap = new Map(ads.map((ad: GroupItem) => [ad.id, ad.name]))

  const namedMetric = metrics.map((metric: colectionItem) => ({
    ...metric,
    itemName: {
      agencyId: agencyMap.get(metric.agencyId),
      campaignId: campaignMap.get(metric.campaignId),
      groupId: adGroupMap.get(metric.groupId),
      adId: adMap.get(metric.adId),
    },
  }))

  return transformAdMetrics(namedMetric)
}

export const isValidDayjs = (value: any): boolean => {
  return value && dayjs.isDayjs(value) && value.isValid()
}

export const formatUSNumber = (value: number | string): string => {
  return value?.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })
}
