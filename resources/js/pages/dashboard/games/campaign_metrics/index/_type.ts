import { GridKeyValue } from '@mui/x-data-grid-premium'

export interface groupItem {
  title: string
  isShow: boolean
  id: string
}

export interface GroupItem {
  id: string | number | null | undefined
  name: string
}

interface RoasNthDayRates {
  [key: string]: number
}

interface RetentionNthDayRates {
  [key: string]: number
}

interface ActiveUserNthDayCounts {
  [key: string]: number
}

interface SessionNthDayCounts {
  [key: string]: number
}

interface AdRevNthDayGrossAmounts {
  [key: string]: number
}

export interface colectionItem {
  date?: string | null | undefined
  adId?: string | null | undefined
  groupId?: string | null | undefined
  campaignId?: string | null | undefined
  countryCode?: string | null | undefined
  agencyId?: number | null | undefined
  adRevGrossAmount: number
  adCostNonTaxAmount: number
  impressionCount: number
  clickCount: number
  installCount: number
  adRevNthDayGrossAmounts?: AdRevNthDayGrossAmounts | null
  roasNthDayRates?: RoasNthDayRates | null
  retentionNthDayRates?: RetentionNthDayRates | null
  activeUserNthDayCounts?: ActiveUserNthDayCounts | null
  sessionNthDayCounts?: SessionNthDayCounts | null
  cpi: number
  ctr: number
  cvr: number
  cpc: number
  ipm: number
  roas: number
}

export interface storedCollection extends colectionItem {
  agencyName?: GroupItem
  campaignName?: GroupItem
  groupName?: GroupItem
  adName?: GroupItem
}

export interface AdMetric extends storedCollection {
  [key: `adRevNthDayGrossAmountsD${string}`]: number | null | undefined
  [key: `roasNthDayRatesD${string}`]: number | null | undefined
  [key: `retentionNthDayRatesD${string}`]: number | null | undefined
  [key: `activeUserNthDayCountsD${string}`]: number | null | undefined
  [key: `sessionNthDayCountsD${string}`]: number | null | undefined
}

export interface CampaignMetricsDataType {
  adGroups: GroupItem[]
  ads: GroupItem[]
  agencies: GroupItem[]
  campaigns: GroupItem[]
  collection: colectionItem[]
}

export interface expandingGroupItem {
  groupingField: string | null
  groupingKey: GridKeyValue | null
  isExpanded: boolean | undefined
}

export interface GroupByItem {
  group: string
  id: number | string
  isExpand: boolean
}

export interface GroupByType {
  agency: GroupByItem
  campaign: GroupByItem
  adGroup: GroupByItem
  ad: GroupByItem
}

// Interface for cohort metric configuration
export interface CohortMetric {
  name: string
  template: string
  cohortDay: string
}

export interface FilterType {
  metric: string
  filterOperator: string
  min: string
  max: string
}
