import * as Yup from 'yup'
import dayjs from 'dayjs'

import { FilterOperator } from '#graphql/main'

import { isValidDayjs } from './_util'

export const operatorMeanings: Record<FilterOperator, string> = {
  [FilterOperator.Between]: 'Between',
  [FilterOperator.Eq]: 'Equal',
  [FilterOperator.Gt]: 'Greater than',
  [FilterOperator.Gte]: 'Greater than or equal',
  [FilterOperator.Lt]: 'Less than',
  [FilterOperator.Lte]: 'Less than or equal',
  [FilterOperator.Ne]: 'Not equal',
}

export const campaignFilterschema = Yup.object().shape({
  from: Yup.mixed()
    .test('is-dayjs', 'From date must be a valid date', isValidDayjs)
    .default(() => dayjs().subtract(7, 'day')),
  to: Yup.mixed()
    .test('is-dayjs', 'To date must be a valid date', isValidDayjs)
    .default(() => dayjs()),
  dateRange: Yup.array()
    .of(Yup.mixed().test('is-dayjs', 'Must be a valid date', isValidDayjs))
    .length(2, 'Date range must contain exactly two dates')
    .default(() => [dayjs().subtract(7, 'day'), dayjs()])
    .required('Date range is required'),
  metric: Yup.string().default(''),
  min: Yup.string().optional(),
  max: Yup.string()
    .optional()
    .test({
      name: 'max-gte-min',
      message: 'Max must be greater than or equal to min',
      test(value) {
        const min = this.parent.min
        if (!value || !min) return true // Skip if either is empty
        const maxNum = parseFloat(value)
        const minNum = parseFloat(min)
        if (isNaN(maxNum) || isNaN(minNum)) return false // Ensure both are valid numbers
        return maxNum >= minNum
      },
    }),
})
