import { Button, ButtonGroup, ButtonProps } from '@mui/material'

interface CustomFilterActionsProps {
  onSubmit?: () => void
  onReset?: () => void
  onSaveView?: () => void
  onDeleteView?: () => void
  slotProps?: {
    submit?: ButtonProps
    reset?: ButtonProps
    save?: ButtonProps
    delete?: ButtonProps
  }
}

export function CustomFilterActions({
  onSubmit,
  onReset,
  slotProps: { submit: submitProps = {}, reset: resetProps = {} } = {},
}: Readonly<CustomFilterActionsProps>) {
  return (
    <ButtonGroup>
      <Button
        sx={{ paddingY: '4px', maxHeight: 31 }}
        variant="contained"
        type="submit"
        onClick={onSubmit}
        {...submitProps}
      >
        Apply
      </Button>

      <Button
        sx={{ paddingY: '4px', maxHeight: 31 }}
        variant="contained"
        type="button"
        color="warning"
        onClick={onReset}
        {...resetProps}
      >
        Reset
      </Button>
    </ButtonGroup>
  )
}
