import {
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'
import { Head } from '@inertiajs/react'
import { DateRang<PERSON>, FilterContainer, FilterGroup, Select } from '@/components/form'
import { Loading } from '@/components/loading'
import { Box, styled, CircularProgress, <PERSON><PERSON>, Tab, Button } from '@mui/material'
import { useForm } from 'react-hook-form'
import { routes, useNavigate } from '@/components/location'
import { useServerProp } from '@/components/ssr'
import { InferPageProps } from '@adonisjs/inertia/types'
import GameCreativeMetricsController from '#controllers/dashboard/game/creative_metrics_controller'
import GroupDataTree from '@/components/table/data_tree'
import {
  useParam,
  useParams,
  useSetParams,
  viewPresetsQuery,
  createViewPresetMutation,
  deleteViewPresetsMutation,
} from './index/_util'
import { CustomFilterActions } from './custom_filter_actions'
import { useEffect, useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import CustomizedExpand from '@/components/expand'
import MultiSelectButtonGroup from '@/components/table/multi_select_group'
import MultiSelectDropdown from '@/components/table/multi_select_dropdown'
import {
  useConfigMapCollections,
  useConfigMapCollectionsBatchProcessor,
} from '@/components/configmaps'
import { CohortMetric, FilterType } from './index/_type'
import { ViewPresetAttributeConfigMap } from '#configmaps/dashboard/view_preset'
import TextField from '@/components/form/textfield'
import { yupResolver } from '@hookform/resolvers/yup'
import { useGraphql, useGraphqlMutation } from '@/components/toolkit-api/graphql'
import { ViewPreset } from '@/graphql'
import { SaveViewDialog } from '../../game/_components/_save_view_dialog'
import { DeleteConfirmDialog } from '../../game/_components/_delete_confirm_dialog'
import { campaignFilterschema, operatorMeanings } from './index/_constant'
import RemoveIcon from '@mui/icons-material/Remove'
import AddIcon from '@mui/icons-material/Add'
import DeleteIcon from '@mui/icons-material/Delete'
import { FilterOperator } from '#graphql/main'
import dayjs from 'dayjs'

type PageProps = InferPageProps<GameCreativeMetricsController, 'index'>

// Styled component for the metrics container
const MetricsContainer = styled(Box)(() => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: 4,
  paddingTop: 12,
}))

export default function Page() {
  const game = useServerProp<PageProps, PageProps['game']>((s) => s.game)
  const pageId = useServerProp<PageProps, PageProps['pageId']>((s) => s.pageId)
  useConfigMapCollectionsBatchProcessor()
  const { data, loading } = useConfigMapCollections(['cmap.viewpreset'])
  const [viewPresetConfigMapCollection] = data || []
  const { t } = useTranslation()

  const { data: viewPresetsData, refetch: refetchViewPresets } = useGraphql(
    viewPresetsQuery as any,
    {
      variables: { pageId },
      skip: !pageId,
    }
  )

  const [createViewPreset] = useGraphqlMutation(createViewPresetMutation, {
    onCompleted: () => {
      refetchViewPresets()
      setSaveViewDialogOpen(false)
    },
  })

  const [deleteViewPresets] = useGraphqlMutation(deleteViewPresetsMutation, {
    onCompleted: () => {
      refetchViewPresets()
      // Reset to default tab after deletion
      setParams({ query_id: 0 })
    },
  })

  const viewPresetsUser = useMemo<ViewPreset[]>(() => {
    if (viewPresetsData && (viewPresetsData as any).viewPresets?.collection) {
      return (viewPresetsData as any).viewPresets.collection as ViewPreset[]
    }
    return []
  }, [viewPresetsData])
  const { refresh } = useNavigate()
  const { gameId } = useParams()
  const dateFrom = useParam((p) => p.from)
  const dateTo = useParam((p) => p.to)
  const filter = useParam((p) => p.filter)
  const filterParams = filter?.map((item: any) => JSON.parse(item ?? ''))
  // const direction = useParam((p) => p.direction) as any
  // const page = useParam((p) => p.page)
  // const perPage = useParam((p) => p.perPage)
  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [filterRef, _, filterSize] = useLayoutComponentRegistration('filter')

  const [filterList, setFilterList] = useState<FilterType[]>(
    filterParams ?? [{ metric: '', filterOperator: FilterOperator.Between, min: '', max: '' }]
  )

  useBreadcrumbs([
    {
      label: 'Games',
      url: routes.dash.games.index(),
    },
    { label: game?.name, url: routes.dash.games.index(game?.id) },
    'Creative Performance',
  ])

  useLayoutHeight(navbarSize, breadcrumbsSize, notificationSize, filterSize)

  const queryId = useParam((p) => p.query_id) || 'default'
  const setParams = useSetParams()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        sessionStorage.clear()
      })
    }
  })

  const { uaNonCohortMetrics, uaCohortMetrics, productNonCohortMetrics, productCohortMetrics } =
    useMemo(() => {
      const uaNonCohortMetrics: string[] = []
      const uaCohortMetrics: CohortMetric[] = []
      const productNonCohortMetrics: string[] = []
      const productCohortMetrics: CohortMetric[] = []

      if (viewPresetConfigMapCollection) {
        const viewPresetConfigMap = viewPresetConfigMapCollection.find(
          (item) => item.pageId === pageId
        )
        if (viewPresetConfigMap) {
          // Properly type the attributes array
          const attributes = viewPresetConfigMap.attributes as ViewPresetAttributeConfigMap[]

          attributes.forEach((attr) => {
            if (attr.section === 'ua') {
              if (!attr.isCohort) {
                uaNonCohortMetrics.push(attr.name)
              } else {
                uaCohortMetrics.push({
                  name: attr.name,
                  template: attr.substituteAttributeTemplate,
                  cohortDay: attr.cohortDay,
                })
              }
            } else if (attr.section === 'product') {
              if (!attr.isCohort) {
                productNonCohortMetrics.push(attr.name)
              } else {
                productCohortMetrics.push({
                  name: attr.name,
                  template: attr.substituteAttributeTemplate,
                  cohortDay: attr.cohortDay,
                })
              }
            }
          })
        }
      }

      return {
        uaNonCohortMetrics,
        uaCohortMetrics,
        productNonCohortMetrics,
        productCohortMetrics,
      }
    }, [viewPresetConfigMapCollection])

  // State for metrics selections
  const [selectedMetricsState, setSelectedMetricsState] = useState<Record<string, string[]>>({})

  // Original selected metrics state
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['cpi', 'ctr'])

  const [tabs, setTabs] = useState([{ id: 0, label: 'Default View', preset: {} }])

  useEffect(() => {
    // Always set the default tab
    const defaultTab = { id: 0, label: 'Default View', preset: {} }

    if (viewPresetsUser && viewPresetsUser.length > 0) {
      const userTabs = viewPresetsUser.map((preset: ViewPreset) => ({
        id: preset.id,
        label: preset.name,
        preset: preset,
      }))

      setTabs([defaultTab, ...userTabs])
    } else {
      // If there are no user presets, just show the default tab
      setTabs([defaultTab])
    }
  }, [viewPresetsUser])

  useEffect(() => {
    if (viewPresetsUser && viewPresetsUser.length > 0 && queryId) {
      const numericId = Number(queryId)
      if (!isNaN(numericId) && numericId !== 0) {
        const matchingPreset = viewPresetsUser.find((preset: ViewPreset) => preset.id === numericId)
        if (matchingPreset) {
          applyViewPreset(matchingPreset)
        }
      }
    }
  }, [viewPresetsUser, queryId])

  const currentTabIndex = useMemo(() => {
    const index = tabs.findIndex((tab) => tab.id === queryId)
    return index >= 0 ? index : 0
  }, [tabs, queryId])

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    const selectedTab = tabs[newValue]
    setParams({ query_id: selectedTab.id })

    if (selectedTab.id === 0) {
      setSelectedMetrics([])
      setSelectedMetricsState({})
    } else {
      setSelectedMetrics([])
      setSelectedMetricsState({})
      setTimeout(() => {
        applyViewPreset(selectedTab.preset)
      }, 0)
    }
  }

  const filterOperatorOptions = Object.values(FilterOperator).map((value) => ({
    label: operatorMeanings[value],
    value,
  }))

  const applyViewPreset = (preset: any) => {
    const attributes = preset.attributes ?? []

    const selectedNonCohortMetrics: string[] = []
    const selectedCohortMetricsState: Record<string, string[]> = {}

    attributes.forEach((attr: any) => {
      if (!attr.isCohort) {
        selectedNonCohortMetrics.push(attr.name)
      } else if (attr.cohortDays && attr.cohortDays.length > 0) {
        selectedCohortMetricsState[attr.name] = attr.cohortDays.map((day: number) => `D${day}`)
      }
    })

    setSelectedMetrics(selectedNonCohortMetrics)
    setSelectedMetricsState(selectedCohortMetricsState)
  }

  // State for save view dialog
  const [saveViewDialogOpen, setSaveViewDialogOpen] = useState(false)

  const openSaveViewDialog = () => {
    setSaveViewDialogOpen(true)
  }

  const handleSaveView = async (name: string) => {
    // Collect all selected metrics
    const attributes = []

    // Add non-cohort metrics
    for (const metricName of selectedMetrics) {
      attributes.push({
        name: metricName,
        cohortDays: [],
      })
    }

    // Add cohort metrics with their selected days
    for (const [metricName, days] of Object.entries(selectedMetricsState)) {
      if (days.length > 0) {
        attributes.push({
          name: metricName,
          cohortDays: days.map((day) => parseInt(day.replace('D', ''))),
        })
      }
    }

    try {
      // Create the view preset using the GraphQL mutation
      await createViewPreset({
        variables: {
          form: {
            name,
            pageId,
            attributes,
          },
        },
      })
    } catch (error) {
      console.error('Error creating view preset:', error)
    }
  }

  // State for delete confirmation dialog
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [tabToDelete, setTabToDelete] = useState<number | null>(null)

  const deleteView = (tabId: number) => {
    if (tabId === 0) return // Don't delete default view

    // Set the tab to delete and open confirmation dialog
    setTabToDelete(tabId)
    setDeleteConfirmOpen(true)
  }

  const confirmDelete = async () => {
    if (tabToDelete === null) return

    try {
      await deleteViewPresets({
        variables: {
          ids: [tabToDelete],
        },
      })
      setDeleteConfirmOpen(false)
      setTabToDelete(null)
    } catch (error) {
      console.error('Error deleting view preset:', error)
    }
  }

  useBreadcrumbs([
    {
      label: 'Games',
      url: routes.dash.games.index(),
    },
    { label: game?.name, url: routes.dash.games.index(game?.id) },
    'Creative Performance',
  ])

  const { setValue, handleSubmit } = useForm({
    resolver: yupResolver(campaignFilterschema),
    defaultValues: {
      dateRange: [dateFrom, dateTo],
      metric: '',
      min: undefined,
      max: undefined,
    },
  })

  useLayoutHeight(navbarSize, breadcrumbsSize, notificationSize, filterSize)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        sessionStorage.clear()
      })
    }
  }, [])
  const handleAddFilter = () => {
    setFilterList((prev: any) => [
      ...prev,
      { metric: '', filterOperator: FilterOperator.Between, min: '', max: '' },
    ])
  }

  const handleDeleteFilter = (index: number) => {
    const data = [...filterList]
    data.splice(index, 1)

    setFilterList(data)
  }

  const handleChageFilterValue = (
    e: { target: { name: string; value: string } },
    index: number
  ) => {
    setFilterList((prevList) => {
      const newList = [...prevList]
      newList[index] = {
        ...newList[index],
        [e.target.name]: e.target.value,
      }
      return newList
    })
  }

  const DataGridCols: string[] = useMemo(() => {
    if (
      !selectedMetricsState &&
      !uaNonCohortMetrics.length &&
      !uaCohortMetrics.length &&
      !productCohortMetrics.length &&
      !productNonCohortMetrics.length
    ) {
      return ['']
    }
    const activeUserNthDayCount = selectedMetricsState.activeUserNthDayCounts?.map(
      (item) => `activeUserNthDayCounts${item}`
    )
    const adRevNthDayGrossAmount = selectedMetricsState.adRevNthDayGrossAmounts?.map(
      (item) => `adRevNthDayGrossAmounts${item}`
    )
    const retentionNthDayRate = selectedMetricsState.retentionNthDayRates?.map(
      (item) => `retentionNthDayRates${item}`
    )

    const roasNthDayRate = selectedMetricsState.roasNthDayRates?.map(
      (item) => `roasNthDayRates${item}`
    )

    const sessionNthDayCount = selectedMetricsState.sessionNthDayCounts?.map(
      (item) => `sessionNthDayCounts${item}`
    )

    const metricsCols = [
      adRevNthDayGrossAmount,
      roasNthDayRate,
      activeUserNthDayCount,
      retentionNthDayRate,
      sessionNthDayCount,
    ]
      .flat()
      .filter((item) => item !== undefined)

    return [...selectedMetrics, ...metricsCols]
  }, [
    uaNonCohortMetrics,
    uaCohortMetrics,
    productCohortMetrics,
    productNonCohortMetrics,
    selectedMetricsState,
  ])

  const metricFilterOptions = useMemo(() => {
    if (!DataGridCols.length) {
      return []
    }

    return DataGridCols.map((metric) => {
      // Get the base field name without cohort day suffix
      let baseFieldName = metric
      let cohortDay = ''

      // Handle cohort metrics like activeUserNthDayCounts, adRevNthDayGrossAmounts, etc.
      if (
        metric.match(
          /^(activeUserNthDayCounts|adRevNthDayGrossAmounts|retentionNthDayRates|roasNthDayRates|sessionNthDayCounts)D\d+$/
        )
      ) {
        const match = metric.match(/^(.+?)(D\d+)$/)
        if (match) {
          baseFieldName = match[1]
          cohortDay = match[2]
        }
      }

      // Get translation for the field name
      let translatedName = t(`campaignMetrics.${baseFieldName}`, baseFieldName)

      // Add cohort day to the label if present
      let label = translatedName
      if (cohortDay) {
        // Extract the day number from the cohortDay (e.g., 'D7' -> '7')
        const dayNumber = cohortDay.replace('D', '')
        // Replace <n> with the actual day number
        label = translatedName.replace('<n>', dayNumber)
      }

      return { label, value: metric }
    })
  }, [DataGridCols, t])

  return (
    <GameDashboardLayout gameId={gameId}>
      <Head title={`Creative Performance | ${game?.name}`} />

      <CustomizedExpand expandTitle="Filter" sx={{ display: 'flex', flexDirection: 'column' }}>
        <FilterContainer ref={filterRef}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '14px', marginTop: '6px' }}>
            <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
              <span style={{ minWidth: 80 }}>Date range</span>
              <FilterGroup sx={{ flex: 1 }}>
                <DateRange onChange={(values) => setValue('dateRange', [values[0]!, values[1]!])} />
              </FilterGroup>
            </div>
            <div style={{ display: 'flex', gap: '1rem', alignItems: 'start' }}>
              <span style={{ minWidth: 80 }}>Filter</span>
              <div
                style={{ display: 'flex', alignItems: 'start', flexDirection: 'column', gap: 4 }}
              >
                {filterList.map((item: FilterType, index: number) => (
                  <div
                    key={`${JSON.stringify(item)}${index}`}
                    style={{ display: 'flex', alignItems: 'center', gap: 8 }}
                  >
                    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                      <FilterGroup
                        sx={{
                          width: {
                            xs: '100%',
                            sm: '150px',
                          },
                        }}
                      >
                        <Select
                          name="metric"
                          options={metricFilterOptions}
                          size="small"
                          defaultValue=""
                          onChange={(e: any) => handleChageFilterValue(e, index)}
                          value={item.metric}
                        />
                      </FilterGroup>

                      <FilterGroup
                        sx={{
                          width: {
                            xs: '100%',
                            sm: '150px',
                          },
                        }}
                      >
                        <Select
                          name="filterOperator"
                          options={filterOperatorOptions}
                          size="small"
                          defaultValue={FilterOperator.Between}
                          onChange={(e: any) => {
                            handleChageFilterValue(e, index)
                          }}
                          value={item.filterOperator}
                          variant="outlined"
                        />
                      </FilterGroup>

                      <FilterGroup
                        sx={{
                          width: '100%',
                          flex: 1,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            gap: '1rem',
                            alignItems: 'center',
                            maxWidth: '300px',
                          }}
                        >
                          <TextField
                            placeholder="Min"
                            name="min"
                            type="number"
                            value={item.min}
                            onChange={(name, value) =>
                              handleChageFilterValue({ target: { name, value } }, index)
                            }
                          />
                          {item.filterOperator === FilterOperator.Between && (
                            <>
                              <span>-</span>
                              <TextField
                                placeholder="Max"
                                name="max"
                                type="number"
                                value={item.max}
                                onChange={(name, value) =>
                                  handleChageFilterValue({ target: { name, value } }, index)
                                }
                              />
                            </>
                          )}
                        </div>
                      </FilterGroup>
                    </div>

                    <div>
                      <Button
                        disabled={filterList.length <= 1}
                        onClick={() => filterList.length > 1 && handleDeleteFilter(index)}
                        sx={{
                          border: '1px solid black',
                          borderRadius: '50%',
                          minWidth: 'unset',
                          padding: 0,
                          marginRight: '4px',
                        }}
                      >
                        <RemoveIcon />
                      </Button>
                      {index === filterList.length - 1 && (
                        <Button
                          onClick={() => handleAddFilter()}
                          sx={{
                            border: '1px solid black',
                            borderRadius: '50%',
                            minWidth: 'unset',
                            padding: 0,
                          }}
                        >
                          <AddIcon />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <CustomFilterActions
              onSubmit={handleSubmit((formData) => {
                setParams({
                  from: formData.dateRange[0]
                    ? dayjs(formData.dateRange[0] as string | number | Date)
                    : undefined,
                  to: formData.dateRange[1]
                    ? dayjs(formData.dateRange[1] as string | number | Date)
                    : undefined,
                  filter: filterList.map((item) => JSON.stringify(item)),
                })
              })}
              onReset={() => refresh()}
              onSaveView={openSaveViewDialog}
              onDeleteView={queryId !== 0 ? () => deleteView(Number(queryId)) : undefined}
            />
          </div>
        </FilterContainer>

        {/* Metrics Tabs */}
        <Box sx={{ mt: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Tabs
              value={currentTabIndex}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider', flex: 1 }}
            >
              {tabs.map((tab) => {
                const tabLabel = (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {tab.label}
                    {tab.id !== 0 && (
                      <Box
                        component="span"
                        sx={{
                          'display': 'inline-flex',
                          'padding': '2px',
                          'borderRadius': '50%',
                          'color': 'error.main',
                          'cursor': 'pointer',
                          '&:hover': { backgroundColor: 'rgba(211, 47, 47, 0.04)' },
                        }}
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteView(tab.id)
                        }}
                      >
                        <DeleteIcon fontSize="small" color="error" />
                      </Box>
                    )}
                  </Box>
                )

                return <Tab key={tab.id} label={tabLabel} />
              })}
            </Tabs>
            <Button
              sx={{ paddingY: '4px', maxHeight: 31 }}
              variant="contained"
              type="button"
              color="primary"
              onClick={() => openSaveViewDialog()}
            >
              Save View
            </Button>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress size={32} />
          </Box>
        ) : (
          <Box sx={{ mt: 2 }}>
            {/* Default View Tab Panel */}
            {tabs[currentTabIndex]?.id === 0 && (
              <Box>
                <Box sx={{ mb: 2 }}>
                  {uaNonCohortMetrics.length > 0 && (
                    <Box>
                      <MultiSelectButtonGroup
                        key={`ua-metrics-${JSON.stringify(selectedMetrics)}`}
                        title="UA Metrics"
                        options={uaNonCohortMetrics}
                        defaultSelected={selectedMetrics}
                        onChange={(selected) => setSelectedMetrics(selected)}
                      />
                    </Box>
                  )}

                  <MetricsContainer>
                    {uaCohortMetrics.map((metric) => {
                      const metricName = metric.name
                      const days = metric.cohortDay.includes('-')
                        ? metric.cohortDay.split('-').map(Number)
                        : [1]

                      // Generate day options based on the cohortDay range
                      let cohortDayOptions: string[] = []
                      if (days.length === 2) {
                        for (let i = days[0]; i <= days[1]; i++) {
                          cohortDayOptions.push(`D${i}`)
                        }
                      } else {
                        cohortDayOptions = ['D1', 'D3', 'D5', 'D7', 'D14', 'D30']
                      }

                      return (
                        <MultiSelectDropdown
                          key={metricName}
                          label={metricName}
                          options={cohortDayOptions}
                          value={selectedMetricsState[metricName] || []}
                          onChange={(selected) => {
                            setSelectedMetricsState((prev) => ({
                              ...prev,
                              [metricName]: selected,
                            }))
                          }}
                        />
                      )
                    })}
                  </MetricsContainer>
                </Box>

                <Box>
                  {productNonCohortMetrics.length > 0 && (
                    <Box>
                      <MultiSelectButtonGroup
                        key={`product-metrics-${JSON.stringify(selectedMetrics)}`}
                        title="Product Metrics"
                        options={productNonCohortMetrics}
                        defaultSelected={selectedMetrics}
                        onChange={(selected) => setSelectedMetrics(selected)}
                      />
                    </Box>
                  )}

                  <MetricsContainer>
                    {productCohortMetrics.map((metric) => {
                      const metricName = metric.name
                      const days = metric.cohortDay.includes('-')
                        ? metric.cohortDay.split('-').map(Number)
                        : [1]

                      // Generate day options based on the cohortDay range
                      let cohortDayOptions: string[] = []
                      if (days.length === 2) {
                        for (let i = days[0]; i <= days[1]; i++) {
                          cohortDayOptions.push(`D${i}`)
                        }
                      } else {
                        cohortDayOptions = ['D1', 'D3', 'D5', 'D7', 'D14', 'D30']
                      }

                      return (
                        <MultiSelectDropdown
                          key={metricName}
                          label={metricName}
                          options={cohortDayOptions}
                          value={selectedMetricsState[metricName] || []}
                          onChange={(selected) => {
                            setSelectedMetricsState((prev) => ({
                              ...prev,
                              [metricName]: selected,
                            }))
                          }}
                        />
                      )
                    })}
                  </MetricsContainer>
                </Box>
              </Box>
            )}

            {/* User Preset View Tab Panel */}
            {tabs[currentTabIndex]?.id !== 0 && (
              <Box sx={{ p: 3 }}>
                <Box sx={{ mb: 4 }}>
                  {uaNonCohortMetrics.length > 0 && (
                    <Box sx={{ mb: 3 }}>
                      <MultiSelectButtonGroup
                        key={`ua-metrics-preset-${JSON.stringify(selectedMetrics)}`}
                        title="UA Metrics"
                        options={uaNonCohortMetrics}
                        defaultSelected={selectedMetrics}
                        onChange={(selected) => setSelectedMetrics(selected)}
                      />
                    </Box>
                  )}

                  <MetricsContainer>
                    {uaCohortMetrics.map((metric) => {
                      const metricName = metric.name
                      const days = metric.cohortDay.includes('-')
                        ? metric.cohortDay.split('-').map(Number)
                        : [1]

                      let cohortDayOptions: string[] = []
                      if (days.length === 2) {
                        for (let i = days[0]; i <= days[1]; i++) {
                          cohortDayOptions.push(`D${i}`)
                        }
                      } else {
                        cohortDayOptions = ['D1', 'D3', 'D5', 'D7', 'D14', 'D30']
                      }

                      return (
                        <MultiSelectDropdown
                          key={metricName}
                          label={metricName}
                          options={cohortDayOptions}
                          value={selectedMetricsState[metricName] || []}
                          onChange={(selected) => {
                            setSelectedMetricsState((prev) => ({
                              ...prev,
                              [metricName]: selected,
                            }))
                          }}
                        />
                      )
                    })}
                  </MetricsContainer>
                </Box>

                <Box sx={{ mt: 3 }}>
                  {productNonCohortMetrics.length > 0 && (
                    <Box sx={{ mb: 3 }}>
                      <MultiSelectButtonGroup
                        key={`product-metrics-preset-${JSON.stringify(selectedMetrics)}`}
                        title="Product Metrics"
                        options={productNonCohortMetrics}
                        defaultSelected={selectedMetrics}
                        onChange={(selected) => setSelectedMetrics(selected)}
                      />
                    </Box>
                  )}

                  <MetricsContainer>
                    {productCohortMetrics.map((metric) => {
                      const metricName = metric.name
                      const days = metric.cohortDay.includes('-')
                        ? metric.cohortDay.split('-').map(Number)
                        : [1]

                      let cohortDayOptions: string[] = []
                      if (days.length === 2) {
                        for (let i = days[0]; i <= days[1]; i++) {
                          cohortDayOptions.push(`D${i}`)
                        }
                      } else {
                        cohortDayOptions = ['D1', 'D3', 'D5', 'D7', 'D14', 'D30']
                      }

                      return (
                        <MultiSelectDropdown
                          key={metricName}
                          label={metricName}
                          options={cohortDayOptions}
                          value={selectedMetricsState[metricName] || []}
                          onChange={(selected) => {
                            setSelectedMetricsState((prev) => ({
                              ...prev,
                              [metricName]: selected,
                            }))
                          }}
                        />
                      )
                    })}
                  </MetricsContainer>
                </Box>
              </Box>
            )}
          </Box>
        )}
      </CustomizedExpand>

      <Loading visible={false}>
        <Box sx={{ height: `calc(100vh` }}>
          <GroupDataTree tableColumns={DataGridCols} />
        </Box>
      </Loading>
      {/* Save View Dialog */}
      <SaveViewDialog
        open={saveViewDialogOpen}
        onClose={() => setSaveViewDialogOpen(false)}
        onSave={handleSaveView}
        pageId={pageId || ''}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        onConfirm={confirmDelete}
        viewName={tabs.find((tab) => tab.id === tabToDelete)?.label ?? ''}
      />
    </GameDashboardLayout>
  )
}
