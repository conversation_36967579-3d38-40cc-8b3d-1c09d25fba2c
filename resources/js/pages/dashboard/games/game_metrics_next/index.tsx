import { useForm } from 'react-hook-form'
import { Box } from '@mui/material'
import * as changeCase from 'case-anything'
import { useEffect, useMemo } from 'react'

import {
  DashboardLayout,
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { useGraphql } from '@/components/toolkit-api'
import { isLoading, Loading } from '@/components/loading'
import { routes, useNavigate } from '@/components/location'
import { DateRange, FilterActions, FilterContainer, FilterGroup, Select } from '@/components/form'
import { canRead } from '#utils/acl'

import {
  getInitDataQuery,
  getMetricsQuery,
  usePagination,
  useParam,
  useParams,
  useSetParams,
} from './index/_util'
import { Table } from './index/_table'
import { SwitchButton } from '@/components/icons/switch_button'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'
import { NavTabs } from '../../game/_components/_nav_tabs'

export default function Page() {
  const { refresh, navigate } = useNavigate()

  const params = useParams()
  const setParams = useSetParams()
  const view = useParam((p) => p.view)
  const dateTo = useParam((p) => p.dateTo)
  const dateFrom = useParam((p) => p.dateFrom)
  const page = useParam((p) => p.page)
  const perPage = useParam((p) => p.perPage)

  const form = useForm({
    defaultValues: {
      dateRange: [dateFrom, dateTo],
    },
  })

  const getInitData = useGraphql(getInitDataQuery, { variables: { gameId: params.gameId } })
  const acl = useMemo(() => {
    if (!getInitData.data) {
      return {}
    }

    return Object.assign(
      Object.fromEntries(
        (getInitData.data?.attributes?.collection || []).map((a) => {
          return [`canView${changeCase.pascalCase(a.name)}`, canRead(a.permission)]
        })
      ),
      Object.fromEntries(
        (getInitData.data?.gameRevenueAttributes?.collection || []).map((a) => {
          return [`canViewGameRevenue${changeCase.pascalCase(a.name)}`, canRead(a.permission)]
        })
      )
    )
  }, [getInitData.data])

  const getMetrics = useGraphql(getMetricsQuery, {
    variables: {
      gameId: params.gameId,
      page,
      perPage,
      dateTo: dateTo.format('YYYY-MM-DD'),
      dateFrom: dateFrom.format('YYYY-MM-DD'),
      ...acl,
    },
    skip: isLoading([getInitData.error, getInitData.loading]),
  })

  const pagination = usePagination(getMetrics.data?.v2?.gameMetrics?.pageInfo)

  const loading = isLoading([
    getInitData.error,
    getInitData.loading,
    getMetrics.loading,
    getMetrics.error,
  ])

  useBreadcrumbs([
    {
      label: 'Games',
      url: routes.dash.games.index(),
    },
    {
      label: getInitData.data?.game?.name || params.gameId,
      url: routes.dash.gameMetrics.index(params.gameId),
    },
    'New Game Metrics',
  ])

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [, gameNavSize] = useLayoutSize('gameNav')
  const [filterRef, setFilterSize, filterSize] = useLayoutComponentRegistration('filter')
  const layoutHeight = useLayoutHeight(
    navbarSize,
    breadcrumbsSize,
    notificationSize,
    gameNavSize,
    filterSize
  )
  console.log('layoutHeight', layoutHeight)

  useEffect(() => {
    setFilterSize({
      height: filterRef.current?.clientHeight ?? 0,
    })
  }, [loading, filterRef, setFilterSize])

  if (loading) {
    return (
      <DashboardLayout>
        <NavTabs storeId={params.gameId} />

        <Loading visible />
      </DashboardLayout>
    )
  }

  return (
    <GameDashboardLayout gameId={params.gameId}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <FilterContainer sx={{ pb: 1 }} ref={filterRef}>
          <FilterGroup label="DATE">
            <DateRange
              defaultValue={[dateFrom, dateTo]}
              onChange={(dates) => {
                if (dates[0] && dates[1]) {
                  form.setValue('dateRange', [dates[0], dates[1]])
                }
              }}
            />
          </FilterGroup>

          <FilterGroup label="AD TYPE METRICS VIEW">
            <Select
              options={[
                { label: 'ImpsDau', value: 'impsDau' },
                { label: 'eCPM', value: 'ecpm' },
                { label: 'ARPDAU', value: 'arpDau' },
                { label: '% Revenue', value: 'revenue' },
              ].filter((e) =>
                getInitData.data!.gameRevenueAttributes.collection.some(
                  (a) => a.name === e.value && canRead(a.permission)
                )
              )}
              value={view}
              onChange={(e) => {
                setParams({ view: e.target.value as any })
              }}
            />
          </FilterGroup>

          <FilterActions
            onReset={() => refresh({ query: {} })}
            onSubmit={form.handleSubmit((data) => {
              setParams({
                dateFrom: data.dateRange[0]!,
                dateTo: data.dateRange[1]!,
              })
            })}
          />
        </FilterContainer>
        <SwitchButton
          tooltip="Switch to Partner view"
          onClick={() => navigate(routes.partner.games.metrics(params.gameId))}
        />
      </Box>

      <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem)` }}>
        <Table
          metrics={getMetrics.data!.v2.gameMetrics.collection}
          adTypes={getInitData.data!.adTypes.collection}
          summary={getMetrics.data!.v2.summary}
          total={getMetrics.data!.v2.total}
          view={view}
          pagination={pagination}
          attributes={getInitData.data!.attributes.collection}
        />
      </Box>
    </GameDashboardLayout>
  )
}
