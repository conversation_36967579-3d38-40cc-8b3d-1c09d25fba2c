import { GridColDef } from '@mui/x-data-grid-pro'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { orderBy, sumBy } from 'lodash-es'
import dayjs from 'dayjs'
import classNames from 'classnames'

import { formatter } from '#utils/formatter'
import {
  formatCurrency,
  formatDuration,
  formatNumber,
  formatPercentage,
} from '@/components/typography'
import { CURRENCY_COLORS, DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table'
import { PaginationState } from '@/components/pagination'
import { avgBy, maxBy, minBy, safeDivide } from '#utils/math'
import { EditTextarea } from '@/components/table/textarea'
import { canRead } from '#utils/acl'

import {
  AdTypeCollection,
  Aggregation,
  Attributes,
  GameMetric,
  GameMetricCollection,
} from './_util'

export function Table({
  metrics,
  adTypes,
  view,
  pagination,
  total,
  summary,
  attributes,
}: {
  metrics: GameMetricCollection
  adTypes: AdTypeCollection
  view: 'impsDau' | 'arpDau' | 'ecpm' | 'revenue'
  pagination: PaginationState
  total: Aggregation
  summary: Aggregation
  attributes: Attributes
}) {
  const { t } = useTranslation()

  const columns = useMemo<GridColDef<GameMetric>[]>(() => {
    return (
      [
        {
          field: 'date',
          valueFormatter: (v) => v,
        },
        {
          field: 'day',
          valueGetter: (_, row) => row.date,
          valueFormatter: (value, row) => (dayjs(row.date).isValid() ? formatter.day(value) : ''),
        },
        {
          field: 'paidInstalls',
          headerClassName: 'orange',
        },
        {
          field: 'organicInstalls',
          headerClassName: 'orange',
        },
        {
          field: 'totalInstalls',
          headerClassName: 'cyan',
        },
        {
          field: 'organicPercentage',
          headerClassName: 'cyan',
          valueFormatter: formatPercentage,
        },
        {
          field: 'cost',
          headerClassName: 'cyan',
          valueFormatter: formatCurrency,
        },
        {
          field: 'cpi',
          headerClassName: 'cyan',
          valueFormatter: (value) => formatCurrency(value, 4),
        },
        {
          field: 'roas',
          headerClassName: 'green',
          valueFormatter: formatPercentage,
        },
        {
          field: 'revenue',
          headerClassName: 'green',
          valueFormatter: formatCurrency,
        },
        {
          field: 'profit',
          headerClassName: 'green',
          valueFormatter: formatCurrency,
          cellClassName: ({ value }) => classNames({ green: value > 0, pink: value < 0 }),
        },
        {
          field: 'dailyActiveUsers',
          headerClassName: 'yellow',
        },
        {
          field: 'retentionRateDay1',
          headerClassName: 'orange',
          valueFormatter: formatPercentage,
        },
        {
          field: 'retentionRateDay3',
          headerClassName: 'orange',
          valueFormatter: formatPercentage,
        },
        {
          field: 'retentionRateDay7',
          headerClassName: 'orange',
          valueFormatter: formatPercentage,
        },
        ...orderBy(adTypes, (a) => a.order, 'asc')
          .flatMap((adType) => {
            const cols: GridColDef<GameMetric>[] = ['impsDau', 'ecpm', 'arpDau'].map((v) => ({
              field: `adPerformances.${adType.id}.${v}`,
              headerName: `${adType.name} ${t(`gameRevenue.${v}`)}`,
              headerClassName: 'orange',
              valueGetter: (_, row) =>
                row.adPerformances.find((e) => e.adType === adType.id)?.[
                  v as 'impsDau' | 'arpDau' | 'ecpm'
                ] ?? 0,
              valueFormatter: (value) => formatNumber(value, v === 'arpDau' ? 3 : 2),
            }))

            cols.push({
              field: `adPerformances.${adType.id}.revenue`,
              headerName: `${adType.name} ${t(`gameRevenue.revenue`)}`,
              headerClassName: 'orange',
              valueGetter: (_, row) =>
                safeDivide(
                  row.adPerformances.find((e) => e.adType === adType.id)?.revenue ?? 0,
                  row.revenue ?? 0
                ),
              valueFormatter: formatPercentage,
            })

            return cols
          })
          .filter((def) => def.field.endsWith(view)),
        {
          field: 'sessions',
          headerClassName: 'orange',
        },
        {
          field: 'playtime',
          headerClassName: 'orange',
          valueFormatter: formatDuration,
        },
        ...['versionNote', 'uaNote', 'monetNote', 'productNote', 'note'].map(
          (field): GridColDef<GameMetric> => ({
            field: field,
            valueFormatter: (v) => v,
            width: 200,
            editable: true,
            type: 'string',
            renderEditCell: (params) => <EditTextarea {...params} />,
          })
        ),
      ] as GridColDef<GameMetric>[]
    )
      .map((e) => ({
        headerName: t(`gameMetric.${e.field}`),
        valueFormatter: formatNumber,
        ...e,
      }))
      .filter((e) => {
        const attribute = attributes.find((a) => a.name === e.field)
        return attribute ? canRead(attribute.permission) : true
      })
  }, [view, t, adTypes])

  const calculateAggregation = useCallback(
    (fn: any, label: string): GameMetric => {
      const entries = Object.entries(total).map(([key, value]) => {
        if (key === 'adPerformances') {
          return [
            key,
            adTypes.map((adType) => {
              const adPerformances = metrics.flatMap((m) =>
                m.adPerformances.filter((ap) => ap.adType === adType.id)
              )

              return {
                adType: adType.id,
                impsDau: fn(adPerformances, 'impsDau'),
                revenue: fn(adPerformances, 'revenue'),
                ecpm: fn(adPerformances, 'ecpm'),
                arpDau: fn(adPerformances, 'arpDau'),
              }
            }),
          ]
        }
        try {
          return [key, fn(metrics, key)]
        } catch (err) {
          return [key, value]
        }
      })

      return {
        ...Object.fromEntries(entries),
        date: label,
        hierarchy: ['TOTAL', label],
      }
    },
    [total, metrics, adTypes]
  )

  const rows = useMemo(
    (): GameMetric[] => [
      { ...summary, date: 'SUMMARY', gameId: '' },
      { ...total, date: 'TOTAL', gameId: '', hierarchy: ['TOTAL'] },
      calculateAggregation(sumBy, 'SUM'),
      calculateAggregation(avgBy, 'AVG'),
      calculateAggregation(minBy, 'MIN'),
      calculateAggregation(maxBy, 'MAX'),
      ...metrics,
    ],
    [summary, total, metrics, calculateAggregation]
  )

  return (
    <DataGrid
      treeData
      getTreeDataPath={(row) => row.hierarchy || [row.date]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      initialState={{
        sorting: {
          sortModel: [
            {
              field: 'date',
              sort: 'desc',
            },
          ],
        },
        pinnedColumns: {
          left: ['date', 'day'],
        },
      }}
      pinnedRows={{
        top: [rows[0]],
      }}
      disableColumnSorting={false}
      actionColumn={false}
      editable
      pagination={pagination}
      showCellVerticalBorder
      getRowId={(row) => row.date}
      columns={columns}
      rows={rows}
      sx={{
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
          whiteSpace: 'break-spaces',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
        },
        ...CURRENCY_COLORS,
        '& .text-center': {
          textAlign: 'center !important',
        },
        '& .font-bold': {
          fontWeigth: 'bold',
        },
      }}
    />
  )
}
