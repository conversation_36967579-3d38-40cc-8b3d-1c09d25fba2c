import { mixed, number, object, string } from 'yup'
import dayjs from 'dayjs'

import { createParamsStore, routes } from '@/components/location'
import { graphql } from '@/gql'
import { GameMetricIndexV2_GetInitDataQuery, GameMetricIndexV2_GetMetricsQuery } from '@/graphql'

export const { useParam, useSetParams, useParams, usePagination } = createParamsStore<{
  gameId: string
  view: 'arpDau' | 'impsDau' | 'ecpm' | 'revenue'
  dateFrom: dayjs.Dayjs
  dateTo: dayjs.Dayjs
  page: number
  perPage: number
}>(routes.dash.gameMetricsNext.index(':gameId'), {
  parse: object({
    view: string().oneOf(['arpDau', 'impsDau', 'ecpm']).default('impsDau'),
    gameId: string().required(),
    dateFrom: mixed<dayjs.Dayjs>()
      .paramDate({
        format: 'YYYY-MM-DD',
        defaultValue: () => dayjs().subtract(30, 'days'),
      })
      .required(),
    dateTo: mixed<dayjs.Dayjs>()
      .paramDate({
        format: 'yyyy-MM-dd',
        defaultValue: () => dayjs(),
      })
      .required(),
    page: number().default(1),
    perPage: number().default(200).lessThan(1000),
  }),
  stringify: object({
    dateFrom: mixed().stringifyDate(),
    dateTo: mixed().stringifyDate(),
    gameId: mixed().transform(() => ''),
    page: mixed(),
    perPage: mixed(),
  }),
})

export const getInitDataQuery = graphql(`
  query GameMetricIndexV2_GetInitData($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }

    adTypes {
      collection {
        ...AdTypeAttributes
      }
    }

    attributes(where: { gameId: $gameId, modelName: "GameMetricV2" }) {
      collection {
        ...ModelAttribute
      }
    }

    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: "GameRevenue" }) {
      collection {
        ...ModelAttribute
      }
    }
  }
`)

export const getMetricsQuery = graphql(`
  query GameMetricIndexV2_GetMetrics(
    $gameId: ID!
    $page: Int!
    $perPage: Int!
    $dateFrom: Date!
    $dateTo: Date!
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewDailyActiveUsers: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewSessions: Boolean = false
    $canViewPlaytime: Boolean = false
    $canViewUaNote: Boolean = false
    $canViewMonetNote: Boolean = false
    $canViewProductNote: Boolean = false
    $canViewVersionNote: Boolean = false
    $canViewNote: Boolean = false
    $canViewGameRevenueRevenue: Boolean = false
    $canViewGameRevenueArpDau: Boolean = false
    $canViewGameRevenueEcpm: Boolean = false
    $canViewGameRevenueImpsDau: Boolean = false
  ) {
    v2 {
      gameMetrics(
        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }
        offset: { page: $page, perPage: $perPage }
      ) {
        collection {
          date
          gameId
          ...AclGameMetricV2Attributes

          uaNote @include(if: $canViewUaNote)
          monetNote @include(if: $canViewMonetNote)
          productNote @include(if: $canViewProductNote)
          versionNote @include(if: $canViewVersionNote)
          note @include(if: $canViewNote)
        }

        pageInfo {
          ...PageInfoAttributes
        }
      }

      summary: aggregateGameMetrics(id: "summary", where: { gameId: $gameId }) {
        ...AclGameMetricV2Attributes
      }

      total: aggregateGameMetrics(
        id: "total"
        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }
      ) {
        ...AclGameMetricV2Attributes
      }
    }
  }
`)

export type GameMetricCollection =
  GameMetricIndexV2_GetMetricsQuery['v2']['gameMetrics']['collection']
export type GameMetric = GameMetricCollection[0] & { hierarchy?: string[] }
export type Aggregation = GameMetricIndexV2_GetMetricsQuery['v2']['total']

export type AdTypeCollection = GameMetricIndexV2_GetInitDataQuery['adTypes']['collection']

export type Attributes = GameMetricIndexV2_GetInitDataQuery['attributes']['collection']
