import { Box } from '@mui/material'
import * as changeCase from 'case-anything'
import { fromPairs } from 'lodash-es'
import { useEffect, useMemo } from 'react'

import { useGraphql } from '@/components/toolkit-api'
import { usePagination } from '@/components/pagination'
import { routes, useParams } from '@/components/location'
import { useBreadcrumbs } from '@/components/layout/breadcrumbs'
import { useLayoutComponentRegistration, useLayoutHeight, useLayoutSize } from '@/components/layout'
import { useNavigate } from '@/components/ssr'
import { isLoading, Loading } from '@/components/loading'
import {
  DateRange,
  FilterActions,
  FilterContainer,
  FilterGroup,
  Select,
  useFilterForm,
} from '@/components/form'
import { canRead } from '#utils/acl'
import { GameMetricShow_GetInitDataQuery } from '@/graphql'

import { GameMetricTable } from './index/_table'
import { getDataQuery, getInitDataQuery } from './index/_util'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'

declare module '@/components/layout/size.js' {
  export interface LayoutComponents {
    filter: Size
  }
}

export default function PageWithLayout() {
  const { storeId } = useParams<{ storeId: string }>(routes.dash.gameMetrics.index(':storeId'))
  const getInitData = useGraphql(getInitDataQuery, {
    variables: {
      gameId: storeId,
    },
  })

  const loading = isLoading([getInitData.error, getInitData.loading])

  useBreadcrumbs(
    [
      {
        label: 'All Games',
        url: routes.dash.games.index(),
      },
      {
        label: getInitData.data?.game?.name ?? storeId,
        url: routes.dash.gameMetrics.index(storeId),
      },
      'Game Metrics',
    ],
    'dashboard'
  )

  return (
    <GameDashboardLayout gameId={storeId}>
      {loading ? <Loading visible /> : <Page initData={getInitData.data!} />}
    </GameDashboardLayout>
  )
}

function Page({ initData }: { initData: GameMetricShow_GetInitDataQuery }) {
  const pagination = usePagination()
  const { navigate } = useNavigate()

  const { storeId } = useParams<{ storeId: string }>(routes.dash.gameMetrics.index(':storeId'))

  const [filterForm, { direction, from, to }] = useFilterForm()

  const attrs = initData.attributes.collection
  const acl = useMemo(() => {
    return fromPairs(
      attrs
        .filter((a) => canRead(a.permission))
        .map((a) => [`canView${changeCase.upperCamelCase(a.name)}`, true])
    )
  }, [attrs])

  const getData = useGraphql(getDataQuery, {
    variables: {
      gameId: storeId,
      page: 1,
      perPage: 200,
      dateGte: from?.format('YYYY-MM-DD'),
      dateLte: to?.format('YYYY-MM-DD'),
      ...acl,
    },
  })

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [, gameNavSize] = useLayoutSize('gameNav')
  const [filterRef, setFilterSize, filterSize] = useLayoutComponentRegistration('filter')
  const layoutHeight = useLayoutHeight(
    navbarSize,
    breadcrumbsSize,
    notificationSize,
    gameNavSize,
    filterSize
  )
  console.log('layoutHeight', layoutHeight)

  useEffect(() => {
    setFilterSize({
      height: filterRef.current?.clientHeight || 0,
    })
  }, [filterRef, setFilterSize, getData.loading])

  if (isLoading([getData.loading, getData.error])) {
    return <Loading visible />
  }

  return (
    <>
      <FilterContainer
        sx={{
          pb: 1,
          gap: 2,
        }}
        ref={filterRef}
        spacing={0}
        direction={{
          xs: 'column',
          sm: 'row',
        }}
        alignItems={{
          xs: 'flex-start',
          sm: 'flex-end',
        }}
        component="form"
        onSubmit={filterForm.handleSubmit((formData) => {
          navigate(
            '',
            {
              from: formData.dateRange[0]?.format('YYYY-MM-DD'),
              to: formData.dateRange[1]?.format('YYYY-MM-DD'),
              direction: formData.orderDirection,
            },
            true
          )
        })}
      >
        <FilterGroup
          label="Select date"
          sx={{
            width: {
              xs: '100%',
              sm: '350px',
            },
          }}
        >
          <DateRange
            defaultValue={[from!, to!]}
            onChange={(values) => {
              filterForm.setValue('dateRange', [values[0]!, values[1]!])
            }}
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '100px',
            },
          }}
          label="Sort"
        >
          <Select
            {...filterForm.register('orderDirection')}
            defaultValue={direction}
            options={[
              { label: 'ASC', value: 'asc' },
              { label: 'DESC', value: 'desc' },
            ]}
            hint="Sort by DATE"
          />
        </FilterGroup>

        <FilterActions
          onReset={() => {
            navigate('', {}, false)
          }}
        />
      </FilterContainer>

      <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem)` }}>
        <GameMetricTable
          metrics={getData.data!.gameMetrics.collection}
          summary={getData.data!.summary}
          total={getData.data!.total}
          attrs={attrs}
          pagination={pagination}
          onUpdate={() => getData.refetch()}
        />
      </Box>
    </>
  )
}
