import { graphql } from '@/gql'
import { GameMetricShow_GetDataQuery } from '@/graphql'

export type GameMetricAcl = {
  [P in keyof Omit<GameMetric, '__typename'> as `canView${Capitalize<P>}`]+?: boolean
}

export const getInitDataQuery = graphql(`
  query GameMetricShow_GetInitData($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }

    attributes(where: { gameId: $gameId, modelName: "GameMetric" }) {
      collection {
        ...ModelAttribute
      }
    }
  }
`)

export const getDataQuery = graphql(`
  query GameMetricShow_GetData(
    $gameId: ID!
    $page: Int!
    $perPage: Int!
    $dateGte: Date
    $dateLte: Date
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewBannerImpsDau: Boolean = false
    $canViewInterImpsDau: Boolean = false
    $canViewRewardImpsDau: Boolean = false
    $canViewAoaImpsDau: Boolean = false
    $canViewMrecImpsDau: Boolean = false
    $canViewAudioImpsDau: Boolean = false
    $canViewAoaAdmobImpsDau: Boolean = false
    $canViewCollapseAdmobImpsDau: Boolean = false
    $canViewNativeAdmobImpsDau: Boolean = false
    $canViewAdaptiveAdmobImpsDau: Boolean = false
    $canViewMrecAdmobImpsDau: Boolean = false
    $canViewAverageSession: Boolean = false
    $canViewPlaytime: Boolean = false
    $canViewUaNote: Boolean = false
    $canViewMonetNote: Boolean = false
    $canViewProductNote: Boolean = false
    $canViewVersionNote: Boolean = false
    $canViewNote: Boolean = false
  ) {
    gameMetrics(
      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }
      offset: { page: $page, perPage: $perPage }
    ) {
      collection {
        id
        date
        ...AclGameMetricAttributes
        ...AclGameMetricMetadataAttributes
      }

      pageInfo {
        ...PageInfoAttributes
      }
    }

    summary: aggregateGameMetric(where: { gameId: $gameId }) {
      ...AclGameMetricAttributes
    }

    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {
      ...AclGameMetricAttributes
    }
  }
`)

export type Aggregation = GameMetricShow_GetDataQuery['summary']
export type GameMetrics = GameMetricShow_GetDataQuery['gameMetrics']['collection']
export type GameMetric = GameMetrics[0] & { hierarchy?: string[]; autogen?: boolean }
