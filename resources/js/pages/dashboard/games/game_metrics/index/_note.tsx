import { Mo<PERSON>, <PERSON>, Typo<PERSON>, <PERSON><PERSON>ield, Button, Tooltip } from '@mui/material'
import { useState } from 'react'
import {
  GridRenderEditCellParams,
  GridTreeNodeWithRender,
  useGridApiContext,
} from '@mui/x-data-grid-pro'

import GameMetric from '#models/game_metric'

export function GameMetricNoteField({
  params: { value, hasFocus, colDef, id, row },
}: {
  params: GridRenderEditCellParams<GameMetric, any, any, GridTreeNodeWithRender>
}) {
  const [isOpen, setIsOpen] = useState(hasFocus)
  const apiRef = useGridApiContext()

  if (row.autogen) {
    return null
  }

  return (
    <>
      <Tooltip enterDelay={150} title={value}>
        <Box
          sx={{
            width: '100%',
          }}
          onClick={() => setIsOpen(true)}
        >
          {value}
        </Box>
      </Tooltip>
      <Modal open={isOpen} onClose={() => setIsOpen(false)}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            bgcolor: 'background.paper',
            border: '2px solid #000',
            boxShadow: 24,
            p: 4,
          }}
        >
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Edit {colDef.headerName}
          </Typography>
          <Box id="modal-modal-description" sx={{ mt: 2 }}>
            <TextField
              multiline
              defaultValue={value}
              autoFocus
              fullWidth
              onChange={(event) => {
                apiRef.current.setEditCellValue({
                  id,
                  value: event.target.value,
                  field: colDef.field,
                  debounceMs: 100,
                })
              }}
            />

            <Button
              variant="contained"
              size="small"
              sx={{ mt: 2 }}
              onClick={() => setIsOpen(false)}
            >
              Save
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  )
}
