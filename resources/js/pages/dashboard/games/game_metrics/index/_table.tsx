import { HTMLAttributes, TableHTMLAttributes, useCallback, useMemo } from 'react'
import { GridColDef, GridRowEditStopReasons, GridRowId, useGridApiRef } from '@mui/x-data-grid-pro'
import classNames from 'classnames'
import { fromPairs, sumBy, isNil } from 'lodash-es'
import { useTranslation } from 'react-i18next'

import { formatter } from '#utils/formatter'
import { PaginationState } from '@/components/pagination'
import { useUpdateGameMetric } from '@/components/toolkit-api'
import { CURRENCY_COLORS, DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table/datagrid'
import { avgBy, maxBy, minBy } from '#utils/math'
import { mergeIf } from '#utils/pure'
import { ModelAttributeFragment } from '@/graphql'
import { canRead } from '#utils/acl'
import { EditTextarea } from '@/components/table/textarea'

import { Aggregation, GameMetric } from './_util'
import { routes, useParams } from '@/components/location'

const SUMMARY_ID = -2
export function isAggregationRow(row: GameMetric) {
  return row.id < 0 || row.id === SUMMARY_ID
}

function isKeyboardEvent(event: any): event is React.KeyboardEvent {
  return !!event.key
}

export function GameMetricTable({
  metrics,
  summary,
  total,
  attrs,
  pagination,
  onUpdate,
}: {
  metrics: GameMetric[]
  summary: Aggregation
  total: Aggregation
  pagination: PaginationState
  attrs: ModelAttributeFragment[]
  onUpdate: () => void
} & HTMLAttributes<HTMLTableElement & TableHTMLAttributes<HTMLTableElement>>) {
  const { t } = useTranslation()
  const apiRef = useGridApiRef()
  const { storeId } = useParams<{ storeId: string }>(routes.dash.gameMetrics.index(':storeId'))

  const columns = useMemo<GridColDef<GameMetric>[]>(() => {
    return [
      {
        field: 'date',
        headerName: 'Date',
        width: 80,
        colSpan: (_, row) => {
          if (isAggregationRow(row)) {
            return 2
          }

          return 1
        },
        cellClassName: ({ row }) =>
          classNames('text-center', {
            'font-bold': isAggregationRow(row),
          }),
      },
      {
        field: 'day',
        headerName: 'Day',
        width: 50,
        valueGetter: (_, metric) => formatter.day(metric.date as any),
        cellClassName: 'text-center',
      },
      {
        field: 'paidInstalls',
        headerName: 'Paid Installs',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 75,
      },
      {
        field: 'organicInstalls',
        headerName: 'Organic Installs',
        valueFormatter: (value) => !isNil(value) && formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 75,
      },
      {
        field: 'totalInstalls',
        headerName: 'Total Installs',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'cyan',
        width: 75,
      },
      {
        field: 'organicPercentage',
        headerName: '% Organic',
        valueFormatter: (value) => formatter.percentage(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'cyan',
        width: 75,
      },
      {
        field: 'cost',
        headerName: 'Cost',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'cyan',
        width: 80,
        editable: true,
      },
      {
        field: 'cpi',
        headerName: 'CPI',
        valueFormatter: (value) => formatter.round(value, 4),
        valueGetter: (value) => value || 0,
        headerClassName: 'cyan',
        width: 60,
      },
      {
        field: 'roas',
        headerName: 'ROAS',
        valueFormatter: (value) => formatter.percentage(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'green',
        width: 75,
      },
      {
        field: 'revenue',
        headerName: 'Total Revenue',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'green',
        width: 80,
        editable: true,
      },
      {
        field: 'profit',
        headerName: 'Profit',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'green',
        cellClassName: ({ value }) =>
          classNames(
            !isNil(value) && {
              pink: value <= 0,
              green: value > 0,
            }
          ),
        width: 75,
      },
      {
        field: 'retentionRateDay1',
        headerName: 'RR D-1',
        valueFormatter: (value) => formatter.percentage(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 65,
      },
      {
        field: 'retentionRateDay3',
        headerName: 'RR D-3',
        valueFormatter: (value) => formatter.percentage(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 65,
      },
      {
        field: 'retentionRateDay7',
        headerName: 'RR D-7',
        valueFormatter: (value) => formatter.percentage(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 65,
      },
      {
        field: 'bannerImpsDau',
        headerName: 'Banner ImpsDAU',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'interImpsDau',
        headerName: 'Inter ImpsDAU',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'rewardImpsDau',
        headerName: 'Reward ImpsDAU',
        valueFormatter: (value) => formatter.round(value),
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'aoaImpsDau',
        headerName: 'AOA ImpsDAU',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'mrecImpsDau',
        headerName: 'MREC ImpsDAU',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'aoaAdmobImpsDau',
        headerName: 'AOA Admob ImpsDAU',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'collapseAdmobImpsDau',
        headerName: 'Collapse Admob ImpsDAU',
        valueFormatter: (value) => (isNil(value) ? null : formatter.round(value)),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'nativeAdmobImpsDau',
        headerName: 'Native Admob ImpsDAU',
        valueFormatter: (value) => (isNil(value) ? null : formatter.round(value)),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'adaptiveAdmobImpsDau',
        headerName: 'Adaptive Admob ImpsDAU',
        valueFormatter: (value) => (isNil(value) ? null : formatter.round(value)),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'mrecAdmobImpsDau',
        headerName: 'MREC Admob ImpsDAU',
        valueFormatter: (value) => (isNil(value) ? null : formatter.round(value)),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'audioImpsDau',
        headerName: 'Audio ImpsDAU',
        valueFormatter: (value) => (isNil(value) ? null : formatter.round(value)),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'averageSession',
        headerName: 'AVG Sessions',
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      {
        field: 'playtime',
        headerName: 'Playtime (minutes)',
        valueFormatter: (value) => {
          const time = formatter.time('s', 'm')(value)
          return `${formatter.round(time['m'])}m${formatter.round(time['s'])}s`
        },
        valueGetter: (value) => value || 0,
        headerClassName: 'orange',
        width: 80,
      },
      ...['versionNote', 'uaNote', 'monetNote', 'productNote', 'note'].map(
        (field): GridColDef<GameMetric> => ({
          field: field,
          width: 200,
          editable: true,
          type: 'string',
          renderEditCell: (params) => <EditTextarea {...params} />,
          headerName: t(`gameMetric.${field}`),
        })
      ),
      {
        field: 'dummy',
        headerName: '',
        valueGetter: () => '',
        width: 30,
      },
    ]
  }, [t])

  const aggregate = useCallback(
    (
      fn: any,
      hierarchy: string[],
      id: number
    ): { -readonly [P in keyof GameMetric]: GameMetric[P] } => {
      return {
        id,
        date: hierarchy[hierarchy.length - 1] as any,
        hierarchy,
        ...fromPairs(
          Object.keys(summary)
            .map((key) => {
              try {
                const val = fn(metrics, key)
                return [key, val]
              } catch (err) {
                return null
              }
            })
            .filter((e): e is [string, any] => !!e)
        ),
      } as unknown as GameMetric
    },
    [summary, metrics]
  )

  const rows = useMemo<GameMetric[]>(() => {
    return [
      { ...summary, id: -1, date: 'Summary' as any } as GameMetric,
      { ...total, id: -2, date: 'Total' as any } as GameMetric,
      aggregate(sumBy, ['Total', 'SUM'], -3),
      aggregate(avgBy, ['Total', 'AVG'], -4),
      aggregate(maxBy, ['Total', 'MAX'], -5),
      aggregate(minBy, ['Total', 'MIN'], -6),
      ...metrics,
    ]
  }, [metrics, summary, total])

  const updateGameMetric = useUpdateGameMetric({
    onSuccess: () => {
      return onUpdate()
    },
  } as any)

  const onRowUpdate = async (
    updatedRow: GameMetric,
    originalRow: GameMetric,
    params: { rowId: GridRowId }
  ) => {
    if (originalRow.autogen) {
      return originalRow
    }

    const metric = await updateGameMetric.mutateAsync({
      date: originalRow.date,
      gameId: storeId,
      metadata: {
        monetNote: updatedRow.monetNote || '',
        uaNote: updatedRow.uaNote || '',
        productNote: updatedRow.productNote || '',
        versionNote: updatedRow.versionNote || '',
        note: updatedRow.note || '',
      },
      override: mergeIf({}, [
        {
          check: updatedRow.cost !== originalRow.cost,
          value: { cost: updatedRow.cost },
        },
        {
          check: updatedRow.revenue !== originalRow.revenue,
          value: { revenue: updatedRow.revenue },
        },
        {
          check: String(updatedRow.revenue) === '',
          value: { revenue: null },
        },
        {
          check: String(updatedRow.cost) === '',
          value: { cost: null },
        },
      ]),
    })

    apiRef.current.stopRowEditMode({ id: params.rowId })

    return Object.assign({}, originalRow, metric.metadata, metric)
  }

  const tableColumns: GridColDef<GameMetric>[] = [...columns]

  return (
    <DataGrid
      rows={rows}
      columns={tableColumns.filter((c) => {
        const attr = attrs.find((a) => a.name === c.field)
        return attr ? canRead(attr.permission) : true
      })}
      pinnedRows={{
        top: [rows[0]],
      }}
      initialState={{
        pinnedColumns: {
          left: ['date', 'day'],
          right: ['actions'],
        },
      }}
      apiRef={apiRef}
      treeData
      getTreeDataPath={(row) => row.hierarchy || [row.date.toString()]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      editable
      fieldToFocus="versionNote"
      actionColumn={{
        width: 80,
        getActions: ({ row }) => {
          if (isAggregationRow(row) || row.autogen) {
            return []
          }
        },
      }}
      disableColumnSorting={false}
      processRowUpdate={onRowUpdate}
      onProcessRowUpdateError={(err) => {
        console.error(err)
      }}
      pagination={pagination}
      sx={{
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
        },
        ...CURRENCY_COLORS,
        '& .text-center': {
          textAlign: 'center !important',
        },
        '& .font-bold': {
          fontWeigth: 'bold',
        },
      }}
      onRowEditStop={(params, event) => {
        if (params.reason !== GridRowEditStopReasons.enterKeyDown) {
          return
        }

        if (isKeyboardEvent(event) && !event.ctrlKey && !event.metaKey) {
          event.defaultMuiPrevented = true
        }
      }}
    />
  )
}
