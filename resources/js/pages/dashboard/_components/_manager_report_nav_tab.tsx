import { NavTabs, useLayoutComponentRegistration } from '@/components/layout'
import { routes } from '@/components/location'
import { Box } from '@mui/material'
import { useLayoutEffect, useMemo } from 'react'

export function ManagerReportNavTab() {
  const [ref, setSize] = useLayoutComponentRegistration('gameNav')

  useLayoutEffect(() => {
    setSize({ height: ref.current?.clientHeight ?? 0 })
  }, [])

  return (
    <Box sx={{ pb: 2 }} ref={ref}>
      <NavTabs
        items={useMemo(
          () => [
            { href: routes.dash.revenueReports.index(), label: 'Revenue Report' },
            { href: routes.dash.gameStudioMetrics.index(), label: 'Studio Metrics' },
          ],
          []
        )}
      />
    </Box>
  )
}
