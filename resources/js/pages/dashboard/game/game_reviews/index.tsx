import Box from '@mui/material/Box'
import { Grid } from '@mui/material'
import { yupResolver } from '@hookform/resolvers/yup'
import { mixed, object } from 'yup'
import dayjs from 'dayjs'
import { useForm } from 'react-hook-form'

import { useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import { GameAttributesFragment } from '@/graphql'
import { useGraphql } from '@/components/toolkit-api'
import { routes, useParams } from '@/components/location'
import { isLoading, Loading } from '@/components/loading'
import { DatePicker, FilterActions, FilterContainer, FilterGroup } from '@/components/form'
import { useNavigate } from '@/components/ssr'
import { useConfigMaps } from '@/components/configmaps'

import { Notes } from './index/_note'
import { WeeklyTracking } from './index/_weekly_tracking'
import {
  dataQueryDocument,
  GameReviewPerformanceSetting,
  settingQueryDocument,
} from './index/_types'
import { Benchmark } from './index/_benchmark'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useMemo } from 'react'

render(PageWithLayout)

const schema = object({
  date: mixed<dayjs.Dayjs>().required(),
})

export function PageWithLayout() {
  const { gameId } = useParams<{ gameId: string; date: string }>(
    routes.dash.gameReviews.index(':gameId')
  )

  const getData = useGraphql(settingQueryDocument, {
    variables: { gameId },
  })

  return (
    <GameDashboardLayout gameId={gameId}>
      {getData.loading ? (
        <Loading visible />
      ) : (
        <Page
          game={getData.data!.game!}
          performanceSetting={getData.data!.gamePerformanceSetting}
        />
      )}
    </GameDashboardLayout>
  )
}

function Page({
  game,
  performanceSetting,
}: {
  game: GameAttributesFragment
  performanceSetting: GameReviewPerformanceSetting
}) {
  const { date } = useParams<{ date: string }>()
  const { navigate } = useNavigate()

  useBreadcrumbs([
    {
      url: routes.dash.games.index(),
      label: 'All Games',
    },
    {
      url: routes.dash.gameMetrics.index(game.id),
      label: game.name,
    },
    'Game Review',
  ])

  const { formState, setValue, handleSubmit } = useForm({
    // @ts-ignore
    resolver: yupResolver(schema),
    defaultValues: { date: date ? dayjs(date, 'YYYY-MM-DD') : undefined },
  })

  const shouldGetGameMetrics = formState.isValid && !formState.isDirty

  const getData = useGraphql(dataQueryDocument, {
    variables: {
      gameId: game.id,
      from: date,
      to: dayjs(date, 'YYYY-MM-DD').add(6, 'day').format('YYYY-MM-DD'),
    },
    skip: !shouldGetGameMetrics,
  })

  const [[gameReviewConfigMapCollection], getConfigMaps] = useConfigMap(
    useMemo(() => ['cmap.dash.review'], [])
  )

  if (
    isLoading([
      shouldGetGameMetrics && getData.loading,
      getData.error,
      getConfigMaps.loading,
      getConfigMaps.error,
    ])
  ) {
    return <Loading visible />
  }

  return (
    <Grid container>
      <Grid size={6}>
        <FilterContainer
          sx={{
            mb: 2,
            gap: 2,
          }}
          spacing={0}
          direction={{
            xs: 'column',
            sm: 'row',
          }}
          alignItems={{
            xs: 'flex-start',
            sm: 'flex-end',
          }}
          component="form"
          onSubmit={handleSubmit((data) => {
            navigate('', {
              date: data.date?.format('YYYY-MM-DD'),
            })
          })}
        >
          <FilterGroup label="Date">
            <DatePicker
              onChange={(value) => setValue('date', value!)}
              slotProps={{ textField: { size: 'small' } }}
              defaultValue={date ? dayjs(date) : null}
              format="YYYY-MM-DD"
            />
          </FilterGroup>

          <FilterActions />
        </FilterContainer>

        <Benchmark performanceSetting={performanceSetting} game={game} />

        {shouldGetGameMetrics && (
          <>
            <WeeklyTracking
              metrics={getData.data!.gameMetrics.collection}
              gameReviewConfigMap={gameReviewConfigMapCollection[0]}
              performanceSetting={performanceSetting}
            />

            <Box mt={2}>
              <Notes game={game} date={date} gameReview={getData.data!.gameReview} />
            </Box>
          </>
        )}
      </Grid>
      <Grid size={6}></Grid>
    </Grid>
  )
}
