import { Box, Button, Typography } from '@mui/material'

import { useEditorControl, WYSIWYG } from '@/components/wysiwyg'
import { GameAttributesFragment } from '@/graphql'
import { useUpdateGameReview } from '@/components/toolkit-api'

import { GameReview } from './_types'

export function Notes({
  game,
  date,
  gameReview,
}: {
  game: GameAttributesFragment
  date: string
  gameReview: GameReview
}) {
  const productNoteEditor = useEditorControl()
  const mktNoteEditor = useEditorControl()

  const updateGameReview = useUpdateGameReview()

  const updateProductNote = () => {
    return updateGameReview.mutateAsync({
      gameId: game.id,
      date,
      productNote: productNoteEditor.getContent(),
    })
  }

  const updateMarketingNote = () => {
    return updateGameReview.mutateAsync({
      gameId: game.id,
      date,
      marketingNote: mktNoteEditor.getContent(),
    })
  }

  return (
    <div>
      <Box
        sx={{
          [`.ck-editor__editable`]: {
            minHeight: 300,
          },
        }}
      >
        <Typography>Product</Typography>
        <WYSIWYG
          control={productNoteEditor}
          config={{ initialData: gameReview.productNote || '' }}
        />

        <Button onClick={updateProductNote} variant="contained" sx={{ mt: 1 }}>
          Save
        </Button>
      </Box>

      <Box
        mt={2}
        sx={{
          [`.ck-editor__editable`]: {
            minHeight: 300,
          },
        }}
      >
        <Typography>Marketing</Typography>
        <WYSIWYG control={mktNoteEditor} config={{ initialData: gameReview.marketingNote || '' }} />

        <Button onClick={updateMarketingNote} variant="contained" sx={{ mt: 1 }}>
          Save
        </Button>
      </Box>
    </div>
  )
}
