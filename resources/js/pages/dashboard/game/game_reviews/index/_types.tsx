import { createEnum } from '#utils/pure'
import {
  GamePerformanceCriteriaConclusion,
  GameReviewIndex_DataQuery,
  GameReviewIndex_SettingQuery,
  graphql,
} from '@/graphql'

export type GameReviewPerformanceSetting = GameReviewIndex_SettingQuery['gamePerformanceSetting']
export type GamePerformanceCriteria = GameReviewPerformanceSetting['criterias'][0]
export type GameMetric = GameReviewIndex_DataQuery['gameMetrics']['collection'][0] & {
  monetImpsDau?: number
}
export type GameReview = GameReviewIndex_DataQuery['gameReview']

export const Conclusion = createEnum({
  [GamePerformanceCriteriaConclusion.Fail]: 0,
  [GamePerformanceCriteriaConclusion.Pass]: 1,
  [GamePerformanceCriteriaConclusion.Drop]: -1,
})

export const settingQueryDocument = graphql(`
  query GameReviewIndex_Setting($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }
    gamePerformanceSetting(gameId: $gameId) {
      criterias {
        conclusion
        metrics
      }
    }
  }
`)

export const dataQueryDocument = graphql(`
  query GameReviewIndex_Data($gameId: ID!, $from: Date!, $to: Date!) {
    gameMetrics(
      where: { gameId: $gameId, dateGte: $from, dateLte: $to }
      offset: { perPage: 7, page: 1 }
      order: { direction: ASC }
    ) {
      collection {
        date
        roas
        playtime
        retentionRateDay1
        cpi
        bannerImpsDau
        interImpsDau
        ...GameMetricMetadata
      }
    }

    gameReview(gameId: $gameId, date: $from) {
      productNote
      marketingNote
    }
  }
`)
