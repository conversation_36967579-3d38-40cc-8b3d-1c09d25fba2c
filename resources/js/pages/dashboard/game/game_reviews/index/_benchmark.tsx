import Box from '@mui/material/Box'
import { Typography } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useMemo } from 'react'
import { set } from 'lodash-es'
import { useApolloClient } from '@apollo/client'

import { GameAttributesFragment } from '@/graphql'
import { useUpdateGamePerformanceSetting } from '@/components/toolkit-api'
import { DataGrid } from '@/components/table'
import { formatter } from '#utils/formatter'

import {
  GamePerformanceCriteria,
  GameReviewPerformanceSetting,
  settingQueryDocument,
} from './_types'

export function Benchmark({
  performanceSetting,
  game,
}: {
  performanceSetting: GameReviewPerformanceSetting
  game: GameAttributesFragment
}) {
  const updatePerformanceSetting = useUpdateGamePerformanceSetting({
    onSuccess: (response) => {
      apolloClient.cache.updateQuery(
        { query: settingQueryDocument, variables: { gameId: game.id } },
        (data) => {
          return {
            ...data!,
            gamePerformanceSetting: {
              ...data!.gamePerformanceSetting!,
              criterias: response.data.criterias,
            },
          }
        }
      )
    },
  })
  const apolloClient = useApolloClient()

  const rows = useMemo(() => {
    return performanceSetting.criterias.flatMap((c) => ({
      ...c,
      metrics: {
        ...c.metrics,
      },
    }))
  }, [performanceSetting.criterias])

  const columns = useMemo<GridColDef<GamePerformanceCriteria>[]>(() => {
    return [
      {
        field: 'conclusion',
        headerName: 'Status',
      },
      {
        field: 'metrics.roas',
        headerName: 'ROAS',
        valueGetter: (_, row) => row.metrics.roas,
        valueSetter: (value, row, col) => set(row, col.field, value),
        valueFormatter: (value) => formatter.percentage(value),
        editable: true,
      },
      {
        field: 'metrics.playtime',
        headerName: 'Timeplay',
        valueGetter: (_, row) => row.metrics.playtime,
        valueSetter: (value, row, col) => set(row, col.field, value),
        valueFormatter: (value) => {
          const units = formatter.time('s', 'm')(value)
          return `${units['m']}m${units['s']}s`
        },
        editable: true,
      },
      {
        field: 'metrics.retentionRateDay1',
        headerName: 'RR D1',
        valueGetter: (_, row) => row.metrics.retentionRateDay1,
        valueSetter: (value, row, col) => set(row, col.field, value),
        valueFormatter: (value) => formatter.percentage(value),
        editable: true,
      },
      {
        field: 'metrics.cpi',
        headerName: 'UA (CPI)',
        valueGetter: (_, row) => row.metrics.cpi,
        valueSetter: (value, row, col) => set(row, col.field, value),
        valueFormatter: (value) => `$${formatter.round(value)}`,
        editable: true,
      },
      {
        field: 'metrics.monetImpsDau',
        headerName: 'Monet (Ad)',
        valueGetter: (_, row) => row.metrics.monetImpsDau,
        valueSetter: (value, row, col) => set(row, col.field, value),
        valueFormatter: (value) => formatter.round(value),
        editable: true,
      },
    ]
  }, [])

  return (
    <Box>
      <Typography>Benchmark</Typography>
      <DataGrid
        processRowUpdate={async (updatedRow, originalRow) => {
          return updatePerformanceSetting
            .mutateAsync({
              gameId: game.id,
              criterias: performanceSetting.criterias.map((c) => {
                console.log(updatedRow, c)
                if (c.conclusion === updatedRow.conclusion) {
                  return updatedRow
                }

                return {
                  conclusion: c.conclusion,
                  metrics: c.metrics,
                }
              }),
            })
            .then(() => updatedRow)
            .catch(() => originalRow)
        }}
        getRowId={(row) => row.conclusion}
        columns={columns}
        rows={rows}
        editable
        actionColumn={{}}
        hideFooter
        slots={{
          toolbar: null,
        }}
      />
    </Box>
  )
}
