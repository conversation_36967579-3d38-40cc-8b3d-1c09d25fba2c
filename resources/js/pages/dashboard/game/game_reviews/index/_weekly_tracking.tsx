import Box from '@mui/material/Box'
import { Typography } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useCallback, useMemo } from 'react'
import dayjs from 'dayjs'
import { sumBy, thru, isNil } from 'lodash-es'
import { useTranslation } from 'react-i18next'

import { DataGrid } from '@/components/table'
import { formatter } from '#utils/formatter'
import { avgBy, safeDivide } from '#utils/math'
import GameReviewConfigMap, {
  GamePerformanceSuggestionConfigMap,
} from '#configmaps/dashboard/game_review'
import { GamePerformanceCriteriaConclusion, Operator } from '#graphql/main'

import { Conclusion, GameMetric, GameReviewPerformanceSetting } from './_types'

export function WeeklyTracking({
  metrics,
  gameReviewConfigMap,
  performanceSetting,
}: {
  metrics: GameMetric[]
  gameReviewConfigMap: GameReviewConfigMap
  performanceSetting: GameReviewPerformanceSetting
}) {
  const { t } = useTranslation()

  const evaluateMetric = useCallback(
    (metric: GameMetric, metricId: string): GamePerformanceCriteriaConclusion => {
      const criteriaConfigMap = gameReviewConfigMap.performanceCriterias.find(
        (c) => c.metricId === metricId
      )!

      console.log(metricId, criteriaConfigMap)

      const metricValue = metric[metricId as unknown as keyof typeof metric]
      const conclusion = criteriaConfigMap.comparisons.find((c) => {
        const baseValue = performanceSetting.criterias.find((s) => s.conclusion === c.conclusion)!
          .metrics[metricId as unknown as keyof typeof metric]

        console.log('compare', metricId, baseValue)

        switch (c.operator) {
          case Operator.Gte:
            return metricValue >= baseValue

          case Operator.Lte:
            return metricValue <= baseValue

          case Operator.Eq:
            return metricValue === baseValue

          case Operator.Ne:
            return metricValue !== baseValue

          case Operator.Gt:
            return metricValue > baseValue

          case Operator.Lt:
            return metricValue < baseValue

          default:
            return null
        }
      })?.conclusion

      return isNil(conclusion) ? GamePerformanceCriteriaConclusion.Fail : conclusion
    },
    [gameReviewConfigMap, performanceSetting]
  )

  const formatTracking = useCallback(
    (
      trackingFormatter: (value: any, row: any) => any,
      evalFormatter: (value: any, row: any) => any
    ) => {
      return (value: any, row: any, _col: any) => {
        if (row.date !== 'Status') {
          return trackingFormatter(value, row)
        }

        return evalFormatter(value, row)
      }
    },
    [metrics]
  )

  // @ts-ignore
  const columns = useMemo<GridColDef<GameMetric>[]>(() => {
    return [
      {
        field: 'id',
        headerName: 'Week 1',
        valueGetter: (_, row, _col, api) =>
          api.current.getRowIndexRelativeToVisibleRows(row.date) + 1,
        valueFormatter: (value) => (value <= metrics.length ? `Day ${value}` : ''),
        width: 70,
      },
      {
        field: 'date',
        headerName: 'Date',
        valueGetter: (value, row) =>
          thru(dayjs(row.date, 'YYYY-MM-DD'), (d) =>
            d.isValid() ? d.format('YYYY-MM-DD') : value
          ),
        width: 90,
      },
      {
        field: 'roas',
        headerName: 'ROAS',
        valueFormatter: formatTracking(
          (value) => formatter.percentage(value),
          (value) => t(`gameReview.conclusion.${value}`)
        ),
      },
      {
        field: 'playtime',
        headerName: 'Timeplay',
        valueFormatter: formatTracking(
          (value) => {
            const units = formatter.time('s', 'm')(value)
            return `${units['m']}m${units['s']}s`
          },
          (value) => t(`gameReview.conclusion.${value}`)
        ),
      },
      {
        field: 'retentionRateDay1',
        headerName: 'RR D1',
        valueFormatter: formatTracking(
          (value) => formatter.percentage(value),
          (value) => t(`gameReview.conclusion.${value}`)
        ),
      },
      {
        field: 'cpi',
        headerName: 'UA (CPI)',
        valueFormatter: formatTracking(
          (value) => `$${formatter.round(value)}`,
          (value) => t(`gameReview.conclusion.${value}`)
        ),
      },
      {
        field: 'monetImpsDau',
        headerName: 'Monet (Ad)',
        valueFormatter: formatTracking(
          (_, row) => formatter.round(row.bannerImpsDau + row.interImpsDau),
          (_, row) => t(`gameReview.conclusion.${row.monetImpsDau}`)
        ),
      },
      {
        field: 'note',
        headerName: 'Note',
        width: 200,
        valueGetter: (_, row) => row.metadata?.note,
      },
      {
        field: 'versionNote',
        headerName: 'Version',
        width: 200,
        valueGetter: (_, row) => row.metadata?.versionNote,
      },
      {
        field: 'uaNote',
        headerName: 'UA',
        width: 200,
        valueGetter: (_, row) => row.metadata?.uaNote,
      },
      {
        field: 'productNote',
        headerName: 'Product',
        width: 200,
        valueGetter: (_, row) => row.metadata?.productNote,
      },
      {
        field: 'monetNote',
        headerName: 'Monet',
        width: 200,
        valueGetter: (_, row) => row.metadata?.monetNote,
      },
    ]
  }, [t, formatTracking])

  const avgRow: GameMetric = {
    date: 'AVG',
    cpi: avgBy(metrics, 'cpi'),
    bannerImpsDau: safeDivide(
      sumBy(metrics, 'bannerImpsDau') + sumBy(metrics, 'interImpsDau'),
      metrics.length
    ),
    monetImpsDau: safeDivide(
      sumBy(metrics, 'bannerImpsDau') + sumBy(metrics, 'interImpsDau'),
      metrics.length
    ),
    interImpsDau: 0,
    playtime: avgBy(metrics, 'playtime'),
    retentionRateDay1: avgBy(metrics, 'retentionRateDay1'),
    roas: avgBy(metrics, 'roas'),
  }

  const evalRow: GameMetric = {
    date: 'Status',
    cpi: Conclusion.getValue(evaluateMetric(avgRow, 'cpi'))!,
    bannerImpsDau: 0,
    interImpsDau: 0,
    monetImpsDau: Conclusion.getValue(evaluateMetric(avgRow, 'monetImpsDau'))!,
    playtime: Conclusion.getValue(evaluateMetric(avgRow, 'playtime'))!,
    retentionRateDay1: Conclusion.getValue(evaluateMetric(avgRow, 'retentionRateDay1'))!,
    roas: Conclusion.getValue(evaluateMetric(avgRow, 'roas'))!,
  }

  const suggestionRow =
    gameReviewConfigMap.performanceSuggestions.find((e) => {
      return (
        e.cpi === Conclusion.getKey(evalRow.cpi) &&
        e.monetImpsDau === Conclusion.getKey(evalRow.monetImpsDau!) &&
        e.playtime === Conclusion.getKey(evalRow.playtime) &&
        e.retentionRateDay1 === Conclusion.getKey(evalRow.retentionRateDay1) &&
        e.roas === Conclusion.getKey(evalRow.roas)
      )
    }) || ({ id: '0' } as GamePerformanceSuggestionConfigMap)

  const suggestionColumns = useMemo<GridColDef<GamePerformanceSuggestionConfigMap>[]>(() => {
    return [
      {
        field: 'optimizeTimeAdvice',
        headerName: 'Optimise Time',
      },
      {
        field: 'conclusion',
        headerName: 'Conclusion',
      },
      {
        field: 'productTeamAdvice',
        headerName: 'Product',
        width: 200,
      },
      {
        field: 'marketingTeamAdvice',
        headerName: 'Marketing',
        width: 200,
      },
      {
        field: 'largeScalePlan',
        headerName: 'Large Scale Plan',
      },
      {
        field: 'smallScalePlan',
        headerName: 'Small Scale Plan',
      },
    ]
  }, [])

  const rows = useMemo(() => {
    return metrics
  }, [metrics])

  return (
    <Box sx={{ mt: 2 }}>
      <Typography>WeeklyTracking</Typography>

      <DataGrid
        initialState={{
          pinnedColumns: {
            left: ['id', 'date'],
          },
        }}
        getRowId={(row) => row.date!}
        columns={columns}
        rows={[...rows, avgRow, evalRow]}
        editable={false}
        actionColumn={false}
        hideFooter
        slots={{
          toolbar: null,
        }}
        sx={{
          '& .MuiDataGrid-columnHeaderTitle': {
            lineHeight: 1,
            fontSize: '0.8rem',
          },
          '& .MuiDataGrid-cell': {
            px: 0.5,
            fontSize: '0.8rem',
          },
        }}
      />

      <Typography mt={2}>Suggestion</Typography>

      <DataGrid
        columns={suggestionColumns}
        rows={[suggestionRow]}
        actionColumn={false}
        editable={false}
        hideFooter
        slots={{
          toolbar: null,
        }}
        sx={{
          '& .MuiDataGrid-columnHeaderTitle': {
            lineHeight: 1,
            fontSize: '0.8rem',
          },
          '& .MuiDataGrid-cell': {
            px: 0.5,
            fontSize: '0.8rem',
          },
        }}
      />
    </Box>
  )
}
