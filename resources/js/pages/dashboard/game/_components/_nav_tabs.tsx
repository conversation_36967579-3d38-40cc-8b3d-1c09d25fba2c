import { BoxProps } from '@mui/material/Box'
import { useEffect, useMemo } from 'react'

import { NavTabs as LayoutNavTabs, useLayoutComponentRegistration } from '@/components/layout'
import { routes } from '@/components/location'

export function NavTabs({ storeId: gameId, ...props }: { storeId: string } & BoxProps) {
  const tabItems = useMemo(() => {
    return [
      {
        label: 'GAME METRICS',
        href: routes.dash.gameMetrics.index(gameId),
      },
      {
        label: 'NEW GAME METRICS',
        href: routes.dash.gameMetricsNext.index(gameId),
      },
      {
        label: 'COST EXPLORER',
        href: routes.dash.games.costs(gameId),
      },
      {
        label: 'REVENUE EXPLORER',
        href: routes.dash.games.revenues(gameId),
      },
      {
        label: 'LEVEL DROP EXPLORER',
        href: routes.dash.games.levelDrops(gameId),
      },
      {
        label: 'GAME REVIEW',
        href: routes.dash.gameReviews.index(gameId),
      },
      {
        label: 'PRODUCT METRICS',
        href: routes.dash.games.productMetrics(gameId),
      },
      {
        label: 'CREATIVE PERFORMANCE',
        href: routes.dash.games.creativeMetrics(gameId),
      },
      {
        label: 'CAMPAIGN PERFORMANCE',
        href: routes.dash.games.campaignMetrics(gameId),
      },
    ]
  }, [gameId])

  const [ref, setSize] = useLayoutComponentRegistration('gameNav')

  useEffect(() => {
    setSize({
      height: ref.current?.clientHeight || 0,
    })
  }, [gameId, ref])

  return <LayoutNavTabs items={tabItems} {...props} ref={ref} />
}

declare module '@/components/layout/size.js' {
  export interface LayoutComponents {
    gameNav: Size
  }
}
