import { GridColDef } from '@mui/x-data-grid-pro'
import { Box, colors } from '@mui/material'

import { formatter } from '#utils/formatter'
import { GameLevelDropAttributesFragment } from '@/graphql'

const colorMap: Array<[number, string, string?]> = [
  [0.05, colors.common.white],
  [0.08, colors.red[50]],
  [0.11, colors.red[100]],
  [0.14, colors.red[200]],
  [0.17, colors.red[300]],
  [0.2, colors.red[400]],
  [0.23, colors.red[500]],
  [0.26, colors.red[600], colors.common.white],
  [0.29, colors.red[700], colors.common.white],
  [0.3, colors.red[800], colors.common.white],
]

function getCellColor(value: number): [string, string] {
  const colorDef = colorMap.find(([thresold]) => value <= thresold) || colorMap[colorMap.length - 1]
  return [colorDef[1] || colors.red[900], colorDef[2] || colors.common.black]
}

export const levelDropColumns: GridColDef<GameLevelDropAttributesFragment>[] = [
  { field: 'world', headerName: 'World', width: 60 },
  { field: 'level', headerName: 'Level', width: 60 },
  {
    field: 'activeUserCount',
    headerName: 'Active users',
    valueFormatter: (value) => {
      return formatter.round(value)
    },
  },
  {
    field: 'attemptCountPerUser',
    headerName: 'Attempts Per User',
    valueFormatter: (value) => {
      return formatter.round(value)
    },
  },
  {
    field: 'userDropCount',
    headerName: 'User Drop',
    valueFormatter: (value) => {
      return formatter.round(value)
    },
  },
  {
    field: 'userDropRate',
    headerName: '% Drop',
    valueFormatter: (value) => {
      return formatter.percentage(value)
    },
    renderCell: ({ value, formattedValue }) => {
      const color = getCellColor(value)
      return (
        <Box
          sx={{
            color: color[1],
            backgroundColor: color[0],
            mx: -2,
            px: 2,
          }}
        >
          {formattedValue}
        </Box>
      )
    },
  },
  {
    field: 'overallUserDropRate',
    headerName: '% Drop / Total',
    valueFormatter: (value) => {
      return formatter.percentage(value)
    },
  },
  {
    field: 'completionRate',
    headerName: 'Complete Rate',
    valueFormatter: (value) => {
      return formatter.percentage(value)
    },
  },
  {
    field: 'winRate',
    headerName: 'Win Rate',
    valueFormatter: (value) => {
      return formatter.percentage(value)
    },
  },
  {
    field: 'playtimeSecPerUser',
    headerName: 'AVG Playtime Per User (m)',
    valueFormatter: (value) => {
      const time = formatter.time('s', 'm')(value)
      return `${formatter.round(time['m'])}m${formatter.round(time['s'])}s`
    },
  },
]
