import {
  Dialog,
  DialogTitle,
  <PERSON>alogContent,
  DialogContentText,
  DialogActions,
  Button,
  CircularProgress,
} from '@mui/material'
import { useState } from 'react'

type DeleteConfirmDialogProps = {
  open: boolean
  onClose: () => void
  onConfirm: () => Promise<void>
  viewName: string
}

export function DeleteConfirmDialog({
  open,
  onClose,
  onConfirm,
  viewName,
}: Readonly<DeleteConfirmDialogProps>) {
  const [isDeleting, setIsDeleting] = useState(false)

  const handleConfirm = async () => {
    setIsDeleting(true)
    try {
      await onConfirm()
    } catch (error) {
      console.error('Error during deletion:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Delete View</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to delete the view "{viewName}"? This action cannot be undone.
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={isDeleting}>
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          color="error"
          disabled={isDeleting}
          startIcon={isDeleting ? <CircularProgress size={20} color="inherit" /> : null}
        >
          {isDeleting ? 'Deleting...' : 'Delete'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
