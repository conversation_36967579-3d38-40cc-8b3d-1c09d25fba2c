import { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  CircularProgress,
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { object, string } from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'

import { YupFormGroup } from '@/components/form'

const schema = object({
  name: string().required('View name is required'),
})

type SaveViewDialogProps = {
  open: boolean
  onClose: () => void
  onSave: (name: string) => Promise<void>
  pageId: string
}

export function SaveViewDialog({ open, onClose, onSave, pageId }: Readonly<SaveViewDialogProps>) {
  const [isSaving, setIsSaving] = useState(false)

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
    },
  })

  const handleSubmit = form.handleSubmit(async (data) => {
    setIsSaving(true)
    try {
      await onSave(data.name)
      form.reset()
    } catch (error) {
      console.error('Error saving view:', error)
    } finally {
      setIsSaving(false)
    }
  })

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Save View</DialogTitle>
      <Box component="form" onSubmit={handleSubmit}>
        <DialogContent>
          <YupFormGroup control={form.control} name="name" fullWidth>
            <TextField
              autoFocus
              margin="dense"
              label="View Name"
              fullWidth
              variant="outlined"
              {...form.register('name')}
            />
          </YupFormGroup>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} variant="outlined" disabled={isSaving}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  )
}
