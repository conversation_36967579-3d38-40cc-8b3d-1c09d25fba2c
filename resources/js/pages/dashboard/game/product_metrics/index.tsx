import { Box, Grid } from '@mui/material'

import { useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import { useGraphql } from '@/components/toolkit-api'
import { isLoading, Loading } from '@/components/loading'
import { routes, useNavigate } from '@/components/location'

import { Metrics } from './_index/_metrics'
import { FormSchema, getGameQuery, useParam, useSetParams } from './_index/_utils'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'

render(PageWithLayout)

function PageWithLayout() {
  const gameId = useParam((s) => s.gameId)

  return (
    <GameDashboardLayout gameId={gameId}>
      <Page />
    </GameDashboardLayout>
  )
}

function Page() {
  const gameId = useParam((s) => s.gameId)
  const { refresh } = useNavigate()

  const getGame = useGraphql(getGameQuery, {
    variables: {
      gameId,
    },
  })

  const loading = isLoading([getGame.loading, getGame.error])

  useBreadcrumbs(
    [
      {
        label: 'All Games',
        url: routes.dash.games.index(),
      },
      {
        label: getGame.data?.game?.name ?? gameId,
        url: routes.dash.gameMetrics.index(gameId),
      },
      'Product Metrics',
    ],
    'dashboard'
  )

  const aParams = useParam((p) => p.a)
  const bParams = useParam((p) => p.b)
  const setParams = useSetParams()

  if (loading) {
    return <Loading visible />
  }

  return (
    <Box>
      <Grid container columnSpacing={4}>
        <Grid size={6} container>
          <Metrics
            params={aParams}
            onSubmit={(values: FormSchema) => {
              setParams({
                a: {
                  activeFrom: values.activeDate?.[0],
                  activeTo: values.activeDate?.[1],
                  versions: values.versions,
                  rewardUsageGroup: values.rewardUsageGroup,
                  installFrom: values.installDate?.[0],
                  installTo: values.installDate?.[1],
                },
              })
            }}
            onReset={() => refresh({ query: { a: undefined } })}
          />
        </Grid>

        <Grid size={6}>
          <Metrics
            params={bParams}
            onSubmit={(values: FormSchema) => {
              setParams({
                b: {
                  activeFrom: values.activeDate?.[0],
                  activeTo: values.activeDate?.[1],
                  versions: values.versions,
                  rewardUsageGroup: values.rewardUsageGroup,
                  installFrom: values.installDate?.[0],
                  installTo: values.installDate?.[1],
                },
              })
            }}
            onReset={() => refresh({ query: { b: undefined } })}
          />
        </Grid>
      </Grid>
    </Box>
  )
}
