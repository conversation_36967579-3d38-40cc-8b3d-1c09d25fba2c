import dayjs, { Dayjs } from 'dayjs'
import { object, array, mixed, string, InferType } from 'yup'

import { createParamsStore, routes } from '@/components/location'
import { graphql } from '@/gql'
import { ProductMetricIndex_VersionsDataQuery } from '@/graphql'

export const metricsParseSchema = object({
  activeFrom: mixed<Dayjs>()
    .paramDate({ defaultValue: () => dayjs().subtract(1, 'month') })
    .required(),
  activeTo: mixed<Dayjs>()
    .paramDate({ defaultValue: () => dayjs() })
    .required(),
  versions: array(string().required()).paramArray().required(),
  rewardUsageGroup: string().required().default('level'),
  installFrom: mixed<Dayjs>().paramDate().optional(),
  installTo: mixed<Dayjs>().paramDate().optional(),
})

export type MetricsSchema = InferType<typeof metricsParseSchema>

export const metricsStringifySchema = object({
  activeFrom: mixed().stringifyDate(),
  activeTo: mixed().stringifyDate(),
  versions: array(string()),
  rewardUsageGroup: string(),
  installFrom: mixed().stringifyDate(),
  installTo: mixed().stringifyDate(),
})

export const schema = {
  parse: object({
    gameId: string().required(),
    a: metricsParseSchema,
    b: metricsParseSchema,
  }),
  stringify: object({
    gameId: string(),
    a: metricsStringifySchema,
    b: metricsStringifySchema,
  }),
}

export const { useParams, useSetParams, useParam } = createParamsStore<{
  gameId: string
  a: {
    activeFrom: Dayjs
    activeTo: Dayjs
    versions: string[]
    rewardUsageGroup: string
    installFrom?: Dayjs
    installTo?: Dayjs
  }
  b: {
    activeFrom: Dayjs
    activeTo: Dayjs
    versions: string[]
    rewardUsageGroup: string
    installFrom?: Dayjs
    installTo?: Dayjs
  }
}>(routes.dash.games.productMetrics(':gameId'), schema)

export const formSchema = object({
  activeDate: array(mixed<Dayjs>().required()).required(),
  versions: array(string().required()).required(),
  rewardUsageGroup: string().required(),
  installDate: array(mixed<Dayjs>().optional()).optional(),
})

export type FormSchema = InferType<typeof formSchema>

export const getVersionsQuery = graphql(`
  query ProductMetricIndex_GetVersions($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {
    gameRewardUsageVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {
      collection
    }

    gameDailyLevelDropVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {
      collection
    }

    gamePlaytimeVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {
      collection
    }

    gameRetentionRateVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {
      collection
    }
  }
`)

export const getGameQuery = graphql(`
  query ProductMetricIndex_GetGame($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }
  }
`)

export const getVersionsDataQuery = graphql(`
  query ProductMetricIndex_VersionsData(
    $gameId: ID!
    $versions: [String!]!
    $groupByLevel: Boolean!
    $groupByLocation: Boolean!
    $group: [String!]!
    $order: OrderDirection!
    $activeDateFrom: Date!
    $activeDateTo: Date!
    $installTimeFrom: Date
    $installTimeTo: Date
  ) {
    gameRewardUsages(
      where: {
        dateFrom: $activeDateFrom
        dateTo: $activeDateTo
        gameId: $gameId
        versions: $versions
      }
      group: { fields: $group }
      order: { direction: $order }
    ) {
      collection {
        location @include(if: $groupByLocation)
        level @include(if: $groupByLevel)
        world @include(if: $groupByLevel)
        useCount
      }
    }

    gameLevelDrops(
      where: {
        dateFrom: $activeDateFrom
        dateTo: $activeDateTo
        gameId: $gameId
        versions: $versions
        installDateFrom: $installTimeFrom
        installDateTo: $installTimeTo
      }
    ) {
      collection {
        ...GameLevelDropAttributes
      }
    }

    gameRetentionRates(
      where: {
        dateFrom: $activeDateFrom
        dateTo: $activeDateTo
        gameId: $gameId
        versions: $versions
      }
    ) {
      collection {
        date
        gameId
        newUsers
        day1
        day2
        day3
        day4
        day5
        day6
        day7
      }
    }

    aggregatePlaytime(
      where: {
        gameId: $gameId
        activeDateFrom: $activeDateFrom
        activeDateTo: $activeDateTo
        installDateFrom: $installTimeFrom
        installDateTo: $installTimeTo
        versions: $versions
      }
    ) {
      engagementSessionCount
      playtimeSec
    }
  }
`)

export type GameRewardUsage =
  ProductMetricIndex_VersionsDataQuery['gameRewardUsages']['collection'][0]
