import { Box, Grid, Stack, Typography } from '@mui/material'
import { useForm } from 'react-hook-form'
import { isNil, uniq } from 'lodash-es'
import { useEffect, useMemo } from 'react'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useDebounce } from '@uidotdev/usehooks'
import { yupResolver } from '@hookform/resolvers/yup'

import { useGraphql } from '@/components/toolkit-api'
import { compareSemver } from '#utils/semver'
import { FilterContainer, FilterGroup, DateRange, Select, FilterActions } from '@/components/form'
import { levelDropColumns } from '../../_components/_level_drop'
import { ProductMetricIndex_GetVersionsQuery } from '@/graphql'
import { isLoading, Loading } from '@/components/loading'
import { GameRetentionRate, OrderDirection } from '#graphql/main'
import { DataGrid } from '@/components/table'
import { formatter } from '#utils/formatter'

import {
  formSchema,
  GameRewardUsage,
  getVersionsDataQuery,
  getVersionsQuery,
  MetricsSchema,
  useParam,
} from './_utils'
import { formatPercentage } from '@/components/typography'
import dayjs from 'dayjs'

const getAvailableVersions = (data: ProductMetricIndex_GetVersionsQuery) => {
  return uniq(
    data.gameDailyLevelDropVersions.collection
      .concat(data.gameRewardUsageVersions.collection)
      .concat(data.gamePlaytimeVersions.collection)
      .concat(data.gameRetentionRateVersions.collection)
  ).sort((a, b) => 0 - compareSemver(a, b))
}

export function Metrics({
  params,
  onSubmit,
  onReset,
}: {
  params: MetricsSchema
  onSubmit: any
  onReset: any
}) {
  const { rewardUsageGroup, versions, activeFrom, activeTo, installFrom, installTo } = params
  const form = useForm({
    resolver: yupResolver(formSchema),
    defaultValues: {
      activeDate: [activeFrom, activeTo],
      versions: versions,
      rewardUsageGroup: rewardUsageGroup,
      installDate: [installFrom, installTo],
    },
  })
  const gameId = useParam((s) => s.gameId)

  const activeDate = form.watch('activeDate')
  const activeDateDebounced = useDebounce(activeDate, 500)

  const installDate = form.watch('installDate')

  const getVersions = useGraphql(getVersionsQuery, {
    variables: {
      dateFrom: activeDateDebounced[0].format('YYYY-MM-DD'),
      dateTo: activeDateDebounced[1].format('YYYY-MM-DD'),
      gameId,
    },
    onCompleted: (data) => {
      if (versions.length > 0) {
        return
      }

      const allVersions = getAvailableVersions(data)
      const selectedVersions = allVersions.slice(0, 2)
      form.setValue('versions', selectedVersions)
      onSubmit(form.getValues())
    },
    skip: !activeDateDebounced[1] || !activeDateDebounced[0],
  })

  const loading = isLoading([getVersions.loading, getVersions.error, !getVersions.called])

  const versionsValue = form.watch('versions')

  const rewardUsageGroupDebounced = useDebounce(form.watch('rewardUsageGroup'), 500)

  useEffect(() => {
    onSubmit(form.getValues())
  }, [rewardUsageGroupDebounced])

  const getVersionData = useGraphql(getVersionsDataQuery, {
    variables: {
      groupByLevel: rewardUsageGroup === 'level',
      groupByLocation: rewardUsageGroup === 'location',
      group: rewardUsageGroup === 'level' ? ['world', 'level'] : ['location'],
      order: OrderDirection.Asc,
      versions,
      activeDateFrom: activeFrom.format('YYYY-MM-DD'),
      activeDateTo: activeTo.format('YYYY-MM-DD'),
      gameId,
      installTimeFrom: installFrom?.format('YYYY-MM-DD'),
      installTimeTo: installTo?.format('YYYY-MM-DD'),
    },
    skip: loading || !activeFrom || !activeTo,
  })

  const rewardUsageColumns = useMemo<GridColDef<GameRewardUsage>[]>(() => {
    return [
      ...(rewardUsageGroup === 'level'
        ? [
            {
              field: 'world',
              headerName: 'World',
            },
            {
              field: 'level',
              headerName: 'Level',
            },
          ]
        : [
            {
              field: 'location',
              headerName: 'Location',
              width: 250,
            },
          ]),
      {
        field: 'useCount',
        headerName: 'Reward Count',
      },
    ]
  }, [rewardUsageGroup])

  const retentionRateColumns = useMemo<GridColDef<GameRetentionRate>[]>(() => {
    return [
      { field: 'date', headerName: 'Active Date' },
      { field: 'newUsers', headerName: 'New Users' },
      { field: 'day1', headerName: 'RR D-1', valueFormatter: formatPercentage },
      { field: 'day2', headerName: 'RR D-2', valueFormatter: formatPercentage },
      { field: 'day3', headerName: 'RR D-3', valueFormatter: formatPercentage },
      { field: 'day4', headerName: 'RR D-4', valueFormatter: formatPercentage },
      { field: 'day5', headerName: 'RR D-5', valueFormatter: formatPercentage },
      { field: 'day6', headerName: 'RR D-6', valueFormatter: formatPercentage },
      { field: 'day7', headerName: 'RR D-7', valueFormatter: formatPercentage },
    ]
  }, [])

  const contentLoading = isLoading([
    !getVersionData.called,
    getVersionData.loading,
    getVersionData.error,
  ])

  if (loading || contentLoading) {
    return <Loading visible />
  }

  const availableVersions = getAvailableVersions(getVersions.data!)
  const formattedPlaytime = formatter.time(
    's',
    'm'
  )(getVersionData.data!.aggregatePlaytime.playtimeSec)

  return (
    <>
      <Box component="form">
        <FilterContainer sx={{ mt: 1, mb: 3 }} flexWrap="wrap" rowGap={2}>
          <FilterGroup label="Active Date">
            <DateRange
              defaultValue={[activeFrom, activeTo]}
              onChange={(values) =>
                form.setValue('activeDate', [values[0]! || dayjs(), values[1]! || dayjs()])
              }
              value={activeDate as any}
            />
          </FilterGroup>

          <FilterGroup label="Versions">
            <Select
              options={availableVersions.map((v) => ({ label: v, value: v }))}
              defaultValue={versions}
              multiple
              {...form.register('versions')}
              value={versionsValue}
            />
          </FilterGroup>

          <FilterActions onSubmit={form.handleSubmit(onSubmit)} onReset={onReset} />
        </FilterContainer>

        <Box textAlign="center">
          <Typography fontWeight="bold">{versions.join(', ')}</Typography>
        </Box>

        <FilterContainer>
          <FilterGroup label="Install Date">
            <DateRange
              defaultValue={[installFrom || null, installTo || null]}
              onChange={(values) =>
                form.setValue('installDate', [values[0]! || dayjs(), values[1]! || dayjs()])
              }
              value={installDate as any}
            />
          </FilterGroup>
        </FilterContainer>
      </Box>

      <Grid
        my={2}
        size={12}
        container
        sx={{
          '--Grid-borderWidth': '1px',
          'borderColor': 'divider',
          '& > div': {
            borderRight: 'var(--Grid-borderWidth) solid',
            borderBottom: 'var(--Grid-borderWidth) solid',
            borderColor: 'divider',
            justifyContent: 'center',
            alignItems: 'center',
            display: 'flex',
          },
          '& > div:nth-of-type(2n)': {
            borderRight: 0,
          },
          '& > div:nth-of-type(3), & > div:nth-of-type(4)': {
            borderBottom: 0,
          },
        }}
      >
        <Grid size={6} height={40}>
          Sessions
        </Grid>
        <Grid size={6} height={40}>
          Timeplay
        </Grid>
        <Grid size={6} height="calc(100% - 40px)">
          <Typography variant="h4">
            {formatter.round(getVersionData.data!.aggregatePlaytime.engagementSessionCount)}
          </Typography>
        </Grid>
        <Grid size={6} height="calc(100% - 40px)">
          <Typography variant="h4">
            {formattedPlaytime['m']}m{formattedPlaytime['s']}s
          </Typography>
        </Grid>
      </Grid>

      <Grid size={12} sx={{ flexShrink: 0, flexGrow: 0, overflow: 'hidden' }}>
        <Typography variant="h6" py={2}>
          Level Drop
        </Typography>

        <Box sx={{ height: `500px` }}>
          <DataGrid
            columns={levelDropColumns}
            getRowId={(row) => [row.world, row.level].join('_')}
            rows={getVersionData.data!.gameLevelDrops.collection}
            actionColumn={false}
            editable={false}
            disableColumnSorting={false}
          />
        </Box>
      </Grid>

      <Grid size={12} sx={{ flexShrink: 0, flexGrow: 0 }}>
        <Stack direction="row" alignItems="center" spacing={2} py={2}>
          <Typography variant="h6">Reward Count</Typography>

          <Select
            fullWidth={false}
            sx={{ width: 200 }}
            options={[
              { label: 'Level', value: 'level' },
              { label: 'Location', value: 'location' },
            ]}
            hint="GROUP BY"
            {...form.register('rewardUsageGroup')}
            value={rewardUsageGroup}
          />
        </Stack>

        <Box sx={{ height: `500px` }}>
          <DataGrid
            columns={rewardUsageColumns}
            getRowId={(row) =>
              [row.world, row.level, row.location].filter((e) => !isNil(e)).join('_')
            }
            rows={getVersionData.data!.gameRewardUsages.collection}
            actionColumn={false}
            editable={false}
            disableColumnSorting={false}
          />
        </Box>
      </Grid>

      <Grid size={12} sx={{ flexShrink: 0, flexGrow: 0 }}>
        <Typography variant="h6" sx={{ my: 2 }}>
          Retention Rate
        </Typography>
        <Box sx={{ height: `500px` }}>
          <DataGrid
            columns={retentionRateColumns}
            getRowId={(row) => row.date}
            rows={getVersionData.data!.gameRetentionRates.collection}
            actionColumn={false}
            editable={false}
            disableColumnSorting={false}
          />
        </Box>
      </Grid>
    </>
  )
}
