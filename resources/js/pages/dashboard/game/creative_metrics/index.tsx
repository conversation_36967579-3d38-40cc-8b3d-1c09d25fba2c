import GameCreativeMetricsController from '#controllers/dashboard/game/creative_metrics_controller'
import { OrderDirection } from '#graphql/main'
import {
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { createParamsStore, routes, useNavigate } from '@/components/location'
import { useServerProp } from '@/components/ssr'
import { useGraphql } from '@/components/toolkit-api'
import { graphql } from '@/gql'
import { InferPageProps } from '@adonisjs/inertia/types'
import { Box } from '@mui/material'
import dayjs from 'dayjs'
import { mixed, number, object, string } from 'yup'
import { DateRange, FilterActions, FilterContainer, FilterGroup } from '@/components/form'
import { Head } from '@inertiajs/react'
import { DataGrid } from '@/components/table'
import { COLORS } from '@/components/color'
import { useEffect, useMemo } from 'react'
import { GridColDef } from '@mui/x-data-grid-pro'
import { GameCreativeMetricAttributesFragment } from '@/graphql'
import { isLoading, Loading } from '@/components/loading'
import { formatCurrency, formatNumber, formatPercentage } from '@/components/typography'
import { useForm } from 'react-hook-form'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'

const { useParam, usePagination, useSetParams } = createParamsStore(
  routes.dash.games.creativeMetrics(':gameId'),
  {
    parse: object({
      from: mixed<dayjs.Dayjs>()
        .paramDate({ defaultValue: () => dayjs().subtract(1, 'month') })
        .required(),
      to: mixed<dayjs.Dayjs>()
        .paramDate({ defaultValue: () => dayjs() })
        .required(),
      page: number().default(1),
      perPage: number().default(200),
      direction: string().default(OrderDirection.Desc),
    }),
    stringify: object({
      from: mixed().stringifyDate(),
      to: mixed().stringifyDate(),
    }),
  }
)

graphql(`
  fragment GameCreativeMetricAttributes on GameCreativeMetric {
    id
    date
    campaign
    adGroup
    adGroupStartDate
    adSet
    editor
    isPlayable
    targetRoasRate
    preTaxCostAmount
    grossRevenueAmount
    clickCount
    impressionCount
    installCount
    roasRate
    cpi
    conversionRate
    clickthroughRate

    agency {
      ...AdAgencyAttributes
    }
  }
`)

const getInitialDataQuery = graphql(`
  query GameCreativeMetrics_Index(
    $gameId: ID!
    $dateFrom: Date!
    $dateTo: Date!
    $page: Int!
    $perPage: Int!
    $direction: OrderDirection!
  ) {
    gameCreativeMetrics(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
      offset: { page: $page, perPage: $perPage }
      order: { direction: $direction }
    ) {
      collection {
        ...GameCreativeMetricAttributes
      }
      pageInfo {
        ...PageInfoAttributes
      }
    }
  }
`)

type PageProps = InferPageProps<GameCreativeMetricsController, 'index'>

export default function Page() {
  const game = useServerProp<PageProps, PageProps['game']>((s) => s.game)
  useBreadcrumbs([
    { label: 'All Games', url: routes.dash.games.index('1') },
    { label: game.name, url: routes.dash.games.index(game.id) },
    'Creative Performance',
  ])
  const { refresh } = useNavigate()

  const setParams = useSetParams()
  const dateFrom = useParam((p) => p.from)
  const dateTo = useParam((p) => p.to)
  const direction = useParam((p) => p.direction) as any
  const page = useParam((p) => p.page)
  const perPage = useParam((p) => p.perPage)

  const form = useForm({
    defaultValues: {
      dateRange: [dateFrom, dateTo],
    },
  })

  const getInitialData = useGraphql(getInitialDataQuery, {
    variables: {
      dateFrom: dateFrom.format('YYYY-MM-DD'),
      dateTo: dateTo.format('YYYY-MM-DD'),
      gameId: game.id,
      page,
      perPage,
      direction,
    },
  })

  const pagination = usePagination(getInitialData.data?.gameCreativeMetrics?.pageInfo)

  const columns = useMemo<GridColDef<GameCreativeMetricAttributesFragment>[]>(() => {
    return [
      { field: 'date', headerName: 'Date' },
      { field: 'agency.name', headerName: 'Network', valueGetter: (_, row) => row.agency.name },
      { field: 'campaign', headerName: 'Campaign Name' },
      { field: 'adGroup', headerName: 'Ad Group' },
      { field: 'adGroupStartDate', headerName: 'Ad Group Start Date' },
      { field: 'editor', headerName: 'Editor' },
      {
        field: 'isPlayable',
        headerName: 'Playable',
        valueFormatter: (value) => `${value}`.toUpperCase(),
      },
      { field: 'targetRoasRate', headerName: 'Target ROAS', valueFormatter: formatPercentage },
      { field: 'preTaxCostAmount', headerName: 'Cost', valueFormatter: formatCurrency },
      { field: 'clickCount', headerName: 'Clicks', valueFormatter: formatNumber },
      { field: 'impressionCount', headerName: 'Impressions', valueFormatter: formatNumber },
      { field: 'grossRevenueAmount', headerName: 'Revenue', valueFormatter: formatCurrency },
      { field: 'installCount', headerName: 'Installs', valueFormatter: formatNumber },
      { field: 'roasRate', headerName: 'ROAS', valueFormatter: formatPercentage },
      { field: 'cpi', headerName: 'CPI', valueFormatter: (v) => formatCurrency(v, 3) },
      { field: 'clickthroughRate', headerName: 'CTR', valueFormatter: formatPercentage },
      { field: 'conversionRate', headerName: 'CVR', valueFormatter: formatPercentage },
    ]
  }, [])

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [, gameNavSize] = useLayoutSize('gameNav')
  const [filterRef, setFilterSize, filterSize] = useLayoutComponentRegistration('filter')
  const layoutHeight = useLayoutHeight(
    navbarSize,
    breadcrumbsSize,
    notificationSize,
    filterSize,
    gameNavSize
  )

  const loading = isLoading([getInitialData.error, getInitialData.loading])

  useEffect(() => {
    setFilterSize({
      height: filterRef.current?.clientHeight || 0,
    })
  }, [loading])

  return (
    <GameDashboardLayout gameId={game.id}>
      <Head title={`Creative Performance | ${game.name}`} />

      <FilterContainer sx={{ pb: 2 }} ref={filterRef}>
        <FilterGroup label="DATE">
          <DateRange
            defaultValue={[dateFrom, dateTo]}
            onChange={(values) => form.setValue('dateRange', [values[0]!, values[1]!])}
          />
        </FilterGroup>

        <FilterActions
          onSubmit={form.handleSubmit((formData) => {
            setParams({ from: formData.dateRange[0], to: formData.dateRange[1] })
          })}
          onReset={() => refresh()}
        />
      </FilterContainer>

      <Loading visible={loading}>
        <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem)` }}>
          <DataGrid
            rows={getInitialData.data?.gameCreativeMetrics?.collection!}
            getRowId={(m) => m.id}
            columns={columns}
            editable={false}
            actionColumn={false}
            pagination={pagination}
            sx={{ ...COLORS }}
            disableColumnSorting={false}
          />
        </Box>
      </Loading>
    </GameDashboardLayout>
  )
}
