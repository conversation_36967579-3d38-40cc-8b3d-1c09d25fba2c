import { Box, Checkbox, FormControlLabel, FormGroup, Grid, <PERSON>ack, Typography } from '@mui/material'
import TabContext from '@mui/lab/TabContext'
import TabPanel from '@mui/lab/TabPanel'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useDebounce } from '@uidotdev/usehooks'

import { useBreadcrumbs } from '@/components/layout'
import { routes, useParams } from '@/components/location'
import { render } from '@/react'
import { useGraphql } from '@/components/toolkit-api'
import { isLoading, Loading } from '@/components/loading'
import { useClientNavigate, useNavigate } from '@/components/ssr'
import { ChartViewButton, TableViewButton, YupCheckbox } from '@/components/form'
import { graphql } from '@/gql'

import { LevelDropChart } from './show/_chart'
import { LevelDropTable } from './show/_table'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'

type FilterFormData = {
  versions: string[]
}

render(PageWithLayout)

function PageWithLayout() {
  const params = useParams<{ storeId: string; view: string; versions: string[] }>(
    '/dash/game_level_drops/:storeId'
  )

  return (
    <GameDashboardLayout gameId={params.storeId}>
      <Page />
    </GameDashboardLayout>
  )
}

const dataQuery = graphql(`
  query GameLevelDropIndex_QueryData($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }

    gameLevelDropVersions(where: { gameId: $gameId }) {
      collection
    }
  }
`)

const versionsDataQuery = graphql(`
  query GameLevelDropIndex_QueryVersions($gameId: ID!, $versions: [String!]!) {
    gameLevelDrops(where: { gameId: $gameId, versions: $versions }) {
      collection {
        ...GameLevelDropAttributes
      }
    }
  }
`)

function Page() {
  const params = useParams<{ storeId: string; view: string; versions: string[] }>(
    '/dash/game_level_drops/:storeId'
  )
  const { clientNavigate } = useClientNavigate()
  const { navigate } = useNavigate()

  const versionParams = params.versions ? Array(params.versions).flat() : []
  const filterForm = useForm<FilterFormData>({
    defaultValues: {
      versions: versionParams,
    },
  })

  const getData = useGraphql(dataQuery, {
    variables: { gameId: params.storeId },
    onCompleted: (data) => {
      if (versionParams.length === 0) {
        filterForm.setValue('versions', data.gameLevelDropVersions.collection.slice(0, 1))
      }
    },
  })

  const viewType = params.view || 'table'

  const versions = filterForm.watch('versions')
  const versionsDebounced = useDebounce(versions, 750)

  useEffect(() => {
    clientNavigate('', {
      versions: versionsDebounced,
    })
  }, [versionsDebounced])

  const getVersionsData = useGraphql(versionsDataQuery, {
    variables: { gameId: params.storeId, versions: versionsDebounced },
    skip: getData.loading || !versionsDebounced.length,
  })

  useBreadcrumbs(
    [
      {
        label: 'All Games',
        url: routes.dash.games.index(),
      },
      {
        label: getData.data?.game?.name ?? params.storeId,
        url: routes.dash.gameMetrics.index(params.storeId),
      },
      'Level Drop Explorer',
    ],
    'dashboard'
  )

  const onChangeViewTypeButtonClick = (newValue: string) => (_event: React.SyntheticEvent) => {
    navigate('', { view: newValue, versions: versionsDebounced })
  }

  const isInitialLoaing = isLoading([getData.loading, getData.error])

  if (isInitialLoaing) {
    return <Loading visible />
  }

  const allVersions = getData.data!.gameLevelDropVersions.collection
  const isVersionsDataLoading = isLoading([
    getVersionsData.loading,
    getVersionsData.error,
    !getVersionsData.called,
  ])

  return (
    <TabContext value={viewType}>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            lg: 1,
          }}
          order={0}
          sx={{ borderColor: 'divider', borderBottomWidth: 1, borderRightWidth: 1 }}
        >
          <Stack direction="row" sx={{ alignItems: 'center', height: '100%' }}>
            <Typography>Version</Typography>
          </Stack>
        </Grid>

        <Grid
          size={{
            xs: 12,
            lg: 11,
          }}
          order={{
            xs: 2,
            lg: 1,
          }}
          sx={{ borderColor: 'divider', borderBottomWidth: 1 }}
        >
          <Stack direction="row" sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
            {viewType === 'chart' ? (
              <Typography>Chart view</Typography>
            ) : (
              <Typography>Table view</Typography>
            )}
            <Stack direction="row" spacing={0}>
              <TableViewButton
                active={viewType === 'table'}
                onClick={onChangeViewTypeButtonClick('table')}
              />

              <ChartViewButton
                active={viewType === 'chart'}
                onClick={onChangeViewTypeButtonClick('chart')}
              />
            </Stack>
          </Stack>
        </Grid>

        <Grid
          size={{
            xs: 12,
            lg: 1,
          }}
          order={{
            xs: 1,
            lg: 2,
          }}
          sx={{ borderColor: 'divider', borderRightWidth: 1 }}
        >
          <FormGroup
            sx={{
              display: 'flex',
              flexDirection: { xs: 'row', lg: 'column' },
              justifyContent: 'start',
              gap: 1,
              height: '100%',
            }}
          >
            {allVersions.map((version) => (
              <FormControlLabel
                key={version}
                control={
                  <YupCheckbox
                    control={filterForm.control}
                    name="versions"
                    value={version}
                    render={(field) => (
                      <Checkbox {...field} disabled={!field.checked && versions.length === 3} />
                    )}
                  />
                }
                label={version}
              />
            ))}
          </FormGroup>
        </Grid>

        <Grid
          size={{
            xs: 12,
            lg: 11,
          }}
          order={3}
        >
          <TabPanel sx={{ padding: 0 }} value="table">
            <Box sx={{ overflow: 'auto' }}>
              <Stack direction="row" spacing={2}>
                {versionsDebounced.map((version) => {
                  if (isVersionsDataLoading) {
                    return <Loading visible={isVersionsDataLoading} align="left" key={version} />
                  }

                  const levels = getVersionsData.data!.gameLevelDrops.collection.filter(
                    (ld) => ld.version === version
                  )

                  return (
                    <Box sx={{ flexShrink: 0 }} key={version}>
                      <LevelDropTable version={version} data={levels} />
                    </Box>
                  )
                })}
              </Stack>
            </Box>
          </TabPanel>
          <TabPanel sx={{ padding: 0 }} value="chart">
            <Box sx={{ overflowX: 'auto' }}>
              {isVersionsDataLoading ? (
                <Loading visible />
              ) : (
                <LevelDropChart
                  levels={getVersionsData.data!.gameLevelDrops.collection}
                  versions={versionsDebounced}
                />
              )}
            </Box>
          </TabPanel>
        </Grid>
      </Grid>
    </TabContext>
  )
}
