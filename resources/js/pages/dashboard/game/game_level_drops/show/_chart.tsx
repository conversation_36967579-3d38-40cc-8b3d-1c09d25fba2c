import { colors } from '@mui/material'
import { BarPlot } from '@mui/x-charts/BarChart'
import { ChartContainer } from '@mui/x-charts/ChartContainer'
import { axisClasses } from '@mui/x-charts/ChartsAxis'
import { ChartsLegend } from '@mui/x-charts/ChartsLegend'
import { ChartsTooltip } from '@mui/x-charts/ChartsTooltip'
import { ChartsXAxis } from '@mui/x-charts/ChartsXAxis'
import { ChartsYAxis } from '@mui/x-charts/ChartsYAxis'
import { LinePlot } from '@mui/x-charts/LineChart'
import { ChartsAxisHighlight } from '@mui/x-charts/ChartsAxisHighlight'
import { orderBy } from 'lodash-es'
import { useMemo } from 'react'

import { formatter } from '#utils/formatter'
import { GameLevelDropAttributesFragment } from '@/graphql'

const chartColors = [colors.red[300], colors.green[400], colors.blue[600]]
const WIDTH_PER_LEVEL = 40 / 3

export function LevelDropChart({
  versions,
  levels,
}: {
  levels: GameLevelDropAttributesFragment[]
  versions: string[]
}) {
  const width = Math.max(levels.length * WIDTH_PER_LEVEL * versions.length)
  const Container = ChartContainer
  const sizingProps = width > document.body.clientWidth ? { width } : {}

  const dataset = useMemo(() => {
    const levelToVersionData = new Map<
      string,
      { world: number; level: number; versions: Record<string, GameLevelDropAttributesFragment> }
    >()

    levels.forEach((levelDrop) => {
      const levelId = [levelDrop.world, levelDrop.level].join('_')

      if (!levelToVersionData.has(levelId)) {
        levelToVersionData.set(levelId, {
          world: levelDrop.world,
          level: levelDrop.level,
          versions: {},
        })
      }

      levelToVersionData.get(levelId)!.versions[levelDrop.version] = levelDrop
    })

    return orderBy(Array.from(levelToVersionData.values()), ['world', 'level'], ['asc', 'asc'])
  }, [levels])

  const yAxisActiveUserCount = useMemo(() => {
    return dataset
      .filter((d) => versions.some((v) => d.versions[v]))
      .flatMap((level) => Object.values(level.versions).map((v) => v.activeUserCount))
      .sort()
  }, [versions, dataset])

  const yAxisUserDropRate = useMemo(() => {
    return dataset
      .filter((d) => versions.some((v) => d.versions[v]))
      .flatMap((level) => Object.values(level.versions).map((v) => v.userDropRate))
      .sort()
  }, [versions, dataset])

  return (
    // @ts-ignore sizingProps already contains ResponsiveChartContainer props
    <Container
      series={versions.flatMap((version, index) => {
        return [
          {
            type: 'line',
            data: dataset.map((d) => d.versions[version]?.activeUserCount || 0),
            color: versions.length === 1 ? chartColors[2] : chartColors[index],
            yAxisId: 'y-axis-active-user',
            label: `Active User ${version}`,
            valueFormatter: (value: any) => formatter.round(value)!,
          },
          {
            type: 'bar',
            data: dataset.map((d) => d.versions[version]?.userDropRate || 0),
            color: versions.length === 1 ? chartColors[0] : chartColors[index],
            yAxisId: 'y-axis-drop-percentage',
            label: `% Drop ${version}`,
            valueFormatter: (value: any) => formatter.percentage(value)!,
          },
        ]
      })}
      xAxis={[
        {
          scaleType: 'band',
          data: levels.map((level) => level.level),
          label: 'Level',
          id: 'x-axis-level',
          tickSize: 5,
          position: 'bottom',
        },
      ]}
      yAxis={[
        {
          id: 'y-axis-active-user',
          scaleType: 'linear',
          data: yAxisActiveUserCount,
          valueFormatter: (value: any) => formatter.round(value)!,
          position: 'left',
        },
        {
          id: 'y-axis-drop-percentage',
          scaleType: 'linear',
          data: yAxisUserDropRate,
          valueFormatter: (value: any) => formatter.percentage(value),
          position: 'right',
          label: '% Drop',
        },
      ]}
      sx={{
        [`.${axisClasses.left} .${axisClasses.label}`]: {
          transform: 'translateX(-35px)',
        },
        [`.${axisClasses.right} .${axisClasses.label}`]: {
          transform: 'translateX(35px)',
        },
        [`.${axisClasses.bottom} .${axisClasses.label}`]: {
          fontSize: '3rem',
        },
        touchAction: 'auto',
      }}
      margin={{ left: 100, right: 100, bottom: 100 }}
      height={500}
      {...sizingProps}
    >
      <BarPlot />
      <LinePlot />
      <ChartsLegend />

      <ChartsXAxis axisId="x-axis-level" labelStyle={{ fontSize: 18 }} />
      <ChartsYAxis axisId="y-axis-active-user" />
      <ChartsYAxis axisId="y-axis-drop-percentage" />
      <ChartsTooltip />
      <ChartsAxisHighlight x="band" />
    </Container>
  )
}
