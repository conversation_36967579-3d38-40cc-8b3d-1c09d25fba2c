import { GridPinnedRowsProp, useGridApiRef } from '@mui/x-data-grid-pro'
import { Box, Typography } from '@mui/material'

import { DataGrid } from '@/components/table/datagrid'
import { NON_TABLE_HEIGHT } from '../../_components/_metrics_layout'
import { GameLevelDropAttributesFragment } from '@/graphql'
import { levelDropColumns } from '../../_components/_level_drop'

const pinnedRows: GridPinnedRowsProp<GameLevelDropAttributesFragment> = {
  top: [],
}

export function LevelDropTable({
  version,
  data,
}: {
  version: string
  data: GameLevelDropAttributesFragment[]
}) {
  const apiRef = useGridApiRef()

  return (
    <Box
      sx={{
        height: `calc(100vh - ${NON_TABLE_HEIGHT}px - 60px)`,
        width: '100%',
        minHeight: '400px',
      }}
    >
      <Typography
        sx={{
          textAlign: 'center',
          fontWeight: 'bold',
          borderWidth: 1,
          borderBottomWidth: 0,
          borderColor: 'divider',
        }}
      >
        {version}
      </Typography>

      <DataGrid
        apiRef={apiRef}
        rows={data}
        columns={levelDropColumns}
        getRowId={(row) => [row.world, row.level].join('_')}
        hideFooter
        pinnedRows={pinnedRows}
        editable={false}
        actionColumn={false}
        disableColumnSorting
        sx={{
          '& .MuiDataGrid-columnHeaderTitle': {
            fontSize: '0.8rem',
            whiteSpace: 'break-spaces',
            lineHeight: 1,
            textAlign: 'center',
          },
          '&.MuiDataGrid-root .MuiDataGrid-columnHeader--alignRight .MuiDataGrid-columnHeaderTitleContainer':
            {
              pl: 1,
            },
          '& .MuiDataGrid-columnHeader': {
            borderRightWidth: 1,
            height: '5rem !important',
          },
          '& .MuiDataGrid-cell:not(:last-child)': {
            textAlign: 'right',
            borderRightWidth: 1,
          },
          '& .MuiDataGrid-cell': {
            px: 0.5,
            fontSize: '0.8rem',
          },
        }}
      />
    </Box>
  )
}
