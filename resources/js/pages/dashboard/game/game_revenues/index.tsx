import {
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { render } from '@/react'
import { useGraphql } from '@/components/toolkit-api'
import { isLoading, Loading } from '@/components/loading'
import { DateRange, FilterActions, FilterContainer, FilterGroup, Select } from '@/components/form'

import { getInitDataQuery, paramsStringifySchema, useParams, useSetParams } from './index/_util'
import { routes, useNavigate } from '@/components/location'
import { useForm } from 'react-hook-form'
import { GameRevenueOverviewTable } from './index/_overview_table'
import { GameRevenueComparisonTable } from './index/_comparison_table'
import { OrderDirection } from '#graphql/main'
import { useEffect } from 'react'
import { Box } from '@mui/material'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'

render(Page)

function Page() {
  const params = useParams()
  const { gameId, from: dateFrom, to: dateTo, sort, view } = params
  const setParams = useSetParams()

  const { refresh, navigate } = useNavigate()

  const getInitData = useGraphql(getInitDataQuery, {
    variables: {
      gameId,
    },
  })

  const loading = isLoading([getInitData.error, getInitData.loading])

  useBreadcrumbs(
    [
      {
        label: 'All Games',
        url: routes.dash.games.index(),
      },
      {
        label: getInitData.data?.game?.name ?? gameId,
        url: routes.dash.gameMetrics.index(gameId),
      },
      `Revenue Explorer / ${view === 'network' ? 'Comparison' : 'Overview'}`,
    ],
    'dashboard'
  )

  const form = useForm({
    defaultValues: {
      dateRange: [dateFrom, dateTo],
      orderDirection: sort,
    },
  })

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [, gameNavSize] = useLayoutSize('gameNav')
  const [filterRef, setFilterSize, filterSize] = useLayoutComponentRegistration('filter')
  const layoutHeight = useLayoutHeight(
    navbarSize,
    breadcrumbsSize,
    notificationSize,
    gameNavSize,
    filterSize
  )
  console.log('layoutHeight', layoutHeight)

  useEffect(() => {
    setFilterSize({
      height: filterRef.current?.clientHeight || 0,
    })
  }, [filterRef, setFilterSize])

  return (
    <GameDashboardLayout gameId={gameId}>
      <FilterContainer
        ref={filterRef}
        sx={{
          pb: 1,
          gap: 2,
        }}
        spacing={0}
        direction={{
          xs: 'column',
          sm: 'row',
        }}
        alignItems={{
          xs: 'flex-start',
          sm: 'flex-end',
        }}
        component="form"
        onSubmit={form.handleSubmit((formData) => {
          setParams({
            from: formData.dateRange[0],
            to: formData.dateRange[1],
            sort: formData.orderDirection,
          })
        })}
      >
        <FilterGroup
          label="Select date"
          sx={{
            width: {
              xs: '100%',
              sm: '350px',
            },
          }}
        >
          <DateRange
            defaultValue={[dateFrom, dateTo]}
            onChange={(values) => {
              form.setValue('dateRange', [values[0]!, values[1]!])
            }}
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '150px',
            },
          }}
          label="View"
        >
          <Select
            value={view}
            options={[
              { label: 'Overview', value: 'mediation' },
              { label: 'Comparison', value: 'network' },
            ]}
            hint="VIEW"
            onChange={(e) =>
              navigate(routes.dash.games.revenues(gameId), {
                query: { ...params, view: e.target.value },
                schema: {
                  stringify: paramsStringifySchema,
                },
              })
            }
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '100px',
            },
          }}
          label="Sort"
        >
          <Select
            {...form.register('orderDirection')}
            defaultValue={sort}
            options={[
              { label: 'ASC', value: OrderDirection.Asc },
              { label: 'DESC', value: OrderDirection.Desc },
            ]}
            hint="Sort by DATE"
          />
        </FilterGroup>

        <FilterActions
          onReset={() => {
            refresh()
          }}
        />
      </FilterContainer>

      <Loading visible={loading}>
        <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem)` }}>
          {view === 'mediation' ? <GameRevenueOverviewTable /> : <GameRevenueComparisonTable />}
        </Box>
      </Loading>
    </GameDashboardLayout>
  )
}
