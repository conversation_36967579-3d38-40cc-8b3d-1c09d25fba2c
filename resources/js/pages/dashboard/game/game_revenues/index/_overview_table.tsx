import { GridColDef, GridColumnGroup, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { sumBy, fromPairs } from 'lodash-es'
import { colors } from '@mui/material'

import { DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table/datagrid'
import {
  MediationAggregationPresenter,
  MediationDailyRevenueGroup,
  MediationPresenter,
} from '#controllers/dashboard/game/mediation_presenter'
import { formatter } from '#utils/formatter'
import type { MediationDailyRevenue } from '#models/game_revenue'
import { PaginationState } from '@/components/pagination'
import { GameRevenueMediationId } from '#config/enums'
import { avgBy, maxBy, minBy, safeDivide } from '#utils/math'
import { useQuery } from '@tanstack/react-query'
import { isLoading, Loading } from '@/components/loading'
import { usePagination, useParams } from './_util'
import { autoFillDate, toolkitApi } from '@/components/toolkit-api'

const getHeaderClassName = (mediationId: number) => {
  if (mediationId === GameRevenueMediationId.UnityLP) {
    return 'unityLPCell'
  }

  if (mediationId === GameRevenueMediationId.Applovin) {
    return 'applovinCell'
  }

  if (mediationId === GameRevenueMediationId.Admob) {
    return 'admobCell'
  }

  if (mediationId === GameRevenueMediationId.NonMediation) {
    return 'nonMediationCell'
  }

  throw new Error('Unknown mediation id')
}

export function GameRevenueOverviewTable() {
  const params = useParams()
  const pagination = usePagination()

  const listRevenueQuery = useQuery({
    queryKey: [
      'game',
      'revenue',
      'list',
      params.gameId,
      params.from.format('YYYY-MM-DD'),
      params.to.format('YYYY-MM-DD'),
      params.sort,
      params.page,
      params.perPage,
    ],
    queryFn: async () => {
      const response = await toolkitApi.game.listRevenue({
        storeId: params.gameId,
        from: params.from.format('YYYY-MM-DD'),
        to: params.to.format('YYYY-MM-DD'),
        direction: params.sort,
        page: pagination.page,
        perPage: pagination.perPage,
      })

      const groups = autoFillDate(
        response.data,
        (attrs) =>
          ({
            ...attrs,
            date: attrs.date.format('YYYY-MM-DD') as any,
            revenues: [],
          }) as unknown as MediationDailyRevenueGroup,
        params.sort
      )

      return {
        ...response,
        data: groups,
      }
    },
  })

  const loading = isLoading([
    listRevenueQuery.isLoading,
    listRevenueQuery.isRefetching,
    listRevenueQuery.isError,
  ])

  return (
    <Loading visible={loading}>
      <Table
        mediations={listRevenueQuery.data?.meta?.mediations!}
        dailyRevenueGroups={listRevenueQuery.data?.data!}
        pagination={pagination}
        aggregation={listRevenueQuery.data?.meta?.aggregation!}
        aggregationAttributes={listRevenueQuery.data?.meta?.aggregationAttributes!}
      />
    </Loading>
  )
}

function Table({
  mediations,
  dailyRevenueGroups,
  pagination,
  aggregation,
  aggregationAttributes,
}: {
  mediations: MediationPresenter[]
  dailyRevenueGroups: MediationDailyRevenueGroup[]
  pagination: PaginationState
  aggregation: MediationDailyRevenueGroup
  aggregationAttributes: MediationAggregationPresenter[]
}) {
  const { t } = useTranslation()

  const revenueGroups = useMemo(() => {
    return dailyRevenueGroups.map((group) => {
      const bannerRevenue = sumBy(group.revenues, 'bannerRevenue')
      const interRevenue = sumBy(group.revenues, 'interRevenue')
      const rewardRevenue = sumBy(group.revenues, 'rewardRevenue')
      const mrecRevenue = sumBy(group.revenues, 'mrecRevenue')
      const grossRevenue = sumBy(group.revenues, 'revenue')
      const audioRevenue = sumBy(group.revenues, 'audioRevenue')
      const aoaRevenue = sumBy(group.revenues, 'aoaRevenue')
      return {
        ...group,
        total: {
          bannerRevenue: bannerRevenue,
          bannerRevenueRate: safeDivide(bannerRevenue, grossRevenue),
          interRevenue: interRevenue,
          interRevenueRate: safeDivide(interRevenue, grossRevenue),
          rewardRevenue: rewardRevenue,
          rewardRevenueRate: safeDivide(rewardRevenue, grossRevenue),
          mrecRevenue: mrecRevenue,
          mrecRevenueRate: safeDivide(mrecRevenue, grossRevenue),
          netRevenue: sumBy(group.revenues, 'netRevenue'),
          grossRevenue,
          audioRevenue,
          audioRevenueRate: safeDivide(audioRevenue, grossRevenue),
          aoaRevenue,
          aoaRevenueRate: safeDivide(aoaRevenue, grossRevenue),
        },
      }
    })
  }, [dailyRevenueGroups])

  const autoAggregationFn = useCallback(
    (collection: any[], attr: string, revenue: MediationDailyRevenue) => {
      try {
        const aggregationAttribute = aggregationAttributes
          .find((m) => m.mediationId === revenue?.mediationId)!
          .attributes.find((a) => a.name === attr)

        switch (aggregationAttribute?.operator) {
          case 'avg':
            return avgBy(collection, attr)

          case 'sum':
            return sumBy(collection, attr)

          default:
            return sumBy(collection, attr)
        }
      } catch (err) {
        return sumBy(collection, attr)
      }
    },
    [aggregationAttributes]
  )

  const summaryRow = useMemo(() => {
    const grossRevenue = sumBy(aggregation.revenues, 'revenue')
    const bannerRevenue = sumBy(aggregation.revenues, 'bannerRevenue')
    const interRevenue = sumBy(aggregation.revenues, 'interRevenue')
    const aoaRevenue = sumBy(aggregation.revenues, 'aoaRevenue')
    const rewardRevenue = sumBy(aggregation.revenues, 'rewardRevenue')
    const mrecRevenue = sumBy(aggregation.revenues, 'mrecRevenue')
    const audioRevenue = sumBy(aggregation.revenues, 'audioRevenue')

    return {
      date: 'Summary' as any,
      revenues: aggregation.revenues,
      autogen: true,
      total: {
        bannerRevenue,
        bannerRevenueRate: safeDivide(bannerRevenue, grossRevenue),
        interRevenue,
        interRevenueRate: safeDivide(interRevenue, grossRevenue),
        aoaRevenue,
        aoaRevenueRate: safeDivide(aoaRevenue, grossRevenue),
        rewardRevenue,
        rewardRevenueRate: safeDivide(rewardRevenue, grossRevenue),
        mrecRevenue,
        mrecRevenueRate: safeDivide(mrecRevenue, grossRevenue),
        netRevenue: sumBy(aggregation.revenues, 'netRevenue'),
        grossRevenue,
        audioRevenue,
        audioRevenueRate: safeDivide(audioRevenue, grossRevenue),
      },
    } as MediationDailyRevenueGroup
  }, [aggregation])

  const aggregate = useCallback(
    (fn: any, hierarchy: string[]): MediationDailyRevenueGroup => {
      const id = hierarchy[hierarchy.length - 1]

      return {
        date: id as any,
        hierarchy,
        autogen: true,
        revenues: summaryRow.revenues.map((r) => {
          const mediationDailyRevenues = revenueGroups.map((g) =>
            g.revenues.find((rev) => rev.mediationId === r.mediationId)
          )

          return {
            ...fromPairs(
              Object.keys(r)
                .map((attr) => {
                  try {
                    const val = fn(mediationDailyRevenues, attr, r)
                    return [attr, val]
                  } catch (err) {
                    return null
                  }
                })
                .filter((e): e is [string, any] => !!e)
            ),
            mediationId: r.mediationId,
          } as unknown as MediationDailyRevenue
        }),
        total: {
          ...fromPairs(
            Object.keys(summaryRow.total)
              .map((attr) => {
                try {
                  const val = fn(
                    revenueGroups.map((e) => e.total),
                    attr
                  )
                  return [attr, val]
                } catch (err) {
                  console.log(err)
                  return null
                }
              })
              .filter((e): e is [string, any] => !!e)
          ),
        },
      } as MediationDailyRevenueGroup
    },
    [summaryRow, revenueGroups]
  )

  const rows = useMemo<MediationDailyRevenueGroup[]>(() => {
    console.log(aggregate(sumBy, ['SUM']))

    return [
      summaryRow,
      aggregate(autoAggregationFn, ['Total']),
      aggregate(sumBy, ['Total', 'SUM']),
      aggregate(avgBy, ['Total', 'AVG']),
      aggregate(minBy, ['Total', 'MIN']),
      aggregate(maxBy, ['Total', 'MAX']),
      ...revenueGroups,
    ]
  }, [revenueGroups, aggregation])

  const columns = useMemo<GridColDef<MediationDailyRevenueGroup>[]>(
    () => [
      {
        field: 'date',
        headerName: 'Date',
        width: 85,
        headerClassName: 'dateCell',
      },
      {
        field: 'netRevenue',
        headerName: 'Revenue (NET)',
        valueGetter: (_, row) => row.total.netRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'revCell',
      },
      {
        field: 'revenue',
        headerName: 'Revenue (Gross)',
        valueGetter: (_, row) => row.total.grossRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'revCell',
      },
      {
        field: 'arpDau',
        headerName: 'Total ARPDAU',
        headerClassName: 'dauCell',
        width: 85,
        valueFormatter: (value) => formatter.round(value),
        valueGetter: (_, row) =>
          safeDivide(row.total.grossRevenue, row.revenues[0]?.dailyActiveUserCount || 0),
      },
      {
        field: 'bannerRevenue',
        headerName: 'Banner rev final',
        valueGetter: (_, row) => row.total.bannerRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'bannerRevenueRate',
        headerName: '%',
        valueGetter: (_, row) => row.total.bannerRevenueRate,
        valueFormatter: (value) => formatter.percentage(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'interRevenue',
        headerName: 'Inter rev final',
        valueGetter: (_, row) => row.total.interRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'interRevenueRate',
        headerName: '%',
        valueGetter: (_, row) => row.total.interRevenueRate,
        valueFormatter: (value) => formatter.percentage(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'aoaRevenue',
        headerName: 'AOA rev final',
        valueGetter: (_, row) => row.total.aoaRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'aoaRevenueRate',
        headerName: '%',
        valueGetter: (_, row) => row.total.aoaRevenueRate,
        valueFormatter: (value) => formatter.percentage(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'rewardRevenue',
        headerName: 'Reward rev final',
        valueGetter: (_, row) => row.total.rewardRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'rewardRevenueRate',
        headerName: '%',
        valueGetter: (_, row) => row.total.rewardRevenueRate,
        valueFormatter: (value) => formatter.percentage(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'mrecRevenue',
        headerName: 'MREC rev final',
        valueGetter: (_, row) => row.total.mrecRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'mrecRevenueRate',
        headerName: '%',
        valueGetter: (_, row) => row.total.mrecRevenueRate,
        valueFormatter: (value) => formatter.percentage(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'audioRevenue',
        headerName: 'Audio rev final',
        valueGetter: (_, row) => row.total.audioRevenue,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'audioRevenueRate',
        headerName: '%',
        valueGetter: (_, row) => row.total.audioRevenueRate,
        valueFormatter: (value) => formatter.percentage(value),
        width: 85,
        headerClassName: 'yellow',
      },
      {
        field: 'dailyActiveUserCount',
        headerName: 'Daily Active Users',
        valueGetter: (_, row) => row.revenues[0]?.dailyActiveUserCount || 0,
        valueFormatter: (value) => formatter.round(value),
        width: 85,
        headerClassName: 'dauCell',
      },
      ...mediations.flatMap((mediation) => {
        return mediation.columns.map((c): GridColDef<MediationDailyRevenueGroup> => {
          return {
            field: `${mediation.id}_${c.name}`,
            headerName: c.header,
            valueGetter: (_, row) =>
              row.revenues.find((r) => r.mediationId === mediation.id)?.[
                c.name as keyof MediationDailyRevenue
              ] || 0,
            valueFormatter: (value) => formatter.round(value),
            width: 85,
            headerClassName: getHeaderClassName(mediation.id),
          }
        })
      }),
    ],
    [mediations]
  )

  const columnGrouping = useMemo<GridColumnGroupingModel>(() => {
    return mediations
      .map(
        (mediation): GridColumnGroup => ({
          groupId: mediation.id.toString(),
          headerName: t(`mediations.${mediation.id}.name`),
          description: '',
          children: mediation.columns.map((c) => ({ field: `${mediation.id}_${c.name}` })),
          headerClassName: `columnHeaderGroup ${getHeaderClassName(mediation.id)}`,
        })
      )
      .concat([
        {
          groupId: 'banner',
          children: [{ field: 'bannerRevenue' }, { field: 'bannerRevenueRate' }],
          headerName: 'Banner',
          headerClassName: 'columnHeaderGroup yellow',
        },
        {
          groupId: 'inter',
          children: [{ field: 'interRevenue' }, { field: 'interRevenueRate' }],
          headerName: 'Inter',
          headerClassName: 'columnHeaderGroup yellow',
        },
        {
          groupId: 'aoa',
          children: [{ field: 'aoaRevenue' }, { field: 'aoaRevenueRate' }],
          headerName: 'AOA',
          headerClassName: 'columnHeaderGroup yellow',
        },
        {
          groupId: 'reward',
          children: [{ field: 'rewardRevenue' }, { field: 'rewardRevenueRate' }],
          headerName: 'Reward',
          headerClassName: 'columnHeaderGroup yellow',
        },
        {
          groupId: 'mrec',
          children: [{ field: 'mrecRevenue' }, { field: 'mrecRevenueRate' }],
          headerName: 'MREC',
          headerClassName: 'columnHeaderGroup yellow',
        },
        {
          groupId: 'audio',
          children: [{ field: 'audioRevenue' }, { field: 'audioRevenueRate' }],
          headerName: 'Audio',
          headerClassName: 'columnHeaderGroup yellow',
        },
      ])
  }, [mediations, t])

  return (
    <DataGrid
      /**
       * Models
       */
      rows={rows}
      getRowId={(row) => row.date.toString()}
      pagination={pagination}
      pinnedRows={{
        top: [rows[0]],
      }}
      treeData
      getTreeDataPath={(row) => row.hierarchy || [row.date.toString()]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      /**
       * Col def
       */
      columns={columns}
      columnGroupingModel={columnGrouping}
      editable={false}
      actionColumn={false}
      /**
       * Styles
       */
      sx={{
        '& .columnHeaderGroup.MuiDataGrid-columnHeader': {
          minHeight: 50,
          height: 50,
        },
        '& .MuiDataGrid-columnHeader:not(.MuiDataGrid-columnHeader--emptyGroup):not(.columnHeaderGroup)':
          {
            minHeight: 75,
          },
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
          borderRight: 1,
          borderColor: 'grey.200',
        },
        '& .dateCell': {
          backgroundColor: colors.blue[100],
        },
        '& .revCell': {
          backgroundColor: colors.purple[100],
        },
        '& .dauCell, & .tuningCell': {
          backgroundColor: colors.blue[300],
        },
        '& .unityLPCell': {
          backgroundColor: colors.blue[100],
        },
        '& .applovinCell': {
          backgroundColor: colors.green[100],
        },
        '& .admobCell': {
          backgroundColor: colors.red[100],
        },
        '& .yellow': {
          backgroundColor: colors.yellow[100],
        },
        '& .nonMediationCell': {
          backgroundColor: colors.orange[200],
        },
      }}
    />
  )
}
