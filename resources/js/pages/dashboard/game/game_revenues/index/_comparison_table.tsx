import { useGraphql, useUpdateGameMetric } from '@/components/toolkit-api'
import { graphql } from '@/gql'
import { colors } from '@mui/material'
import dayjs from 'dayjs'
import { useMemo } from 'react'
import { useParam, useParams } from './_util'
import { isLoading, Loading } from '@/components/loading'
import {
  GameRevenueIndex_GetNetworkRevenueQuery,
  GameRevenueMetricAttributesFragment,
} from '@/graphql'
import { GameNetworkRevenue } from '#graphql/main'
import { GridColDef, GridColumnGroupingModel, useGridApiRef } from '@mui/x-data-grid-pro'
import { groupBy, orderBy, sumBy, toPairs } from 'lodash-es'
import { CURRENCY_COLORS, DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table'
import { formatter } from '#utils/formatter'
import classNames from 'classnames'
import { formatCurrency } from '@/components/typography'
import { avgBy, maxBy, minBy } from '#utils/math'
import { COLORS } from '@/components/color'
import { inRange } from '#utils/pure'

graphql(`
  fragment GameNetworkRevenueAggregationAttributes on GameNetworkRevenue {
    networkId
    revenue
    mediationRevenue
    varianceRate
  }
`)

graphql(`
  fragment GameRevenueMetricAttributes on AgencyMetric {
    revenue
  }
`)

graphql(`
  fragment GameNetworkRevenueAttributes on GameNetworkRevenue {
    ...GameNetworkRevenueAggregationAttributes
    date
  }

  fragment GameNetworkRevenueWithMetricAttributes on GameNetworkRevenue {
    ...GameNetworkRevenueAttributes
    metric {
      ...GameRevenueMetricAttributes
    }
  }
`)

const query = graphql(`
  query GameRevenueIndex_getNetworkRevenue(
    $dateFrom: Date!
    $dateTo: Date!
    $gameId: ID!
    $page: Int!
    $perPage: Int!
    $weeklyDateFrom: Date!
    $weeklyDateTo: Date!
    $orderDirection: OrderDirection!
  ) {
    gameNetworkRevenues(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
      offset: { page: $page, perPage: $perPage }
      order: { direction: $orderDirection }
    ) {
      collection {
        ...GameNetworkRevenueWithMetricAttributes
      }
      pageInfo {
        ...PageInfoAttributes
      }
    }

    summary: aggregateGameNetworkRevenue(where: { gameId: $gameId }) {
      collection {
        ...GameNetworkRevenueAggregationAttributes
        network {
          id
          name
        }
      }
    }

    total: aggregateGameNetworkRevenue(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        ...GameNetworkRevenueAggregationAttributes
      }
    }

    weekly: aggregateGameNetworkRevenue(
      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }
    ) {
      collection {
        ...GameNetworkRevenueAggregationAttributes
      }
    }
  }
`)

export function GameRevenueComparisonTable() {
  const today = useMemo(() => dayjs(), [])
  const params = useParams()
  const getData = useGraphql(query, {
    variables: {
      gameId: params.gameId,
      page: params.page,
      perPage: params.perPage,
      weeklyDateTo: today.format('YYYY-MM-DD'),
      weeklyDateFrom: today.subtract(6, 'days').format('YYYY-MM-DD'),
      dateTo: params.to.format('YYYY-MM-DD'),
      dateFrom: params.from.format('YYYY-MM-DD'),
      orderDirection: params.sort,
    },
  })
  const loading = isLoading([getData.error, getData.loading])

  return (
    <Loading visible={loading}>
      <Table data={getData.data!} />
    </Loading>
  )
}

type Row = {
  date: string
  networks: GameNetworkRevenue[]
  hierarchy?: string[]
  revenue: number
  mediationRevenue: number
  metric: GameRevenueMetricAttributesFragment
}

const isAggregationRow = (row: Row) => !dayjs(row.date).isValid()

function Table({
  data: { summary, weekly, total, gameNetworkRevenues: revenues },
}: {
  data: GameRevenueIndex_GetNetworkRevenueQuery
}) {
  const sort = useParam((p) => p.sort)

  const networks = useMemo(() => {
    return orderBy(
      summary.collection.map((rev) => rev.network),
      (network) => {
        return weekly.collection.find((r) => r.networkId === network.id)?.mediationRevenue || 0
      },
      sort.toLowerCase() as any
    )
  }, [summary, weekly])

  const rows = useMemo<Row[]>(() => {
    const dates = orderBy(
      toPairs(groupBy(revenues.collection, 'date')).map(
        ([date, networks]) =>
          ({
            date: dayjs(date).format('YYYY-MM-DD'),
            networks,
            mediationRevenue: sumBy(networks, 'mediationRevenue'),
            revenue: sumBy(networks, 'revenue'),
            metric: networks[0].metric,
          }) as Row
      ),
      'date',
      sort.toLowerCase() as any
    )

    const makeAggregationRow = (fn: any, id: string): Row => {
      const networkRevs = networks.map((network) => {
        const networkRevenues = dates.map(
          (d) => d.networks.find((n) => n.networkId === network.id)!
        )
        return {
          networkId: network.id,
          revenue: fn(networkRevenues, 'revenue'),
          mediationRevenue: fn(networkRevenues, 'mediationRevenue'),
          varianceRate: fn(networkRevenues, 'varianceRate'),
          date: id,
          weeklyRevenue: 0,
          __typename: 'GameNetworkRevenue',
          network,
          metric: {} as any,
        }
      }) as any[]

      return {
        date: id,
        networks: networkRevs,
        hierarchy: ['Total', id],
        mediationRevenue: fn(networks, 'mediationRevenue'),
        revenue: fn(networkRevs, 'revenue'),
        metric: {} as any,
      }
    }

    return [
      { date: 'Summary', networks: summary.collection } as Row,
      { date: 'Total', networks: total.collection } as Row,
      makeAggregationRow(sumBy, 'SUM'),
      makeAggregationRow(avgBy, 'AVG'),
      makeAggregationRow(maxBy, 'MAX'),
      makeAggregationRow(minBy, 'MIN'),
      ...dates,
    ]
  }, [revenues, networks])

  const columns = useMemo<GridColDef<Row>[]>(() => {
    return [
      {
        field: 'date',
        headerName: 'Date',
      },
      {
        field: 'totalMetricRevenue',
        headerName: 'from Game Metrics',
        headerClassName: 'bg-purple-200',
        editable: true,
        valueFormatter: formatCurrency,
        valueGetter: (_, row) => (isAggregationRow(row) ? null : row?.metric?.revenue),
      },
      {
        field: 'totalMediationRevenue',
        headerName: 'from Mediation',
        headerClassName: 'bg-purple-200',
        valueFormatter: formatCurrency,
        valueGetter: (_, row) => (isAggregationRow(row) ? null : row.mediationRevenue),
      },
      {
        field: 'totalRevenue',
        headerName: 'from Ad Network',
        headerClassName: 'bg-purple-200',
        valueFormatter: formatCurrency,
        valueGetter: (_, row) => (isAggregationRow(row) ? null : row.revenue),
      },
      ...networks.flatMap((network): GridColDef<Row>[] => [
        {
          field: `${network.id}.mediationRevenue`,
          headerName: 'from Mediation',
          valueGetter: (_, row) =>
            row.networks.find((n) => n.networkId === network.id)?.mediationRevenue ?? 0,
          valueFormatter: formatCurrency,
          headerClassName: 'pink',
        },
        {
          field: `${network.id}.revenue`,
          headerName: 'from Ad Network',
          valueGetter: (_, row) =>
            row.networks.find((n) => n.networkId === network.id)?.revenue ?? 0,
          valueFormatter: formatCurrency,
          headerClassName: 'network',
        },
        {
          field: `${network.id}.varianceRate`,
          headerName: '% difference',
          headerClassName: 'difference',
          valueGetter: (_, row) => {
            return row.networks.find((n) => n.networkId === network.id)?.varianceRate || 0
          },
          valueFormatter: (value) => formatter.percentage(value, 4),
          cellClassName: ({ value }) =>
            classNames({
              'bg-green-100': inRange(value, { lower: 0.025, upper: 0.1, upperNotion: ')' }),
              'bg-green-300': inRange(value, { lower: 0.1, upper: 0.2, upperNotion: ')' }),
              'bg-green-600': inRange(value, { lower: 0.2, upper: Number.POSITIVE_INFINITY }),
              'bg-red-100': inRange(value, { lower: -0.1, lowerNotion: '(', upper: -0.025 }),
              'bg-red-300': inRange(value, { lower: -0.2, lowerNotion: '(', upper: -0.1 }),
              'bg-red-600': inRange(value, { lower: Number.NEGATIVE_INFINITY, upper: -0.2 }),
            }),
        },
      ]),
    ]
  }, [networks, revenues])

  const columnGroupingModel = useMemo<GridColumnGroupingModel>(() => {
    return [
      {
        groupId: 'total',
        headerName: 'Total',
        headerClassName: 'networkGroup',
        children: [
          { field: 'totalMetricRevenue' },
          { field: 'totalMediationRevenue' },
          { field: 'totalRevenue' },
        ],
      },
      ...networks.map((network) => ({
        groupId: `${network.id}`,
        headerName: network.name,
        headerClassName: 'networkGroup',
        children: [
          { field: `${network.id}.mediationRevenue` },
          { field: `${network.id}.revenue` },
          { field: `${network.id}.varianceRate` },
        ],
      })),
    ]
  }, [networks])

  const updateGameMetric = useUpdateGameMetric()
  const apiRef = useGridApiRef()

  const gameId = useParams().gameId

  const onUpdate = async (postUpdateRow: any, preUpdateRow: any, row: any) => {
    const newValue = postUpdateRow['totalMetricRevenue']
    await updateGameMetric.mutateAsync({
      gameId,
      date: preUpdateRow.date,
      override: { revenue: newValue === '' ? null : Number(newValue) } as any,
      metadata: {},
    })

    postUpdateRow.metric = {
      ...postUpdateRow.metric,
      revenue: newValue,
    }

    apiRef.current.stopRowEditMode({ id: row.rowId })

    return postUpdateRow
  }

  return (
    <DataGrid
      treeData
      apiRef={apiRef}
      getTreeDataPath={(row) => row.hierarchy || [row.date]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      columns={columns}
      columnGroupingModel={columnGroupingModel}
      processRowUpdate={onUpdate}
      rows={rows}
      getRowId={(r) => r.date}
      editable
      actionColumn={false}
      pinnedColumns={{
        left: ['date'],
      }}
      pinnedRows={{ top: [rows[0]] }}
      sx={{
        ...CURRENCY_COLORS,
        [`& .network`]: { bgcolor: colors.green[200] },
        [`& .difference`]: { bgcolor: colors.lightBlue[200] },
        [`& .networkGroup`]: { bgcolor: colors.yellow[200] },
        [`& .MuiDataGrid-columnHeaderTitle`]: {
          paddingY: 1.5,
        },
        ...COLORS,
      }}
    />
  )
}
