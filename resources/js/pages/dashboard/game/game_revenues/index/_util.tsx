import { OrderDirection } from '#graphql/main'
import { createParamsStore, routes } from '@/components/location'
import { graphql } from '@/gql'
import dayjs from 'dayjs'
import { mixed, number, object, string } from 'yup'

export const getInitDataQuery = graphql(`
  query GameRevenueExplorerIndex_Init($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }
  }
`)

export const paramsStringifySchema = object({
  from: mixed().stringifyDate(),
  to: mixed().stringifyDate(),
  sort: mixed(),
  page: number(),
  perPage: number(),
  view: mixed(),
})

export const { useParam, useParams, useSetParams, usePagination } = createParamsStore(
  routes.dash.games.revenues(':gameId'),
  {
    parse: object({
      gameId: string().required(),
      from: mixed<dayjs.Dayjs>()
        .paramDate({ defaultValue: () => dayjs().subtract(1, 'month') })
        .required(),
      to: mixed<dayjs.Dayjs>()
        .paramDate({ defaultValue: () => dayjs() })
        .required(),
      sort: string().oneOf(Object.values(OrderDirection)).default(OrderDirection.Desc),
      page: number().optional().default(1),
      perPage: number().optional().default(200),
      view: string().oneOf(['mediation', 'network']).default('mediation'),
    }),
    stringify: paramsStringifySchema,
  }
)
