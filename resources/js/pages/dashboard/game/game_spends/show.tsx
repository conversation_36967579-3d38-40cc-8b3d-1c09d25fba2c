import { routes, useNavigate } from '@/components/location'
import { useGraphql } from '@/components/toolkit-api'
import { render } from '@/react'
import {
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { DateRange, FilterActions, FilterContainer, FilterGroup, Select } from '@/components/form'
import { isLoading, Loading } from '@/components/loading'

import { getInitDataQuery, paramsStringifySchema, useParams, useSetParams } from './show/_util'
import { useForm } from 'react-hook-form'
import { GameCostComparisonTable } from './show/_comparison_table'
import { OrderDirection } from '#graphql/main'
import { GameCostOverviewTable } from './show/_overview_table'
import { useEffect } from 'react'
import { Box } from '@mui/material'
import { GameDashboardLayout } from '@/components/layout/game_dashboard_layout'

render(Page)

function Page() {
  const params = useParams()
  const { view, gameId, from: dateFrom, to: dateTo, sort: orderDirection } = params
  const setParams = useSetParams()

  const { refresh, navigate } = useNavigate()

  const getInitData = useGraphql(getInitDataQuery, {
    variables: {
      gameId,
    },
  })

  const loading = isLoading([getInitData.error, getInitData.loading])

  useBreadcrumbs(
    [
      {
        label: 'All Games',
        url: routes.dash.games.index(),
      },
      {
        label: getInitData.data?.game?.name ?? gameId,
        url: routes.dash.gameMetrics.index(gameId),
      },
      'Cost Explorer',
    ],
    'dashboard'
  )

  const filterForm = useForm({
    defaultValues: {
      dateRange: [dateFrom, dateTo],
      orderDirection,
      view,
    },
  })

  const [, navbarSize] = useLayoutSize('navbar')
  const [, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [, notificationSize] = useLayoutSize('notification')
  const [, gameNavSize] = useLayoutSize('gameNav')
  const [filterRef, setFilterSize, filterSize] = useLayoutComponentRegistration('filter')
  const layoutHeight = useLayoutHeight(
    navbarSize,
    breadcrumbsSize,
    notificationSize,
    gameNavSize,
    filterSize
  )
  console.log('layoutHeight', layoutHeight)

  useEffect(() => {
    setFilterSize({
      height: filterRef.current?.clientHeight || 0,
    })
  }, [filterRef, setFilterSize])

  return (
    <GameDashboardLayout gameId={gameId}>
      <FilterContainer
        ref={filterRef}
        sx={{ pb: 1, gap: 2 }}
        direction={{
          xs: 'column',
          sm: 'row',
        }}
        alignItems={{
          xs: 'flex-start',
          sm: 'flex-end',
        }}
        component="form"
        onSubmit={filterForm.handleSubmit((formData) => {
          setParams({
            from: formData.dateRange[0],
            to: formData.dateRange[1],
            sort: formData.orderDirection,
            view: params.view,
          })
        })}
      >
        <FilterGroup
          label="Select Date"
          sx={{
            width: {
              xs: '100%',
              sm: '350px',
            },
          }}
        >
          <DateRange
            defaultValue={[dateFrom, dateTo] as any}
            onChange={(values) => {
              filterForm.setValue('dateRange', [values[0]!, values[1]! as any])
            }}
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '150px',
            },
          }}
          label="View"
        >
          <Select
            value={view}
            options={[
              { value: 'mediation', label: 'Overview' },
              { value: 'agency', label: 'Comparison' },
            ]}
            onChange={(e) => {
              navigate(routes.dash.games.costs(gameId), {
                query: {
                  ...params,
                  view: e.target.value,
                },
                schema: {
                  stringify: paramsStringifySchema,
                },
              })
            }}
            hint="Sort by Date"
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '100px',
            },
          }}
          label="Sort"
        >
          <Select
            {...filterForm.register('orderDirection')}
            defaultValue={orderDirection}
            options={[
              { value: OrderDirection.Asc, label: 'ASC' },
              { value: OrderDirection.Desc, label: 'DESC' },
            ]}
            hint="Sort by Date"
          />
        </FilterGroup>

        <FilterActions
          onReset={() => {
            refresh()
          }}
        />
      </FilterContainer>

      <Loading visible={loading}>
        <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem)` }}>
          {view === 'mediation' ? <GameCostOverviewTable /> : <GameCostComparisonTable />}
        </Box>
      </Loading>
    </GameDashboardLayout>
  )
}
