import { GridColDef, GridEditInputCell } from '@mui/x-data-grid-pro'
import { HTMLAttributes, TableHTMLAttributes, useMemo } from 'react'
import { DateTime } from 'luxon'
import classNames from 'classnames'
import { groupBy, isNil, orderBy, sumBy } from 'lodash-es'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Box, colors } from '@mui/material'

import { formatter } from '#utils/formatter'
import { AdNetworkInputType } from '#config/enums'
import { DataGrid } from '@/components/table/datagrid'
import { PaginationState } from '@/components/pagination/index'
import { Loading } from '@/components/loading/index.jsx'
import { autoFillDate, toolkitApi } from '@/components/toolkit-api/index.js'

import { DailySpend, usePagination, useParams } from './_util.js'
import GameSpend from '#models/game_spend'
import { useQueryToast } from '@/components/toast/use_query_toast.js'
import { useCsrf } from '@/components/csrf/index.jsx'
import { useMutationNotification } from '@/components/toolkit-api/notification.js'

export function GameCostOverviewTable() {
  const queryClient = useQueryClient()

  const { data: aggregation } = useQuery<GameSpend>({
    queryKey: ['dashboard', 'game_spends', 'aggregation'],
    enabled: false,
  })

  const { gameId, page, perPage, sort: direction, from: dateFrom, to: dateTo } = useParams()
  const pagination = usePagination()

  const listGameSpendQuery = useQuery({
    queryKey: [
      'dashboard',
      'game_spends',
      gameId,
      dateFrom.format('YYYY-MM-DD'),
      dateTo.format('YYYY-MM-DD'),
      direction,
      page,
      perPage,
    ],
    queryFn: () =>
      toolkitApi
        .listGameSpend(gameId, {
          perPage,
          page,
          from: dateFrom.format('YYYY-MM-DD'),
          to: dateTo.format('YYYY-MM-DD'),
          direction,
        })
        .then((r) => {
          queryClient.setQueryData(['dashboard', 'game_spends', 'aggregation'], r.meta.aggregation)
          pagination.setFromMeta(r.meta)
          return r.data
        }),
  })

  const spends = listGameSpendQuery.data

  useQueryToast(listGameSpendQuery)

  const csrf = useCsrf()

  const updateSpendMutation = useMutation({
    mutationFn: async (form: { date: string; spends: any[] }) => {
      return await toolkitApi.updateGameSpend(gameId, {
        ...form,
        ...csrf.getField(),
      })
    },
    mutationKey: ['dashboard', 'game_spends', 'update'],
    onSuccess: (response) => {
      queryClient.setQueryData(
        ['dashboard', 'game_spends', 'aggregation'],
        response.meta.aggregation
      )

      queryClient.setQueryData(
        [
          'dashboard',
          'game_spends',
          gameId,
          dateFrom.format('YYYY-MM-DD'),
          dateTo.format('YYYY-MM-DD'),
          direction,
          page,
          perPage,
        ],
        (oldData: GameSpend[]) => {
          const date = response.data[0]!.date
          return oldData.filter((s) => s.date !== date).concat(response.data)
        }
      )
    },
  })

  useMutationNotification(updateSpendMutation)

  const dailySpends = useMemo<DailySpend[]>(() => {
    const dailies = orderBy(
      Object.entries(groupBy(spends, (s) => s.date)).map((entry) => ({
        date: entry[0],
        spends: entry[1],
        preTaxAmount: sumBy(entry[1], (s) => s.preTaxAmount || 0),
        totalAmount: sumBy(entry[1], (s) => s.totalAmount || 0),
        autogen: false,
        mmp: entry[1][0].mmp,
      })),
      (g) => new Date(g.date),
      direction.toLowerCase() as any
    )

    return autoFillDate(
      dailies,
      (attrs) => {
        return {
          ...attrs,
          date: attrs.date.format('YYYY-MM-DD'),
          spends: [],
          preTaxAmount: 0,
          totalAmount: 0,
          mmp: 0,
        }
      },
      direction
    )
  }, [spends])

  const rows = useMemo(() => {
    return [
      {
        date: AGGREGATION_ID,
        preTaxAmount: aggregation?.preTaxAmount || 0,
        totalAmount: aggregation?.totalAmount || 0,
        spends: [],
        autogen: false,
        mmp: aggregation?.mmp || 0,
      },
      ...dailySpends,
    ]
  }, [aggregation, dailySpends])

  return (
    <GameSpendTable
      data={rows}
      pagination={pagination}
      onRowUpdate={(spend: DailySpend) =>
        updateSpendMutation.mutateAsync({
          date: spend.date,
          spends: spend.spends,
        })
      }
    />
  )
}

export const AGGREGATION_ID = 'SUMMARY'

const isAggregationRow = (row: DailySpend) => row.date === AGGREGATION_ID

const columns: GridColDef<DailySpend>[] = [
  {
    field: 'date',
    headerName: 'Date',
    width: 80,
    colSpan: (_, row) => (isAggregationRow(row) ? 2 : 1),
    cellClassName: ({ row }) => classNames('text-center', { 'font-bold': isAggregationRow(row) }),
  },
  {
    field: 'day',
    headerName: 'Day',
    width: 50,
    valueGetter: (_, metric) => DateTime.fromISO(metric.date as any).weekdayLong?.slice(0, 3),
    cellClassName: 'text-center',
  },
  {
    field: 'totalAmount',
    headerName: 'Total Cost',
    valueFormatter: (value) => `$${formatter.round(value)}`,
    valueGetter: (_, row) => row.totalAmount + row.mmp,
    width: 85,
    headerClassName: 'cyan',
  },
  {
    field: 'preTaxAmount',
    headerName: 'Cost (non-Tax)',
    valueFormatter: (value) => `$${formatter.round(value)}`,
    valueGetter: (_, row) => row.preTaxAmount + row.mmp,
    width: 85,
    headerClassName: 'cyan',
  },
  {
    field: 'mmp',
    headerName: 'MMP',
    valueFormatter: (value) => `$${formatter.round(value)}`,
    width: 85,
    headerClassName: 'cyan',
  },
]

export function GameSpendTable({
  data,
  onRowUpdate,
  pagination,
}: {
  data: DailySpend[]
  onRowUpdate: (spend: DailySpend) => any
  pagination: PaginationState
} & HTMLAttributes<HTMLTableElement & TableHTMLAttributes<HTMLTableElement>>) {
  const listAdNetworkQuery = useQuery({
    queryKey: ['ad_network', 'list'],
    queryFn: async () => {
      const response = await toolkitApi.listAdNetwork()
      return response.data
    },
  })

  if (listAdNetworkQuery.isLoading) {
    return <Loading visible />
  }

  const adNetworks = listAdNetworkQuery.data!

  const tableColumns: GridColDef<DailySpend>[] = [
    ...columns,
    ...adNetworks!.map(
      (adNetwork): GridColDef<DailySpend> => ({
        field: adNetwork.id.toString(),
        headerName: adNetwork.name,
        width: 85,
        valueGetter: (_, row) => {
          if (isAggregationRow(row)) {
            return null
          }

          const networkSpend = row.spends.find((s) => s.networkId === adNetwork.id)
          return networkSpend?.preTaxAmount || 0
        },
        valueFormatter: (value) => (isNil(value) ? null : `$${formatter.round(value)}`),
        headerClassName: classNames({
          orange: adNetwork.inputType === AdNetworkInputType.Auto,
          green: adNetwork.inputType === AdNetworkInputType.Manual,
        }),
        editable: adNetwork.inputType === AdNetworkInputType.Manual,
        renderEditCell: (props) =>
          props.row.autogen ? <Box>{props.formattedValue}</Box> : <GridEditInputCell {...props} />,
      })
    ),
  ]

  return (
    <DataGrid
      columns={tableColumns}
      actionColumn={{
        width: 80,
        getActions: ({ row }) => {
          if (isAggregationRow(row) || row.autogen) {
            return []
          }
        },
      }}
      rows={data}
      getRowId={(row) => row.date}
      initialState={{
        pinnedColumns: {
          left: ['date', 'day'],
          right: ['actions'],
        },
      }}
      editable
      processRowUpdate={(updatedRow, originalRow) => {
        if (originalRow.autogen) {
          return originalRow
        }

        const manualNetworks = adNetworks.filter(
          (adNetwork) => adNetwork.inputType === AdNetworkInputType.Manual
        )
        onRowUpdate({
          date: updatedRow.date,
          // @ts-expect-error Update partial
          spends: manualNetworks.map((adNetwork) => ({
            networkId: adNetwork.id,
            preTaxAmount: updatedRow[adNetwork.id.toString() as keyof DailySpend] || 0,
          })),
        })
        return updatedRow
      }}
      pagination={pagination}
      sx={{
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
        },
        '& .green': {
          bgcolor: colors.green['A400'],
        },
        '& .orange': {
          bgcolor: colors.orange['A400'],
        },
        '& .cyan': {
          bgcolor: colors.cyan['A400'],
        },
        '& .text-center': {
          textAlign: 'center !important',
        },
        '& .font-bold': {
          fontWeigth: 'bold',
        },
      }}
    />
  )
}
