import { OrderDirection } from '#graphql/main'
import GameSpend from '#models/game_spend'
import { createParamsStore, routes } from '@/components/location'
import { graphql } from '@/gql'
import dayjs from 'dayjs'
import { string } from 'yup'
import { mixed, number, object } from 'yup'

export type DailySpend = {
  date: string
  spends: GameSpend[]
  preTaxAmount: number
  totalAmount: number
  autogen: boolean
  mmp: number
}

export const getInitDataQuery = graphql(`
  query GameSpendShow_Init($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }
  }
`)

export const paramsParseSchema = object({
  gameId: string().required(),
  from: mixed<dayjs.Dayjs>()
    .paramDate({ defaultValue: () => dayjs().subtract(30, 'days') })
    .required(),
  to: mixed<dayjs.Dayjs>()
    .paramDate({ defaultValue: () => dayjs() })
    .required(),
  page: number().default(1),
  perPage: number().default(200),
  view: string().oneOf(['mediation', 'network']).default('mediation'),
  sort: string().oneOf(Object.values(OrderDirection)).default(OrderDirection.Desc),
})

export const paramsStringifySchema = object({
  gameId: string(),
  from: mixed<dayjs.Dayjs>().stringifyDate(),
  to: mixed<dayjs.Dayjs>().stringifyDate(),
  page: number(),
  perPage: number(),
  view: string(),
  sort: string(),
})

export const { useParam, useParams, useSetParams, usePagination } = createParamsStore(
  routes.dash.games.costs(':gameId'),
  {
    parse: paramsParseSchema,
    stringify: paramsStringifySchema,
  }
)
