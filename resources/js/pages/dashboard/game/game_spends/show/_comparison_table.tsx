import { useGraphql, useUpdateGameMetric } from '@/components/toolkit-api'
import { graphql } from '@/gql'
import { colors } from '@mui/material'
import { useParam, useParams } from './_util'
import dayjs from 'dayjs'
import { isLoading, Loading } from '@/components/loading'
import {
  GameAgencyCostAttributesFragment,
  GameAgencyMetricAttributesFragment,
  GameCostIndex_AgencyDataQuery,
} from '@/graphql'
import { useMemo } from 'react'
import { GridColDef, GridColumnGroupingModel, useGridApiRef } from '@mui/x-data-grid-pro'
import { formatter } from '#utils/formatter'
import { groupBy, orderBy, sumBy, thru, toPairs } from 'lodash-es'
import { CURRENCY_COLORS, DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table'
import { formatCurrency } from '@/components/typography'
import classNames from 'classnames'
import { avgBy, maxBy, minBy } from '#utils/math'
import { COLORS } from '@/components/color'
import { inRange } from '#utils/pure'

graphql(`
  fragment GameAgencyCostAttributes on GameAgencyCost {
    totalCost
    mediationCost
    varianceRate
    agencyId
  }
`)

const query = graphql(`
  query GameCostIndex_AgencyData(
    $gameId: ID!
    $dateFrom: Date!
    $dateTo: Date!
    $orderDirection: OrderDirection!
    $page: Int!
    $perPage: Int!
    $weeklyDateFrom: Date!
    $weeklyDateTo: Date!
  ) {
    gameAgencyCosts(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
      offset: { page: $page, perPage: $perPage }
      order: { direction: $orderDirection }
    ) {
      collection {
        ...GameAgencyCostAttributes
        date
        metric {
          ...GameAgencyMetricAttributes
        }
      }
      pageInfo {
        ...PageInfoAttributes
      }
    }

    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {
      collection {
        ...GameAgencyCostAttributes
        agency {
          ...AdAgencyAttributes
        }
      }
    }

    total: aggregateGameAgencyCost(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        ...GameAgencyCostAttributes
      }
    }

    weekly: aggregateGameAgencyCost(
      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }
    ) {
      collection {
        ...GameAgencyCostAttributes
      }
    }
  }
`)

export function GameCostComparisonTable() {
  const { perPage, page, gameId, to: dateTo, from: dateFrom, sort: direction } = useParams()
  const getData = useGraphql(query, {
    variables: {
      gameId,
      page,
      perPage,
      dateTo: dateTo.format('YYYY-MM-DD'),
      dateFrom: dateFrom.format('YYYY-MM-DD'),
      weeklyDateTo: dayjs().format('YYYY-MM-DD'),
      weeklyDateFrom: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
      orderDirection: direction,
    },
  })
  const loading = isLoading([getData.error, getData.loading])
  return (
    <Loading visible={loading}>
      <Table data={getData.data!} />
    </Loading>
  )
}

type Row = {
  date: string
  agencies: GameAgencyCostAttributesFragment[]
  hierarchy?: string[]
  metric: GameAgencyMetricAttributesFragment
}

const isAggregation = (row: Row) => !dayjs(row.date).isValid()

function Table({ data }: { data: GameCostIndex_AgencyDataQuery }) {
  const orderDirection = useParam((p) => p.sort)
  const { gameId } = useParams()
  const { total, weekly, summary, gameAgencyCosts: costs } = data
  const apiRef = useGridApiRef()
  const agencies = useMemo(
    () =>
      orderBy(
        summary.collection.map((i) => i.agency),
        (agency) => weekly.collection.find((c) => c.agencyId === agency.id)?.mediationCost ?? 0,
        'desc'
      ),
    [summary, weekly]
  )
  const rows = useMemo<Row[]>(() => {
    const dates = orderBy(
      toPairs(groupBy(costs.collection!, 'date')).map(([date, agencies]) => {
        return {
          date,
          agencies,
          metric: agencies[0].metric,
        }
      }),
      'date',
      orderDirection.toLowerCase() as any
    )

    const makeAggregationRow = (fn: any, id: string): Row => {
      return {
        date: id,
        hierarchy: ['Total', id],
        agencies: agencies.map((agency) => {
          const agencyCosts = dates.map((date) =>
            date.agencies.find((a) => a.agencyId === agency.id)
          )

          return {
            mediationCost: fn(agencyCosts, 'mediationCost'),
            totalCost: fn(agencyCosts, 'totalCost'),
            varianceRate: fn(agencyCosts, 'varianceRate'),
            agencyId: agency.id,
          }
        }),
        metric: { paidInstalls: 0, cost: 0 },
      }
    }

    return [
      { agencies: summary.collection, date: 'Summary', metric: { paidInstalls: 0, cost: 0 } },
      { agencies: total.collection, date: 'Total', metric: { paidInstalls: 0, cost: 0 } },
      makeAggregationRow(sumBy, 'SUM'),
      makeAggregationRow(avgBy, 'AVG'),
      makeAggregationRow(maxBy, 'MAX'),
      makeAggregationRow(minBy, 'MIN'),
      ...dates,
    ]
  }, [costs, total, summary])

  const columns = useMemo<GridColDef<Row>[]>(() => {
    return [
      {
        field: 'date',
        headerName: 'Date',
        valueFormatter: (value: dayjs.Dayjs) =>
          thru(dayjs(value), (v) => (v.isValid() ? v.format('YYYY-MM-DD') : value)),
      },
      {
        field: 'totalCost',
        headerName: 'from Game Metrics',
        valueGetter: (_, row) => (isAggregation(row) ? null : row.metric.cost),
        valueFormatter: formatCurrency,
        headerClassName: 'bg-purple-200',
        editable: true,
      },
      {
        field: 'totalMediationCost',
        headerName: 'from MMP',
        valueGetter: (_, row) =>
          isAggregation(row)
            ? null
            : sumBy(row.agencies, (a) => a.mediationCost) + row.metric.paidInstalls * 0.0012,
        valueFormatter: formatCurrency,
        headerClassName: 'bg-purple-200',
      },
      {
        field: 'totalAgencyCost',
        headerName: 'from Agency',
        valueGetter: (_, row) =>
          isAggregation(row)
            ? null
            : sumBy(row.agencies, (a) => a.totalCost || a.totalCost) +
              row.metric.paidInstalls * 0.0012,
        valueFormatter: formatCurrency,
        headerClassName: 'bg-purple-200',
      },

      ...agencies.flatMap((agency): GridColDef<Row>[] => [
        {
          field: `${agency.id}.mediationCost`,
          valueGetter: (_, row) =>
            row.agencies.find((a) => a.agencyId === agency.id)?.mediationCost ?? 0,
          valueFormatter: formatCurrency,
          headerName: 'from MMP',
          headerClassName: 'pink',
        },
        {
          field: `${agency.id}.cost`,
          valueGetter: (_, row) =>
            row.agencies.find((a) => a.agencyId === agency.id)?.totalCost ?? 0,
          valueFormatter: formatCurrency,
          headerName: 'from Agency',
          headerClassName: 'agency',
        },
        {
          field: `${agency.id}.varianceRate`,
          valueGetter: (_, row) =>
            row.agencies.find((a) => a.agencyId === agency.id)?.varianceRate ?? 0,
          valueFormatter: (value) => formatter.percentage(value, 3),
          headerName: '% difference',
          headerClassName: 'difference',
          cellClassName: ({ value }) => {
            const diff = Math.abs(value)
            return classNames({
              'bg-red-100': inRange(diff, { upper: 0.1, upperNotion: ')', lower: 0.025 }),
              'bg-red-300': inRange(diff, { upper: 0.2, upperNotion: ')', lower: 0.1 }),
              'bg-red-600': inRange(diff, { upper: Number.POSITIVE_INFINITY, lower: 0.2 }),
            })
          },
        },
      ]),
    ]
  }, [agencies])

  const columnGroupingModel = useMemo<GridColumnGroupingModel>(() => {
    return [
      {
        headerName: 'Total',
        groupId: 'total',
        headerClassName: 'group',
        children: [
          { field: 'totalCost' },
          { field: 'totalMediationCost' },
          { field: 'totalAgencyCost' },
        ],
      },
      ...agencies.map((agency) => ({
        headerName: agency.name,
        groupId: agency.name,
        headerClassName: 'group',
        children: [
          { field: `${agency.id}.mediationCost` },
          { field: `${agency.id}.cost` },
          { field: `${agency.id}.varianceRate` },
        ],
      })),
    ]
  }, [agencies])

  const updateGameMetric = useUpdateGameMetric()

  const onUpdate = async (postUpdateRow: any, preUpdateRow: any, row: any) => {
    const newCost = postUpdateRow['totalCost']
    await updateGameMetric.mutateAsync({
      gameId,
      date: preUpdateRow.date,
      override: { cost: newCost === '' ? null : Number(newCost) } as any,
      metadata: {},
    })

    postUpdateRow.metric = {
      ...postUpdateRow.metric,
      cost: newCost,
    }

    apiRef.current.stopRowEditMode({ id: row.rowId })

    return postUpdateRow
  }

  return (
    <DataGrid
      treeData
      getTreeDataPath={(row) => row.hierarchy || [row.date]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      rows={rows}
      apiRef={apiRef}
      getRowId={(row) => row.date}
      columns={columns}
      columnGroupingModel={columnGroupingModel}
      editable={true}
      processRowUpdate={onUpdate}
      onProcessRowUpdateError={(e) => console.error(e)}
      actionColumn={false}
      pinnedColumns={{
        left: ['date'],
      }}
      pinnedRows={{ top: [rows[0]] }}
      sx={{
        ...CURRENCY_COLORS,
        [`& .agency`]: { bgcolor: colors.green[200] },
        [`& .difference`]: { bgcolor: colors.lightBlue[200] },
        [`& .group`]: { bgcolor: colors.yellow[200] },
        [`& .MuiDataGrid-columnHeaderTitle`]: {
          paddingY: 1.5,
        },
        ...COLORS,
      }}
    />
  )
}
