import {
  DashboardLayout,
  useBreadcrumbs,
  useLayoutComponentRegistration,
  useLayoutHeight,
  useLayoutSize,
} from '@/components/layout'
import { Head } from '@inertiajs/react'
import { ManagerReportNavTab } from '../_components/_manager_report_nav_tab'
import { createParamsStore, routes, useNavigate } from '@/components/location'
import { DatePicker, FilterActions, FilterContainer, FilterGroup, Select } from '@/components/form'
import { mixed, number, object } from 'yup'
import dayjs from 'dayjs'
import { InferPageProps } from '@adonisjs/inertia/types'
import GameStudioMetricsController from '#controllers/dashboard/game_studio_metrics_controller'
import { useServerProp } from '@/components/ssr'
import { graphql } from '@/gql'
import { useGraphql } from '@/components/toolkit-api'
import { isLoading, Loading } from '@/components/loading'
import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import { GameStudioMetricAttributesFragment } from '@/graphql'
import { Box, Link } from '@mui/material'
import { CenterCell, CURRENCY_COLORS, DataGrid } from '@/components/table'
import { formatCurrency, formatNumber } from '@/components/typography'
import { COLORS } from '@/components/color'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import classNames from 'classnames'

const { useParam, useSetParams } = createParamsStore(routes.dash.gameStudioMetrics.index(), {
  parse: object({
    month: mixed<dayjs.Dayjs>()
      .paramDate({ format: 'YYYY-MM', defaultValue: () => dayjs() })
      .required(),
    studioId: number().nullable().optional(),
  }),
  stringify: object({
    month: mixed().stringifyDate('YYYY-MM'),
  }),
})

type PageProps = InferPageProps<GameStudioMetricsController, 'index'>

const getInitialDataQuery = graphql(`
  query GameStudioMetricsIndex_GetInitialData {
    gameStudios {
      collection {
        ...GameStudioAttributes
      }
    }
  }
`)

const getMetricsQuery = graphql(`
  query GameStudioMetricsIndex_GetMetrics($studioId: Int!, $dateFrom: Date!, $dateTo: Date!) {
    metrics: gameStudioMetrics(
      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        ...GameStudioMetricAttributes
      }
    }
  }
`)

export default function Page() {
  const studioIdParam = useParam((p) => p.studioId)
  const monthParam = useParam((p) => p.month)
  const defaultStudio = useServerProp<PageProps, PageProps['defaultStudio']>((s) => s.defaultStudio)
  const setParams = useSetParams()
  const { refresh } = useNavigate()

  const studioId = studioIdParam ?? defaultStudio.id

  const getInitialData = useGraphql(getInitialDataQuery)

  const getMetrics = useGraphql(getMetricsQuery, {
    variables: {
      studioId,
      dateFrom: monthParam.startOf('month').format('YYYY-MM-DD'),
      dateTo: dayjs.min(monthParam.endOf('month'), dayjs()).format('YYYY-MM-DD'),
    },
  })

  useBreadcrumbs(
    [
      {
        label: 'Manager Reports',
        url: routes.dash.revenueReports.index(),
      },
      'Studio Metrics',
    ],
    'dashboard'
  )

  const initialLoading = isLoading([getInitialData.error, getInitialData.loading])
  const metricsLoading = isLoading([getMetrics.error, getMetrics.loading])

  const columns = useMemo<GridColDef<GameStudioMetricAttributesFragment>[]>(() => {
    return [
      {
        headerName: 'Month',
        field: 'month',
        valueGetter: () => monthParam.format('MM/YYYY'),
        headerClassName: 'bg-purple-100',
      },
      {
        headerName: 'Game Title',
        field: 'game.name',
        valueGetter: (_, row) => row.game.name,
        width: 200,
        headerClassName: 'bg-pink-100',
        renderCell: ({ formattedValue, row }) => (
          <CenterCell
            className="text-left"
            component={Link}
            href={routes.dash.gameMetricsNext.index(row.game.id)}
          >
            {formattedValue}
          </CenterCell>
        ),
      },
      {
        headerName: 'Store ID',
        field: 'game.id',
        valueGetter: (_, row) => row.game.id,
        width: 250,
        headerClassName: 'bg-pink-100',
        cellClassName: 'text-left',
      },
      {
        headerName: 'Platform',
        field: 'game.platform',
        valueGetter: (_, row) => row.game.platform,
        valueFormatter: (value: string) => value.toUpperCase(),
        headerClassName: 'bg-pink-100',
        cellClassName: 'text-center',
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
      {
        headerName: 'Number of Downloads',
        field: 'totalInstalls',
        valueFormatter: formatNumber,
        headerClassName: 'bg-yellow-100',
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
      {
        headerName: 'Appsflyer Cost',
        field: 'mmpCostAmount',
        valueFormatter: formatCurrency,
        headerClassName: 'bg-blue-200',
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
      {
        headerName: 'UA Cost',
        field: 'totalAgencyCost',
        valueFormatter: formatCurrency,
        headerClassName: 'bg-blue-200',
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
      {
        headerName: 'Total Revenue',
        field: 'revenue',
        valueFormatter: formatCurrency,
        headerClassName: 'bg-green-200',
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
      {
        headerName: 'Total Profit',
        field: 'profit',
        valueFormatter: formatCurrency,
        valueGetter: (_, row) => row.profit - row.revenue * 0.01,
        headerClassName: 'bg-green-200',
        cellClassName: ({ value }) => classNames({ red: value < 0, green: value > 0 }),
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
      {
        headerName: '1% Revenue',
        field: '1pRevenue',
        valueGetter: (_, row) => row.revenue * 0.01,
        valueFormatter: formatCurrency,
        headerClassName: 'bg-green-200',
        rowSpanValueGetter: (value, row) => [row.game.id, value],
      },
    ]
  }, [monthParam])

  const columnGroupingModel = useMemo<GridColumnGroupingModel | undefined>(() => {
    if (!getInitialData.data) {
      return undefined
    }

    const studios = getInitialData.data.gameStudios.collection
    const studio = studios.find((s) => s.id === studioId)!
    return [
      {
        headerName: '',
        groupId: 'month',
        headerClassName: 'bg-orange-200 studio-name',
        children: [{ field: 'month' }, { field: 'game.name' }],
      },
      {
        headerName: studio.name,
        groupId: studio.id.toString(),
        headerClassName: 'bg-orange-200 studio-name',
        children: [
          { field: 'game.id' },
          { field: 'game.platform' },
          { field: 'totalInstalls' },
          { field: 'mmpCostAmount' },
          { field: 'totalAgencyCost' },
          { field: 'revenue' },
          { field: 'profit' },
          { field: '1pRevenue' },
        ],
      },
    ] as GridColumnGroupingModel
  }, [studioId, getInitialData.data])

  const form = useForm({
    defaultValues: { month: monthParam, studioId: studioId.toString() },
  })

  const [_1, navbarSize] = useLayoutSize('navbar')
  const [_2, breadcrumbsSize] = useLayoutSize('breadcrumbs')
  const [_3, notificationSize] = useLayoutSize('notification')
  const [_4, navTabSize] = useLayoutSize('gameNav')
  const [_5, filterSize] = useLayoutSize('filter')
  const [filterRef, setFilterSize] = useLayoutComponentRegistration('filter')

  const layoutHeight = useLayoutHeight(
    navbarSize,
    breadcrumbsSize,
    notificationSize,
    navTabSize,
    filterSize
  )

  useEffect(() => {
    if (!initialLoading) {
      setFilterSize({
        height: filterRef.current?.clientHeight ?? 0,
      })
    }
  }, [filterRef, initialLoading, setFilterSize])

  return (
    <DashboardLayout>
      <Head title="Studio Metrics" />

      <ManagerReportNavTab />

      <Loading visible={initialLoading}>
        <FilterContainer
          ref={filterRef}
          sx={{ pb: 2 }}
          component="form"
          onSubmit={form.handleSubmit((formData) => {
            console.log(formData)
            setParams({
              month: formData.month,
              studioId: Number(formData.studioId),
            })
          })}
        >
          <FilterGroup label="month">
            <DatePicker
              slotProps={{
                shortcuts: {
                  items: [
                    { label: 'Current month', getValue: () => dayjs() },
                    { label: 'Last month', getValue: () => dayjs().subtract(1, 'month') },
                    { label: '2 months ago', getValue: () => dayjs().subtract(2, 'month') },
                    { label: '3 months ago', getValue: () => dayjs().subtract(3, 'month') },
                  ],
                },
              }}
              format="MM/YYYY"
              views={['month', 'year']}
              defaultValue={monthParam}
              onChange={(v) => {
                form.setValue('month', v!)
              }}
            />
          </FilterGroup>

          <FilterGroup label="Partner">
            <Select
              {...form.register('studioId')}
              defaultValue={studioId.toString()}
              options={
                getInitialData.data?.gameStudios?.collection?.map((s) => ({
                  label: s.name,
                  value: s.id.toString(),
                })) || [{ label: defaultStudio.name, value: defaultStudio.id.toString() }]
              }
            />
          </FilterGroup>

          <FilterActions onReset={() => refresh()} />
        </FilterContainer>

        <Loading visible={metricsLoading}>
          <Box sx={{ height: `calc(100vh - ${layoutHeight}px - 0.5rem)` }}>
            <DataGrid
              columnGroupingModel={columnGroupingModel}
              getRowId={(row) => row.game.id}
              pinnedColumns={{
                left: ['month', 'game.name'],
              }}
              columns={columns}
              rows={getMetrics.data?.metrics?.collection!}
              editable={false}
              actionColumn={false}
              sx={{
                ...COLORS,
                ...CURRENCY_COLORS,
                [`& .studio-name .MuiDataGrid-columnHeaderTitle`]: { py: 0.5 },
                [`& .MuiDataGrid-columnHeaderTitleContainer`]: { py: 1 },
                [`& .text-left`]: { textAlign: 'left!important' },
                [`& .text-center`]: { textAlign: 'center!important' },
              }}
              unstable_rowSpanning
            />
          </Box>
        </Loading>
      </Loading>
    </DashboardLayout>
  )
}
