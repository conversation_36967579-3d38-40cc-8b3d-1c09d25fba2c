import { Box, Button, Checkbox, Divider, Grid, Typography } from '@mui/material'
import {
  axisClasses,
  BarPlot,
  ChartsLegend,
  ChartsTooltip,
  ChartsXAxis,
  ChartsYAxis,
  ChartContainer,
} from '@mui/x-charts'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import { thru } from 'lodash-es'
import { useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { array, InferType, object, string } from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'

import { SubjectPresenter } from '#controllers/dashboard/team_revenue_presenter'
import { SubjectMetric } from '#models/game_metric'
import { formatter } from '#utils/formatter'
import { errorsToMessage, YupCheckbox } from '@/components/form'
import { ConfirmationPopover, usePopoverControl } from '@/components/confirmation'
import { useClientNavigate } from '@/components/ssr'
import { useParams } from '@/components/location'
import { DailySubjectMetricGroup } from '@/components/toolkit-api'

const METRIC_IDS = ['revenue', 'cost', 'profit', 'externalProfit']

const schema = object({
  metricIds: array()
    .of(string().oneOf(METRIC_IDS).required())
    .required()
    .min(1, 'You must select at least 1 metric'),
  subjectIds: array()
    .of(string().required())
    .required()
    .min(1, 'You must select at least 1 member')
    // @ts-ignore
    .when('metricIds', ([metricIds], s) => {
      if (metricIds.length > 1) {
        return s.max(1, 'You can select only 1 member when comparing multiple metrics')
      }
    }),
})

const currencyFormatter = (value: any) => `$${formatter.round(value)}`

export function TeamRevenueChart({
  aggregations,
  dailies,
  subjects,
}: {
  aggregations: SubjectMetric[]
  dailies: DailySubjectMetricGroup[]
  subjects: SubjectPresenter[]
}) {
  const { t } = useTranslation()
  const { clientNavigate } = useClientNavigate()
  const params = useParams<{ metrics: string[] }>()

  const errorPopoverControl = usePopoverControl<{ error: string }>()

  const [filter, setFilter] = useState<InferType<typeof schema>>({
    metricIds: thru(
      Array(params.metrics || [])
        .flat()
        .filter(Boolean),
      (ids) => (ids.length ? ids : METRIC_IDS)
    ),
    subjectIds: subjects.slice(0, 1).map((s) => s.id.toString()),
  })

  useEffect(() => {
    clientNavigate('', { ...params, metrics: filter.metricIds })
  }, [filter])

  const form = useForm<InferType<typeof schema>>({
    resolver: yupResolver(schema),
    defaultValues: filter,
  })

  const { metricIds, subjectIds } = filter

  console.log(metricIds, subjectIds)

  const dataset = useMemo(
    () =>
      dailies.map((d) => {
        return {
          date: d.date,
          metrics: subjectIds.map((subjectId) => {
            return (
              d.metrics.find((m) => m.subject.toString() === subjectId) ||
              ({
                subject: subjectId,
                revenue: 0,
                profit: 0,
                externalProfit: 0,
                cost: 0,
              } as SubjectMetric)
            )
          }),
        }
      }),
    [dailies, subjectIds]
  )

  return (
    <>
      <Grid
        container
        spacing={2}
        component="form"
        onSubmit={form.handleSubmit(
          (data) => {
            setFilter({
              metricIds: [...data.metricIds],
              subjectIds: [...data.subjectIds],
            })
          },
          (errors) => {
            errorPopoverControl.onClick({
              error: errorsToMessage(errors),
            })({ currentTarget: document.querySelector('#submit')! } as any)
          }
        )}
      >
        <Grid size={2}>
          <Button variant="outlined" type="submit" id="submit">
            apply
          </Button>
        </Grid>
        <Grid size={10}>Performance Comparison</Grid>

        <Grid size={2}>
          <Typography>Metric</Typography>
          {METRIC_IDS.map((metricId) => (
            <Box key={metricId}>
              <YupCheckbox
                control={form.control}
                name="metricIds"
                value={metricId}
                render={(field) => {
                  return <Checkbox {...field} />
                }}
              />
              {t(`revenue.${metricId}`)}
            </Box>
          ))}

          <Divider />

          <Typography sx={{ mt: 1 }}>Members</Typography>
          {aggregations.map((a) => (
            <Box key={a.subject}>
              <YupCheckbox
                control={form.control}
                name="subjectIds"
                value={a.subject.toString()}
                render={(field) => {
                  return <Checkbox {...field} />
                }}
              />
              {subjects.find((s) => s.id.toString() === a.subject.toString())?.name}
            </Box>
          ))}
        </Grid>
        <Grid size={10}>
          <ChartContainer
            series={subjectIds.flatMap((subjectId) =>
              metricIds.map((metricId) => ({
                type: 'bar',
                data: dataset.map(
                  (d) =>
                    d.metrics.find((m) => m.subject.toString() === subjectId)![
                      metricId as unknown as keyof SubjectMetric
                    ]
                ),
                label: `${subjects.find((s) => s.id.toString() === subjectId.toString())?.name} ${t(`revenue.${metricId}`)}`,
                valueFormatter: currencyFormatter,
              }))
            )}
            yAxis={[
              {
                scaleType: 'linear',
                valueFormatter: currencyFormatter,
                position: 'left',
              },
            ]}
            xAxis={[
              {
                id: 'date',
                scaleType: 'band',
                data: dataset.map((d) => d.date),
                valueFormatter: (value: any) => dayjs(value).format('MM-DD'),
                position: 'bottom',
              },
            ]}
            height={500}
            sx={{
              [`.${axisClasses.left} .${axisClasses.label}`]: {
                transform: 'translate(-30px, 0)',
              },
            }}
            margin={{ left: 100 }}
          >
            <BarPlot />
            <ChartsLegend />

            <ChartsXAxis labelStyle={{ fontSize: 18 }} />
            <ChartsYAxis label="Performance" />
            <ChartsTooltip />
          </ChartContainer>
        </Grid>
      </Grid>

      <ConfirmationPopover
        control={errorPopoverControl}
        title={errorPopoverControl.data?.error || 'The form contains errors'}
        slotProps={{
          button: {
            onClick: errorPopoverControl.onClose,
          },
        }}
      />
    </>
  )
}
