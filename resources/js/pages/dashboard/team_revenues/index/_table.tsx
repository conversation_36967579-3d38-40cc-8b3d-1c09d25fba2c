import { GridColDef, GridColumnGroup, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import { useCallback, useMemo } from 'react'
import { fromPairs, isNil, sumBy, thru, toPairs } from 'lodash-es'
import { Link } from '@mui/material'
import classNames from 'classnames'

import { SubjectMetric } from '#models/game_metric'
import { CURRENCY_COLORS, DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table/datagrid'
import { PaginationState } from '@/components/pagination'
import { formatter } from '#utils/formatter'
import { SubjectPresenter } from '#controllers/dashboard/team_revenue_presenter'
import { avgBy, maxBy, minBy } from '#utils/math'
import { DailySubjectMetricGroup } from '@/components/toolkit-api'
import { CenterCell } from '@/components/table'

const formatCurrency = (value?: any) => {
  if (isNil(value)) {
    return ''
  }

  return `$${formatter.round(value)}`
}

type ValueGetter = keyof SubjectMetric | ((item: SubjectMetric) => any)

const getMetricValue = (collection: SubjectMetric[], subject: string, key: ValueGetter) => {
  const gameMetric = collection.find((e) => e.subject === subject)
  if (gameMetric) {
    if (typeof key === 'string') {
      return gameMetric?.[key]
    }

    return key(gameMetric)
  }

  return 0
}

export function TeamRevenueTable({
  aggregations,
  pagination,
  subjects,
  aggregation,
  dailyAggregations,
  dailies,
  makeSubjectUrl,
}: {
  aggregations: SubjectMetric[]
  pagination: PaginationState
  subjects: SubjectPresenter[]
  aggregation: SubjectMetric
  dailyAggregations: SubjectMetric[]
  makeSubjectUrl: (subject: any) => string
  dailies: DailySubjectMetricGroup[]
}) {
  const aggregationRow = useMemo<DailySubjectMetricGroup>(() => {
    return { date: 'Summary', metrics: aggregations }
  }, [aggregations])

  const makeMetricFnRow = useCallback(
    (fn: any, hierarchy: string[]): DailySubjectMetricGroup => {
      return {
        hierarchy,
        date: hierarchy[hierarchy.length - 1] as any,
        metrics: aggregations.map((a): SubjectMetric => {
          const metrics = dailies.flatMap((d) => d.metrics.filter((m) => m.storeId === a.storeId))
          return {
            ...fromPairs(
              toPairs(a)
                .map(([k]) => {
                  try {
                    const value = fn(
                      metrics.filter((m) => m.subject === a.subject),
                      k
                    )
                    return [k, value]
                  } catch (err) {
                    return null
                  }
                })
                .filter((v): v is [string, number] => !isNil(v))
            ),
            subject: a.subject,
          } as SubjectMetric
        }),
      }
    },
    [aggregations]
  )

  const rows = useMemo(
    () => [
      aggregationRow,
      makeMetricFnRow(sumBy, ['SUM']),
      makeMetricFnRow(avgBy, ['SUM', 'AVG']),
      makeMetricFnRow(maxBy, ['SUM', 'MAX']),
      makeMetricFnRow(minBy, ['SUM', 'MIN']),
      ...dailies,
    ],
    [dailies, aggregationRow, makeMetricFnRow]
  )

  const makeDailyFnRow = useCallback(
    (fn: any, hierarchy: string[]) => {
      return {
        ...fromPairs(
          toPairs(aggregation)
            .map(([k]) => {
              try {
                const value = fn(dailyAggregations, k)
                console.log(value)
                return [k, value]
              } catch (err) {
                console.log(err)
                return null
              }
            })
            .filter((v): v is [string, number] => !isNil(v))
        ),
        date: hierarchy[hierarchy.length - 1] as any,
        hierarchy,
      } as SubjectMetric
    },
    [dailyAggregations, aggregation]
  )

  const dailyRows = useMemo(
    () => [
      { ...aggregation, date: 'Summary' as any } as SubjectMetric,
      makeDailyFnRow(sumBy, ['SUM']),
      makeDailyFnRow(avgBy, ['SUM', 'AVG']),
      makeDailyFnRow(maxBy, ['SUM', 'MAX']),
      makeDailyFnRow(minBy, ['SUM', 'MIN']),
      ...dailyAggregations,
    ],
    [dailyAggregations, aggregation, makeDailyFnRow]
  )

  const columns = useMemo<GridColDef<DailySubjectMetricGroup>[]>(
    () => [
      {
        field: 'date',
        headerName: 'Date',
        width: 80,
      },
      {
        field: 'day',
        headerName: 'Day',
        width: 50,
        valueFormatter: (_, metric) => formatter.day(metric.date as any),
      },
      {
        field: 'aggregation.spend',
        headerName: 'Spend',
        width: 85,
        valueGetter: (_, row) => dailyRows.find((m) => m.date === (row.date as any))?.cost,
        valueFormatter: formatCurrency,
      },
      {
        field: 'aggregation.revenue',
        headerName: 'Revenue',
        width: 85,
        valueGetter: (_, row) => dailyRows.find((m) => m.date === (row.date as any))?.revenue,
        valueFormatter: formatCurrency,
      },
      {
        field: 'aggregation.grossProfit',
        headerName: 'Profit (Gross)',
        width: 85,
        valueGetter: (_, row) => dailyRows.find((m) => m.date === (row.date as any))?.profit,
        valueFormatter: formatCurrency,
        cellClassName: ({ value }) => classNames({ green: value > 0, red: value < 0 }),
      },
      {
        field: 'aggregation.netProfit',
        headerName: 'Profit (NET)',
        width: 85,
        valueGetter: (_, row) =>
          thru(
            dailyRows.find((m) => m.date === (row.date as any)),
            (a) => a && a.externalProfit + a.internalProfit
          ),
        valueFormatter: formatCurrency,
        cellClassName: ({ value }) => classNames({ green: value > 0, red: value < 0 }),
      },
      {
        field: 'aggregation.externalProfit',
        headerName: 'External',
        width: 85,
        valueGetter: (_, row) =>
          dailyRows.find((m) => m.date === (row.date as any))?.externalProfit,
        valueFormatter: formatCurrency,
        cellClassName: ({ value }) => classNames({ green: value > 0, red: value < 0 }),
      },
      {
        field: 'aggregation.internalProfit',
        headerName: 'Internal',
        width: 85,
        valueGetter: (_, row) =>
          dailyRows.find((m) => m.date === (row.date as any))?.internalProfit,
        valueFormatter: formatCurrency,
        cellClassName: ({ value }) => classNames({ green: value > 0, red: value < 0 }),
      },
      ...aggregations.flatMap((a): GridColDef<DailySubjectMetricGroup>[] => {
        return [
          {
            field: `${a.subject}.spend`,
            headerName: 'Spend',
            width: 85,
            valueGetter: (_, row) => getMetricValue(row.metrics, a.subject, 'cost'),
            valueFormatter: formatCurrency,
          },
          {
            field: `${a.subject}.revenue`,
            headerName: 'Revenue',
            width: 85,
            valueGetter: (_, row) => getMetricValue(row.metrics, a.subject, 'revenue'),
            valueFormatter: formatCurrency,
          },
          {
            field: `${a.subject}.profit`,
            headerName: 'Profit',
            width: 85,
            valueGetter: (_, row) => getMetricValue(row.metrics, a.subject, 'profit'),
            valueFormatter: formatCurrency,
            cellClassName: ({ value }) => classNames({ green: value > 0, red: value < 0 }),
          },
          {
            field: `${a.subject}.externalProfit`,
            headerName: 'PubProfit',
            width: 85,
            valueGetter: (_, row) => getMetricValue(row.metrics, a.subject, 'externalProfit'),
            valueFormatter: formatCurrency,
          },
        ]
      }),
    ],
    [aggregations, dailyRows]
  )

  const columnGroupingModel = useMemo<GridColumnGroupingModel>(
    () => [
      {
        groupId: 'day',
        headerName: 'Calendar',
        children: [
          {
            field: 'date',
          },
          {
            field: 'day',
          },
        ],
      },
      {
        groupId: 'aggregation',
        headerName: 'Total',
        children: [
          {
            field: 'aggregation.spend',
          },
          {
            field: 'aggregation.revenue',
          },
          {
            field: 'aggregation.grossProfit',
          },
          {
            field: 'aggregation.netProfit',
          },
          {
            field: 'aggregation.externalProfit',
          },
          {
            field: 'aggregation.internalProfit',
          },
        ],
      },
      ...aggregations.map((a): GridColumnGroup => {
        return {
          groupId: a.subject.toString(),
          headerName: subjects.find((s) => s.id === a.subject.toString())?.name,
          headerAlign: 'center',
          renderHeaderGroup: ({ headerName }) => {
            return (
              <CenterCell>
                <Link
                  sx={{ alignItems: 'center', display: 'flex', justifyContent: 'center', py: 2 }}
                  className="MuiDataGrid-columnHeaderTitle"
                  href={makeSubjectUrl(a.subject)}
                >
                  {headerName}
                </Link>
              </CenterCell>
            )
          },
          children: [
            {
              field: `${a.subject}.spend`,
            },
            {
              field: `${a.subject}.revenue`,
            },
            {
              field: `${a.subject}.profit`,
            },
            {
              field: `${a.subject}.externalProfit`,
            },
          ],
        }
      }),
    ],
    [aggregations, subjects]
  )

  return (
    <DataGrid
      treeData
      getTreeDataPath={(row) => row.hierarchy || [row.date.toString()]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      editable={false}
      getRowId={(r) => r.date as any}
      actionColumn={false}
      rows={rows}
      columns={columns}
      columnGroupingModel={columnGroupingModel}
      pagination={pagination}
      initialState={{
        pinnedColumns: {
          left: ['date', 'day'],
        },
      }}
      pinnedRows={{
        top: [rows[0]],
      }}
      sx={{
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
        },
        ...CURRENCY_COLORS,
      }}
    />
  )
}
