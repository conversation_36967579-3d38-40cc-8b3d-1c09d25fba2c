import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { array, InferType, mixed, number, object, string } from 'yup'
import dayjs from 'dayjs'
import { Box, Stack } from '@mui/material'
import { useTranslation } from 'react-i18next'

import { render } from '@/react.jsx'
import { DashboardLayout, NavTabs } from '@/components/layout'
import { createParamsStore, makeUrl, routes } from '@/components/location'
import { useListTeam, useListTeamRevenue } from '@/components/toolkit-api'
import { Loading } from '@/components/loading'
import {
  ChartViewButton,
  DateRange,
  errorsToMessage,
  FilterActions,
  FilterContainer,
  FilterGroup,
  Select,
  TableViewButton,
  useSortOptions,
} from '@/components/form'
import { useConfigMaps } from '@/components/configmaps'
import { ConfirmationPopover, usePopoverControl } from '@/components/confirmation'

import { TeamRevenueTable } from './index/_table'
import { TeamRevenueChart } from './index/_chart'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useMemo } from 'react'

render(PageWithLayout)

const { useParam, useSetParams, useParams, usePagination } = createParamsStore(
  routes.dash.teamRevenues.index(),
  {
    parse: object({
      teamId: number().nullable().optional(),
      roleId: string().optional(),
      from: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs().subtract(1, 'month') }),
      to: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs() }),
      groupBy: string().oneOf(['user', 'team']).optional().default('team'),
      direction: string().oneOf(['asc', 'desc']).optional().default('desc'),
      view: string().oneOf(['table', 'chart']).optional().default('table'),
      page: number().default(1),
      perPage: number().default(30),
    }),
  }
)

function PageWithLayout() {
  return (
    <DashboardLayout>
      <NavTabs
        items={[
          {
            href: routes.dash.gameRevenues.index(),
            label: 'Game Revenue',
          },
          {
            href: routes.dash.teamRevenues.index(),
            label: 'Team Revenue',
          },
        ]}
        sx={{ mb: 1 }}
      />

      <Page />
    </DashboardLayout>
  )
}

const formSchema = object({
  teamId: number()
    .transform((v) => v || undefined)
    .optional(),
  roleId: string().required(),
  dateRange: array()
    .of(mixed<dayjs.Dayjs>().nullable())
    .required()
    .when('view', ([view], schema) => {
      if (view === 'chart') {
        return schema.test('tooFar', "Date range can't be more than 14 days", (value) => {
          const [from, to] = value as [dayjs.Dayjs | null, dayjs.Dayjs | null]
          if (!from || !to) {
            return true
          }

          return to.diff(from, 'day') <= 14
        })
      }

      return schema
    }),
  groupBy: string().oneOf(['team', 'user']).required(),
  orderDirection: string().oneOf(['asc', 'desc']).required(),
  view: string().oneOf(['table', 'chart']).required(),
})

type FormData = InferType<typeof formSchema>

function Page() {
  const { t } = useTranslation()

  const errorPopoverControl = usePopoverControl<{ error: string }>()

  const [[roleConfigMapCollection], getConfigMaps] = useConfigMap(
    useMemo(() => ['cmap.dash.role'], [])
  )

  const params = useParams()
  const teamIdParam = useParam((p) => p.teamId)
  const roleIdParam = useParam((p) => p.roleId)
  const fromParam = useParam((p) => p.from)
  const toParam = useParam((p) => p.to)
  const groupByParam = useParam((p) => p.groupBy)
  const directionParam = useParam((p) => p.direction)
  const viewParam = useParam((p) => p.view)
  const pageParam = useParam((p) => p.page)
  const perPageParam = useParam((p) => p.perPage)
  const setParams = useSetParams()

  const { formState, handleSubmit, setValue, register, watch } = useForm<FormData>({
    defaultValues: {
      teamId: teamIdParam,
      roleId: roleIdParam,
      dateRange: [fromParam, toParam],
      groupBy: groupByParam,
      orderDirection: directionParam,
      view: viewParam,
    } as any,
    resolver: yupResolver(formSchema),
  })

  const listTeamRevenue = useListTeamRevenue(
    {
      teamId: teamIdParam,
      roleId: roleIdParam,
      from: fromParam.format('YYYY-MM-DD'),
      to: toParam.format('YYYY-MM-DD'),
      groupBy: groupByParam,
      orderDirection: directionParam,
      page: pageParam,
      perPage: perPageParam,
    } as any,
    {
      enabled: !formState.isDirty && formState.isValid,
    }
  )

  const pagination = usePagination(listTeamRevenue.data?.meta)

  const listTeam = useListTeam({})

  const roleId = watch('roleId')

  const sortOptions = useSortOptions()

  const onViewChange = (viewType: 'table' | 'chart') => {
    return () => {
      setParams({ view: viewType })
    }
  }

  if (listTeamRevenue.isLoading || getConfigMaps.loading || listTeam.isLoading) {
    return <Loading visible />
  }

  const roleOptions = roleConfigMapCollection!.map((role) => ({
    label: `${role.group} ${role.name}`,
    value: role.id,
  }))

  const teamOptions = listTeam
    .data!.filter((team) => !roleId || team.roleId === roleId)
    .map((team) => ({
      label: team.name,
      value: team.id.toString(),
    }))

  const makeSubjectUrl = (subject: any) => {
    if (groupByParam === 'team') {
      return makeUrl(routes.dash.teamRevenues.index(), {
        query: { ...params, groupBy: 'user', teamId: subject },
      })
    } else {
      return makeUrl(routes.dash.gameRevenues.index(), {
        query: {
          ...params,
          member: subject,
        },
      })
    }
  }

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between">
        <FilterContainer
          sx={{
            mb: 2,
            gap: 2,
          }}
          spacing={0}
          direction={{
            xs: 'column',
            sm: 'row',
          }}
          flexWrap="wrap"
          alignItems={{
            xs: 'flex-start',
            sm: 'flex-end',
          }}
          component="form"
          onSubmit={handleSubmit(
            (formData) => {
              setParams({
                teamId: formData.teamId,
                roleId: formData.roleId,
                from: formData.dateRange[0]!,
                to: formData.dateRange[1]!,
                groupBy: formData.groupBy,
                direction: formData.orderDirection,
                view: formData.view,
              })
            },
            (errors) => {
              errorPopoverControl.onClick({
                error: errorsToMessage(errors),
              })({ currentTarget: document.querySelector('#revenues_filter_submit')! } as any)
            }
          )}
        >
          <FilterGroup
            label={t('team.filter.datePlaceholder')}
            sx={{
              width: {
                xs: '100%',
                sm: '350px',
              },
            }}
          >
            <DateRange
              localeText={{ start: 'Start Date', end: 'End Date' }}
              defaultValue={[fromParam, toParam]}
              format="YYYY-MM-DD"
              onChange={(values) => setValue('dateRange', values)}
            />
          </FilterGroup>

          <FilterGroup
            sx={{
              width: {
                xs: '100%',
                sm: '100px',
              },
            }}
            label="sort"
          >
            <Select
              defaultValue={directionParam}
              {...register('orderDirection')}
              options={sortOptions}
              hint={t('team.filter.sortPlaceholder')}
            />
          </FilterGroup>

          <FilterGroup
            sx={{
              width: {
                xs: '100%',
                sm: '100px',
              },
            }}
            label="role"
          >
            <Select
              defaultValue={roleIdParam || ''}
              {...register('roleId')}
              options={roleOptions}
              hint={t('team.filter.rolePlaceholder')}
            />
          </FilterGroup>

          <FilterGroup
            sx={{
              width: {
                xs: '100%',
                sm: '100px',
              },
            }}
            label="group by"
          >
            <Select
              defaultValue={groupByParam || ''}
              {...register('groupBy')}
              options={[
                { label: 'Team', value: 'team' },
                { label: 'Member', value: 'user' },
              ]}
              hint="Select one"
            />
          </FilterGroup>

          <FilterGroup
            sx={{
              width: {
                xs: '100%',
                sm: '100px',
              },
            }}
            label="team"
          >
            <Select
              defaultValue={teamIdParam || ''}
              {...register('teamId')}
              options={teamOptions}
              hint="Select team"
              allowEmpty
            />
          </FilterGroup>

          <FilterActions
            slotProps={{
              reset: {
                onClick: () => {
                  setParams({})
                },
              },
              submit: {
                id: 'revenues_filter_submit',
              },
            }}
          />
        </FilterContainer>

        {listTeamRevenue.data && (
          <Stack direction="row">
            <Box>
              <TableViewButton
                active={viewParam === 'table'}
                onClick={onViewChange('table')}
                disabled={!formState.isValid}
              />
            </Box>
            <Box>
              <ChartViewButton
                active={viewParam === 'chart'}
                onClick={onViewChange('chart')}
                disabled={!formState.isValid}
              />
            </Box>
          </Stack>
        )}
      </Stack>

      {listTeamRevenue.data &&
        (viewParam === 'table' ? (
          <Box sx={{ height: `calc(100vh - 300px)`, minHeight: '500px' }}>
            <TeamRevenueTable
              aggregations={listTeamRevenue.data.data}
              pagination={pagination}
              subjects={listTeamRevenue.data.meta.subjects}
              aggregation={listTeamRevenue.data.meta.aggregation.data}
              dailyAggregations={listTeamRevenue.data.meta.aggregation.dailies}
              dailies={listTeamRevenue.data.meta.dailies}
              makeSubjectUrl={makeSubjectUrl}
            />
          </Box>
        ) : (
          <TeamRevenueChart
            aggregations={listTeamRevenue.data.data}
            dailies={listTeamRevenue.data.meta.dailies}
            subjects={listTeamRevenue.data.meta.subjects}
          />
        ))}

      <ConfirmationPopover
        control={errorPopoverControl}
        title={errorPopoverControl.data?.error || 'The filter form contains errors'}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        slotProps={{
          button: {
            onClick: errorPopoverControl.onClose,
          },
        }}
      />
    </Box>
  )
}
