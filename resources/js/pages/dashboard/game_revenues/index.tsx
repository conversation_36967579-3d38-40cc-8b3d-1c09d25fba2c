import { Box } from '@mui/material'
import dayjs from 'dayjs'
import { useMemo } from 'react'

import { DashboardLayout, NavTabs, useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import { Loading } from '@/components/loading'
import { useLayoutSSRData } from '@/components/ssr'
import { useQueryToast } from '@/components/toast'
import { DateRange, FilterActions, FilterContainer, FilterGroup, Select } from '@/components/form'
import { createParamsStore, routes, useNavigate } from '@/components/location'
import {
  useGetProfile,
  useGetTeam,
  useGraphql,
  useListGameRevenue,
  useListTeam,
  useListUser,
} from '@/components/toolkit-api'
import { useForm } from 'react-hook-form'
import { object, string, number, mixed } from 'yup'

import { GameRevenueTable } from './index/_table'
import { graphql } from '@/gql'

const { useParam, useSetParams, usePagination } = createParamsStore(
  routes.dash.gameRevenues.index(),
  {
    parse: object({
      team: string().optional().default(''),
      member: string().optional().default(''),
      sort: string().default('weekly_cost'),
      from: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs().subtract(1, 'month') }),
      to: mixed<dayjs.Dayjs>().paramDate({ defaultValue: () => dayjs() }),
      direction: string().oneOf(['asc', 'desc']).default('desc'),
      page: number().default(1),
      perPage: number().default(30),
    }),
    stringify: object({
      from: mixed().stringifyDate(),
      to: mixed().stringifyDate(),
    }),
  }
)

render(Page)

graphql(`
  fragment GameRevenueIndex_UserAttributes on User {
    email
    fullName
  }
`)

const getUsersQuery = graphql(`
  query GameRevenuesIndex_GetUsers {
    users(where: {}, offset: { page: 1, perPage: 1000 }) {
      collection {
        ...GameRevenueIndex_UserAttributes
      }
    }
  }
`)

function Page() {
  const { canViewTeam } = useLayoutSSRData()

  const getUsers = useGraphql(getUsersQuery)
  const listTeamQuery = useListTeam({
    enabled: Boolean(canViewTeam),
  })

  const isLoading = getUsers.loading || listTeamQuery.isLoading

  const { refresh } = useNavigate()

  const teamParam = useParam((p) => p.team)
  const memberParam = useParam((p) => p.member)
  const sortParam = useParam((p) => p.sort)
  const fromParam = useParam((p) => p.from)
  const toParam = useParam((p) => p.to)
  const directionParam = useParam((p) => p.direction)
  const pageParam = useParam((p) => p.page)
  const setParams = useSetParams()

  console.log('memberParam', memberParam)

  const {
    handleSubmit,
    setValue,
    register,
    formState: { defaultValues },
  } = useForm({
    defaultValues: {
      memberEmail: memberParam,
      teamId: teamParam,
      dateRange: [fromParam, toParam],
      orderDirection: directionParam,
      sort: sortParam,
    },
  })

  const dateFrom = fromParam ? dayjs(fromParam) : dayjs().subtract(3, 'month')
  const dateTo = toParam ? dayjs(toParam) : dayjs()

  const listRevenueQuery = useListGameRevenue({
    page: pageParam,
    from: dateFrom.format('YYYY-MM-DD'),
    to: dateTo.format('YYYY-MM-DD'),
    direction: directionParam,
    teamId: teamParam,
    memberEmail: memberParam,
    sort: sortParam,
    date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  })

  const pagination = usePagination(listRevenueQuery.data?.meta)

  useQueryToast(listRevenueQuery)

  const getProfile = useGetProfile()

  const getTeam = useGetTeam(getProfile?.data?.profile?.teamLead?.id!, {
    enabled: Boolean(getProfile.isSuccess && getProfile.data?.profile?.teamLead?.id),
  })

  useBreadcrumbs([{ label: 'Game Revenue', url: '/dash/game_revenues' }], 'dashboard')

  const memberOptions = useMemo(() => {
    if (getProfile.data?.permission.isManager) {
      return (
        getUsers.data?.users?.collection?.map((u) => ({
          label: u.fullName,
          value: u.email,
        })) || []
      )
    }

    return getTeam.data
      ? getTeam
          .data!.members!.map((m) => ({
            label: m.fullName || m.email,
            value: m.email,
          }))
          .concat({
            label: getTeam.data!.leader!.fullName || getTeam.data?.leader!.email,
            value: getTeam.data!.leader!.email,
          })
      : []
  }, [getProfile.data, getUsers.data, getTeam.data])

  if (listRevenueQuery.isLoading || getProfile.isLoading || getTeam.isLoading) {
    return <Loading visible />
  }

  return (
    <DashboardLayout>
      <NavTabs
        items={[
          {
            href: routes.dash.gameRevenues.index(),
            label: 'Game Revenue',
          },
          {
            href: routes.dash.teamRevenues.index(),
            label: 'Team Revenue',
          },
        ]}
        sx={{ mb: 1 }}
      />

      <FilterContainer
        sx={{
          mb: 2,
          gap: 2,
        }}
        spacing={0}
        direction={{
          xs: 'column',
          sm: 'row',
        }}
        alignItems={{
          xs: 'flex-start',
          sm: 'flex-end',
        }}
        component="form"
        onSubmit={handleSubmit((formData) => {
          setParams({
            from: formData.dateRange[0]!,
            to: formData.dateRange[1]!,
            direction: formData.orderDirection,
            member: formData.memberEmail,
            team: formData.teamId,
            sort: formData.sort,
          })
        })}
      >
        <FilterGroup
          label="select date"
          sx={{
            width: {
              xs: '100%',
              sm: '350px',
            },
          }}
        >
          <DateRange
            localeText={{ start: 'Start Date', end: 'End Date' }}
            defaultValue={defaultValues!.dateRange! as any}
            format="YYYY-MM-DD"
            onChange={(values) => setValue('dateRange', [values[0]!, values[1]!])}
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '150px',
            },
          }}
          label="sort by"
        >
          <Select
            {...register('sort')}
            defaultValue={defaultValues?.sort}
            options={[
              { label: 'Total Profit', value: 'profit' },
              { label: 'Daily Spend', value: 'daily_cost' },
              { label: 'Daily Profit', value: 'daily_profit' },
              { label: 'Weekly Spend', value: 'weekly_cost' },
              { label: 'Weekly Profit', value: 'weekly_profit' },
            ]}
            hint="Sort Criteria"
          />
        </FilterGroup>

        <FilterGroup
          sx={{
            width: {
              xs: '100%',
              sm: '100px',
            },
          }}
          label="direction"
        >
          <Select
            {...register('orderDirection')}
            defaultValue={defaultValues?.orderDirection}
            options={[
              { label: 'ASC', value: 'asc' },
              { label: 'DESC', value: 'desc' },
            ]}
            hint="Sort Direction"
          />
        </FilterGroup>

        {canViewTeam && (
          <FilterGroup
            sx={{
              width: {
                xs: '100%',
                sm: '100px',
              },
            }}
            label="team"
          >
            <Select
              defaultValue={teamParam}
              {...register('teamId')}
              options={
                listTeamQuery.data?.map((t) => ({
                  label: t.name,
                  value: t.id.toString(),
                })) || []
              }
              hint="Select team"
              allowEmpty
            />
          </FilterGroup>
        )}

        {(canViewTeam || getProfile.data!.permission.isTeamLeader) && (
          <FilterGroup
            sx={{
              width: {
                xs: '100%',
                sm: '100px',
              },
            }}
            label="member"
          >
            <Select
              {...register('memberEmail')}
              defaultValue={defaultValues?.memberEmail}
              options={memberOptions}
              hint="Select member"
              allowEmpty
            />
          </FilterGroup>
        )}

        <FilterActions
          onReset={() => {
            refresh()
          }}
        />
      </FilterContainer>

      <Loading visible={isLoading}>
        <Box sx={{ height: `calc(100vh - 300px)`, minHeight: '500px' }}>
          <GameRevenueTable
            revenues={listRevenueQuery.data!.data}
            dailies={listRevenueQuery.data!.meta.dailies}
            pagination={pagination}
            dailyAggregations={listRevenueQuery.data!.meta.aggregation.dailies}
            aggregation={listRevenueQuery.data!.meta.aggregation.data}
          />
        </Box>
      </Loading>
    </DashboardLayout>
  )
}
