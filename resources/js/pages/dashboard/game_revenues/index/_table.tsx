import { GridColDef, GridColumnGroup, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import classNames from 'classnames'
import { DateTime } from 'luxon'
import { Link } from '@mui/material'
import { fromPairs, isNil, sumBy, toPairs } from 'lodash-es'
import { useCallback } from 'react'

import { avgBy, maxBy, minBy } from '#utils/math'
import GameMetric from '#models/game_metric'
import { CURRENCY_COLORS, DataGrid, TREE_GROUPING_COL_DEF } from '@/components/table/datagrid'
import { formatter } from '#utils/formatter'
import { PaginationState } from '@/components/pagination'
import { GamePlatform } from '#config/enums'
import { Android, Ios } from '@/components/platform'
import { routes } from '@/components/location'
import { DailyMetricGroup } from '@/components/toolkit-api'
import { CenterCell } from '@/components/table'

export const AGGREGATION_ID = 'Summary' as unknown as DateTime

const isAggregation = (row: DailyMetricGroup) =>
  [AGGREGATION_ID, 'SUM', 'AVG', 'MIN', 'MAX'].includes(row.date as any)

const formatCurrency = (value?: any) => {
  if (isNil(value)) {
    return ''
  }

  return `$${formatter.round(value)}`
}

type ValueGetter = keyof GameMetric | ((item: GameMetric) => any)

const getMetricValue = (collection: GameMetric[], storeId: string, key: ValueGetter) => {
  const gameMetric = collection.find((e) => e.storeId === storeId)
  if (gameMetric) {
    if (typeof key === 'string') {
      return gameMetric?.[key]
    }

    return key(gameMetric)
  }

  return 0
}

export function GameRevenueTable({
  revenues,
  dailies,
  pagination,
  dailyAggregations,
  aggregation,
}: {
  revenues: GameMetric[]
  aggregation: GameMetric
  dailies: DailyMetricGroup[]
  dailyAggregations: GameMetric[]
  pagination: PaginationState
}) {
  const makeAggregationFnRow = useCallback(
    (collection: any[], fn: any, hierarchy: string[]) => {
      return {
        ...fromPairs(
          toPairs(aggregation)
            .map(([key]) => {
              try {
                return [key, fn(collection, key)]
              } catch (err) {
                return null
              }
            })
            .filter((e): e is [string, any] => !!e)
        ),
        date: hierarchy[hierarchy.length - 1] as unknown as any,
      } as GameMetric
    },
    [dailyAggregations, aggregation]
  )

  const aggregationRow = {
    date: AGGREGATION_ID as unknown as any,
    metrics: revenues,
  } as DailyMetricGroup

  const makeGameAggregationRow = useCallback(
    (fn: any, hierarchy: string[]) => {
      return {
        date: hierarchy[hierarchy.length - 1] as unknown as any,
        metrics: aggregationRow.metrics.map((metric) => {
          const gameMetrics = dailies.flatMap((daily) =>
            daily.metrics.filter((r) => r.storeId === metric.storeId)
          )
          return {
            ...fromPairs(
              toPairs(metric)
                .map(([key]) => {
                  try {
                    return [key, fn(gameMetrics, key)]
                  } catch (err) {
                    return null
                  }
                })
                .filter((e): e is [string, any] => !!e)
            ),
            storeId: metric.storeId,
          } as GameMetric
        }),
        hierarchy,
      } as DailyMetricGroup
    },
    [aggregationRow, dailies]
  )

  const rows = [
    aggregationRow,
    makeGameAggregationRow(sumBy, ['SUM']),
    makeGameAggregationRow(avgBy, ['SUM', 'AVG']),
    makeGameAggregationRow(maxBy, ['SUM', 'MAX']),
    makeGameAggregationRow(minBy, ['SUM', 'MIN']),
    ...dailies,
  ]

  const aggregations = [
    { ...aggregation, date: AGGREGATION_ID as unknown as any } as GameMetric,
    makeAggregationFnRow(dailyAggregations, sumBy, ['SUM']),
    makeAggregationFnRow(dailyAggregations, avgBy, ['SUM', 'AVG']),
    makeAggregationFnRow(dailyAggregations, maxBy, ['SUM', 'MAX']),
    makeAggregationFnRow(dailyAggregations, minBy, ['SUM', 'MIN']),
    ...dailyAggregations,
  ]

  const columns: GridColDef<DailyMetricGroup>[] = [
    {
      field: 'date',
      headerName: 'Date',
      width: 80,
      cellClassName: ({ row }) => classNames({ 'font-bold': isAggregation(row) }),
    },
    {
      field: 'day',
      headerName: 'Day',
      width: 50,
      valueFormatter: (_, metric) => formatter.day(metric.date as any),
      sortable: false,
    },
    {
      field: 'spend',
      headerName: 'Spend',
      width: 85,
      valueGetter: (_, row) => aggregations.find((a) => (a.date as any) === row.date)?.cost || 0,
      valueFormatter: formatCurrency,
    },
    {
      field: 'revenue',
      headerName: 'Revenue',
      width: 85,
      valueGetter: (_, row) => aggregations.find((a) => (a.date as any) === row.date)?.revenue || 0,
      valueFormatter: formatCurrency,
    },
    {
      field: 'profit',
      headerName: 'Profit (Gross)',
      valueGetter: (_, row) => {
        return aggregations.find((a) => (a.date as any) === row.date)?.profit || 0
      },
      valueFormatter: formatCurrency,
      cellClassName: ({ value }) => classNames({ red: value < 0, green: value > 0 }),
    },
    {
      field: 'netProfit',
      headerName: 'Profit (NET)',
      valueGetter: (_, row) => {
        const agg = aggregations.find((a) => (a.date as any) === row.date)
        return agg ? agg.externalProfit + agg.internalProfit : 0
      },
      valueFormatter: formatCurrency,
      cellClassName: ({ value }) => classNames({ red: value < 0, green: value > 0 }),
    },
    {
      field: 'externalProfit',
      width: 85,
      valueGetter: (_, row) =>
        aggregations.find((a) => (a.date as any) === row.date)?.externalProfit || 0,
      valueFormatter: formatCurrency,
      headerName: 'External',
      cellClassName: ({ value }) => classNames({ red: value < 0, green: value > 0 }),
    },
    {
      field: 'internalProfit',
      width: 85,
      valueGetter: (_, row) =>
        aggregations.find((a) => (a.date as any) === row.date)?.internalProfit || 0,
      valueFormatter: formatCurrency,
      headerName: 'Internal',
      cellClassName: ({ value }) => classNames({ red: value < 0, green: value > 0 }),
    },
    {
      field: 'totalInstalls',
      width: 85,
      valueGetter: (_, row) =>
        aggregations.find((a) => (a.date as any) === row.date)?.totalInstalls || 0,
      valueFormatter: (value) => formatter.round(value),
      headerName: 'Total Installs',
    },
    ...revenues.flatMap((r): GridColDef<DailyMetricGroup>[] => {
      return [
        {
          field: `${r.storeId}.spend`,
          headerName: 'Spend',
          width: 85,
          valueGetter: (_, row) => getMetricValue(row.metrics, r.storeId, 'cost'),
          valueFormatter: formatCurrency,
        },
        {
          field: `${r.storeId}.revenue`,
          headerName: 'Revenue',
          width: 85,
          valueGetter: (_, row) => getMetricValue(row.metrics, r.storeId, 'revenue'),
          valueFormatter: formatCurrency,
        },
        {
          field: `${r.storeId}.profit`,
          headerName: 'Profit',
          width: 85,
          valueGetter: (_, row) => getMetricValue(row.metrics, r.storeId, 'profit'),
          valueFormatter: formatCurrency,
          cellClassName: ({ value }) => classNames({ red: value < 0, green: value > 0 }),
        },
        ...(r.game.isInhouse
          ? []
          : ([
              {
                field: `${r.storeId}.externalProfit`,
                headerName: 'PubProfit',
                width: 85,
                valueGetter: (_, row) =>
                  isAggregation(row)
                    ? r.externalProfit
                    : getMetricValue(row.metrics, r.storeId, 'externalProfit'),
                valueFormatter: (value) => `$${formatter.round(value)}`,
              },
            ] as GridColDef<DailyMetricGroup>[])),
        {
          field: `${r.storeId}.totalInstalls`,
          headerName: 'Total Installs',
          width: 85,
          valueGetter: (_, row) => getMetricValue(row.metrics, r.storeId, 'totalInstalls'),
          valueFormatter: (value) => formatter.round(value),
        },
      ]
    }),
  ]

  const columnGroupingModel: GridColumnGroupingModel = [
    {
      groupId: 'aggregation',
      headerName: '',
      children: [
        {
          field: 'spend',
        },
        {
          field: 'revenue',
        },
        {
          field: 'profit',
        },
        {
          field: 'netProfit',
        },
        {
          field: 'externalProfit',
        },
        {
          field: 'internalProfit',
        },
        {
          field: 'totalInstalls',
        },
      ],
    },
    ...revenues.map((r) => {
      return {
        groupId: r.storeId,
        headerName: r.game.name,
        headerAlign: 'center',
        renderHeaderGroup: ({ headerName }) => (
          <CenterCell>
            <Link
              sx={{ alignItems: 'center', display: 'flex', justifyContent: 'center' }}
              className="MuiDataGrid-columnHeaderTitle"
              href={routes.dash.gameMetrics.index(r.game.storeId)}
            >
              <div>{r.game.platform === GamePlatform.Android ? <Android /> : <Ios />}</div>
              <span>{headerName}</span>
            </Link>
          </CenterCell>
        ),
        children: [
          {
            field: `${r.storeId}.spend`,
          },
          {
            field: `${r.storeId}.revenue`,
          },
          {
            field: `${r.storeId}.profit`,
          },
        ]
          .concat(
            r.game.isInhouse
              ? []
              : [
                  {
                    field: `${r.storeId}.externalProfit`,
                  },
                ]
          )
          .concat([
            {
              field: `${r.storeId}.totalInstalls`,
            },
          ]),
      } as GridColumnGroup
    }),
  ]

  return (
    <DataGrid
      treeData
      getTreeDataPath={(row) => row.hierarchy || [row.date.toString()]}
      groupingColDef={TREE_GROUPING_COL_DEF}
      editable={false}
      getRowId={(r) => r.date as any}
      actionColumn={false}
      rows={rows}
      columns={columns}
      columnGroupingModel={columnGroupingModel}
      pagination={pagination}
      pinnedRows={{
        top: [aggregationRow],
      }}
      disableColumnSorting={false}
      initialState={{
        pinnedColumns: {
          left: ['date', 'day'],
          right: ['actions'],
        },
        sorting: {
          sortModel: [
            {
              field: 'date',
              sort: 'desc',
            },
          ],
        },
      }}
      sx={{
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
        },
        ...CURRENCY_COLORS,
      }}
    />
  )
}
