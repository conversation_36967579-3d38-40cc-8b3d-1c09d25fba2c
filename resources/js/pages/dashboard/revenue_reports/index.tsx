import { Box } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

import { DashboardLayout } from '@/components/layout'
import { toolkitApi } from '@/components/toolkit-api'
import { render } from '@/react'
import { useQueryToast } from '@/components/toast'

import { RevenueReportTable } from './index/_table'
import { ManagerReportNavTab } from '../_components/_manager_report_nav_tab'

render(PageWithLayout)

function PageWithLayout() {
  return (
    <DashboardLayout>
      <ManagerReportNavTab />
      <Page />
    </DashboardLayout>
  )
}

function Page() {
  const listRevenueReportQuery = useQuery({
    queryKey: ['dashboard', 'revenue_reports', 'list'],
    queryFn: async () => {
      const response = await toolkitApi.listRevenueReport({})
      return response.data
    },
  })

  useQueryToast(listRevenueReportQuery)

  const reports = listRevenueReportQuery.data ?? []

  return (
    <Box sx={{ height: `calc(100vh - 160px)` }}>
      <RevenueReportTable reports={reports} />
    </Box>
  )
}
