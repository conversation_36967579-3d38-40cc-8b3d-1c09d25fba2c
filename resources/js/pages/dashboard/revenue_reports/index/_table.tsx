import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro'
import dayjs from 'dayjs'
import { orderBy, toPairs, groupBy, sumBy } from 'lodash-es'

import GameMetric from '#models/game_metric'
import { CURRENCY_COLORS, DataGrid } from '@/components/table/datagrid'
import { formatter } from '#utils/formatter'
import { safeDivide } from '#utils/math'

const numberOfDaysInMonth = (date: any) => {
  const day = dayjs(date)
  const currentDate = dayjs()
  const startDate = dayjs.min(day.startOf('month'), currentDate)
  const endDate = dayjs.min(day.endOf('month'), currentDate)
  return endDate.diff(startDate, 'day') + 1
}

const numberOfDaysInQuarter = (date: any) => {
  const day = dayjs(date)
  const currentDate = dayjs()
  const startDate = dayjs.min(day.startOf('quarter'), currentDate)
  const endDate = dayjs.min(day.endOf('quarter'), currentDate)

  return endDate.diff(startDate, 'day') + 1
}

const numberOfDaysInYear = (date: any) => {
  const day = dayjs(date)
  const currentDate = dayjs()
  const startDate = dayjs.min(day.startOf('year'), currentDate)
  const endDate = dayjs.min(day.endOf('year'), currentDate)
  return endDate.diff(startDate, 'day') + 1
}

const numberOfDays = (row: GameMetric) => {
  if (row.id && row.id < -100_000) {
    return numberOfDaysInQuarter(row.date)
  }

  if (row.id && row.id < 0) {
    return numberOfDaysInYear(row.date)
  }

  return numberOfDaysInMonth(row.date)
}

const columns: GridColDef<GameMetric>[] = [
  {
    field: 'date',
    headerName: 'Month',
    width: 90,
    valueFormatter: (value, row) => {
      const date = dayjs(value, 'YYYY-MM-DD')

      if (row.id && row.id < -100_000) {
        return `Q${date.quarter()} ${date.year()}`
      }

      if (row.id && row.id < 0) {
        return `SUM ${date.year()}`
      }

      return date.format('YYYY/MM')
    },
  },
  {
    field: 'cost',
    headerName: 'Spend',
    width: 120,
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'revenue',
    headerName: 'Revenue',
    width: 120,
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'profitNet',
    headerName: 'MKT Profit (Net)',
    width: 120,
    valueGetter: (_, row) => row.internalProfit + row.externalProfit,
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'dailySpend',
    headerName: 'Daily Spend',
    width: 100,
    valueGetter: (_, row) => row.cost / numberOfDays(row),
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'dailyRevenueGross',
    headerName: 'Daily Rev (gross)',
    width: 100,
    valueGetter: (_, row) => row.revenue / numberOfDays(row),
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'dailyProfitNet',
    headerName: 'Profit trung bình',
    width: 100,
    valueGetter: (_, row) => (row.internalProfit + row.externalProfit) / numberOfDays(row),
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'roi',
    headerName: 'ROI',
    width: 65,
    valueGetter: (_, row) => {
      const dailyProfitNet =
        (row.internalProfit + row.externalProfit) / numberOfDaysInMonth(row.date)
      const dailySpend = row.cost / numberOfDaysInMonth(row.date)
      return safeDivide(dailyProfitNet, dailySpend)
    },
    valueFormatter: (value) => formatter.percentage(value),
  },
  {
    field: 'numberOfDaysInMonth',
    headerName: 'Số ngày',
    width: 60,
    valueGetter: (_, row) => numberOfDays(row),
  },
  {
    field: 'externalProfit',
    headerName: 'Outside',
    width: 120,
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'internalProfit',
    headerName: 'Mirai only',
    width: 120,
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'profit',
    headerName: 'MKT Profit (Gross)',
    width: 120,
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'dailyProfitGross',
    headerName: 'Profit trung bình',
    width: 100,
    valueGetter: (_, row) => row.profit / numberOfDays(row),
    valueFormatter: (value) => `$${formatter.round(value)}`,
  },
  {
    field: 'roas',
    headerName: 'ROAS',
    width: 65,
    valueGetter: (_, row) => safeDivide(row.revenue, row.cost),
    valueFormatter: (value) => formatter.percentage(value),
  },
]

const columnGroupingModel: GridColumnGroupingModel = [
  {
    groupId: 'mirai',
    headerName: 'Mirai',
    headerClassName: 'orange',
    children: [
      { field: 'dailySpend' },
      { field: 'dailyRevenueGross' },
      { field: 'dailyProfitNet' },
      { field: 'roi' },
      { field: 'numberOfDaysInMonth' },
    ],
  },
  {
    groupId: 'overall',
    headerName: 'Overall',
    headerClassName: 'green',
    children: [
      { field: 'externalProfit' },
      { field: 'internalProfit' },
      { field: 'profit' },
      { field: 'dailyProfitGross' },
      { field: 'roas' },
    ],
  },
]

export function RevenueReportTable({ reports }: { reports: GameMetric[] }) {
  const annualyReports = orderBy(
    toPairs(
      groupBy(reports, (r) =>
        dayjs(r.date as any, 'YYYY-MM-DD')
          .startOf('year')
          .format('YYYY-MM-DD')
      )
    ).map(([year, rs], index) => {
      return {
        date: year as any,
        id: 0 - index - 1,
        cost: sumBy(rs, (r) => r.cost),
        revenue: sumBy(rs, (r) => r.revenue),
        internalProfit: sumBy(rs, (r) => r.internalProfit),
        externalProfit: sumBy(rs, (r) => r.externalProfit),
        profit: sumBy(rs, (r) => r.profit),
      } as GameMetric
    }),
    'date',
    'desc'
  )

  const quarterlyReports = orderBy(
    toPairs(
      groupBy(reports, (r) =>
        dayjs(r.date as any, 'YYYY-MM-DD')
          .startOf('quarter')
          .format('YYYY-MM-DD')
      )
    ).map(([quarter, rs], index) => {
      return {
        date: quarter as any,
        id: 0 - index - 1 - 100_000,
        cost: sumBy(rs, (r) => r.cost),
        revenue: sumBy(rs, (r) => r.revenue),
        internalProfit: sumBy(rs, (r) => r.internalProfit),
        externalProfit: sumBy(rs, (r) => r.externalProfit),
        profit: sumBy(rs, (r) => r.profit),
      } as GameMetric
    }),
    'date',
    'desc'
  )

  return (
    <DataGrid
      rows={[...reports, ...annualyReports, ...quarterlyReports]}
      getRowId={(r) => r.id ?? r.date.toString()}
      columns={columns}
      columnGroupingModel={columnGroupingModel}
      editable={false}
      actionColumn={false}
      sx={{
        '& .MuiDataGrid-columnHeaderTitle': {
          lineHeight: 1,
          fontSize: '0.8rem',
        },
        '& .MuiDataGrid-cell': {
          px: 0.5,
          fontSize: '0.8rem',
        },
        ...CURRENCY_COLORS,
      }}
    />
  )
}
