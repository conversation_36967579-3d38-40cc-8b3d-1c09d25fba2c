import { AdminLayout } from '@/components/layout'
import { PartnerLayout } from '@/components/layout/partner_layout'
import { useCurrentUser } from '@/components/ssr'
import { UserKind } from '@/graphql'
import { useMemo } from 'react'

export default function HomePage() {
  const currentUser = useCurrentUser()

  const Layout = useMemo(() => {
    if (currentUser.kind === UserKind.Inhouse) {
      return AdminLayout
    }

    return PartnerLayout
  }, [currentUser])

  return (
    <Layout>
      <p data-testid="welcome">Welcome</p>
    </Layout>
  )
}
