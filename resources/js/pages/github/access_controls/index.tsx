import { Box } from '@mui/material'

import { AdminLayout, useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import {
  useConfigMapCollections,
  useConfigMapCollectionsBatchProcessor,
} from '@/components/configmaps'
import { isLoading, Loading } from '@/components/loading'

import { Repos } from './index/_repos'
import { Teams } from './index/_teams'
import { Collaborators } from './index/_collaborators'
import { useMemo } from 'react'

render(PageWithLayout)

function PageWithLayout() {
  useBreadcrumbs(['GitHub ACL'])

  useConfigMapCollectionsBatchProcessor()
  const getConfigMaps = useConfigMapCollections(
    useMemo(() => ['cmap.gh.acl', 'cmap.gh.member', 'cmap.gh.team'], [])
  )

  const loading = isLoading([getConfigMaps.loading, getConfigMaps.error])
  const [aclConfigMapCollection, memberConfigMapCollection, teamConfigMapCollection] =
    getConfigMaps.data

  return (
    <AdminLayout>
      <Loading visible={loading}>
        <Box>
          <Box sx={{ mb: 2 }}>
            <Teams teams={teamConfigMapCollection} members={memberConfigMapCollection} />
          </Box>

          <Box sx={{ mb: 2 }}>
            <Repos acls={aclConfigMapCollection} />
          </Box>

          <Collaborators acls={aclConfigMapCollection} />
        </Box>
      </Loading>
    </AdminLayout>
  )
}
