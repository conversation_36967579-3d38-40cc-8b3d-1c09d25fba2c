import { ConfigMapCollection } from '@munkit/main'
import { Accordion, AccordionDetails, AccordionSummary, Box, Grid } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import LoadingButton from '@mui/lab/LoadingButton'

import GithubAccessControlConfigMap, {
  GithubAclRepositoryConfigMap,
  GithubAclTeamConfigMap,
} from '#configmaps/github/github_access_control'
import { DataGrid } from '@/components/table'
import { useUpdateGitHubAcl } from '@/components/toolkit-api'
import { isLoading } from '@/components/loading'
import { Title } from '../../_components/_title'
import { Table } from '../../_components/_table'

const repoColumns: GridColDef<GithubAclRepositoryConfigMap>[] = [
  {
    field: 'name',
    headerName: 'Repository',
    flex: 3,
  },
  {
    field: 'collaborators',
    headerName: 'Outside collaborators',
    valueGetter: (_, row) => {
      return row.outsideCollaborators
        .map((collaborator) => `<${collaborator.permission}> ${collaborator.fullName}`)
        .join('\n')
    },
    flex: 2,
  },
  {
    field: 'teams',
    headerName: 'Team overrides',
    flex: 2,
  },
]

const teamColumns: GridColDef<GithubAclTeamConfigMap>[] = [
  {
    field: 'name',
    headerName: 'Team',
    flex: 2,
  },
  {
    field: 'defaultPermission',
    headerName: 'Permission',
    flex: 1,
  },
]

export function Repos({
  acls: accessControlConfigMapCollection,
}: {
  acls: ConfigMapCollection<GithubAccessControlConfigMap>
}) {
  const updateAcl = useUpdateGitHubAcl()
  const submitting = isLoading([updateAcl.isPending])
  const onSubmit = (ids: string[]) => updateAcl.mutateAsync(ids)

  return (
    <>
      <Title>ACL</Title>

      <LoadingButton
        variant="contained"
        sx={{ my: 2 }}
        onClick={() => onSubmit([])}
        disabled={submitting}
        loading={submitting}
      >
        Update All ACL
      </LoadingButton>

      {accessControlConfigMapCollection.map((aclConfigMap) => {
        return (
          <Accordion key={aclConfigMap.id}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />} id={aclConfigMap.id}>
              {aclConfigMap.id}
            </AccordionSummary>

            <AccordionDetails>
              <Grid container columnSpacing={4}>
                <Grid size={8}>
                  <Table
                    columns={repoColumns}
                    rows={aclConfigMap.repositories}
                    getRowId={(row) => row.name}
                  />
                </Grid>

                <Grid size={4}>
                  <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                    <DataGrid
                      sx={{
                        '& .MuiDataGrid-cell': {
                          textAlign: 'left !important',
                        },
                        '& .MuiDataGrid-columnHeaderTitle': {
                          textAlign: 'left !important',
                        },
                      }}
                      columns={teamColumns}
                      rows={aclConfigMap.teams}
                      getRowId={(row) => row.name}
                      actionColumn={false}
                      editable={false}
                      hideFooter
                      slots={{ toolbar: () => null }}
                    />
                  </Box>
                </Grid>
              </Grid>

              <LoadingButton
                fullWidth
                variant="contained"
                sx={{ mt: 2 }}
                onClick={() => onSubmit([aclConfigMap.id])}
                disabled={submitting}
                loading={submitting}
              >
                Update {aclConfigMap.id} ACL
              </LoadingButton>
            </AccordionDetails>
          </Accordion>
        )
      })}
    </>
  )
}
