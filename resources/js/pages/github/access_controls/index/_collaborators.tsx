import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material'
import { ConfigMapCollection } from '@munkit/main'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { GridColDef } from '@mui/x-data-grid-pro'

import GithubAccessControlConfigMap, {
  GithubAclRepositoryOutsideCollaboratorConfigMap,
} from '#configmaps/github/github_access_control'
import { Table } from '../../_components/_table'
import { Title } from '../../_components/_title'

const collaboratorColumns: GridColDef<GithubAclRepositoryOutsideCollaboratorConfigMap>[] = [
  { field: 'fullName', headerName: 'Full Name', flex: 1 },
  { field: 'permission', headerName: 'Permission', flex: 1 },
]

export function Collaborators({
  acls: aclConfigMapCollection,
}: {
  acls: ConfigMapCollection<GithubAccessControlConfigMap>
}) {
  const repositoryCollaboratorConfigMaps = aclConfigMapCollection
    .map((x) => x)
    .flatMap((aclConfigMap) => aclConfigMap.repositories.map((x) => x))
    .reduce((result, aclRepositoryConfigMap) => {
      if (aclRepositoryConfigMap.outsideCollaborators.length === 0) {
        return result
      }

      if (!result.has(aclRepositoryConfigMap.name)) {
        result.set(aclRepositoryConfigMap.name, [])
      }

      aclRepositoryConfigMap.outsideCollaborators.forEach((collaborator) => {
        result.get(aclRepositoryConfigMap.name)!.push(collaborator)
      })

      return result
    }, new Map<string, GithubAclRepositoryOutsideCollaboratorConfigMap[]>())

  return (
    <>
      <Title sx={{ mb: 2 }}>OUTSIDE COLLABORATORS</Title>

      {Array.from(repositoryCollaboratorConfigMaps.entries()).map(([repository, collaborators]) => (
        <Accordion key={repository} defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />} id={repository}>
            {repository}
          </AccordionSummary>

          <AccordionDetails>
            <Table
              columns={collaboratorColumns}
              rows={collaborators}
              getRowId={(row) => row.fullName}
            />
          </AccordionDetails>
        </Accordion>
      ))}
    </>
  )
}
