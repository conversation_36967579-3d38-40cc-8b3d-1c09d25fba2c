import { ConfigMapCollection } from '@munkit/main'
import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { GridColDef } from '@mui/x-data-grid-pro'
import LoadingButton from '@mui/lab/LoadingButton'

import GithubMemberConfigMap from '#configmaps/github/github_member'
import GithubTeamConfigMap from '#configmaps/github/github_team'
import { useUpdateGitHubTeam } from '@/components/toolkit-api'
import { isLoading } from '@/components/loading'
import { Title } from '../../_components/_title'
import { Table } from '../../_components/_table'

const memberColumns: GridColDef<GithubMemberConfigMap>[] = [
  {
    field: 'fullName',
    headerName: 'Member',
    flex: 1,
  },
  {
    field: 'username',
    headerName: 'GitHub Username',
    flex: 2,
  },
]

export function Teams({
  members: memberConfigMapCollection,
  teams: teamConfigMapCollection,
}: {
  members: ConfigMapCollection<GithubMemberConfigMap>
  teams: ConfigMapCollection<GithubTeamConfigMap>
}) {
  const updateTeam = useUpdateGitHubTeam()
  const onSubmit = (names: string[]) => updateTeam.mutateAsync(names)
  const submitting = isLoading([updateTeam.isPending])

  return (
    <>
      <Title>TEAM</Title>

      <LoadingButton
        variant="contained"
        sx={{ my: 2 }}
        onClick={() => onSubmit([])}
        disabled={submitting}
        loading={submitting}
      >
        Update All Teams
      </LoadingButton>

      {teamConfigMapCollection.map((teamConfigMap) => {
        const members = memberConfigMapCollection.filter((m) =>
          teamConfigMap.members.some((member) => member.fullName === m.fullName)
        )

        return (
          <Accordion key={teamConfigMap.name}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />} id={teamConfigMap.name}>
              {teamConfigMap.name}
            </AccordionSummary>

            <AccordionDetails>
              <Table getRowId={(r) => r.username} columns={memberColumns} rows={members} />

              <LoadingButton
                variant="contained"
                fullWidth
                sx={{ mt: 2 }}
                onClick={() => onSubmit([teamConfigMap.name])}
                disabled={submitting}
                loading={submitting}
              >
                Update {teamConfigMap.name}
              </LoadingButton>
            </AccordionDetails>
          </Accordion>
        )
      })}
    </>
  )
}
