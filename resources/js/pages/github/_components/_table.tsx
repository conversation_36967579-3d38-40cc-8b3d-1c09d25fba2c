import { Box } from '@mui/material'
import { GridValidRowModel } from '@mui/x-data-grid-pro'

import { DataGrid, DataGridProps } from '@/components/table'

export function Table<T extends GridValidRowModel>({
  sx = {},
  ...props
}: Omit<DataGridProps<T>, 'actionColumn' | 'editable'>) {
  return (
    <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
      <DataGrid
        {...props}
        sx={{
          '& .MuiDataGrid-cell': {
            textAlign: 'left !important',
          },
          '& .MuiDataGrid-columnHeaderTitle': {
            textAlign: 'left !important',
          },
          ...sx,
        }}
        actionColumn={false}
        editable={false}
        hideFooter
        slots={{
          toolbar: () => null,
        }}
      />
    </Box>
  )
}
