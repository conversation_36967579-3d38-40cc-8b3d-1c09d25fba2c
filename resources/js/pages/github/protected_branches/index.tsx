import { Accordion, AccordionDetails, AccordionSummary, Box, Grid, Typography } from '@mui/material'
import { GridColDef } from '@mui/x-data-grid-pro'
import LoadingButton from '@mui/lab/LoadingButton'

import { AdminLayout, useBreadcrumbs } from '@/components/layout'
import { render } from '@/react'
import { Title } from '../_components/_title'
import {
  useConfigMapCollections,
  useConfigMapCollectionsBatchProcessor,
} from '@/components/configmaps'
import { isLoading, Loading } from '@/components/loading'
import { Table } from '../_components/_table'
import GithubRulesetConfigMap, {
  GithubRulesetBranchConfigMap,
} from '#configmaps/github/github_ruleset'
import { useUpdateGitHubProtectedBranch } from '@/components/toolkit-api'
import { useMemo } from 'react'

render(PageWithLayout)

const rulesetColumns: GridColDef<GithubRulesetBranchConfigMap>[] = [
  {
    field: 'name',
    headerName: 'Branch',
    flex: 1,
  },
  {
    field: 'rules',
    headerName: 'Rules',
    valueGetter: (_, row) => {
      return row.rules.map((rule) => `${rule.rule}: ${rule.value}`).join('\n')
    },
    flex: 2,
  },
]

const branchColumns: GridColDef<GithubRulesetConfigMap>[] = [
  {
    field: 'defaultBranch',
    headerName: 'Default Branch',
    flex: 1,
  },
  {
    field: 'merge',
    headerName: 'Merge Strategies',
    valueGetter: (_, row) => row.mergeStrategies.map((s) => s.name).join('\n'),
    flex: 1,
  },
]

export function PageWithLayout() {
  useBreadcrumbs(['GitHub Protected Branch'])
  useConfigMapCollectionsBatchProcessor()

  const getConfigMaps = useConfigMapCollections(
    useMemo(() => ['cmap.gh.pbranch', 'cmap.gh.ruleset'], [])
  )
  const loading = isLoading([getConfigMaps.loading, getConfigMaps.error])

  const updateProtectedBranch = useUpdateGitHubProtectedBranch()

  const [protectedBranchConfigMapCollection, rulesetConfigMapCollection] = getConfigMaps.data

  return (
    <AdminLayout>
      <Loading visible={loading}>
        <Box>
          <Title sx={{ mb: 2 }}>Protected branch</Title>

          {protectedBranchConfigMapCollection?.map((protectedBranchConfigMap) => {
            const rulesetConfigMap = protectedBranchConfigMap.rulesetId
              ? rulesetConfigMapCollection.find((r) => r.id === protectedBranchConfigMap.rulesetId)!
              : protectedBranchConfigMap.inlineRulesets[0]!

            return (
              <Accordion key={protectedBranchConfigMap.repository}>
                <AccordionSummary>
                  <Typography pr={1} color="secondary">{`<${rulesetConfigMap.id}>`}</Typography>
                  {protectedBranchConfigMap.repository}
                </AccordionSummary>

                <AccordionDetails>
                  <Grid container columnSpacing={4}>
                    <Grid size={8}>
                      <Table
                        sx={{
                          '& .MuiDataGrid-cell': {
                            textAlign: 'left !important',
                            whiteSpace: 'pre !important',
                          },
                        }}
                        rows={rulesetConfigMap.branches}
                        columns={rulesetColumns}
                        getRowId={(row) => row.name}
                        getRowHeight={() => 'auto'}
                      />
                    </Grid>

                    <Grid size={4}>
                      <Table
                        sx={{
                          '& .MuiDataGrid-cell': {
                            textAlign: 'left !important',
                            whiteSpace: 'pre !important',
                          },
                        }}
                        rows={[rulesetConfigMap]}
                        columns={branchColumns}
                        getRowId={(row) => row.id}
                        getRowHeight={() => 'auto'}
                      />
                    </Grid>
                  </Grid>

                  <LoadingButton
                    variant="contained"
                    onClick={() =>
                      updateProtectedBranch.mutateAsync([protectedBranchConfigMap.repository])
                    }
                    loading={false}
                    disabled={false}
                    fullWidth
                    sx={{ mt: 2 }}
                  >
                    Update {protectedBranchConfigMap.repository} rules
                  </LoadingButton>
                </AccordionDetails>
              </Accordion>
            )
          })}
        </Box>
      </Loading>
    </AdminLayout>
  )
}
