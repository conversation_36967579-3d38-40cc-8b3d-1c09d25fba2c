import { Box, Button } from '@mui/material'
import { useMutation, useQuery } from '@tanstack/react-query'

import { AdminLayout } from '@/components/layout/admin_layout'
import { useBreadcrumbs } from '@/components/layout'
import { toolkitApi } from '@/components/toolkit-api'
import { useCsrf } from '@/components/csrf'
import { useMutationNotification } from '@/components/toolkit-api/notification'
import { useParams } from '@/components/location'
import { Loading } from '@/components/loading'
import { useQueryToast } from '@/components/toast'
import { useEditorControl, WYSIWYG } from '@/components/wysiwyg'

export default function PageWithLayout() {
  return (
    <AdminLayout>
      <Page />
    </AdminLayout>
  )
}

function Page() {
  const { docId } = useParams<{ docId: string }>('/documents/:docId/edit')

  useBreadcrumbs([`Edit ${docId}`], 'dashboard')

  // const { clientNavigate } = useClientNavigate()

  const getDocumentQuery = useQuery({
    queryKey: ['document', docId],
    queryFn: async () => {
      return toolkitApi.getDocument(docId)
    },
  })

  const { data, isLoading } = getDocumentQuery

  useQueryToast(getDocumentQuery)

  const csrf = useCsrf()

  const wysiwygControl = useEditorControl()

  const updatePrivacyPolicyMutation = useMutation({
    mutationFn: async () => {
      await toolkitApi.updateDocument({
        ...csrf.getField(),
        content: wysiwygControl.getContent(),
        id: docId,
      })
    },
    mutationKey: ['document', docId],
  })

  useMutationNotification(updatePrivacyPolicyMutation)

  if (isLoading) {
    return (
      <Box>
        <Loading visible />
      </Box>
    )
  }

  return (
    <Box
      sx={{
        [`.ck-editor__editable`]: {
          minHeight: 'calc(100vh - 300px)',
        },
      }}
    >
      <WYSIWYG
        control={wysiwygControl}
        config={{
          initialData: data?.data?.content || '',
        }}
      />

      <Box sx={{ mt: 2 }}>
        <Button
          color="primary"
          variant="contained"
          onClick={() => updatePrivacyPolicyMutation.mutateAsync()}
        >
          Save
        </Button>
      </Box>
    </Box>
  )
}
