import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import { AdminLayout, useBreadcrumbs } from '@/components/layout'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useMemo } from 'react'

export default function PageWithLayout() {
  return (
    <AdminLayout>
      <Page />
    </AdminLayout>
  )
}

function Page() {
  const [[documentConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.document'], []))

  useBreadcrumbs(['All Documents'], 'dashboard')

  // if (documentConfigMapCollection.length === 0) {
  //   return <Loading visible />
  // }

  return (
    <Box>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell>Doc Name</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {documentConfigMapCollection.map((documentConfigMap) => (
              <TableRow
                key={documentConfigMap.id}
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell component="th" scope="row">
                  <a href={`/documents/${documentConfigMap.id}/edit`}>{documentConfigMap.name}</a>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  )
}
