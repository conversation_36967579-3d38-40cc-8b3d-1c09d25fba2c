import { DashboardLayout, useBreadcrumbs } from '@/components/layout'
import { Head } from '@inertiajs/react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  ButtonGroup,
  Step,
  Step<PERSON>abel,
  <PERSON>per,
  Typo<PERSON>,
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { useState } from 'react'
import { WorkflowForm } from './index/_form'
import { useModal } from '@/components/modal'
import { graphql } from '@/gql'
import { useGraphql, useGraphqlMutation } from '@/components/toolkit-api'
import { isLoading, Loading } from '@/components/loading'
import {
  useConfigMapCollections,
  useConfigMapCollectionsBatchProcessor,
} from '@/components/configmaps'
import { Workflow } from '#graphql/main'
import { WorkflowAttributesFragment } from '@/graphql'

graphql(`
  fragment WorkflowAttributes on Workflow {
    id
    name
    roleId
    steps {
      ...WorkflowStepAttributes
    }
  }
`)

const getInitialDataQuery = graphql(`
  query WorkflowsIndex_GetInitialData {
    workflows {
      collection {
        ...WorkflowAttributes
      }
    }

    users(where: {}, offset: { perPage: 9999 }) {
      collection {
        ...UserPublicProfile
      }
    }
  }
`)

const updateWorkflowMutation = graphql(`
  mutation WorkflowsIndex_Update($form: UpdateWorkflowForm!) {
    updateWorkflow(form: $form) {
      ...WorkflowAttributes
    }
  }
`)

const createWorkflowQuery = graphql(`
  mutation WorkflowsIndex_Create($form: CreateWorkflowForm!) {
    createWorkflow(form: $form) {
      ...WorkflowAttributes
    }
  }
`)

export default function Page() {
  useBreadcrumbs(['All workflows'], 'dashboard')
  useConfigMapCollectionsBatchProcessor()

  const getInitialData = useGraphql(getInitialDataQuery)
  const modalControl = useModal({})

  const getConfigMaps = useConfigMapCollections(['cmap.dash.role'])

  const [model, setModel] = useState<Workflow>({ name: '', steps: [], roleId: '', id: 0 })

  const loading = isLoading([
    getInitialData.loading,
    getInitialData.error,
    getConfigMaps.loading,
    getConfigMaps.error,
  ])

  const [roleConfigMapCollection] = getConfigMaps.data

  const [updateWorkflow] = useGraphqlMutation(updateWorkflowMutation)
  const [createWorkflow] = useGraphqlMutation(createWorkflowQuery)

  const onSubmit = async (formData: WorkflowAttributesFragment) => {
    if (model.id > 0) {
      updateWorkflow({
        variables: {
          form: {
            id: model.id,
            name: formData.name,
            steps: formData.steps.map((s) => ({
              action: s.action,
              assigneeId: s.assignee.id,
              name: s.name,
              alternateAction: s.alternateAction,
            })),
          },
        },
      })
    } else {
      createWorkflow({
        variables: {
          form: {
            name: formData.name,
            roleId: formData.roleId,
            steps: formData.steps.map((s) => ({
              action: s.action,
              assigneeId: s.assignee.id,
              name: s.name,
              alternateAction: s.alternateAction,
            })),
          },
        },
      })
    }
  }

  return (
    <DashboardLayout>
      <Head title="All workflows" />

      <Button
        onClick={(e) => {
          setModel({ id: 0, name: '', roleId: '', steps: [] })
          modalControl.onOpen(e)
        }}
      >
        Create Workflow
      </Button>

      <Loading visible={loading}>
        <Box sx={{ mb: 3 }}>
          {getInitialData.data?.workflows?.collection?.map((workflow) => {
            return (
              <Accordion key={workflow.id}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography component="span">
                    [{roleConfigMapCollection?.find((r) => r.id === workflow.roleId)?.name}]{' '}
                    {workflow.name}
                  </Typography>
                </AccordionSummary>

                <AccordionDetails>
                  <Stepper sx={{ maxWidth: 200 * workflow.steps.length }}>
                    {workflow.steps.map((step, index) => (
                      <Step
                        key={step.name}
                        completed={index === workflow.steps.length - 1}
                        active
                        disabled={false}
                      >
                        <StepLabel
                          optional={
                            <Typography variant="caption">
                              {step.assignee.fullName} {step.action}
                            </Typography>
                          }
                        >
                          {step.name}
                        </StepLabel>
                      </Step>
                    ))}
                  </Stepper>

                  <ButtonGroup sx={{ mt: 2 }}>
                    <Button variant="contained" color="error">
                      Delete
                    </Button>
                    <Button
                      onClick={(e) => {
                        setModel(workflow as any)
                        return modalControl.onOpen(e)
                      }}
                    >
                      Edit
                    </Button>
                  </ButtonGroup>
                </AccordionDetails>
              </Accordion>
            )
          })}
        </Box>

        <WorkflowForm
          users={getInitialData.data?.users?.collection!}
          key={JSON.stringify(model)}
          model={model}
          control={modalControl}
          onSubmit={onSubmit}
        />
      </Loading>
    </DashboardLayout>
  )
}
