import { useConfigMapCollections } from '@/components/configmaps'
import { YupFormGroup } from '@/components/form'
import { Modal, ModalBody, ModalController, ModalHeader } from '@/components/modal'
import { UserPublicProfileFragment, WorkflowAttributesFragment } from '@/graphql'
import { Button, Divider, Grid, IconButton, MenuItem, OutlinedInput, Select } from '@mui/material'
import { useEffect } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import DeleteIcon from '@mui/icons-material/Delete'

export const INITIAL_VALUES = Object.freeze({ steps: [], id: 0, name: '', roleId: '' })

export function WorkflowForm({
  model = INITIAL_VALUES,
  control,
  users,
  onSubmit,
}: {
  model?: WorkflowAttributesFragment
  control: ModalController
  users: UserPublicProfileFragment[]
  onSubmit: (w: WorkflowAttributesFragment) => any
}) {
  const [roleConfigMapCollection] = useConfigMapCollections(['cmap.dash.role']).data
  const form = useForm({ defaultValues: model })
  const stepsForm = useFieldArray({ control: form.control, name: 'steps' })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      control.onClose(null as any)
    }
  }, [form.formState.isSubmitSuccessful, control.onClose])

  return (
    <Modal
      control={control}
      component="form"
      onSubmit={form.handleSubmit(onSubmit)}
      slots={{
        buttonsRight: (
          <Button variant="outlined" color="success" type="submit">
            Save Workflow
          </Button>
        ),
      }}
    >
      <ModalHeader>Create Workflow</ModalHeader>
      <ModalBody>
        <YupFormGroup label="Name" name="name" control={form.control} fullWidth sx={{ mb: 2 }}>
          <OutlinedInput label="Name" {...form.register('name')} />
        </YupFormGroup>

        <YupFormGroup label="Role" name="roleId" control={form.control} fullWidth sx={{ mb: 2 }}>
          {(props: any) => (
            <Select label="Role" {...props.field}>
              <MenuItem disabled value="">
                Select Role
              </MenuItem>
              {roleConfigMapCollection.map((roleConfigMap) => (
                <MenuItem key={roleConfigMap.id} value={roleConfigMap.id}>
                  {roleConfigMap.name}
                </MenuItem>
              ))}
            </Select>
          )}
        </YupFormGroup>

        {stepsForm.fields.map((stepField, index) => {
          return (
            <>
              <Grid container columnSpacing={2} key={index.toString()}>
                <Grid>
                  <YupFormGroup
                    key={stepField.id}
                    label="Step Name"
                    name={`steps.${index}.name`}
                    control={form.control}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    <OutlinedInput label="Step Name" {...form.register(`steps.${index}.name`)} />
                  </YupFormGroup>
                </Grid>

                <Grid>
                  <YupFormGroup
                    key={stepField.id}
                    label="Action"
                    name={`steps.${index}.action`}
                    control={form.control}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    <OutlinedInput label="Action" {...form.register(`steps.${index}.action`)} />
                  </YupFormGroup>
                </Grid>

                <Grid>
                  <YupFormGroup
                    key={stepField.id}
                    label="Alternate Action"
                    name={`steps.${index}.alternateAction`}
                    control={form.control}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    <OutlinedInput
                      label="Alternate Action"
                      {...form.register(`steps.${index}.alternateAction`)}
                    />
                  </YupFormGroup>
                </Grid>

                <Grid flex={1}>
                  <YupFormGroup
                    key={stepField.id}
                    label="PIC"
                    name={`steps.${index}.assignee.id`}
                    control={form.control}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    {(props: any) => (
                      <Select label="PIC" {...props.field}>
                        <MenuItem disabled value="">
                          Select PIC
                        </MenuItem>
                        {users.map((user) => (
                          <MenuItem key={user.id} value={user.id}>
                            {user.fullName}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  </YupFormGroup>
                </Grid>
              </Grid>

              <Divider sx={{ mb: 2 }}>
                <IconButton color="error" onClick={() => stepsForm.remove(index)}>
                  <DeleteIcon />
                </IconButton>
              </Divider>
            </>
          )
        })}

        <Button
          onClick={() =>
            stepsForm.append({
              name: '',
              assignee: { id: '', fullName: '' },
              action: '',
              alternateAction: '',
            })
          }
        >
          Add Step
        </Button>
      </ModalBody>
    </Modal>
  )
}
