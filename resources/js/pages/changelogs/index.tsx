import {
  useConfigMapCollections,
  useConfigMapCollectionsBatchProcessor,
} from '@/components/configmaps'
import { AdminLayout, useBreadcrumbs } from '@/components/layout'
import { isLoading, Loading } from '@/components/loading'
import { toolkitApi } from '@/components/toolkit-api'
import { render } from '@/react'
import { Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'

render(Page)

function Page() {
  useBreadcrumbs(["What's new"], 'dashboard')

  useConfigMapCollectionsBatchProcessor()

  const getConfigMap = useConfigMapCollections(
    useMemo(() => [{ id: 'cmap.general.setting', sole: true }], [])
  )
  const loadingConfigMap = isLoading([getConfigMap.loading, getConfigMap.error])

  const [settingConfigMap] = getConfigMap.data

  const getDoc = useQuery({
    queryKey: ['doc', settingConfigMap?.changeLogDocId],
    queryFn: () => toolkitApi.getDocument(settingConfigMap?.changeLogDocId),
    enabled: !loadingConfigMap,
  })

  const loading = isLoading([
    loadingConfigMap,
    getDoc.isLoading,
    getDoc.isPending,
    getDoc.isFetching,
    getDoc.error,
  ])

  return (
    <AdminLayout>
      <Loading visible={loading}>
        <Typography
          color="textPrimary"
          dangerouslySetInnerHTML={{ __html: getDoc.data?.data?.content || '' }}
        />
      </Loading>
    </AdminLayout>
  )
}
