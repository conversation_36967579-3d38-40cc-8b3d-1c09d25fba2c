'use client'

import {
  type ColumnFiltersState,
  GroupingState,
  type PaginationState,
  RowData,
  type RowSelectionState,
  type SortingState,
  type TableOptions,
  type TableState,
  type Updater,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getGroupedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  type UseQueryStateOptions,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  useQueryState,
} from 'nuqs'
import * as React from 'react'

import { getSortingStateParser } from '@/next/lib/parsers'
import type { ExtendedColumnSort } from '@/next/types/data-table'

import { UseDataTableFilterFormReturn } from './use_data_table_filter_form'
import { usePersistentState } from './use_persistent_state'

const PAGE_KEY = 'page'
const PER_PAGE_KEY = 'perPage'
const SORT_KEY = 'sort'
const GROUP_KEY = 'group'
const DEBOUNCE_MS = 300
const THROTTLE_MS = 50

export interface UseDataTableProps<TData>
  extends Omit<
      TableOptions<TData>,
      | 'state'
      | 'pageCount'
      | 'getCoreRowModel'
      | 'manualFiltering'
      | 'manualPagination'
      | 'manualSorting'
    >,
    Required<Pick<TableOptions<TData>, 'pageCount'>> {
  initialState?: Omit<Partial<TableState>, 'sorting'> & {
    sorting?: ExtendedColumnSort<TData>[]
  }
  history?: 'push' | 'replace'
  debounceMs?: number
  throttleMs?: number
  clearOnDefault?: boolean
  enableAdvancedFilter?: boolean
  scroll?: boolean
  shallow?: boolean
  startTransition?: React.TransitionStartFunction
  form: UseDataTableFilterFormReturn<any>
}

export function useDataTable<TData>(props: UseDataTableProps<TData>) {
  const {
    columns: colDefs,
    pageCount = -1,
    initialState,
    history = 'replace',
    debounceMs = DEBOUNCE_MS,
    throttleMs = THROTTLE_MS,
    clearOnDefault = false,
    enableAdvancedFilter = false,
    scroll = false,
    shallow = true,
    startTransition,
    form,
    ...tableProps
  } = props

  const columns = React.useMemo(() => {
    return form.makeFilterColumns(colDefs)
  }, [colDefs, form.makeFilterColumns])

  const queryStateOptions = React.useMemo<Omit<UseQueryStateOptions<string>, 'parse'>>(
    () => ({
      history,
      scroll,
      shallow,
      throttleMs,
      debounceMs,
      clearOnDefault,
      startTransition,
    }),
    [history, scroll, shallow, throttleMs, debounceMs, clearOnDefault, startTransition]
  )

  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(
    initialState?.rowSelection ?? {}
  )

  const pageId = React.useMemo(() => {
    return tableProps.meta?.pageId || new Date().getTime().toString()
  }, [tableProps.meta?.pageId])
  const [columnVisibility, setColumnVisibility] = usePersistentState<VisibilityState>(
    `${pageId}_table_column_visibility`,
    initialState?.columnVisibility ?? {},
    {
      driver: pageId ? 'localStorage' : 'sessionStorage',
    }
  )
  const [columnOrder, setColumnOrder] = usePersistentState(
    `${pageId}_table_column_order`,
    initialState?.columnOrder ?? [],
    {
      driver: pageId ? 'localStorage' : 'sessionStorage',
    }
  )

  const [page, setPage] = useQueryState(
    PAGE_KEY,
    parseAsInteger.withOptions(queryStateOptions).withDefault(1)
  )
  const [perPage, setPerPage] = useQueryState(
    PER_PAGE_KEY,
    parseAsInteger
      .withOptions(queryStateOptions)
      .withDefault(initialState?.pagination?.pageSize ?? 10)
  )

  const pagination: PaginationState = React.useMemo(() => {
    return {
      pageIndex: page - 1, // zero-based index -> one-based index
      pageSize: perPage,
    }
  }, [page, perPage])

  const onPaginationChange = React.useCallback(
    (updaterOrValue: Updater<PaginationState>) => {
      if (typeof updaterOrValue === 'function') {
        const newPagination = updaterOrValue(pagination)
        void setPage(newPagination.pageIndex + 1)
        void setPerPage(newPagination.pageSize)
      } else {
        void setPage(updaterOrValue.pageIndex + 1)
        void setPerPage(updaterOrValue.pageSize)
      }
    },
    [pagination, setPage, setPerPage]
  )

  const columnIds = React.useMemo(() => {
    return new Set(columns.map((column) => column.id).filter(Boolean) as string[])
  }, [columns])

  const [sorting, setSorting] = useQueryState(
    SORT_KEY,
    getSortingStateParser<TData>(columnIds)
      .withOptions(queryStateOptions)
      .withDefault(initialState?.sorting ?? [])
  )

  const onSortingChange = React.useCallback(
    (updaterOrValue: Updater<SortingState>) => {
      if (typeof updaterOrValue === 'function') {
        const newSorting = updaterOrValue(sorting)
        setSorting(newSorting as ExtendedColumnSort<TData>[])
      } else {
        setSorting(updaterOrValue as ExtendedColumnSort<TData>[])
      }
    },
    [sorting, setSorting]
  )

  const filterableColumns = React.useMemo(() => {
    if (enableAdvancedFilter) return []

    return columns.filter((column) => column.enableColumnFilter)
  }, [columns, enableAdvancedFilter])

  const { initialValues, setImmediateValues } = form

  const initialColumnFilters: ColumnFiltersState = React.useMemo(() => {
    if (enableAdvancedFilter) return []

    return Object.entries(initialValues).reduce<ColumnFiltersState>((filters, [key, value]) => {
      if (value !== null) {
        // const processedValue = Array.isArray(value)
        //   ? value
        //   : typeof value === 'string' && /[^a-zA-Z0-9]/.test(value)
        //     ? value.split(/[^a-zA-Z0-9]+/).filter(Boolean)
        //     : [value]

        filters.push({
          id: key,
          value,
        })
      }
      return filters
    }, [])
  }, [initialValues, enableAdvancedFilter])

  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(initialColumnFilters)

  const onColumnFiltersChange = React.useCallback(
    (updaterOrValue: Updater<ColumnFiltersState>) => {
      if (enableAdvancedFilter) return

      setColumnFilters((prev) => {
        const next = typeof updaterOrValue === 'function' ? updaterOrValue(prev) : updaterOrValue

        const filterUpdates = next.reduce<Record<string, string | string[] | null>>(
          (acc, filter) => {
            if (filterableColumns.find((column) => column.id === filter.id)) {
              acc[filter.id] = filter.value as string | string[]
            }
            return acc
          },
          {}
        )

        for (const prevFilter of prev) {
          if (!next.some((filter) => filter.id === prevFilter.id)) {
            filterUpdates[prevFilter.id] = null
          }
        }

        setImmediateValues(filterUpdates)
        return next
      })
    },
    [setImmediateValues, filterableColumns, enableAdvancedFilter]
  )

  const [grouping, setGrouping] = useQueryState(
    GROUP_KEY,
    parseAsArrayOf(parseAsString)
      .withOptions(queryStateOptions)
      .withDefault(initialState?.grouping ?? [])
  )

  const onGroupingChange = React.useCallback(
    (updaterOrValue: Updater<GroupingState>) => {
      const next = typeof updaterOrValue === 'function' ? updaterOrValue(grouping) : updaterOrValue
      setGrouping(next)
      return next
    },
    [grouping, setGrouping]
  )

  const table = useReactTable({
    ...tableProps,
    columns,
    initialState: {
      ...(initialState || {}),
      columnFilters: initialColumnFilters,
    },
    pageCount,
    state: {
      pagination,
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      grouping,
      columnOrder,
    },
    defaultColumn: {
      ...tableProps.defaultColumn,
      enableColumnFilter: false,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onPaginationChange,
    onSortingChange,
    onColumnFiltersChange,
    onColumnVisibilityChange: setColumnVisibility,
    onColumnOrderChange: setColumnOrder,
    onGroupingChange,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    getGroupedRowModel: getGroupedRowModel(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    manualGrouping: true,
  })

  return { table, shallow, debounceMs, throttleMs }
}

declare module '@tanstack/table-core' {
  interface TableMeta<TData extends RowData> {
    pageId?: string
  }
}
