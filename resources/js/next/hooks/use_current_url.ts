import { usePage } from '@inertiajs/react'

export function useCurrentUrl() {
  const { url } = usePage()
  return url
}

function normalizeUrl(url: string): string {
  const cleanUrl = url.split('?')[0].split('#')[0]
  return cleanUrl.length > 1 && cleanUrl.endsWith('/') ? cleanUrl.slice(0, -1) : cleanUrl
}

export function isUrlActive(currentUrl: string, targetUrl: string): boolean {
  const normalizedCurrent = normalizeUrl(currentUrl)
  const normalizedTarget = normalizeUrl(targetUrl)

  if (normalizedCurrent === normalizedTarget) {
    return true
  }

  if (normalizedTarget.includes('{')) {
    const pattern = normalizedTarget.replace(/\{[0-9]+\}/g, '[^/]+')
    const regex = new RegExp(`^${pattern}$`)
    return regex.test(normalizedCurrent)
  }

  return normalizedCurrent.startsWith(normalizedTarget + '/')
}
