import { Row, RowData, Table } from '@tanstack/react-table'
import { isNil, sum } from 'lodash-es'
import { useEffect } from 'react'
import { create } from 'zustand'

import { safeDivide } from '#utils/math'

const defaultIsNa = (value: number) => {
  return isNil(value) || value === 0
}

const useTreeTableAggregationStore = create<{
  aggregations: Record<string, Record<string, AggregationRow>>
  setAggregations: (aggregations: Record<string, Record<string, AggregationRow>>) => void
  getGroupAggregation: (row: Row<any>) => Record<string, AggregationRow> | null
  getBaselineAggregation: (row: Row<any>) => Record<string, AggregationRow> | null
  table: Table<any> | null
  setTable: (table: Table<any>) => void
}>((set, get) => ({
  aggregations: {},
  table: null,
  setAggregations: (aggregations) => set({ aggregations }),
  getGroupAggregation: (row) => {
    const table = get().table
    if (!table) return null

    const { grouping } = table.getState()
    const dimensionValues = grouping.map((g) => row.getValue(g)).filter(Boolean)
    const groupDimensionValues = dimensionValues.slice(0, dimensionValues.length - 1)

    const groupKey = groupDimensionValues.length === 0 ? 'root' : groupDimensionValues.join('//')

    return get().aggregations[groupKey]
  },
  getBaselineAggregation: (row) => {
    const table = get().table
    if (!table) return null

    const { grouping } = table.getState()
    const baselineValue = table.options.meta!.baselineValue!
    const baselineColumn = table.options.meta!.baselineColumn!
    const dimensionValues = grouping
      .map((g) => {
        if (g === baselineColumn) return baselineValue
        return row.getValue(g)
      })
      .filter(Boolean)
    return get().aggregations[dimensionValues.join('//')]
  },
  setTable: (table) => set({ table }),
}))

export function useTreeTableAggregationState() {
  const store = useTreeTableAggregationStore()

  return {
    aggregations: store.aggregations,
    getGroupAggregation: store.getGroupAggregation,
    getBaselineAggregation: store.getBaselineAggregation,
  }
}

export function useTreeTableAggregation<TData>(table: Table<TData>) {
  const setTable = useTreeTableAggregationStore((s) => s.setTable)

  useEffect(() => {
    setTable(table)
  }, [table])

  const rows = table.getRowModel().rows
  const setAggregations = useTreeTableAggregationStore((s) => s.setAggregations)
  const baselineValue = table.options.meta!.baselineValue

  useEffect(() => {
    const { grouping } = table.getState()
    const rows = table.getRowModel().rows
    const aggregationColumns = table.options.meta!.aggregationColumns
    const keyToAggregations: Record<
      string,
      Record<
        string,
        {
          sum: number[]
          count: number
          avg: number[]
          min: number
          max: number
          naCount: number
          base: number
        }
      >
    > = {}
    const baselineColumn = table.options.meta!.baselineColumn!

    rows.forEach((row) => {
      const dimensionValues = grouping.map((g) => row.getValue(g)).filter(Boolean)
      const groupDimensionValues = dimensionValues.slice(0, dimensionValues.length - 1)

      const groupKey = groupDimensionValues.length === 0 ? 'root' : groupDimensionValues.join('//')
      keyToAggregations[groupKey] ||= {}

      const rowBaselineValue = baselineColumn && row.getValue(baselineColumn)!
      const isBaselineRow = baselineColumn && baselineValue && rowBaselineValue === baselineValue
      const rowKey = dimensionValues.join('//')

      aggregationColumns.forEach((column) => {
        const value = row.getValue(column.id) as number

        if (isBaselineRow) {
          keyToAggregations[rowKey] ||= {}
          keyToAggregations[rowKey][column.id] ||= {
            sum: [value],
            count: 1,
            avg: [value],
            min: value,
            max: value,
            naCount: 0,
            base: value,
          }
        }

        keyToAggregations[groupKey][column.id] ||= {
          sum: [],
          count: 0,
          avg: [],
          min: value,
          max: value,
          naCount: 0,
          base: 0,
        }

        const aggregation = keyToAggregations[groupKey][column.id]

        const isNa = column.isNa ?? defaultIsNa

        if (isNa(value)) {
          aggregation.naCount++
        } else {
          aggregation.sum.push(value)
          aggregation.count++
          aggregation.avg.push(value)
          aggregation.min = Math.min(aggregation.min, value)
          aggregation.max = Math.max(aggregation.max, value)
        }
      })
    })

    const aggregations = Object.fromEntries(
      Object.entries(keyToAggregations).map(([key, value]) => [
        key,
        Object.fromEntries(
          Object.entries(value).map(([key, value]) => [
            key,
            {
              sum: sum(value.sum),
              count: value.count,
              avg: safeDivide(sum(value.avg), value.count),
              min: value.min,
              max: value.max,
              naRate: safeDivide(value.naCount, value.count),
              base: value.base,
            },
          ])
        ),
      ])
    )

    setAggregations(aggregations)
  }, [table, rows, baselineValue])
}

export interface AggregationColumnDef {
  id: string
  isNa?: (value: number) => boolean
}

export interface AggregationRow {
  sum: number
  count: number
  avg: number
  min: number
  max: number
  naRate: number
  base: number
}

declare module '@tanstack/table-core' {
  interface TableMeta<TData extends RowData> {
    aggregationColumns: Array<AggregationColumnDef>
    aggregations: Record<string, Record<string, AggregationRow>>
    getGroupAggregation: (row: Row<TData>) => Record<string, AggregationRow> | null
    baselineColumn?: string | null
    baselineValue?: any
    getBaselineAggregation?: (row: Row<TData>) => Record<string, AggregationRow> | null
  }
}
