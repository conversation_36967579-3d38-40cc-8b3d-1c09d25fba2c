import { ColumnDef, ColumnMeta } from '@tanstack/react-table'
import {
  parseAsArrayOf,
  parseAsBoolean,
  parseAsFloat,
  parseAsIsoDate,
  parseAsString,
  ParserBuilder,
  useQueryStates,
} from 'nuqs'
import { useCallback, useMemo } from 'react'
import { FieldValues, useForm, UseFormReturn } from 'react-hook-form'

import { parseAsDateRange } from '../components/sdk/nuqs/parse_as_date_range'

type ColVariant = NonNullable<ColumnMeta<any, any>['variant']>

type ColDef<T> = {
  defaultValue?: T
  variant: ColVariant
}

type VariantToType = {
  dateRange: [Date, Date]
  number: number
  multiSelect: string[]
  range: number[]
  boolean: boolean
  date: Date
  text: string
  select: string
}

export type UseDataTableFilterFormReturn<TFormValues extends FieldValues> =
  UseFormReturn<TFormValues> & {
    immediateValues: Record<string, any>
    initialValues: Record<string, any>
    setImmediateValues: (values: Record<string, any>) => void
    makeFilterColumns: <T>(colDefs: T) => T
    syncValues: (...keys: string[]) => void
  }

export function useDataTableFilterForm<TColumns extends Record<string, ColDef<any>>>(
  columns: TColumns
): UseDataTableFilterFormReturn<{
  [K in keyof TColumns]: VariantToType[TColumns[K]['variant']]
}> & {
  immediateValues: { [K in keyof TColumns]: VariantToType[TColumns[K]['variant']] }
  initialValues: { [K in keyof TColumns]: VariantToType[TColumns[K]['variant']] }
  setImmediateValues: (values: {
    [K in keyof TColumns]: VariantToType[TColumns[K]['variant']]
  }) => void
  makeFilterColumns: <T>(colDefs: T) => T
  syncValues: (...keys: (keyof TColumns)[]) => void
} {
  const schema = useMemo(() => {
    return Object.fromEntries(
      Object.entries(columns).map(([name, colDef]) => {
        let parser: ParserBuilder<any>

        switch (colDef.variant) {
          case 'dateRange':
            parser = parseAsDateRange.withDefault((colDef.defaultValue as any) || [])
            break

          case 'number':
            parser = parseAsFloat.withDefault(colDef.defaultValue as any)
            break

          case 'multiSelect':
            parser = parseAsArrayOf(parseAsString).withDefault((colDef.defaultValue as any) || [])
            break

          case 'range':
            parser = parseAsArrayOf(parseAsFloat).withDefault((colDef.defaultValue as any) || [])
            break

          case 'boolean':
            parser = parseAsBoolean.withDefault(colDef.defaultValue as any)
            break

          case 'date':
            parser = parseAsIsoDate.withDefault(colDef.defaultValue as any)
            break

          default:
            parser = parseAsString.withDefault((colDef.defaultValue as any) || '')
            break
        }

        return [name, parser]
      })
    )
  }, [Object.keys(columns).length])

  const [queryValues, setImmediateValues] = useQueryStates(schema)
  const immediateValues = queryValues as {
    [K in keyof TColumns]: VariantToType[TColumns[K]['variant']]
  }

  const initialValues = useMemo(() => immediateValues, [immediateValues])

  const form = useForm({
    defaultValues: immediateValues as any,
  })

  const syncValues = useCallback(
    (...keys: (keyof TColumns)[]) => {
      if (keys.length === 0) {
        keys = Object.keys(columns)
      }

      keys.forEach((key) => {
        form.setValue(key as any, immediateValues[key])
      })
    },
    [form, columns]
  )

  const makeFilterColumns = useCallback(
    (colDefs: ColumnDef<any>[]) => {
      return colDefs.map((colDef) => {
        if (colDef.id === undefined) return colDef

        const column = columns[colDef.id as keyof TColumns]

        if (!column) return colDef

        return {
          ...colDef,
          enableColumnFilter: true,
          meta: {
            ...(colDef.meta || {}),
            variant: column.variant,
          },
        }
      })
    },
    [Object.keys(columns).length]
  )

  return {
    immediateValues,
    initialValues,
    setImmediateValues,
    syncValues,
    ...form,
    makeFilterColumns: makeFilterColumns as any,
  }
}
