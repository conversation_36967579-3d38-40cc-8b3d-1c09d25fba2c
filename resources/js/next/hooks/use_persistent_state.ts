import { isNil } from 'lodash-es'
import { useEffect, useMemo, useState } from 'react'

const localStorageDriver = {
  getItem: <T>(key: string, defaultValue?: T) => {
    const value = localStorage.getItem(key)
    try {
      return value ? JSON.parse(value) : defaultValue
    } catch (err) {
      return defaultValue
    }
  },
  setItem: <T>(key: string, value: T) => {
    localStorage.setItem(key, JSON.stringify(value))
  },
}

const sessionStorageDriver = {
  getItem: <T>(key: string, defaultValue?: T) => {
    const value = sessionStorage.getItem(key)
    try {
      return value ? JSON.parse(value) : defaultValue
    } catch (err) {
      return defaultValue
    }
  },
  setItem: <T>(key: string, value: T) => {
    sessionStorage.setItem(key, JSON.stringify(value))
  },
}

export function usePersistentState<T>(
  key: string,
  initialValue?: T,
  options?: {
    driver?: 'localStorage' | 'sessionStorage'
  }
) {
  const { driver = 'localStorage' } = options || {}

  const storageDriver = useMemo(() => {
    switch (driver) {
      case 'localStorage':
        return localStorageDriver

      case 'sessionStorage':
        return sessionStorageDriver

      default:
        throw new Error(`Unsupported driver: ${driver}`)
    }
  }, [driver])

  const [value, setValue] = useState(storageDriver.getItem(key, initialValue))

  useEffect(() => {
    if (!isNil(value)) {
      storageDriver.setItem(key, value)
    }
  }, [value, storageDriver])

  return [value, setValue] as const
}
