import { get, orderBy, cond, matches, stubTrue, flow, negate, isNumber, isEmpty } from 'lodash-es'
import semver, { SemVer } from 'semver'

import { FirebaseVersionVariantAttributesFragment } from '@/graphql'

/**
 * Comparator cho semantic versioning
 */
export function semverComparator(a?: string | null, b?: string | null): number {
  if (!a || !semver.valid(a)) {
    return -1
  }

  if (!b || !semver.valid(b)) {
    return 1
  }

  const aVersion = new SemVer(a)
  const bVersion = new SemVer(b)

  return aVersion.compare(bVersion)
}

/**
 * Comparator cho ad metrics với pattern: {adType}_ad_{field}
 */
export function adMetricComparator(a: any, b: any, adType: string, field: string): number {
  const aMetric = a.ads?.find((ad: any) => ad.adTypeCategory === adType)
  const bMetric = b.ads?.find((ad: any) => ad.adTypeCategory === adType)

  const aValue = Number(get(aMetric, field, 0))
  const bValue = Number(get(bMetric, field, 0))

  return aValue - bValue
}

/**
 * Comparator cho NthDay fields với pattern: fieldName_D<day>
 */
export function nthDayComparator(a: any, b: any, baseField: string, day: string): number {
  const aValue = Number(get(a, `${baseField}.${day}`, 0))
  const bValue = Number(get(b, `${baseField}.${day}`, 0))

  return aValue - bValue
}

/**
 * Tạo comparator function dựa trên field ID với lodash flow
 */
export function createComparator(id: string) {
  // Ad metric pattern: {adType}_ad_{field} hoặc {adType}_ad_{field}D{day}
  const adMatch = id.match(/^(.+)_ad_(.+)$/)
  if (adMatch) {
    const [, adType, field] = adMatch
    const nthDayMatch = field.match(/^(.+)D(\d+)$/)

    if (nthDayMatch) {
      // NthDay pattern: {adType}_ad_{field}D{day}
      const [, baseField, day] = nthDayMatch
      return flow([
        (item: any) => item.ads?.find(matches({ adTypeCategory: adType })),
        (adMetric: any) => Number(get(adMetric, `${baseField}.${day}`, 0)),
        (value: any) => (isNumber(value) ? value : 0),
      ])
    } else {
      // Regular ad pattern: {adType}_ad_{field}
      return flow([
        (item: any) => item.ads?.find(matches({ adTypeCategory: adType })),
        (adMetric: any) => get(adMetric, field, 0),
        (value: any) => (isNumber(value) ? value : 0),
      ])
    }
  }

  // NthDay pattern: fieldName_D<day>
  const nthDayMatch = id.match(/^(.+)_D(\d+)$/)
  if (nthDayMatch) {
    const [, baseField, day] = nthDayMatch
    return flow([(item: any) => get(item, `${baseField}.${day}`, 0), (value: any) => Number(value)])
  }

  // Version field
  if (id === 'version') {
    return (item: any) => {
      const version = item.version
      if (!version || !semver.valid(version)) {
        return '0.0.0' // Invalid versions go to start/end
      }
      return version
    }
  }

  // Default: simple field comparison
  return (item: any) => {
    const value = get(item, id, '')
    return isNumber(value) ? value : String(value)
  }
}

/**
 * Tạo sort config với lodash cond pattern
 */
export const createSortConfig = (id: string, direction: 'asc' | 'desc') => {
  const isNthDay = /_D\d+$/.test(id)
  const isAdField = id.includes('_ad_')
  const isVersion = id === 'version'

  const fieldResolver = cond([
    [() => isNthDay, () => createComparator(id)],
    [() => isAdField, () => createComparator(id)],
    [() => isVersion, () => createComparator(id)],
    [stubTrue, () => createComparator(id)],
  ])()

  // Dimension sort cần sort theo 3 fields: version, countryCode, date
  if (id === 'dimension') {
    return {
      fields: [
        // Version với semantic versioning
        (item: any) => {
          const version = item.version
          if (!version || !semver.valid(version)) {
            return '0.0.0'
          }
          return version
        },
        // Country code
        'countryCode',
        // Date
        'date',
      ],
      orders: [direction, direction, direction],
    }
  }

  return {
    fields: [fieldResolver],
    orders: [direction],
  }
}

/**
 * Sort collection với lodash orderBy (clean và performant)
 */
export const sortCollection = <T>(
  collection: T[],
  sortId: string,
  direction: 'asc' | 'desc'
): T[] => {
  if (isEmpty(sortId)) return collection

  const config = createSortConfig(sortId, direction)
  return orderBy(collection, config.fields as any, config.orders)
}

/**
 * Sort collection với sort state từ URL
 */
export const sortCollectionWithState = <T>(
  collection: T[],
  sortState: Array<{ id: string; desc: boolean }>
): T[] => {
  const sortItem = sortState[0]
  if (!sortItem?.id) return collection

  return sortCollection(collection, sortItem.id, sortItem.desc ? 'desc' : 'asc')
}

/**
 * Sort collection với multiple sort fields sử dụng lodash
 */
export const sortCollectionMulti = <T>(
  collection: T[],
  sortState: Array<{ id: string; desc: boolean }>
): T[] => {
  if (isEmpty(sortState)) return collection

  const fields = sortState.map((sort) => createComparator(sort.id))
  const orders = sortState.map((sort) => (sort.desc ? 'desc' : 'asc'))

  return orderBy(collection, fields as any, orders)
}

/**
 * Utility function để tạo reverse comparator
 */
export const createReverseComparator = (comparator: (item: any) => any) => {
  return flow([comparator, negate])
}

/**
 * Enhanced sort với custom comparator support
 */
export const sortWithCustomComparator = <T>(
  collection: T[],
  comparator: (a: T, b: T) => number,
  direction: 'asc' | 'desc' = 'asc'
): T[] => {
  const sorted = [...collection].sort(comparator)
  return direction === 'desc' ? sorted.reverse() : sorted
}

export const sortVariants = (
  a: FirebaseVersionVariantAttributesFragment,
  b: FirebaseVersionVariantAttributesFragment
) => {
  if (a.name === '0') return -1
  if (b.name === '0') return 1

  const aNum = parseInt(a.name)
  const bNum = parseInt(b.name)
  if (!isNaN(aNum) && !isNaN(bNum)) {
    return aNum - bNum
  }

  if (a.name === 'std') return 1
  if (b.name === 'std') return -1

  return a.name.localeCompare(b.name)
}
