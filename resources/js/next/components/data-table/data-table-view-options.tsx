'use client'

import type { Table } from '@tanstack/react-table'
import { Check, GripVertical, Settings2 } from 'lucide-react'
import * as React from 'react'

import { Button } from '@/next/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import {
  Sortable,
  SortableContent,
  SortableItem,
  SortableItemHandle,
  SortableOverlay,
} from '@/next/components/ui/sortable'
import { cn } from '@/next/lib/utils'

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>
}

export function DataTableViewOptions<TData>({ table }: DataTableViewOptionsProps<TData>) {
  const columns = React.useMemo(
    () =>
      table
        .getAllColumns()
        .filter((column) => typeof column.accessorFn !== 'undefined' && column.getCanHide()),
    [table]
  )

  const onColumnOrderChange = React.useCallback(
    (newOrder: string[]) => {
      table.setColumnOrder(newOrder)
    },
    [table.setColumnOrder]
  )

  const columnOrder = table.getState().columnOrder

  const onResetOrder = React.useCallback(() => {
    table.setColumnOrder(columns.map((column) => column.id))
  }, [columns, table.setColumnOrder])

  return (
    <Sortable value={columnOrder} onValueChange={onColumnOrderChange}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            aria-label="Toggle columns"
            role="combobox"
            variant="outline"
            size="sm"
            className="ml-auto hidden h-8 lg:flex"
          >
            <Settings2 />
            View
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="end"
          className="w-fit max-w-[320px] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-2.5 p-3"
        >
          <div className="flex flex-col gap-1 pb-2">
            <h4 className="font-medium leading-none">Column visibility</h4>
            <p className="text-muted-foreground text-sm">
              Drag to reorder columns or toggle visibility.
            </p>
          </div>

          <SortableContent asChild>
            <div role="list" className="flex max-h-[50vh] flex-col gap-1.5 overflow-y-auto p-0.5">
              {columnOrder.map((columnId) => {
                const column = columns.find((c) => c.id === columnId)
                if (!column) return null

                return <DataTableColumnItem key={column.id} column={column} />
              })}
            </div>
          </SortableContent>

          <div className="pt-2">
            <Button className="size-8 shrink-0 rounded w-full" onClick={onResetOrder}>
              Reset order
            </Button>
          </div>
        </PopoverContent>
      </Popover>
      <SortableOverlay>
        <div className="flex items-center gap-2">
          <div className="h-8 w-[180px] rounded-sm bg-primary/10" />
          <div className="size-8 shrink-0 rounded-sm bg-primary/10" />
          <div className="size-8 shrink-0 rounded-sm bg-primary/10" />
        </div>
      </SortableOverlay>
    </Sortable>
  )
}

interface DataTableColumnItemProps {
  column: any
}

function DataTableColumnItem({ column }: DataTableColumnItemProps) {
  const isVisible = column.getIsVisible()

  return (
    <SortableItem value={column.id} asChild>
      <div role="listitem" className={cn('flex items-center gap-2', !isVisible && 'opacity-50')}>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'flex-1 justify-between rounded font-normal min-w-0',
            !isVisible && 'text-muted-foreground'
          )}
          onClick={() => column.toggleVisibility(!isVisible)}
        >
          <span className="truncate">{column.columnDef.meta?.label ?? column.id}</span>
          <Check
            className={cn('ml-auto size-4 shrink-0', isVisible ? 'opacity-100' : 'opacity-0')}
          />
        </Button>
        <SortableItemHandle asChild>
          <Button variant="outline" size="icon" className="size-8 shrink-0 rounded">
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}
