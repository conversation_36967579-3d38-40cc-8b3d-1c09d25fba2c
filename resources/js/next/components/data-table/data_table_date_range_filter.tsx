import { Column } from '@tanstack/react-table'
import { useCallback } from 'react'
import { DateRange } from 'react-day-picker'
import { ToolbarFilterDateRange } from '../sdk/toolbar/toolbar_filter_date_range'
import { Table } from '@tanstack/react-table'

export function DataTableDateRangeFilter<TData>({
  column,
  title,
  table,
}: {
  column: Column<TData, unknown>
  title: string
  table: Table<TData>
}) {
  const columnFilterValue = column.getFilterValue() as (Date | null)[]

  const onSelect = useCallback(
    (date: DateRange | undefined) => {
      if (date) {
        column.setFilterValue([date.from || date.to!, date.to! || date.from!])
      }
    },
    [column]
  )

  const onReset = useCallback(() => {
    column.setFilterValue(table.initialState.columnFilters?.find((f) => f.id === column.id)?.value)
  }, [column.setFilterValue])

  return (
    <ToolbarFilterDateRange
      label={title}
      value={{
        from: columnFilterValue[0]!,
        to: columnFilterValue[1]!,
      }}
      onChange={onSelect}
      onReset={onReset}
    />
  )
}
