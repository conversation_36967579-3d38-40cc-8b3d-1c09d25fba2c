'use client'

import type { Table } from '@tanstack/react-table'
import { ChevronsUpDown, GripVertical, Layers, Trash2 } from 'lucide-react'
import * as React from 'react'

import { Badge } from '@/next/components/ui/badge'
import { Button } from '@/next/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/next/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import {
  Sortable,
  SortableContent,
  SortableItem,
  SortableItemHandle,
  SortableOverlay,
} from '@/next/components/ui/sortable'
import { cn } from '@/next/lib/utils'

const OPEN_MENU_SHORTCUT = 'g'
const REMOVE_GROUP_SHORTCUTS = ['backspace', 'delete']

interface DataTableGroupListProps<TData> extends React.ComponentProps<typeof PopoverContent> {
  table: Table<TData>
}

export function DataTableGroupList<TData>({ table, ...props }: DataTableGroupListProps<TData>) {
  const id = React.useId()
  const labelId = React.useId()
  const descriptionId = React.useId()
  const [open, setOpen] = React.useState(false)
  const addButtonRef = React.useRef<HTMLButtonElement>(null)

  const grouping = table.getState().grouping
  const onGroupingChange = table.setGrouping

  // Set default grouping if not already set
  React.useEffect(() => {
    const currentGrouping = table.getState().grouping
    const initialGrouping = table.initialState.grouping || []

    // Only set initial grouping if we don't have any current grouping
    if (currentGrouping.length === 0 && initialGrouping.length > 0) {
      onGroupingChange(initialGrouping)
    }
  }, [table, onGroupingChange])

  const { columnLabels, columns } = React.useMemo(() => {
    const labels = new Map<string, string>()
    const groupingIds = new Set(grouping)
    const availableColumns: { id: string; label: string }[] = []

    for (const column of table.getAllColumns()) {
      if (!column.getCanGroup() || column.columnDef.enableGrouping !== true) continue

      const label = column.columnDef.meta?.label ?? column.id
      labels.set(column.id, label)

      if (!groupingIds.has(column.id)) {
        availableColumns.push({ id: column.id, label })
      }
    }

    return {
      columnLabels: labels,
      columns: availableColumns,
    }
  }, [grouping, table])

  const onGroupAdd = React.useCallback(() => {
    const firstColumn = columns[0]
    if (!firstColumn) return

    onGroupingChange((prevGrouping) => [...prevGrouping, firstColumn.id])
  }, [columns, onGroupingChange])

  const onGroupRemove = React.useCallback(
    (groupId: string) => {
      onGroupingChange((prevGrouping) => prevGrouping.filter((id) => id !== groupId))
    },
    [onGroupingChange]
  )

  const onGroupReplace = React.useCallback(
    (oldGroupId: string, newGroupId: string) => {
      if (oldGroupId === newGroupId) return

      onGroupingChange((prevGrouping) => {
        const index = prevGrouping.indexOf(oldGroupId)
        if (index === -1) return prevGrouping

        if (prevGrouping.includes(newGroupId)) {
          return prevGrouping.filter((id) => id !== oldGroupId)
        }

        const newGrouping = [...prevGrouping]
        newGrouping[index] = newGroupId
        return newGrouping
      })
    },
    [onGroupingChange]
  )

  const onGroupingReset = React.useCallback(
    () => onGroupingChange(table.initialState.grouping || []),
    [onGroupingChange, table.initialState.grouping]
  )

  // Track if we're currently processing a state update
  const isProcessingRef = React.useRef(false)

  React.useEffect(() => {
    function onKeyDown(event: KeyboardEvent) {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      if (
        event.key.toLowerCase() === OPEN_MENU_SHORTCUT &&
        !event.ctrlKey &&
        !event.metaKey &&
        !event.shiftKey
      ) {
        event.preventDefault()
        setOpen(true)
      }

      if (event.key.toLowerCase() === OPEN_MENU_SHORTCUT && event.shiftKey && grouping.length > 0) {
        event.preventDefault()
        onGroupingReset()
      }
    }

    window.addEventListener('keydown', onKeyDown)
    return () => window.removeEventListener('keydown', onKeyDown)
  }, [grouping.length, onGroupingReset])

  const onTriggerKeyDown = React.useCallback(
    (event: React.KeyboardEvent<HTMLButtonElement>) => {
      if (REMOVE_GROUP_SHORTCUTS.includes(event.key.toLowerCase()) && grouping.length > 0) {
        event.preventDefault()
        onGroupingReset()
      }
    },
    [grouping.length, onGroupingReset]
  )

  // Reset expanded state when grouping changes to prevent conflicts
  React.useEffect(() => {
    // Skip if we're already processing an update
    if (isProcessingRef.current) return

    // Only reset if we have grouping and expanded state
    if (grouping.length > 0 && Object.keys(table.getState().expanded).length > 0) {
      isProcessingRef.current = true

      // Use setTimeout to break the synchronous update cycle
      setTimeout(() => {
        table.resetExpanded()
        isProcessingRef.current = false
      }, 0)
    }
  }, [grouping, table])

  return (
    <Sortable value={grouping} onValueChange={onGroupingChange}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" onKeyDown={onTriggerKeyDown} className="h-8 px-2.5">
            <Layers className="size-4 mr-1" />
            Group
            {grouping.length > 0 && (
              <Badge
                variant="secondary"
                className="ml-1 h-[16px] rounded-[3px] px-[4px] font-mono font-normal text-[10px]"
              >
                {grouping.length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          aria-labelledby={labelId}
          aria-describedby={descriptionId}
          className="flex w-fit max-w-[320px] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-2.5 p-3"
          {...props}
        >
          <div className="flex flex-col gap-1">
            <h4 id={labelId} className="font-medium leading-none">
              {grouping.length > 0 ? 'Group by' : 'No grouping applied'}
            </h4>
            <p
              id={descriptionId}
              className={cn('text-muted-foreground text-sm', grouping.length > 0 && 'sr-only')}
            >
              {grouping.length > 0
                ? 'Modify grouping to organize your rows.'
                : 'Add grouping to organize your rows.'}
            </p>
          </div>
          {grouping.length > 0 && (
            <SortableContent asChild>
              <div
                role="list"
                className="flex max-h-[250px] flex-col gap-1.5 overflow-y-auto p-0.5"
              >
                {grouping.map((groupId) => (
                  <DataTableGroupItem
                    key={groupId}
                    groupId={groupId}
                    groupItemId={`${id}-group-${groupId}`}
                    columns={columns}
                    columnLabels={columnLabels}
                    onGroupRemove={onGroupRemove}
                    onGroupReplace={onGroupReplace}
                  />
                ))}
              </div>
            </SortableContent>
          )}
          <div className="flex w-full items-center gap-2">
            <Button
              size="sm"
              className="rounded"
              ref={addButtonRef}
              onClick={onGroupAdd}
              disabled={columns.length === 0}
            >
              Add group
            </Button>
            {grouping.length > 0 && (
              <Button variant="outline" size="sm" className="rounded" onClick={onGroupingReset}>
                Reset
              </Button>
            )}
          </div>
        </PopoverContent>
      </Popover>
      <SortableOverlay>
        <div className="flex items-center gap-2">
          <div className="h-8 w-[180px] rounded-sm bg-primary/10" />
          <div className="size-8 shrink-0 rounded-sm bg-primary/10" />
          <div className="size-8 shrink-0 rounded-sm bg-primary/10" />
        </div>
      </SortableOverlay>
    </Sortable>
  )
}

interface DataTableGroupItemProps {
  groupId: string
  groupItemId: string
  columns: { id: string; label: string }[]
  columnLabels: Map<string, string>
  onGroupRemove: (groupId: string) => void
  onGroupReplace: (oldGroupId: string, newGroupId: string) => void
}

function DataTableGroupItem({
  groupId,
  groupItemId,
  columns,
  columnLabels,
  onGroupRemove,
  onGroupReplace,
}: DataTableGroupItemProps) {
  const fieldListboxId = `${groupItemId}-field-listbox`
  const fieldTriggerId = `${groupItemId}-field-trigger`

  const [showFieldSelector, setShowFieldSelector] = React.useState(false)

  const onItemKeyDown = React.useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      if (showFieldSelector) {
        return
      }

      if (REMOVE_GROUP_SHORTCUTS.includes(event.key.toLowerCase())) {
        event.preventDefault()
        onGroupRemove(groupId)
      }
    },
    [groupId, showFieldSelector, onGroupRemove]
  )

  return (
    <SortableItem value={groupId} asChild>
      <div
        role="listitem"
        id={groupItemId}
        tabIndex={-1}
        className="flex items-center gap-2"
        onKeyDown={onItemKeyDown}
      >
        <Popover open={showFieldSelector} onOpenChange={setShowFieldSelector}>
          <PopoverTrigger asChild>
            <Button
              id={fieldTriggerId}
              role="combobox"
              aria-controls={fieldListboxId}
              variant="outline"
              size="sm"
              className="w-44 justify-between rounded font-normal"
            >
              <span className="truncate">{columnLabels.get(groupId)}</span>
              <ChevronsUpDown className="opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            id={fieldListboxId}
            className="w-[var(--radix-popover-trigger-width)] origin-[var(--radix-popover-content-transform-origin)] p-0"
          >
            <Command>
              <CommandInput placeholder="Search fields..." />
              <CommandList>
                <CommandEmpty>No fields found.</CommandEmpty>
                <CommandGroup>
                  {columns.map((column) => (
                    <CommandItem
                      key={column.id}
                      value={column.id}
                      onSelect={(value) => {
                        onGroupReplace(groupId, value)
                      }}
                    >
                      <span className="truncate">{column.label}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Button
          aria-controls={groupItemId}
          variant="outline"
          size="icon"
          className="size-8 shrink-0 rounded"
          onClick={() => onGroupRemove(groupId)}
        >
          <Trash2 />
        </Button>
        <SortableItemHandle asChild>
          <Button variant="outline" size="icon" className="size-8 shrink-0 rounded">
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}
