import * as React from 'react'
import { XCircle } from 'lucide-react'
import { cn } from '@/next/lib/utils'
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/next/components/ui/sidebar'

export interface NavErrorProps extends React.ComponentPropsWithoutRef<typeof SidebarGroup> {
  className?: string
  onRefetch?: () => void
}

export function NavError({ className, onRefetch, ...props }: NavErrorProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    onRefetch?.()
  }

  return (
    <SidebarGroup className={cn(className)} {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton onClick={handleClick} tooltip="Retry loading menu">
              <XCircle className="h-4 w-4 text-destructive" />
              <span className="text-sm text-muted-foreground">Failed to get main menu</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
