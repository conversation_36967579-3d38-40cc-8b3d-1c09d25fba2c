import { useTranslation } from 'react-i18next'
import { MetabaseFilterParamDef, UseMetabaseFilterFormReturn } from './use_metabase_filter_form'
import { ToolbarFilterFacet } from '../sdk/toolbar/toolbar_filter_facet'
import { useMemo } from 'react'
import { thru } from 'lodash-es'

export function MetabaseFilterMultiSelect({
  form,
  param,
  sort = true,
}: {
  form: UseMetabaseFilterFormReturn
  param: MetabaseFilterParamDef
  sort?: boolean
}) {
  const { t } = useTranslation()

  const onSelect = (value: string[]) => {
    form.setQueryValues({
      [param.param]: value,
    })
  }

  const onReset = () => form.setQueryValues({ [param.param]: null })

  const values = form.queryValues[param.id] as string[]

  const options = useMemo(() => {
    return thru(
      form.configMap.params.find((p) => p.name === param.id)!.value.split(','),
      (items) => {
        if (sort) {
          items.sort()
        }

        return items.map((v) => ({
          value: v,
          label: t(`metabaseChart.${form.configMap.id}.enum.${param.id}.${v}`, {
            defaultValue: v,
          }),
        }))
      }
    )
    // TODO: Make sort configurable
  }, [form.configMap, t, param.id, sort])

  return (
    <ToolbarFilterFacet
      label={t(`metabaseChart.${form.configMap.id}.attribute.${param.id}`)}
      multiple
      values={values}
      onChange={onSelect}
      onReset={onReset}
      options={options}
    />
  )
}
