import { ComponentProps, memo } from 'react'
import { Crash } from '../sdk/loading/crash'
import { DataTableFilterSubmitButton } from '../sdk/data_table'
import { MetabaseFilterTimeGrouping } from './metabase_filter_time_grouping'
import { MetabaseFilterDateRange } from './metabase_filter_date_range'
import { Form } from '@/next/components/ui/form'
import { MetabaseFilterParamDef, UseMetabaseFilterFormReturn } from './use_metabase_filter_form'
import { MetabaseFilterMultiSelect } from './metabase_filter_multi_select'

export interface MetabaseChartProps {
  url: string
  form: UseMetabaseFilterFormReturn
}

export function MetabaseChart({
  url,
  form,
  children,
  ...props
}: MetabaseChartProps & ComponentProps<'div'>) {
  const { onSubmit, paramDefs } = form

  if (!url) {
    return (
      <Crash error>
        <p>Chart cannot be loaded</p>
      </Crash>
    )
  }

  return (
    <div {...props}>
      <Form {...form}>
        <form className="flex flex-warp justify-between gap-2" onSubmit={onSubmit}>
          <div className="flex flex-wrap gap-2">
            <DataTableFilterSubmitButton type="submit" />

            {paramDefs.map((param) => (
              <MetabaseFilter form={form} param={param} key={param.id} />
            ))}
          </div>
          <div>{children}</div>
        </form>
      </Form>

      <MetabaseChartIframe url={url} key={url} />
    </div>
  )
}

export const MetabaseChartIframe = memo(function ({ url }: { url: string }) {
  return (
    <div
      dangerouslySetInnerHTML={{
        __html: `<iframe src="${url}" width="100%" onload="iFrameResize({}, this)"></iframe>`,
      }}
    />
  )
})

export function MetabaseFilter({
  form,
  param,
}: {
  form: UseMetabaseFilterFormReturn
  param: MetabaseFilterParamDef
}) {
  switch (true) {
    case param.configMap.type === 'date-range':
      return <MetabaseFilterDateRange param={param} form={form} />

    case param.configMap.type === 'time-grouping':
      return <MetabaseFilterTimeGrouping param={param} form={form} />

    case param.configMap.type.startsWith('multi-select'):
      return <MetabaseFilterMultiSelect param={param} form={form} />

    default:
      return null
  }
}
