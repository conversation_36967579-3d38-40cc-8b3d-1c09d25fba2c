import { DateRange } from 'react-day-picker'
import { MetabaseFilterParamDef, UseMetabaseFilterFormReturn } from './use_metabase_filter_form'
import { useTranslation } from 'react-i18next'
import { ToolbarFilterDateRange } from '../sdk/toolbar/toolbar_filter_date_range'

export function MetabaseFilterDateRange({
  param,
  form,
}: {
  param: MetabaseFilterParamDef
  form: UseMetabaseFilterFormReturn
}) {
  const { t } = useTranslation()

  const values = form.queryValues[param.id] as [Date, Date]

  const onSelect = (value: DateRange | undefined) => {
    const from = value?.from
    const to = value?.to

    form.setQueryValues({
      [param.param]: [from || values[0], to || values[1]],
    })
  }

  const onReset = () => form.setQueryValues({ [param.param]: null })

  return (
    <ToolbarFilterDateRange
      label={t(`metabaseChart.${form.configMap.id}.attribute.${param.id}`)}
      value={{ from: values[0], to: values[1] }}
      onChange={onSelect}
      onReset={onReset}
    />
  )
}
