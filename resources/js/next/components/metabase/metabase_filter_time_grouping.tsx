import { MetabaseFilterParamDef, UseMetabaseFilterFormReturn } from './use_metabase_filter_form'
import { useTranslation } from 'react-i18next'
import { ToolbarFilterFacet } from '../sdk/toolbar/toolbar_filter_facet'

const options = [
  { value: 'day', label: 'Day' },
  { value: 'week', label: 'Week' },
  { value: 'month', label: 'Month' },
  { value: 'quarter', label: 'Quarter' },
  { value: 'year', label: 'Year' },
]

export function MetabaseFilterTimeGrouping({
  param,
  form,
}: {
  param: MetabaseFilterParamDef
  form: UseMetabaseFilterFormReturn
}) {
  const { t } = useTranslation()

  const onSelect = (value: string) => {
    form.setQueryValues({
      [param.param]: value,
    })
  }

  const onReset = () => form.setQueryValues({ [param.param]: null })

  const value = form.queryValues[param.id] as string

  return (
    <ToolbarFilterFacet
      label={t(`metabaseChart.${form.configMap.id}.attribute.${param.id}`)}
      multiple={false}
      value={value}
      onChange={onSelect}
      onReset={onReset}
      options={options}
    />
  )
}
