import { useMemo } from 'react'
import { UseMetabaseFilterFormReturn } from './use_metabase_filter_form'
import { MetabaseChartParam } from '@/graphql'
import { format } from 'date-fns'

export function useMetabaseParams(form: UseMetabaseFilterFormReturn, extra: Record<string, any>) {
  const values = form.watch()

  const params = useMemo(() => {
    return form.configMap.params.map((param) => {
      if (param.type.startsWith('extra')) {
        const [_, key] = param.type.split('#')
        return {
          name: param.name,
          value: extra[key],
        } as MetabaseChartParam
      }

      if (param.type === 'date-range') {
        const [from, to] = values[param.name] as [Date, Date]
        return {
          name: param.name,
          value: [format(from, 'yyyy-MM-dd'), format(to, 'yyyy-MM-dd')].join('~'),
        } as MetabaseChartParam
      }

      return {
        name: param.name,
        value: values[param.name],
      } as MetabaseChartParam
    })
  }, [values, form.configMap, JSON.stringify(extra)])

  return params
}
