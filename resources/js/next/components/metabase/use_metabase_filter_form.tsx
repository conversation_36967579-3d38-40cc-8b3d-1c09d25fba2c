import { useMemo, BaseSyntheticEvent } from 'react'
import { FieldValues, useForm, UseFormReturn } from 'react-hook-form'
import { fromPairs } from 'lodash-es'
import { parseAsString, useQueryStates, ParserBuilder, parseAsArrayOf } from 'nuqs'
import { subDays } from 'date-fns'

import type DashboardMetabaseChartConfigMap from '#configmaps/dashboard/metabase_chart'
import { parseAsDateRange } from '../sdk/nuqs/parse_as_date_range'
import { DashboardMetabaseChartParamConfigMap } from '#configmaps/dashboard/metabase_chart'

export type MetabaseFilterParamDef = {
  id: string
  param: string
  parser: ParserBuilder<any>
  onChange: (value: any) => void
  configMap: DashboardMetabaseChartParamConfigMap
}

export type UseMetabaseFilterFormReturn<TValues extends FieldValues = Record<string, any>> =
  UseFormReturn<TValues> & {
    onSubmit: (e?: BaseSyntheticEvent<object, any, any>) => any
    configMap: DashboardMetabaseChartConfigMap
    setQueryValues: (values: Record<string, any>) => void
    queryValues: Record<string, any>
    paramDefs: Array<MetabaseFilterParamDef>
  }

export function useMetabaseFilterForm({
  configMap,
}: {
  configMap: DashboardMetabaseChartConfigMap
}): UseMetabaseFilterFormReturn<Record<string, any>> {
  const paramDefs = useMemo(() => {
    return configMap.params
      .filter((p) => !p.type.startsWith('extra'))
      .map((p) => {
        let parser: ParserBuilder<any> = parseAsString

        switch (true) {
          case p.type === 'date-range':
            parser = parseAsDateRange.withDefault([subDays(new Date(), 30), new Date()])

            break

          case p.type === 'time-grouping':
            parser = parseAsString.withDefault(p.defaultValue || 'day')
            break

          case p.type === 'multi-select':
            const defaultValues =
              p.defaultValue === '_all' ? p.value.split(',') : p.defaultValue.split(',')

            parser = parseAsArrayOf(parseAsString).withDefault(defaultValues)
            break

          default:
            break
        }

        return { id: p.name, param: `${configMap.id}-${p.name}`, parser }
      })
  }, [configMap])

  const [queryValues, setQueryValues] = useQueryStates(
    fromPairs(
      paramDefs
        .filter((paramDef) => paramDef.param)
        .map((paramDef) => [paramDef.param, paramDef.parser!])
    )
  )

  const form = useForm({
    defaultValues: useMemo(
      () => fromPairs(paramDefs.map((p) => [p.id, queryValues[p.param]])),
      [paramDefs]
    ),
  })

  const onSubmit = form.handleSubmit(() => {
    paramDefs.forEach((paramDef) => {
      form.setValue(paramDef.id, queryValues[paramDef.param])
    })
  })

  const exposedQueryValues = useMemo(() => {
    return fromPairs(paramDefs.map((paramDef) => [paramDef.id, queryValues[paramDef.param]]))
  }, [queryValues, paramDefs])

  const exposedParamDefs = useMemo(() => {
    return paramDefs.map((p) => ({
      ...p,
      onChange: (value: any) => setQueryValues({ [p.param]: value }),
      configMap: configMap.params.find((pr) => pr.name === p.id)!,
    }))
  }, [paramDefs, configMap])

  return {
    ...form,
    onSubmit,
    configMap,
    setQueryValues,
    queryValues: exposedQueryValues,
    paramDefs: exposedParamDefs,
  }
}
