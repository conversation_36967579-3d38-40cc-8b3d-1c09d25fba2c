import { Button } from '@/next/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { ReactNode, useState } from 'react'

interface ConfirmationPopoverProps {
  isSubmitting?: boolean
  onConfirm: () => void
  trigger: ReactNode
  title?: ReactNode
  submitText?: string
  cancelText?: string
  confirmText: string
}

export function ConfirmationPopover({
  title = 'Confirm your action',
  isSubmitting = false,
  onConfirm,
  trigger,
  submitText = 'Confirm',
  cancelText = 'Cancel',
  confirmText,
}: Readonly<ConfirmationPopoverProps>) {
  const [open, setOpen] = useState(false)

  const handleConfirm = () => {
    onConfirm()
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="flex flex-col gap-3">
          <div className="text-sm font-medium">{title}</div>
          <p className="text-sm text-muted-foreground">{confirmText}</p>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              {cancelText}
            </Button>
            <Button variant="destructive" size="sm" onClick={handleConfirm} disabled={isSubmitting}>
              {isSubmitting ? 'Loading...' : submitText}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
