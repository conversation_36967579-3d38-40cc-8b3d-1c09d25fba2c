import { toast } from 'sonner'

export type ToastType = 'success' | 'error' | 'info' | 'warning' | 'default'

export interface ToastStyle {
  bgColor: string
  textColor: string
  borderColor: string
}

export interface BaseToastProps {
  title?: string
  message: string
  duration?: number
}

export const createToast = (
  type: ToastType,
  style: ToastStyle,
  { title = type.charAt(0).toUpperCase() + type.slice(1), message, duration = 3000 }: BaseToastProps
) => {
  const toastFn = type === 'default' ? toast : (toast[type] as typeof toast)

  toastFn(message, {
    duration,
    className: style.bgColor,
    description: title !== message ? title : undefined,
    style: {
      color: style.textColor,
      border: `1px solid ${style.borderColor}`,
    },
  })
}
