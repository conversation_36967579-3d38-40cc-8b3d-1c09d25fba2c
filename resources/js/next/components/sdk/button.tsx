import { Button } from '@/next/components/ui/button'
import { Loader2 } from 'lucide-react'
import { ComponentProps } from 'react'

export function IconButton({
  loading,
  children,
  disabled,
  icon,
  ...props
}: { loading?: boolean; disabled?: boolean; icon?: React.ReactNode } & ComponentProps<
  typeof Button
>) {
  return (
    <Button {...props} disabled={loading || disabled}>
      {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : icon}
      {children}
    </Button>
  )
}
