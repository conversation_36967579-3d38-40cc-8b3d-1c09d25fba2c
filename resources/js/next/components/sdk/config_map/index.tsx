import { ConfigMapBindings } from '@munkit/main'

import { graphql } from '@/gql'
import { ApolloClient, QueryResult, useApolloClient } from '@apollo/client'
import { configMapCollectionAttributesFragment, GetConfigMapsQuery } from '@/graphql'
import { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@/next/components/sdk/graphql'
import { create } from 'zustand'
import { uniq } from 'lodash-es'

export const getConfigMapsQuery = graphql(`
  query GetConfigMaps($ids: [ID!]!) {
    configMapCollections(ids: $ids) {
      ...ConfigMapCollectionAttributes
    }
  }
`)

type ConfigMapCollectionId = keyof ConfigMapBindings | { id: keyof ConfigMapBindings; sole: true }

type UseConfigMapResult<TIds extends ConfigMapCollectionId[] | []> = {
  [P in keyof TIds]: TIds[P] extends { id: infer U; sole: true }
    ? U extends keyof ConfigMapBindings
      ? ConfigMapBindings[U][0]
      : unknown
    : TIds[P] extends keyof ConfigMapBindings
      ? ConfigMapBindings[TIds[P]]
      : unknown
}

interface ConfigMapStore {
  ids: string[]
  pendingIds: string[]
  queueIds: (ids: ConfigMapCollectionId[]) => void
  flushPendingIds: () => void
  query: QueryResult<GetConfigMapsQuery, { ids: string[] }>
  setQuery: (query: Partial<QueryResult<GetConfigMapsQuery, { ids: string[] }>>) => void
}

const useConfigMapStore = create<ConfigMapStore>((set) => ({
  ids: [],
  pendingIds: [],
  queueIds: (ids) => {
    set((s) => ({
      pendingIds: uniq([...s.pendingIds, ...ids.map((id) => getConfigMapCollectionId(id))]),
    }))
  },
  flushPendingIds: () => {
    set((s) => {
      const newIds = uniq([...s.ids, ...s.pendingIds])
      return { ids: newIds, pendingIds: [] }
    })
  },
  query: {
    loading: true,
    called: true,
    error: null,
  } as any,
  setQuery: (query) =>
    set((s) => ({
      query: {
        ...s.query,
        ...query,
      },
    })),
}))

export function ConfigMapProvider() {
  const { ids, setQuery, flushPendingIds, pendingIds } = useConfigMapStore()
  const apolloClient = useApolloClient()

  // Batching effect: flush pendingIds to ids every 300ms
  useEffect(() => {
    if (pendingIds.length === 0) {
      return
    }

    const interval = setInterval(() => {
      flushPendingIds()
    }, 300)

    return () => clearInterval(interval)
  }, [pendingIds, flushPendingIds])

  const idsToFetch = ids.filter((id) => !Boolean(getConfigMapCollectionFragment(apolloClient, id)))

  const getConfigMaps = useQuery(
    getConfigMapsQuery,
    {
      ids: uniq(idsToFetch),
    },
    { skip: idsToFetch.length === 0 }
  )

  useEffect(() => {
    if (getConfigMaps.loading) {
      // console.log('fetching config maps')
    }
  }, [getConfigMaps.loading])

  useEffect(() => {
    setQuery({
      loading: getConfigMaps.loading,
      error: getConfigMaps.error,
      data: getConfigMaps.data,
    })
  }, [getConfigMaps, setQuery])

  return null
}

export function useConfigMap<
  TIds extends Array<keyof ConfigMapBindings | { id: keyof ConfigMapBindings; sole: true }> | [],
>(ids: TIds): [UseConfigMapResult<TIds>, QueryResult<GetConfigMapsQuery, { ids: string[] }>] {
  const apolloClient = useApolloClient()

  const [isFulfilled, setIsFullied] = useState(
    useMemo(
      () => ids.every((id) => getConfigMapCollectionFragment(apolloClient, id)),
      [apolloClient, ids]
    )
  )

  const { queueIds, query } = useConfigMapStore()

  useEffect(() => {
    if (!isFulfilled) {
      queueIds(ids)
    }
  }, [ids, isFulfilled, queueIds])

  useEffect(() => {
    if (isFulfilled) {
      return
    }

    if (ids.every((id) => getConfigMapCollectionFragment(apolloClient, id))) {
      setIsFullied(true)
    }
  }, [ids, isFulfilled, setIsFullied, query, apolloClient])

  const collections = useMemo(() => {
    if (isFulfilled) {
      console.log('resolve config map', ids)
      return ids.map((id) => getConfigMapCollectionFragment(apolloClient, id)!.items)
    }

    return new Array(ids.length).fill(0).map(() => [])
  }, [isFulfilled, apolloClient, ids])

  const queryState = isFulfilled
    ? { loading: false, called: true }
    : { called: true, loading: true }

  return [collections as any, queryState as any]
}

function getConfigMapCollectionId(id: ConfigMapCollectionId | string) {
  return typeof id === 'string' ? id : id.id
}

function getConfigMapCollectionFragment(
  client: ApolloClient<object>,
  id: ConfigMapCollectionId | string
) {
  return client.readFragment({
    id: `ConfigMapCollection:${getConfigMapCollectionId(id)}`,
    fragmentName: 'ConfigMapCollectionAttributes',
    fragment: configMapCollectionAttributesFragment,
  })
}
