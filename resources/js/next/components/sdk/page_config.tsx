import { Head } from '@inertiajs/react'
import { BreadcrumbItemProp, useBreadcrumbs } from '@/next/components/sdk/breadcrumbs'
import { NavMainItem } from '@/next/components/nav-main'
import { useSubSidebar } from '@/next/components/sdk/layout/use_sub_sidebar'

export function PageConfig({
  title,
  breadcrumbs,
  sidebarSubItems,
  metabase,
}: {
  title: string
  breadcrumbs: BreadcrumbItemProp[]
  sidebarSubItems?: NavMainItem[]
  metabase?: boolean
}) {
  useBreadcrumbs(breadcrumbs)

  useSubSidebar(sidebarSubItems)

  // Prevent render inertia components on storybook due to missing inertia context
  return (
    <>
      {title && (
        <Head title={title}>
          {metabase && <script src="https://metabase.miraistudio.games/app/iframeResizer.js" />}
        </Head>
      )}
    </>
  )
}
