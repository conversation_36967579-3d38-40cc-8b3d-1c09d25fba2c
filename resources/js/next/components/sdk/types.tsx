import { UseQueryStateReturn } from 'nuqs'
import React from 'react'

export type ElementProps = React.PropsWithChildren & React.HTMLProps<HTMLElement>

export type ReactNode =
  | React.ReactNode
  | string
  | React.ReactNode[]
  | React.ReactElement<any, any>
  | any

export type AnyHandler = () => any

export type InferQueryStateType<
  TFunc extends () => TReturn,
  TReturn extends UseQueryStateReturn<any, any> = [any, any],
> = ReturnType<TFunc>[0]
