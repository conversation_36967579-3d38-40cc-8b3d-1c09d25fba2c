import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { Button } from '@/next/components/ui/button'
import { XCircle } from 'lucide-react'
import { Check, PlusCircle } from 'lucide-react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/next/components/ui/command'
import { Separator } from '@/next/components/ui/separator'
import { Badge } from '@/next/components/ui/badge'
import { cn } from '@/next/lib/utils'
import * as React from 'react'

export function ToolbarFilterFacet({
  label,
  onChange,
  onReset,
  options,
  multiple,
  value,
  values,
}: {
  label: string
  onReset: () => void
  options: { value: string; label: string }[]
} & (
  | {
      multiple: true
      values: string[]
      value?: never
      onChange: (value: string[]) => void
    }
  | {
      multiple: false
      value: string
      values?: never
      onChange: (value: string) => void
    }
)) {
  const [open, setOpen] = React.useState(false)

  const selectedValues = multiple ? values : [value]
  const hasValue = selectedValues.length > 0
  const selectedValuesSet = new Set(selectedValues)

  const selectedOptions = options.filter((option) => selectedValuesSet.has(option.value))

  const onItemSelect = React.useCallback(
    (option: { value: string; label: string }, isSelected: boolean) => {
      if (multiple) {
        const newSelectedValues = new Set(selectedValuesSet)
        if (isSelected) {
          newSelectedValues.delete(option.value)
        } else {
          newSelectedValues.add(option.value)
        }
        const filterValues = Array.from(newSelectedValues)
        onChange(filterValues)
      } else {
        onChange(isSelected ? '' : option.value)
        setOpen(false)
      }
    },
    [multiple, selectedValuesSet, onChange]
  )

  const handleReset = React.useCallback(
    (event?: React.MouseEvent) => {
      event?.stopPropagation()
      onReset()
    },
    [onReset]
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="border-dashed">
          {hasValue ? (
            <div
              role="button"
              aria-label={`Clear ${label}filter`}
              tabIndex={0}
              onClick={handleReset}
              className="rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            >
              <XCircle />
            </div>
          ) : (
            <PlusCircle />
          )}
          {label}
          {hasValue && (
            <>
              <Separator
                orientation="vertical"
                className="mx-0.5 data-[orientation=vertical]:h-4"
              />
              <Badge variant="secondary" className="rounded-sm px-1 font-normal lg:hidden">
                {selectedOptions.length}
              </Badge>
              <div className="hidden items-center gap-1 lg:flex">
                {selectedOptions.length > 2 ? (
                  <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                    {selectedOptions.length} selected
                  </Badge>
                ) : (
                  selectedOptions.map((option) => (
                    <Badge
                      variant="secondary"
                      key={option.value}
                      className="rounded-sm px-1 font-normal"
                    >
                      {option.label}
                    </Badge>
                  ))
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[12.5rem] p-0" align="start">
        <Command>
          <CommandInput placeholder={label} />
          <CommandList className="max-h-full">
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup className="max-h-[18.75rem] overflow-y-auto overflow-x-hidden">
              {options.map((option) => {
                const isSelected = selectedValuesSet.has(option.value)

                return (
                  <CommandItem key={option.value} onSelect={() => onItemSelect(option, isSelected)}>
                    <div
                      className={cn(
                        'flex size-4 items-center justify-center rounded-sm border border-primary',
                        isSelected ? 'bg-primary' : 'opacity-50 [&_svg]:invisible'
                      )}
                    >
                      <Check />
                    </div>
                    <span className="truncate">{option.label}</span>
                  </CommandItem>
                )
              })}
            </CommandGroup>
            {hasValue && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => handleReset()}
                    className="justify-center text-center"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
