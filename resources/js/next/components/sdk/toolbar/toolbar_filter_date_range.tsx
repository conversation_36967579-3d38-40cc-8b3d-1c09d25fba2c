import { DateRange } from 'react-day-picker'
import { Button } from '../../ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '../../ui/popover'
import { isValid } from 'date-fns'
import { CalendarIcon, XCircle } from 'lucide-react'
import { Separator } from '../../ui/separator'
import { DateRangePickerShortcuts } from '@/next/components/sdk/form/date_range_picker_shortcuts'
import { Calendar } from '../../ui/calendar'
import { useCallback } from 'react'
import { formatDate } from '@/next/lib/format'

export function ToolbarFilterDateRange({
  onChange,
  onReset,
  value,
  label,
}: {
  onChange: (value: DateRange | undefined) => void
  onReset: () => void
  value: DateRange
  label: string
}) {
  const hasValue = [value.from, value.to].every((v) => isValid(v))

  const formatDateRange = useCallback((range: DateRange) => {
    if (!range.from && !range.to) return ''
    if (range.from && range.to) {
      return `${formatDate(range.from)} - ${formatDate(range.to)}`
    }
    return formatDate(range.from ?? range.to)
  }, [])

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="border-dashed">
          {hasValue ? (
            <div
              role="button"
              aria-label={`Clear ${label} filter`}
              tabIndex={0}
              onClick={onReset}
              className="rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            >
              <XCircle />
            </div>
          ) : (
            <CalendarIcon />
          )}
          <span className="flex items-center gap-2">
            <span>{label}</span>
            {hasValue && (
              <>
                <Separator
                  orientation="vertical"
                  className="mx-0.5 data-[orientation=vertical]:h-4"
                />
                <span>{formatDateRange(value)}</span>
              </>
            )}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-2">
          <DateRangePickerShortcuts onValueChange={(value) => onChange(value)} />
        </div>

        <Calendar
          initialFocus
          mode="range"
          selected={value}
          onSelect={(value) => onChange(value)}
        />
      </PopoverContent>
    </Popover>
  )
}
