import { Apple, Smartphone } from 'lucide-react'
import { cn } from '@/next/lib/utils'

export interface PlatformIconProps {
  platform: string
  size?: number
  className?: string
  colorClassName?: string
}

export function PlatformIcon({
  platform,
  size = 4,
  className,
  colorClassName = 'text-muted-foreground',
}: PlatformIconProps) {
  const iconProps = {
    className: cn(`h-${size} w-${size}`, colorClassName, className),
  }

  return platform.toLowerCase() === 'ios' ? <Apple {...iconProps} /> : <Smartphone {...iconProps} />
}
