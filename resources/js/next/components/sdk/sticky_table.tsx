import { isEmpty } from 'lodash-es'
import { ComponentProps, DependencyList, useLayoutEffect, useRef } from 'react'
// @ts-ignore lib
import { StickyTableHeader } from 'vh-sticky-table-header'
import { Table } from '../ui/table'
import { Root, Slottable } from '@radix-ui/react-slot'
import { cn } from '@/next/lib/utils'

export function useStickyTable(
  { enabled, ...options }: { enabled: boolean; [key: string]: any },
  deps: DependencyList
) {
  const tableRef = useRef<HTMLTableElement>(null)
  const tableCloneRef = useRef<HTMLTableElement>(null)

  useLayoutEffect(() => {
    if (!enabled) {
      return
    }

    if (tableRef.current && tableCloneRef.current) {
      // Initialize the sticky header.
      const sticky = new StickyTableHeader(
        tableRef.current,
        tableCloneRef.current,
        isEmpty(options) ? undefined : options
      )
      // Make sure to destory the sticky header once the main table is unmounted.
      return () => sticky.destroy()
    }
  }, [...deps, enabled])

  return [tableRef, tableCloneRef]
}

export interface StickyTableProps extends ComponentProps<'div'> {
  sticky: boolean
  watch: DependencyList
  options?: any
}

export function StickyTable({
  sticky,
  watch,
  options = {},
  children,
  className,
}: StickyTableProps) {
  const [tableRef, tableCloneRef] = useStickyTable({ enabled: sticky, ...options }, watch)

  return (
    <>
      <div
        className={cn(className, {
          'overflow-x-auto overflow-y-hidden': sticky,
          'overflow-auto': !sticky,
        })}
      >
        <Root ref={tableRef}>
          <Slottable>{children}</Slottable>
        </Root>
      </div>

      {sticky && (
        <div className="w-full overflow-x-auto overflow-y-hidden z-100">
          <Table ref={tableCloneRef} />
        </div>
      )}
    </>
  )
}
