import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/next/components/ui/form'
import { Control, FieldValues, Path } from 'react-hook-form'
import { Input } from '@/next/components/ui/input'
import { Button } from '@/next/components/ui/button'
import { cn } from '@/next/lib/utils'
import { Eye, EyeOff } from 'lucide-react'
import { useState } from 'react'

export interface FormPasswordInputFieldProps<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
> extends Omit<React.ComponentProps<'input'>, 'value' | 'onChange' | 'name'> {
  control: Control<TFieldValues>
  name: TName
  label?: string
  placeholder?: string
}

export function FormPasswordInputField<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  label,
  className,
  placeholder = 'Enter password',
  ...props
}: FormPasswordInputFieldProps<TFieldValues, TName>) {
  const [showPassword, setShowPassword] = useState(false)

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          <FormLabel>{label || name}</FormLabel>
          <FormControl>
            <div className="relative">
              <Input
                {...props}
                {...field}
                type={showPassword ? 'text' : 'password'}
                placeholder={placeholder}
                value={field.value ?? ''}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
                <span className="sr-only">{showPassword ? 'Hide password' : 'Show password'}</span>
              </Button>
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
