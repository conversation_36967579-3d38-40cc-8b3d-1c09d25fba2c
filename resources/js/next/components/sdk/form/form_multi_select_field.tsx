'use client'

import * as React from 'react'
import { Check, ChevronsUpDown, X } from 'lucide-react'

import { cn } from '@/next/lib/utils'
import { But<PERSON> } from '@/next/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/next/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { Control, FieldValues, Path } from 'react-hook-form'
import { FormSelectFieldOption } from './form_select_field'
import { ElementProps } from '@/next/components/sdk/types'
import { FormField, FormItem, FormLabel, FormMessage } from '@/next/components/ui/form'
import { Badge } from '@/next/components/ui/badge'

type FormMultiSelectFieldProps<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
> = {
  control: Control<TFieldValues>
  name: TName
  label?: string
  placeholder?: string
  className?: string
  options: FormSelectFieldOption[]
  searchHint?: string
  defaultValue?: string[]
  emptyHint?: string
} & ElementProps

export function FormMultiSelectField<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  options,
  placeholder = 'Select option',
  label,
  searchHint = 'Search',
  emptyHint = 'No option found',
  className,
  ...props
}: FormMultiSelectFieldProps<TFieldValues, TName> & React.ComponentProps<typeof FormItem>) {
  const [open, setOpen] = React.useState(false)

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const hasSelectedOptions = field.value?.length > 0

        return (
          <FormItem className={cn(className)} {...props}>
            <FormLabel>{label || name}</FormLabel>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn('w-full justify-between min-h-[2.5rem] h-auto', className)}
                >
                  <div className="flex flex-wrap gap-1.5 items-center">
                    {hasSelectedOptions ? (
                      field.value.map((value: any) => {
                        const option = options.find((opt) => opt.value === value)
                        if (!option) return null
                        return (
                          <Badge
                            key={value}
                            variant="secondary"
                            className="flex items-center gap-1 px-2 py-0.5"
                          >
                            {option.render
                              ? option.render(option.value, option.label)
                              : option.label}
                            <div
                              role="button"
                              onClick={(e) => {
                                e.stopPropagation()
                                const updatedSelected = (field.value || []).filter(
                                  (item: any) => item !== value
                                )
                                field.onChange(updatedSelected)
                              }}
                              className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                            >
                              <X className="h-3 w-3" />
                              <span className="sr-only">Remove {option.label}</span>
                            </div>
                          </Badge>
                        )
                      })
                    ) : (
                      <span className="text-muted-foreground">{placeholder}</span>
                    )}
                  </div>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Command>
                  <div className="flex items-center justify-between border-b **:data-[slot=command-input-wrapper]:border-none">
                    <CommandInput
                      placeholder={searchHint}
                      className="h-9 px-0 shadow-none focus-visible:ring-0"
                    />
                    {hasSelectedOptions && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-xs"
                        onClick={() => {
                          field.onChange([])
                        }}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Clear all
                      </Button>
                    )}
                  </div>
                  <CommandList>
                    <CommandEmpty>{emptyHint}</CommandEmpty>
                    <CommandGroup>
                      {options.map((option) => (
                        <CommandItem
                          key={option.value}
                          value={option.value}
                          onSelect={() => {
                            const selected = (field.value || []) as string[]
                            const updatedSelected = selected.includes(option.value)
                              ? selected.filter((item) => item !== option.value)
                              : [...selected, option.value]
                            field.onChange(updatedSelected)
                          }}
                        >
                          {option.render ? option.render(option.value, option.label) : option.label}
                          <Check
                            className={cn(
                              'ml-auto h-4 w-4',
                              ((field.value || []) as string[]).includes(option.value)
                                ? 'opacity-100'
                                : 'opacity-0'
                            )}
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
