import { ReactNode } from '@/next/components/sdk/types'
import { Button } from '@/next/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/next/components/ui/command'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/next/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { cn } from '@/next/lib/utils'
import { ChevronsUpDown, Check } from 'lucide-react'
import { ComponentProps } from 'react'
import { Control, FieldValues, Path } from 'react-hook-form'

export type FormSelectFieldOption = {
  value: string
  label: string
  render?: (value: string, label: string) => ReactNode
}

export function FormSelectField<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  options,
  placeholder = 'Select option',
  className,
  label,
  searchHint = 'Search',
  emptyHint = 'No option found',
  ...props
}: {
  control: Control<TFieldValues>
  name: TName
  label?: string
  placeholder?: string
  className?: string
  options: FormSelectFieldOption[]
  searchHint?: string
  emptyHint?: string
} & ComponentProps<typeof Button>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          <FormLabel>{label || name}</FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  className={cn('justify-between', !field.value && 'text-muted-foreground')}
                  {...props}
                >
                  {field.value ? options.find((o) => o.value === field.value)?.label : placeholder}
                  <ChevronsUpDown className="opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="p-0">
              <Command>
                <CommandInput placeholder={searchHint} className="h-9" />
                <CommandList>
                  <CommandEmpty>{emptyHint}</CommandEmpty>
                  <CommandGroup>
                    {options.map((o) => (
                      <CommandItem
                        value={o.label}
                        key={o.value}
                        onSelect={() => {
                          field.onChange(o.value)
                        }}
                      >
                        {o.render ? o.render(o.value, o.label) : o.label}
                        <Check
                          className={cn(
                            'ml-auto',
                            o.value === field.value ? 'opacity-100' : 'opacity-0'
                          )}
                        />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
