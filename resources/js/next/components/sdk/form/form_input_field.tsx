import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/next/components/ui/form'

import { Control, FieldValues, Path } from 'react-hook-form'
import { Input } from '@/next/components/ui/input'
import { cn } from '@/next/lib/utils'

// Define a separate interface for the component props
export interface FormInputFieldProps<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
> extends React.ComponentProps<'input'> {
  control: Control<TFieldValues>
  name: TName
  label?: string
  placeholder?: string
  hint?: string
}

export function FormInputField<TFieldValues extends FieldValues, TName extends Path<TFieldValues>>({
  control,
  name,
  label,
  className,
  placeholder = 'Enter text',
  hint,
  ...props
}: FormInputFieldProps<TFieldValues, TName>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          <FormLabel>{label || name}</FormLabel>
          <FormControl>
            <Input placeholder={placeholder} {...field} {...props} />
          </FormControl>
          <FormMessage />
          {hint && <FormDescription>{hint}</FormDescription>}
        </FormItem>
      )}
    />
  )
}
