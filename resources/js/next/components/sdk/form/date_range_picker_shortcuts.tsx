import {
  endOfMonth,
  endOfWeek,
  startOfMonth,
  startOfWeek,
  subDays,
  subMonths,
  subWeeks,
} from 'date-fns'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select'
import { ComponentProps } from 'react'

export function DateRangePickerShortcuts({
  onValueChange: onValueChangeProp,
  ...props
}: Omit<ComponentProps<typeof Select>, 'onValueChange'> & {
  onValueChange: (value: { from: Date; to: Date }) => any
}) {
  const onValueChange = (value: string) => {
    let to = new Date()
    let from = new Date()
    switch (value) {
      case 'yesterday':
        to = subDays(new Date(), 1)
        from = to
        break

      case 'currentWeek':
        from = startOfWeek(to, { weekStartsOn: 1 })
        break

      case 'lastWeek':
        const week = subWeeks(new Date(), 1)
        to = endOfWeek(week, { weekStartsOn: 1 })
        from = startOfWeek(week, { weekStartsOn: 1 })
        break

      case 'currentMonth':
        from = startOfMonth(to)
        break

      case 'lastMonth':
        from = startOfMonth(subMonths(to, 1))
        to = endOfMonth(from)
        break

      default:
        from = subDays(to, Number(value))
        break
    }

    onValueChangeProp({
      from,
      to,
    })
  }
  return (
    <Select {...props} onValueChange={onValueChange}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Quick select" />
      </SelectTrigger>
      <SelectContent position="popper">
        <SelectItem value="0">Today</SelectItem>
        <SelectItem value="yesterday">Yesterday</SelectItem>
        <SelectItem value="2">Last 3 days</SelectItem>
        <SelectItem value="6">Last 7 days</SelectItem>
        <SelectItem value="currentWeek">This week</SelectItem>
        <SelectItem value="lastWeek">Last week</SelectItem>
        <SelectItem value="29">Last 30 days</SelectItem>
        <SelectItem value="currentMonth">This month</SelectItem>
        <SelectItem value="lastMonth">Last month</SelectItem>
      </SelectContent>
    </Select>
  )
}
