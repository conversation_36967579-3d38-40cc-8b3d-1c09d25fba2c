import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/next/components/ui/form'

import { Control, FieldValues, Path } from 'react-hook-form'
import { Checkbox } from '@/next/components/ui/checkbox'
import { cn } from '@/next/lib/utils'

// Define a separate interface for the component props
export interface FormInputFieldProps<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
> extends React.ComponentProps<typeof Checkbox> {
  control: Control<TFieldValues>
  name: TName
  label?: string
  placeholder?: string
}

export function FormCheckboxField<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({ control, name, label, className, ...props }: FormInputFieldProps<TFieldValues, TName>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { onChange, value, ...field } }) => (
        <FormItem className={cn(className)}>
          <FormLabel>{label || name}</FormLabel>
          <FormControl>
            <Checkbox {...field} {...props} onCheckedChange={onChange} checked={Boolean(value)} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
