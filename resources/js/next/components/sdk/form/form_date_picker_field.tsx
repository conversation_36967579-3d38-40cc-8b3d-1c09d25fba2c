import { Button } from '@/next/components/ui/button'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/next/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { cn } from '@/next/lib/utils'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import { Calendar } from '@/next/components/ui/calendar'
import { Control, FieldValues, Path } from 'react-hook-form'
import { ElementProps } from '@/next/components/sdk/types'

// Define a separate interface for the component props
export interface FormDatePickerFieldProps<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
> extends ElementProps {
  control: Control<TFieldValues>
  name: TName
  label?: string
  placeholder?: string
}

export function FormDatePickerField<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  label,
  placeholder = 'Select date',
  className,
}: FormDatePickerFieldProps<TFieldValues, TName>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className, 'flex flex-col')}>
          <FormLabel>{label || name}</FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant={'outline'}
                  className={cn(
                    ' pl-3 text-left font-normal',
                    !field.value && 'text-muted-foreground'
                  )}
                >
                  {field.value ? format(field.value, 'PPP') : <span>{placeholder}</span>}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
