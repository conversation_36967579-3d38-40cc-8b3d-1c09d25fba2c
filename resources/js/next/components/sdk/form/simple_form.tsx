import { Form, FormField } from '@/next/components/ui/form'
import {
  Children,
  cloneElement,
  ComponentProps,
  createContext,
  isValidElement,
  ReactNode,
  useContext,
} from 'react'
import { FieldValues, UseFormReturn } from 'react-hook-form'

type FormFieldProps = ComponentProps<typeof FormField>

type SimpleFormContextType<T extends FieldValues> = {
  form: UseFormReturn<T>
  state: {
    fields: Record<string, { hidden?: boolean; disabled?: boolean }>
  }
}

const SimpleFormContext = createContext<SimpleFormContextType<any> | null>(null)

export type SimpleFormRootProps<T extends FieldValues> = {
  form: UseFormReturn<T>
  onSubmit: (data: T) => void
  state?: {
    fields: Record<string, { hidden?: boolean; disabled?: boolean }>
  }
  children: ReactNode
} & Omit<ComponentProps<'form'>, 'onSubmit'>

export type SimpleFormContentProps = {
  slots?: { before?: ReactNode; after?: ReactNode }
} & ComponentProps<'div'>

// Root component that provides context
export function SimpleForm<T extends FieldValues>({
  form,
  onSubmit,
  state = { fields: {} },
  children,
  ...props
}: SimpleFormRootProps<T>) {
  return (
    <SimpleFormContext.Provider value={{ form, state }}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit as any)} {...props}>
          {children}
        </form>
      </Form>
    </SimpleFormContext.Provider>
  )
}

// Content component that renders the form fields
export function SimpleFormContent({
  slots: { before, after } = {},
  children,
}: SimpleFormContentProps) {
  const context = useContext(SimpleFormContext)
  if (!context) {
    throw new Error('SimpleFormContent must be used within SimpleForm')
  }

  const { state } = context
  const fields = Children.toArray(children)

  return (
    <>
      {before}
      {fields.map((child) => {
        if (isValidElement<FormFieldProps>(child)) {
          const fieldState = state.fields[child.props.name]

          return (
            fieldState?.hidden ||
            cloneElement(child, {
              disabled: fieldState?.disabled,
            } as Partial<FormFieldProps>)
          )
        }

        throw new Error('Must be Form<Type>Field component')
      })}
      {after}
    </>
  )
}

// Attach Content to Root for better DX
SimpleForm.Content = SimpleFormContent
