import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/next/components/ui/form'
import { Control, FieldValues, Path, useFormContext } from 'react-hook-form'
import { Textarea } from '@/next/components/ui/textarea'
import { cn } from '@/next/lib/utils'

export interface FormTextAreaFieldProps<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
> extends React.ComponentProps<'textarea'> {
  name: TName
  label?: string
  placeholder?: string
  rows?: number
  control?: Control<TFieldValues>
}

export function FormTextAreaField<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  name,
  label,
  className,
  placeholder = 'Enter text',
  rows = 3,
  control: controlProp,
  ...props
}: Readonly<FormTextAreaFieldProps<TFieldValues, TName>>) {
  const formContext = useFormContext<TFieldValues>()
  const control = controlProp ?? formContext.control

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          <FormLabel>{label ?? name}</FormLabel>
          <FormControl>
            <Textarea placeholder={placeholder} rows={rows} {...field} {...props} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
