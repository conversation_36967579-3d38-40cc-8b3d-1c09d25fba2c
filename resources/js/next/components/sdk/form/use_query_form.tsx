import { useForm, UseFormProps, DefaultValues } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryStates, Parser } from 'nuqs'
import { z } from 'zod'
import { useCallback, useEffect, useMemo } from 'react'
import { get, set } from 'lodash-es'

type DeepKeys<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? K | `${K}.${DeepKeys<T[K]>}`
          : K
        : never
    }[keyof T]
  : never

type QuerySyncConfig<T> = {
  [K in DeepKeys<T>]?: {
    lazy?: boolean // When true, only updates on submit. When false (default), updates immediately
    queryKey?: string
    parser?: Parser<any> // Custom nuqs parser for this field
  }
}

interface UseQueryFormOptions<TSchema extends z.ZodObject<any>> {
  schema: TSchema
  syncConfig?: QuerySyncConfig<z.infer<TSchema>>
  formOptions?: Omit<UseFormProps<z.infer<TSchema>>, 'resolver'>
}

// Simple helper function to provide type inference
export function defineQueryFormOptions<TSchema extends z.ZodObject<any>>(
  options: UseQueryFormOptions<TSchema>
): UseQueryFormOptions<TSchema> {
  return options
}

// Helper function to flatten schema shape
function flattenSchema(schema: z.ZodObject<any>): Record<string, z.ZodType> {
  return Object.fromEntries(
    Object.entries(schema.shape).flatMap(([key, value]) => {
      if (value instanceof z.ZodObject) {
        return Object.entries(flattenSchema(value)).map(([nestedKey, nestedValue]) => [
          `${key}.${nestedKey}`,
          nestedValue,
        ])
      }
      if (value instanceof z.ZodType) {
        return [[key, value]]
      }
      return []
    })
  )
}

export function useQueryForm<TSchema extends z.ZodObject<any>>({
  schema,
  syncConfig = {},
  formOptions = {},
}: UseQueryFormOptions<TSchema>) {
  type FormData = z.infer<TSchema>

  // Flatten the schema shape
  const flattenedSchema = useMemo(() => flattenSchema(schema), [schema])

  // Create field to query parameter mapping only for fields in syncConfig
  const fieldToQueryMap = useMemo(() => {
    const configuredFields = Object.keys(syncConfig) as Array<DeepKeys<FormData>>
    return configuredFields.reduce(
      (acc, field) => {
        const config = syncConfig[field]
        // Replace dots with underscores for query parameters
        const queryKey = config?.queryKey ?? field.replace(/\./g, '_')
        acc[field] = queryKey
        return acc
      },
      {} as Record<string, string>
    )
  }, [syncConfig])

  // Create parsers for each field in syncConfig
  const fieldParsers = useMemo(() => {
    const defaultValues = formOptions.defaultValues as DefaultValues<FormData>

    return Object.fromEntries(
      Object.entries(fieldToQueryMap).map(([field, queryKey]): [string, Parser<any>] => {
        const fieldSchema = flattenedSchema[field] as z.ZodType<any>
        const customParser = syncConfig[field as DeepKeys<FormData>]?.parser

        // If a custom parser is provided, use it
        if (customParser) {
          return [queryKey, customParser]
        }

        const parser = {
          parse: (value: string | null) => {
            if (value === null) return null
            try {
              return fieldSchema.parse(value)
            } catch {
              const defaultValue =
                typeof defaultValues === 'function' ? undefined : get(defaultValues ?? {}, field)
              return defaultValue ?? null
            }
          },
          serialize: (value: any | null): string => {
            return value !== null ? String(value) : ''
          },
        } satisfies Parser<any | null>

        return [queryKey, parser]
      })
    )
  }, [flattenedSchema, fieldToQueryMap, formOptions.defaultValues, syncConfig])

  // Initialize query states only for configured fields
  const [queryStates, setQueryStates] = useQueryStates(fieldParsers)

  // Convert query states to form values
  const initialQueryValues = useMemo(() => {
    return Object.entries(fieldToQueryMap).reduce((acc, [field, queryKey]) => {
      const value = queryStates[queryKey]
      if (value !== null) {
        set(acc, field, value)
      }
      return acc
    }, {} as Partial<FormData>)
  }, [queryStates, fieldToQueryMap])

  // Initialize form with react-hook-form
  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    ...formOptions,
    // Override defaultValues to include query string values
    defaultValues: async () => {
      const baseDefaults =
        typeof formOptions.defaultValues === 'function'
          ? await formOptions.defaultValues()
          : (formOptions.defaultValues ?? {})

      return {
        ...baseDefaults,
        ...initialQueryValues,
      }
    },
  })

  // Sync form values to query string based on configuration
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (!name || type !== 'change') return

      // Find all matching nested fields that start with the changed field name
      Object.entries(fieldToQueryMap).forEach(([field, queryKey]) => {
        if (field === name || field.startsWith(`${name}.`)) {
          const fieldConfig = syncConfig[field as DeepKeys<FormData>]
          // Update immediately if the field is not marked as lazy
          if (!fieldConfig?.lazy) {
            const fieldValue = get(value, field)
            setQueryStates({ [queryKey]: fieldValue ?? null })
          }
        }
      })
    })

    return () => subscription.unsubscribe()
  }, [form, syncConfig, fieldToQueryMap, setQueryStates])

  // Custom submit handler that syncs all values to query string
  const handleSubmit = useCallback(
    (onSubmit: (data: FormData) => void) => {
      return form.handleSubmit(async (data) => {
        // Sync all configured form values to query string
        const updates = Object.entries(fieldToQueryMap).reduce(
          (acc, [field, queryKey]) => {
            const fieldConfig = syncConfig[field as DeepKeys<FormData>]
            // On submit, update all fields that are lazy
            if (fieldConfig?.lazy) {
              const value = get(data, field)
              acc[queryKey] = value ?? null
            }
            return acc
          },
          {} as Record<string, any | null>
        )

        if (Object.keys(updates).length > 0) {
          await setQueryStates(updates)
        }
        onSubmit(data)
      })
    },
    [form, fieldToQueryMap, setQueryStates, syncConfig]
  )

  return {
    ...form,
    handleSubmit,
    queryMapping: fieldToQueryMap,
    queryState: initialQueryValues,
  }
}
