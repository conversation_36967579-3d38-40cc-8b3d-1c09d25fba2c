import { HelpCircle } from 'lucide-react'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/next/components/ui/tooltip'
import { cn } from '@/next/lib/utils'
import { ElementProps } from '@/next/components/sdk/types'

export interface HelpTooltipProps extends ElementProps {
  side?: 'top' | 'right' | 'bottom' | 'left'
  align?: 'start' | 'center' | 'end'
  iconSize?: number
  iconClassName?: string
}

export function HelpTooltip({
  children,
  side = 'top',
  align = 'center',
  iconSize = 16,
  className,
  iconClassName,
}: HelpTooltipProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={cn('inline-flex cursor-help', className)}>
            <HelpCircle size={iconSize} className={cn('text-muted-foreground', iconClassName)} />
          </span>
        </TooltipTrigger>
        <TooltipContent side={side} align={align}>
          {children}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
