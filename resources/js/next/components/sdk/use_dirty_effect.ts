import { useEffect, useRef, DependencyList } from 'react'

/**
 * A hook that works like useEffect but skips the first render
 *
 * @param effect The effect callback to run
 * @param deps The dependency array for the effect
 */
export function useDirtyEffect(effect: React.EffectCallback, deps: DependencyList) {
  const isFirstRender = useRef(true)

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false
      return
    }

    return effect()
  }, deps)
}
