import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cn } from '@/next/lib/utils'
import { Separator } from '@/next/components/ui/separator'

interface MenuProps extends React.HTMLAttributes<HTMLUListElement> {
  children: React.ReactNode
  asChild?: boolean
}

function Menu({ className, children, asChild = false, ...props }: MenuProps) {
  const Comp = asChild ? Slot : 'ul'

  return (
    <Comp
      data-slot="menu"
      className={cn(
        'flex w-full flex-col overflow-hidden rounded-md border bg-background shadow-sm',
        className
      )}
      {...props}
    >
      {children}
    </Comp>
  )
}

interface MenuItemProps extends React.HTMLAttributes<HTMLLIElement> {
  children: React.ReactNode
  asChild?: boolean
}

function MenuItem({ className, children, asChild = false, ...props }: MenuItemProps) {
  const Comp = asChild ? Slot : 'li'

  return (
    <Comp
      data-slot="menu-item"
      className={cn(
        'flex w-full items-center px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none',
        className
      )}
      {...props}
    >
      {children}
    </Comp>
  )
}

function MenuSeparator({ className, ...props }: React.ComponentProps<typeof Separator>) {
  return <Separator data-slot="menu-separator" className={cn('my-1', className)} {...props} />
}

export { Menu, MenuItem, MenuSeparator }
