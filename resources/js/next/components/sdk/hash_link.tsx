'use client'
import { createContext, useCallback, useContext, useEffect, useState } from 'react'
import { UseQueryStateOptions, UseQueryStateReturn } from 'nuqs'

const HashLinkContext = createContext<{
  qs: URLSearchParams
  path: string
}>(null!)

function getStateFromHash(hash: string) {
  const [path, qs = ''] = hash.split('?', 2)
  return {
    qs: new URLSearchParams(qs),
    path,
  }
}

function updateHash(newHash: string) {
  history.pushState(null, '', newHash)
  // Dispatch a hashchange event manually since pushState doesn't trigger it
  window.dispatchEvent(new HashChangeEvent('hashchange'))
}

export function HashLinkProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState(() => {
    if (typeof location === 'undefined') {
      return {
        qs: new URLSearchParams(),
        path: '',
      }
    }

    return getStateFromHash(window.location.hash.slice(1))
  })

  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1)
      setState(getStateFromHash(hash))
    }

    window.addEventListener('hashchange', handleHashChange)
    return () => window.removeEventListener('hashchange', handleHashChange)
  }, [setState])

  return <HashLinkContext.Provider value={state}>{children}</HashLinkContext.Provider>
}

// Overloaded function signatures to support both direct parser use and options object
export function useHashState<T>(
  key: string,
  parser: UseQueryStateOptions<T>
): UseQueryStateReturn<T | null, null>
export function useHashState<T>(
  key: string,
  options: UseQueryStateOptions<T> & { defaultValue: T }
): UseQueryStateReturn<T, T>
export function useHashState<T>(
  key: string,
  parserOrOptions: UseQueryStateOptions<T> | (UseQueryStateOptions<T> & { defaultValue: T })
): UseQueryStateReturn<any, any> {
  const { qs } = useContext(HashLinkContext)
  const options = parserOrOptions as UseQueryStateOptions<T> & { defaultValue?: T }
  const defaultValue = 'defaultValue' in options ? options.defaultValue : null

  const [value, setValue] = useState<T | null>(() => {
    const paramValue = qs.get(key)
    if (paramValue === null) return defaultValue as T | null
    try {
      const parsed = options.parse(paramValue)
      return (parsed !== undefined ? parsed : defaultValue) as T | null
    } catch (error) {
      return defaultValue as T | null
    }
  })

  // Update value when URL hash changes
  useEffect(() => {
    const paramValue = qs.get(key)
    if (paramValue === null) {
      if (value !== defaultValue) {
        setValue(defaultValue as T | null)
      }
    } else {
      try {
        const parsed = options.parse(paramValue)
        const newValue = (parsed !== undefined ? parsed : defaultValue) as T | null
        if (value === null && newValue === null) {
          // Both are null, no update needed
        } else if (value === null || newValue === null) {
          // One is null, other isn't - update needed
          setValue(newValue)
        } else if (options.eq ? !options.eq(value, newValue) : value !== newValue) {
          // Neither is null, compare using eq or strict equality
          setValue(newValue)
        }
      } catch (error) {
        if (value !== defaultValue) {
          setValue(defaultValue as T | null)
        }
      }
    }
  }, [qs, key, options, defaultValue, value])

  const updateValue = useCallback(
    (newValue: T | null | ((prevValue: T | null) => T | null)) => {
      const resolvedValue =
        typeof newValue === 'function'
          ? (newValue as (prevValue: T | null) => T | null)(value)
          : newValue

      // Only update if the values are different
      // @ts-ignore
      if (options.eq ? !options.eq(value, resolvedValue) : value !== resolvedValue) {
        setValue(resolvedValue)

        const hash = window.location.hash.slice(1)
        const { qs: currentQs, path: currentPath } = getStateFromHash(hash)

        if (resolvedValue === defaultValue && options.clearOnDefault) {
          currentQs.delete(key)
        } else if (resolvedValue === null) {
          currentQs.delete(key)
        } else {
          const stringValue = options.serialize
            ? options.serialize(resolvedValue)
            : String(resolvedValue)
          currentQs.set(key, stringValue)
        }

        const queryString = currentQs.toString()
        const newHash = `#${currentPath}${queryString ? `?${queryString}` : ''}`

        updateHash(newHash)
      }

      return resolvedValue
    },
    [key, value, options, defaultValue]
  )

  return [value, updateValue] as UseQueryStateReturn<typeof value, typeof defaultValue>
}

export function useHashNavigate() {
  const navigate = useCallback((path: string, params: Record<string, string> = {}) => {
    const qs = new URLSearchParams(params)
    for (const [key, value] of Object.entries(params)) {
      qs.set(key, value)
    }
    const queryString = qs.toString()
    const newHash = `#${path}${queryString ? `?${queryString}` : ''}`

    updateHash(newHash)
  }, [])

  const unsetHashState = useCallback(() => {
    updateHash('#')
  }, [])

  return [navigate, unsetHashState] as const
}

export function useHashLinkContext() {
  return useContext(HashLinkContext)
}
