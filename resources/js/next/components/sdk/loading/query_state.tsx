import { ElementProps } from '@/next/components/sdk/types'
import { QueryResult } from '@apollo/client'
import { Loading } from './index'
import { Crash } from './crash'

export interface QueryStateProps extends ElementProps {
  /**
   * Apollo query result object
   */
  query:
    | Pick<QueryResult<any, any>, 'data' | 'called' | 'loading' | 'error'>
    | Array<Pick<QueryResult<any, any>, 'data' | 'called' | 'loading' | 'error'>>
  centered?: boolean
  /**
   * Custom error message to show when query fails
   * @default 'Failed to get data'
   */
  errorMessage?: string
  /**
   * Custom renderer for error state
   */
  renderError?: () => React.ReactNode
}

export function QueryState({
  query,
  centered = true,
  className,
  children,
  errorMessage = 'Failed to get data',
  renderError,
}: QueryStateProps) {
  const queries = [query].flat()
  // Initial load is when we haven't successfully fetched data yet
  const isInitialLoad = queries.some((q) => !q.called || !q.data)

  // console.log(
  //   'loading',
  //   queries.map((q) => [q.called, q.loading])
  // )

  // Show crash only on initial load failure
  if (isInitialLoad && queries.some((q) => q.error)) {
    return (
      <Crash
        error={true}
        centered={centered}
        className={className}
        message={errorMessage}
        renderError={renderError}
        data-testid="query_state_error"
      />
    )
  }

  const isLoading = queries.some((q) => q.loading || !q.called)

  // Show loading state for both initial and subsequent loads
  return (
    <Loading
      loading={isLoading}
      centered={centered}
      className={className}
      data-testid="query_state_loading"
    >
      {children}
    </Loading>
  )
}
