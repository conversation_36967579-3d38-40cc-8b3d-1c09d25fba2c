import { ComponentProps, useCallback } from 'react'
import { LoadingSpinner } from './loading_spinner'
import { NoContent } from './no_content'
import { cn } from '@/next/lib/utils'

export interface LoadingProps extends ComponentProps<'div'> {
  loading: boolean
  centered?: boolean
}

export function Loading({ loading, centered = true, className, children, ...props }: LoadingProps) {
  const renderLoading = useCallback(() => {
    return (
      <div className={cn(centered && 'flex items-center justify-center', className)} {...props}>
        <LoadingSpinner />
      </div>
    )
  }, [centered, className])

  return <NoContent empty={loading} renderEmpty={renderLoading} children={children} />
}
