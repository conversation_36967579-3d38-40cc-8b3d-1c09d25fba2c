import { XCircle } from 'lucide-react'
import { cn } from '@/next/lib/utils'
import { NoContent } from './no_content'
import { ComponentProps, useCallback } from 'react'

export interface CrashProps extends ComponentProps<'div'> {
  error: boolean
  centered?: boolean
  message?: string
  renderError?: () => React.ReactNode
}

export function Crash({
  error,
  centered = true,
  className,
  children,
  message = 'Failed to get data',
  renderError,
  ...props
}: CrashProps) {
  const defaultRenderError = useCallback(() => {
    return (
      <div
        className={cn(centered && 'flex flex-col items-center justify-center', className, {
          'gap-2': message,
        })}
        {...props}
      >
        <XCircle className="h-6 w-6 text-destructive" />
        <span className="text-sm text-muted-foreground">{message}</span>
      </div>
    )
  }, [centered, className, message])

  return (
    <NoContent empty={error} renderEmpty={renderError || defaultRenderError} children={children} />
  )
}
