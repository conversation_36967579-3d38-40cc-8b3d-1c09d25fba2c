import { Cell, Row } from '@tanstack/table-core'
import { Table } from '@tanstack/table-core'
import { isNil } from 'lodash-es'
import { ComponentProps } from 'react'
import { TableCellHighlight } from '../domain/data_table'
import { CircleQuestionMark, MinusIcon, TrendingDownIcon, TrendingUpIcon } from 'lucide-react'
import { DataTableToolbarButton } from './data_table'
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip'
import { safeDivide } from '#utils/math'
import { formatPercentage } from '@/components/typography'
import { cn } from '@/next/lib/utils'

export function DataTableGroupHighlightingCell<TData>({
  table,
  cell,
  row,
  type = 'number',
  decimals = 2,
  children,
  baselineComparison,
}: ComponentProps<'div'> & {
  table: Table<TData>
  cell: Cell<TData, any>
  row: Row<TData>
  type?: ComponentProps<typeof TableCellHighlight>['type']
  decimals?: number
  baselineComparison?: boolean
}) {
  const groupAggregation = table.options.meta!.getGroupAggregation(row)
  const rowValue = cell.getValue() as number

  const groupAggregationValue = groupAggregation?.[cell.column.id]
  if (!groupAggregationValue) {
    return rowValue
  }

  const isAllNa = groupAggregationValue.naRate > 0.95

  const skipHighlight = isAllNa || isNil(rowValue) || rowValue === 0

  const getHighlightColor = () => {
    const isTop = (percent: number) =>
      rowValue >=
      groupAggregationValue.max -
        (percent * (groupAggregationValue.max - groupAggregationValue.min)) / 100

    switch (true) {
      case skipHighlight:
        return 'bg-muted'

      case isTop(5):
        return 'bg-green-300'
      case isTop(10):
        return 'bg-green-200'
      case isTop(30):
        return 'bg-green-100'
      case isTop(50):
        return 'bg-green-50'
      case isTop(70):
        return 'bg-red-50'
      case isTop(90):
        return 'bg-red-100'
      case isTop(95):
        return 'bg-red-200'

      default:
        return 'bg-red-300'
    }
  }

  return (
    <TableCellHighlight
      variant="custom"
      value={rowValue}
      type={type}
      className={getHighlightColor()}
      decimals={decimals}
    >
      {children}
      {baselineComparison && (
        <DataTableBaselineComparisonCell table={table} cell={cell} row={row} />
      )}
    </TableCellHighlight>
  )
}

export function DataTableGroupHighlightingTips() {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <DataTableToolbarButton>
          <CircleQuestionMark />
        </DataTableToolbarButton>
      </TooltipTrigger>
      <TooltipContent side="left" className="max-w-[300px] flex flex-col gap-2">
        <p>Dark green indicates the highest values, within the top 5% (with a margin of ±5%).</p>
        <p>Slightly lighter green shows values in the top 10% (excluding the very top).</p>
        <p>Light green represents values ranked in the top 10% to 30%.</p>
        <p>Very light green shows values in the top 30% to 50%.</p>
        <p>Very light red shows values in the top 50% to 70%.</p>
        <p>Light red indicates values in the top 70% to 90%.</p>
        <p>Slightly darker red represents values in the bottom 10% (excluding the very lowest).</p>
        <p>Dark red is used for the lowest values, within the bottom 5% (with a margin of ±5%).</p>
      </TooltipContent>
    </Tooltip>
  )
}

export function DataTableBaselineComparisonCell<TData>({
  table,
  cell,
  row,
}: ComponentProps<'div'> & {
  table: Table<TData>
  cell: Cell<TData, any>
  row: Row<TData>
}) {
  const baselineValue = table.options.meta!.baselineValue
  const baselineColumn = table.options.meta!.baselineColumn!
  if (!baselineColumn || !baselineValue) {
    return null
  }

  const isBaselineRow = row.getValue(baselineColumn) === baselineValue
  if (isBaselineRow) {
    return null
  }

  const aggregation = table.options.meta!.getBaselineAggregation!(row)
  const rowValue = cell.getValue() as number
  const aggregationValue = aggregation?.[cell.column.id].base || 0

  const { icon: TrendIcon, color, diffRate } = getTrendInfo(rowValue, aggregationValue)

  return (
    <div className={cn('flex items-center gap-1', color)}>
      {TrendIcon && <TrendIcon size={14} />}
      {diffRate}
    </div>
  )
}

const TREND_COLORS = {
  UP: 'text-green-600',
  DOWN: 'text-red-600',
  NEUTRAL: 'text-gray-500',
}

const TREND_ICONS = {
  UP: TrendingUpIcon,
  DOWN: TrendingDownIcon,
  NEUTRAL: MinusIcon,
}

const getTrendInfo = (rowValue: number, aggregationValue: number) => {
  const diffRate = safeDivide(rowValue, aggregationValue) - 1

  switch (true) {
    case rowValue === 0 && aggregationValue !== 0:
      return {
        icon: TREND_ICONS.UP,
        color: TREND_COLORS.UP,
        diffRate: formatPercentage(Math.abs(diffRate)),
      }
    case diffRate === -1:
      return {
        icon: TREND_ICONS.NEUTRAL,
        color: TREND_COLORS.NEUTRAL,
        diffRate: '',
      }
    case diffRate > 0:
      return {
        icon: TREND_ICONS.UP,
        color: TREND_COLORS.UP,
        diffRate: formatPercentage(Math.abs(diffRate)),
      }

    case diffRate < 0:
      return {
        icon: TREND_ICONS.DOWN,
        color: TREND_COLORS.DOWN,
        diffRate: formatPercentage(Math.abs(diffRate)),
      }

    case diffRate === 0:
      return {
        icon: null,
        color: '',
        diffRate: '0%',
      }

    default:
      return {
        icon: TREND_ICONS.NEUTRAL,
        color: TREND_COLORS.NEUTRAL,
        diffRate: '',
      }
  }
}
