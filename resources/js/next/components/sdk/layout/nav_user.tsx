'use client'

import { Ch<PERSON><PERSON>UpDown, FlaskConical, LogOut, Settings } from 'lucide-react'
import { <PERSON> } from '@inertiajs/react'

import { Avatar, AvatarFallback, AvatarImage } from '@/next/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/next/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/next/components/ui/sidebar'
import queryString from 'query-string'
import { routes } from '@/components/location'
import { useUIFeatureStore } from '@/next/stores/use_ui_feature_store'
import { useServerProp } from '@/components/ssr'

export function NavUser() {
  const { isMobile } = useSidebar()
  const user = useServerProp((s) => s.user)
  const { isExperimentalUIEnabled, toggleExperimentalUI } = useUIFeatureStore()

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage alt={user.fullName} />
                <AvatarFallback className="rounded-lg">
                  {user.fullName?.substring(0, 2)?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.fullName}</span>
                <span className="truncate text-xs">{user.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href={routes.me.profile.index()}>
                  <Settings className="mr-2 size-4" />
                  Account settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => toggleExperimentalUI()}>
                <FlaskConical className="mr-2 size-4" />
                {isExperimentalUIEnabled ? 'Disable' : 'Enable'} Experimental UI
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup asChild>
              <DropdownMenuItem asChild>
                <Link
                  className="w-full"
                  method="post"
                  href={queryString.stringifyUrl({
                    url: routes.auth.sessions.destroy(),
                    query: {
                      _method: 'delete',
                    },
                  })}
                >
                  <LogOut className="mr-2 size-4" />
                  Log out
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
