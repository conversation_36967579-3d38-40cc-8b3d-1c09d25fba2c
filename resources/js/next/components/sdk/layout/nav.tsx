import { type LucideIcon } from 'lucide-react'
import { Link } from '@inertiajs/react'

import { ReactNode } from '@/next/components/sdk/types'
import { SidebarMenuButton, SidebarMenuItem } from '@/next/components/ui/sidebar'
import { ComponentProps, forwardRef, Ref } from 'react'
import { useCurrentUrl, isUrlActive } from '@/next/hooks/use_current_url'

export const NavSecondaryItem = forwardRef(
  (
    {
      icon: Icon,
      title,
      url,
      ...props
    }: ComponentProps<typeof SidebarMenuItem> & {
      icon: LucideIcon
      title: ReactNode
      url?: string
    },
    ref: Ref<HTMLLIElement>
  ) => {
    const currentUrl = useCurrentUrl()
    const isActive = url ? isUrlActive(currentUrl, url) : false

    return (
      <SidebarMenuItem {...props} ref={ref}>
        <SidebarMenuButton size="sm" asChild={!!url} isActive={isActive}>
          {url ? (
            <a href={url}>
              <Icon />
              <span>{title}</span>
            </a>
          ) : (
            <>
              <Icon />
              <span>{title}</span>
            </>
          )}
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }
)
