import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/next/components/ui/sidebar'
import { AppSidebar } from '@/next/components/sdk/layout/app_sidebar'
import { Separator } from '@/next/components/ui/separator'
import { ElementProps } from '@/next/components/sdk/types'
import Breadcrumbs from '@/next/components/sdk/breadcrumbs'

export function BaseLayout({ children }: ElementProps) {
  return (
    <SidebarProvider>
      <AppSidebar />

      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumbs />
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  )
}
