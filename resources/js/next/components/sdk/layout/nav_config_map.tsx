import { ChevronR<PERSON>, RotateCcw } from 'lucide-react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/next/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/next/components/ui/sidebar'
import { ConfigMapScope } from '#config/enums'
import { useTranslation } from 'react-i18next'
import { useAbilities } from '@/components/ssr'
import { graphql } from '@/gql'
import { useMutation } from '@/next/components/sdk/graphql'
import { Button } from '@/next/components/ui/button'

const items = [
  {
    scopes: [ConfigMapScope.Dashboard],
    id: 'dashboard',
  },
  {
    scopes: [ConfigMapScope.GitHub, ConfigMapScope.Publishing, ConfigMapScope.General],
    id: 'github',
  },
]

const updateConfigMapsMutation = graphql(`
  mutation UpdateConfigMaps($where: UpdateConfigMapsWhere!) {
    updateConfigMaps(where: $where)
  }
`)

export function NavConfigMap() {
  const { t } = useTranslation()
  const { canManageDashboard, canManageGitHub } = useAbilities()

  const [updateConfigMaps, updateConfigMapsState] = useMutation(updateConfigMapsMutation, {
    successMessage: 'Config is updating, we will inform you when it is done',
  })

  if (!canManageDashboard && !canManageGitHub) {
    return null
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Setting</SidebarGroupLabel>
      <SidebarMenu>
        <Collapsible asChild defaultOpen={false} className="group/collapsible">
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip="Config">
                <RotateCcw />
                <span>Reload Config</span>
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {items.map((item) => (
                  <SidebarMenuSubItem key={t(`common.sidebar.configMap.${item.id}`)}>
                    <SidebarMenuSubButton asChild>
                      <Button
                        onClick={() =>
                          updateConfigMaps({
                            where: {
                              scopes: item.scopes,
                            },
                          })
                        }
                        disabled={updateConfigMapsState.loading}
                        variant="ghost"
                        className="w-full justify-start font-normal"
                      >
                        {t(`common.sidebar.configMap.${item.id}`)}
                      </Button>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>
    </SidebarGroup>
  )
}
