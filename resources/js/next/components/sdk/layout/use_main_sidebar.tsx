import { useQuery } from '@/next/components/sdk/graphql'
import { graphql } from '@/gql'
import { SideMenuAttributesFragment } from '@/gql/graphql'
import { useMemo } from 'react'
import { DocumentNode } from '@apollo/client'
import {
  DollarSign,
  File,
  FileChartColumnIncreasing,
  List,
  LucideIcon,
  Settings,
  ShieldAlert,
  TrendingUpDown,
  Wallet,
  Workflow,
} from 'lucide-react'
import { groupBy, toPairs } from 'lodash-es'
import { NavMainItem } from '@/next/components/nav-main'
import { useTranslation } from 'react-i18next'
import { useCurrentUrl, isUrlActive } from '@/next/hooks/use_current_url'

export const getSidebarMainMenusQuery = graphql(`
  query Sidebar_GetMainMenus {
    sideMenus {
      collection {
        ...SideMenuAttributes
      }
    }
  }
`)

// Define types for the sidebar items to match NavMain component
export interface SidebarItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: {
    title: string
    url: string
  }[]
}

// Define the query result type
interface SidebarQueryResult {
  sideMenus: {
    collection: SideMenuAttributesFragment[]
  }
}

// Map icon strings from API to actual components
const iconMap: Record<string, LucideIcon> = {
  // all games
  'ManageSearchIcon': List,
  // game revenue
  'RequestQuoteIcon': DollarSign,
  // manager reports
  'AssessmentIcon': FileChartColumnIncreasing,
  // monet admob
  'CurrencyExchangeIcon': TrendingUpDown,
  // budget requests
  'WalletIcon': Wallet,
  '2': Settings,
  '3': Workflow,
  'ShieldAlert': ShieldAlert,
}

export function useMainSidebar() {
  const { t } = useTranslation()
  const currentUrl = useCurrentUrl()

  const query = useQuery<SidebarQueryResult>(
    getSidebarMainMenusQuery as DocumentNode,
    {},
    {
      toastOnError: false,
      errorMessage: 'Failed to load sidebar menu items',
    }
  )

  const { data } = query

  // Transform API data into the format needed by the sidebar component
  const sidebarItems = useMemo(() => {
    if (!data?.sideMenus?.collection) {
      return []
    }

    const menuGroups = toPairs(groupBy(data.sideMenus.collection, 'group'))
      .map(([group, items]) => {
        if (group === '1') {
          return items.map(
            (item): NavMainItem => ({
              title: item.name,
              url: item.path,
              icon: iconMap[item.icon] || File,
              isActive: isUrlActive(currentUrl, item.path),
            })
          )
        }

        const hasActiveSubItem = items.some((sub) => isUrlActive(currentUrl, sub.path))

        return [
          {
            title: t(`common.sidebar.main.group.${group}`),
            url: '#',
            icon: iconMap[group] || File,
            isActive: hasActiveSubItem,
            items: items.map((sub) => ({
              title: sub.name,
              url: sub.path,
              isActive: isUrlActive(currentUrl, sub.path),
            })),
          },
        ]
      })
      .flat()

    return menuGroups
  }, [data, currentUrl, t])

  return {
    sidebarItems,
    query,
  }
}
