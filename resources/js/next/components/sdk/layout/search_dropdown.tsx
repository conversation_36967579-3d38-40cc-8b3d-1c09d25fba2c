import { useEffect } from 'react'
import { Command, CommandEmpty, CommandGroup, CommandList } from '@/next/components/ui/command'
import { NoContent } from '@/next/components/sdk/loading/no_content'
import { UseFormReturn } from 'react-hook-form'
import { SearchFormValues } from './search_form'
import { GameAttributesFragment } from '@/graphql'
import { routes } from '@/components/location'
import { PlatformIcon } from '@/next/components/sdk/platform_icon'

interface SearchDropdownProps {
  open: boolean
  setOpen: (open: boolean) => void
  keyword: string
  form: UseFormReturn<SearchFormValues>
  results: GameAttributesFragment[]
}

export function SearchDropdown({ open, setOpen, keyword, form, results }: SearchDropdownProps) {
  // Render empty state for search results
  const renderNoResults = () => {
    return <CommandEmpty>No results found.</CommandEmpty>
  }

  if (!open || !keyword) {
    return null
  }

  return (
    <div className="absolute z-50 mt-1 w-full rounded-md border border-border bg-popover shadow-md">
      <Command>
        <CommandList>
          <NoContent empty={results.length === 0} renderEmpty={renderNoResults}>
            <CommandGroup>
              {results.map((item) => (
                <a
                  key={item.id}
                  href={routes.dash.gameMetrics.index(item.id)}
                  className="flex items-center justify-between px-2 py-1.5 text-sm hover:bg-accent rounded-sm cursor-pointer"
                  onClick={() => {
                    setOpen(false)
                    form.reset()
                  }}
                >
                  <div className="overflow-hidden whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <PlatformIcon platform={item.platform} />
                      <p className="font-medium">{item.name}</p>
                    </div>
                    <p className="text-muted-foreground overflow-hidden text-ellipsis">{item.id}</p>
                  </div>
                </a>
              ))}
            </CommandGroup>
          </NoContent>
        </CommandList>
      </Command>
    </div>
  )
}

// Hooks for handling keyboard and outside clicks
export function useSearchDropdownHandlers(
  open: boolean,
  setOpen: (open: boolean) => void,
  containerRef: React.RefObject<HTMLFormElement>
) {
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [containerRef, setOpen])

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        setOpen(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [open, setOpen])
}

// Function to filter search results
export function filterSearchResults(
  keyword: string,
  results: GameAttributesFragment[]
): GameAttributesFragment[] {
  if (!keyword) return []

  return results.filter((item) => item.name.toLowerCase().includes(keyword.toLowerCase()))
}
