import { Search } from 'lucide-react'
import { Label } from '@/next/components/ui/label'
import { SidebarGroup, SidebarGroupContent, SidebarInput } from '@/next/components/ui/sidebar'
import { Form, FormField } from '@/next/components/ui/form'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useRef, useState } from 'react'
import { useDebounce } from '@uidotdev/usehooks'
import { SearchDropdown, useSearchDropdownHandlers } from './search_dropdown'
import { graphql } from '@/gql'
import { useQuery } from '@/next/components/sdk/graphql'

const searchSchema = z.object({
  keyword: z.string().trim().min(1),
})

export type SearchFormValues = z.infer<typeof searchSchema>

interface SearchFormProps extends Omit<React.ComponentProps<'form'>, 'onSubmit'> {
  onSubmit?: (values: SearchFormValues) => void
}

export const getGamesQuery = graphql(`
  query Layout_GetGames($keyword: String!) {
    games(where: { keyword: $keyword }, offset: { page: 1, perPage: 10 }) {
      collection {
        ...GameAttributes
      }
    }
  }
`)

export function SearchForm({ onSubmit, ...props }: SearchFormProps) {
  const [open, setOpen] = useState(false)
  const formRef = useRef<HTMLFormElement>(null)
  const [queryVariables, setQueryVariables] = useState({ keyword: '' })
  const form = useForm<SearchFormValues>({
    resolver: zodResolver(searchSchema),
    defaultValues: {
      keyword: '',
    },
  })

  const search = useQuery(getGamesQuery, queryVariables, {
    skip: !queryVariables.keyword,
    onSuccess: () => setOpen(true),
  })

  const keyword = form.watch('keyword')
  const debouncedKeyword = useDebounce(keyword, 300)

  // Use the extracted handlers for keyboard and outside clicks
  useSearchDropdownHandlers(open, setOpen, formRef as React.RefObject<HTMLFormElement>)

  useEffect(() => {
    formRef.current?.dispatchEvent(new Event('submit', { bubbles: true }))
  }, [debouncedKeyword, formRef])

  const handleSubmit = form.handleSubmit((values) => {
    console.log(values)
    setQueryVariables(values)
  })

  return (
    <Form {...form}>
      <form {...props} onSubmit={handleSubmit} ref={formRef} className="relative">
        <SidebarGroup className="py-0">
          <SidebarGroupContent className="relative">
            <Label htmlFor="search" className="sr-only">
              Search
            </Label>
            <FormField
              control={form.control}
              name="keyword"
              render={({ field }) => (
                <SidebarInput
                  autoComplete="off"
                  {...field}
                  placeholder="Search in Mirai Studio..."
                  className="pl-8"
                  onFocus={() => keyword && setOpen(true)}
                />
              )}
            />

            <Search className="pointer-events-none absolute left-2 top-1/2 size-4 -translate-y-1/2 select-none opacity-50" />
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Use the extracted SearchDropdown component */}
        <SearchDropdown
          open={open}
          setOpen={setOpen}
          keyword={keyword}
          form={form}
          results={search.data?.games?.collection || []}
        />
      </form>
    </Form>
  )
}
