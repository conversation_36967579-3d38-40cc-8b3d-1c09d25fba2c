import { NavMainItem } from '@/next/components/nav-main'
import { useLayoutStore } from '@/next/stores/use_layout_store'
import { useEffect } from 'react'

export function useSubSidebar(items?: NavMainItem[]) {
  const { setSidebarSubItems, showSidebarSubItems, hideSidebarSubItems } = useLayoutStore()

  useEffect(() => {
    if (items && items.length > 0) {
      setSidebarSubItems(items)
      showSidebarSubItems()
    } else {
      hideSidebarSubItems()
      setSidebarSubItems([])
    }
  }, [items, setSidebarSubItems])
}
