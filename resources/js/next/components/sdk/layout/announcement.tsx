import { graphql } from '@/gql'
import { AnnouncementAttributesFragment } from '@/graphql'
import { useQuery } from '@/next/components/sdk/graphql'
import { NoContent } from '@/next/components/sdk/loading/no_content'
import { Menu, MenuItem, MenuSeparator } from '@/next/components/sdk/menu'
import { Fragment } from 'react/jsx-runtime'

export const getAnnouncementsQuery = graphql(`
  query Layout_GetAnnouncements {
    dashboardNotifications {
      collection {
        ...AnnouncementAttributes
      }
    }
  }
`)

graphql(`
  fragment AnnouncementAttributes on DashboardNotification {
    id
    message
    isPinned
    isVisible
  }
`)

export function useAnnouncements() {
  return useQuery(getAnnouncementsQuery)
}

export function Announcements({ items }: { items: AnnouncementAttributesFragment[] }) {
  return (
    <>
      <NoContent
        empty={items.length === 0}
        renderEmpty={() => (
          <p className="text-gray-500 text-center py-4 italic">No announcements</p>
        )}
      >
        <h3 className="font-medium text-sm uppercase tracking-wide text-gray-600 mb-1 px-3 pb-1 py-3">
          Announcements
        </h3>
        <Menu className="border-none shadow-none p--3">
          {items.map((ann, index) => (
            <Fragment key={ann.id}>
              {index > 0 && <MenuSeparator className="my-0" />}
              <MenuItem>
                <p>{ann.message}</p>
              </MenuItem>
            </Fragment>
          ))}
        </Menu>
      </NoContent>
    </>
  )
}
