import { ArrowLeft, ArrowRight, GalleryVerticalEnd, Megaphone, Milestone } from 'lucide-react'

import { NavMain } from '@/next/components/nav-main'
import { NavUser } from '@/next/components/sdk/layout/nav_user'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/next/components/ui/sidebar'
import { HelpTooltip } from '@/next/components/sdk/help_tooltip'
import { NavHeader } from '@/next/components/nav-header'
import { useMainSidebar } from './use_main_sidebar'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { NavError } from '@/next/components/nav-error'
import { ComponentProps, useCallback } from 'react'
import { useLayoutStore } from '@/next/stores/use_layout_store'
import { SearchForm, SearchFormValues } from '@/next/components/sdk/layout/search_form'
import { useSidebar } from '@/next/components/ui/sidebar'
import { useIsMobile } from '@/next/hooks/use-mobile'
import { cn } from '@/next/lib/utils'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { NavSecondaryItem } from '@/next/components/sdk/layout/nav'
import { Announcements, useAnnouncements } from '@/next/components/sdk/layout/announcement'
import { NavConfigMap } from './nav_config_map'

const team = {
  name: 'Mirai Studio',
  logo: GalleryVerticalEnd,
  plan: 'Toolkit',
}

export function AppSidebar({ ...props }: ComponentProps<typeof Sidebar>) {
  const { sidebarItems, query } = useMainSidebar()
  const { isSidebarSubItemsVisible, sidebarSubItems, toggleSidebarSubItems } = useLayoutStore()
  const { state } = useSidebar()
  const isMobile = useIsMobile()

  const isSearchFormVisible = state === 'expanded' || isMobile
  const onSubmit = useCallback((data: SearchFormValues) => {
    console.log(data)
  }, [])

  const getAnnouncements = useAnnouncements()

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <NavHeader team={team} />
        {isSearchFormVisible && <SearchForm onSubmit={onSubmit} />}
      </SidebarHeader>
      <SidebarContent className="gap-0">
        {sidebarSubItems.length > 0 && (
          <SidebarGroup className="py-0">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  className="transition-all duration-300 ease-in-out"
                  onClick={toggleSidebarSubItems}
                >
                  {isSidebarSubItemsVisible ? (
                    <>
                      <ArrowLeft size={18} />
                      Main menu
                    </>
                  ) : (
                    <>
                      <ArrowRight size={18} />
                      Back
                    </>
                  )}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
        )}

        <div className="relative overflow-hidden w-full min-h-[100px] flex-grow">
          <div
            className={cn(
              'transition-all duration-300 ease-out absolute inset-0 transform origin-left',
              isSidebarSubItemsVisible
                ? 'translate-x-0 opacity-100 scale-100'
                : 'translate-x-[-100%] opacity-0 scale-95'
            )}
          >
            <NavMain items={sidebarSubItems} />
          </div>

          <div
            className={cn(
              'transition-all duration-300 ease-out absolute inset-0 transform origin-right',
              isSidebarSubItemsVisible
                ? 'translate-x-[100%] opacity-0 scale-95'
                : 'translate-x-0 opacity-100 scale-100'
            )}
          >
            <QueryState
              query={query}
              className="min-h-32"
              renderError={() => <NavError onRefetch={() => query.refetch()} />}
            >
              <NavMain items={sidebarItems} />
              <NavConfigMap />
            </QueryState>
          </div>
        </div>

        <SidebarGroup className="mt-auto">
          <SidebarGroupContent>
            <SidebarMenu>
              <Popover>
                <PopoverTrigger asChild>
                  <NavSecondaryItem icon={Megaphone} title="Announcements" />
                </PopoverTrigger>

                <PopoverContent
                  className="w-80 p-0 max-h-[70vh] overflow-y-auto"
                  align={isMobile ? 'start' : 'end'}
                  side={isMobile ? 'top' : 'right'}
                  sideOffset={16}
                >
                  <QueryState query={getAnnouncements}>
                    <Announcements
                      items={getAnnouncements.data?.dashboardNotifications?.collection || []}
                    />
                  </QueryState>
                </PopoverContent>
              </Popover>

              <NavSecondaryItem
                icon={Milestone}
                title={
                  <div className="flex items-center gap-1">
                    {import.meta.env.VITE_BUILD_VERSION}
                    <HelpTooltip>Click to see release notes</HelpTooltip>
                  </div>
                }
              />
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
