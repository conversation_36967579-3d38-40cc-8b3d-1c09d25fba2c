import { useHashLinkContext, useHashNavigate } from '@/next/components/sdk/hash_link'
import { Dialog, DialogContent } from '@/next/components/ui/dialog'
import { cn } from '@/next/lib/utils'
import { Root } from '@radix-ui/react-slot'
import { ComponentProps, useCallback } from 'react'

export interface FloatingRouteProps extends ComponentProps<typeof DialogContent> {
  id: string
}

export function FloatingRoute({ id, className, ...props }: FloatingRouteProps) {
  const { path } = useHashLinkContext()
  const [_, closeFloatingRoute] = useFloatingRoute()
  const isOpen = path === id

  return (
    <Dialog open={isOpen} onOpenChange={closeFloatingRoute}>
      <DialogContent className={cn(className)} {...props} />
    </Dialog>
  )
}

export function useFloatingRoute() {
  const [navigate, unset] = useHashNavigate()

  const openFloatingRouteWithParams = useCallback(
    <T extends Record<string, string> = {}>(id: string, params: T = {} as T) => {
      navigate(id, params)
    },
    [navigate]
  )

  const closeFloatingRoute = useCallback(() => {
    unset()
  }, [unset])

  return [openFloatingRouteWithParams, closeFloatingRoute] as const
}

export function FloatingRouteContent({ className, ...props }: ComponentProps<typeof Root>) {
  return <Root className={cn('max-h-[calc(100vh-125px)]', className)} {...props}></Root>
}
