// Custom Android icon matching Lucide React style
export const Android = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-4 w-4 text-muted-foreground"
    >
      <path d="M5 16V8c0-3.5 2.5-6 6-6h2c3.5 0 6 2.5 6 6v8c0 .5-.5 1-1 1H6c-.5 0-1-.5-1-1Z" />
      <path d="M12 2v4" />
      <path d="M18 8h.5c.5 0 1 .5 1 1v2" />
      <path d="M5 8h-.5c-.5 0-1 .5-1 1v2" />
      <path d="M12 16v4" />
      <path d="M8 16v4" />
      <path d="M16 16v4" />
      <circle cx="9" cy="9" r="1" />
      <circle cx="15" cy="9" r="1" />
    </svg>
  )
}
