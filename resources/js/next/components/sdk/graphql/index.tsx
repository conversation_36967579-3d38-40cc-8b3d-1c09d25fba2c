import {
  ApolloError,
  DocumentNode,
  OperationVariables,
  TypedDocumentNode,
  useQ<PERSON>y as useApolloQuery,
  useMutation as useApolloMutation,
  useLazyQuery as useApolloLazyQuery,
  QueryHookOptions,
  MutationHookOptions,
} from '@apollo/client'
import { useCallback, useMemo } from 'react'
import { errorToast } from '../toast/error_toast'
import { successToast } from '../toast/success_toast'

type CustomOptions = {
  toastOnError?: boolean
  toastOnSuccess?: boolean
  successMessage?: string
  errorMessage?: string
}

interface UseQueryOptions<TData, TVariables extends OperationVariables>
  extends Omit<QueryHookOptions<TData, TVariables>, 'onError' | 'onCompleted'>,
    CustomOptions {
  onError?: (error: ApolloError) => void
  onSuccess?: (data: TData) => void
}

interface UseMutationOptions<TData, TVariables extends OperationVariables>
  extends Omit<MutationHookOptions<TData, TVariables>, 'onError' | 'onCompleted'>,
    CustomOptions {
  onError?: (error: ApolloError) => void
  onSuccess?: (data: TData) => void
  onMutate?: (variables: TVariables) => void
}

export function useQuery<TData = any, TVariables extends OperationVariables = OperationVariables>(
  query: DocumentNode | TypedDocumentNode<TData, TVariables>,
  variables?: TVariables,
  options: UseQueryOptions<TData, TVariables> = {}
) {
  const {
    onError,
    onSuccess,
    toastOnError = true,
    toastOnSuccess = false,
    successMessage = 'Operation successful',
    errorMessage = 'An error occurred',
    ...apolloOptions
  } = options

  const mergedOptions = useMemo(
    () => ({
      ...apolloOptions,
      variables,
      onError: (error: ApolloError) => {
        if (toastOnError) {
          errorToast({
            title: errorMessage,
            message: error.message,
          })
        }
        onError?.(error)
      },
      onCompleted: (data: TData) => {
        if (toastOnSuccess) {
          successToast({
            message: successMessage,
          })
        }
        onSuccess?.(data)
      },
    }),
    [
      variables,
      onError,
      onSuccess,
      toastOnError,
      toastOnSuccess,
      successMessage,
      errorMessage,
      apolloOptions,
    ]
  )

  return useApolloQuery<TData, TVariables>(query, mergedOptions)
}

export function useLazyQuery<
  TData = any,
  TVariables extends OperationVariables = OperationVariables,
>(
  query: DocumentNode | TypedDocumentNode<TData, TVariables>,
  options: UseQueryOptions<TData, TVariables> = {}
) {
  const {
    onError,
    onSuccess,
    toastOnError = true,
    toastOnSuccess = false,
    successMessage = 'Operation successful',
    errorMessage = 'An error occurred',
    ...apolloOptions
  } = options

  const mergedOptions = useMemo(
    () => ({
      ...apolloOptions,
      onError: (error: ApolloError) => {
        if (toastOnError) {
          errorToast({
            title: errorMessage,
            message: error.message,
          })
        }
        onError?.(error)
      },
      onCompleted: (data: TData) => {
        if (toastOnSuccess) {
          successToast({
            message: successMessage,
          })
        }
        onSuccess?.(data)
      },
    }),
    [onError, onSuccess, toastOnError, toastOnSuccess, successMessage, errorMessage, apolloOptions]
  )

  const [innerExecute, result] = useApolloLazyQuery<TData, TVariables>(query, mergedOptions)
  const memoizedExecute = useCallback(innerExecute, [innerExecute])

  return [memoizedExecute, result] as const
}

export function useMutation<
  TData = any,
  TVariables extends OperationVariables = OperationVariables,
>(
  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,
  options: UseMutationOptions<TData, TVariables> = {}
) {
  const {
    onError,
    onSuccess,
    onMutate,
    toastOnError = true,
    toastOnSuccess = true,
    successMessage = 'Operation successful',
    errorMessage = 'An error occurred',
    ...apolloOptions
  } = options

  const mergedOptions = useMemo(
    () => ({
      ...apolloOptions,
      onError: (error: ApolloError) => {
        if (toastOnError) {
          errorToast({
            title: errorMessage,
            message: error.message,
          })
        }
        onError?.(error)
      },
      onCompleted: (data: TData) => {
        if (toastOnSuccess) {
          successToast({
            message: successMessage,
          })
        }
        onSuccess?.(data)
      },
    }),
    [onError, onSuccess, toastOnError, toastOnSuccess, successMessage, errorMessage, apolloOptions]
  )

  const [execute, result] = useApolloMutation<TData, TVariables>(mutation, mergedOptions)

  const wrappedExecute = useCallback(
    async (variables?: TVariables) => {
      try {
        onMutate?.(variables as TVariables)
        return await execute({ variables })
      } catch (error) {
        // Apollo will handle the error through onError callback
        throw error
      }
    },
    [execute, onMutate]
  )

  return [wrappedExecute, result] as const
}
