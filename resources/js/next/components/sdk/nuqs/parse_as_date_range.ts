import { format, isValid, parse, toDate } from 'date-fns'
import { createParser } from 'nuqs'

export const parseAsDateRange = createParser({
  parse(queryValue) {
    const values = queryValue.split(',')
    const from = isValid(toDate(values[0])) ? parse(values[0], 'yyyy-MM-dd', new Date()) : null
    const to = isValid(toDate(values[1])) ? parse(values[1], 'yyyy-MM-dd', new Date()) : null

    if (from || to) {
      return [from || to, to || from]
    }

    return null
  },
  serialize(value) {
    return value ? value.map((v) => format(v as Date, 'yyyy-MM-dd')).join(',') : ''
  },
})
