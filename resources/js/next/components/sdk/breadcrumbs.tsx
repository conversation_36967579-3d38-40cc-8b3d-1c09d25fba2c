import React, { Fragment, useMemo } from 'react'
import { useLayoutStore } from '@/next/stores/use_layout_store'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/next/components/ui/breadcrumb'

import { useEffect } from 'react'
import type { BreadcrumbItem as BreadcrumbItemType } from '@/next/stores/use_layout_store'
import { Link } from '@inertiajs/react'

export type { BreadcrumbItemType as BreadcrumbItem }
export type BreadcrumbItemProp = BreadcrumbItemType | string

export function useBreadcrumbs(items: BreadcrumbItemProp[]) {
  const setBreadcrumbs = useLayoutStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    // Normalize string items to BreadcrumbItem objects
    const normalizedItems = items.map((item): BreadcrumbItemType => {
      if (typeof item === 'string') {
        return { title: item }
      }
      return item
    })

    setBreadcrumbs(normalizedItems)

    return () => {
      setBreadcrumbs([])
    }
  }, [setBreadcrumbs, JSON.stringify(items)])
}

export const Breadcrumbs: React.FC = () => {
  const breadcrumbs = useLayoutStore((state) => state.breadcrumbs)

  const items = useMemo(() => {
    return [{ title: 'Home', url: '/' }, ...breadcrumbs]
  }, [breadcrumbs])

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {items.map((crumb, index) => {
          const isLast = index === items.length - 1

          return (
            <Fragment key={index.toString()}>
              <BreadcrumbItem className="hidden md:block">
                {crumb.url ? (
                  <BreadcrumbLink asChild>
                    <Link
                      data-testid={`breadcrumb_item_${isLast ? 'last' : index}`}
                      href={crumb.url}
                    >
                      {crumb.title}
                    </Link>
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage data-testid={`breadcrumb_item_${isLast ? 'last' : index}`}>
                    {crumb.title}
                  </BreadcrumbPage>
                )}
              </BreadcrumbItem>

              {!isLast && <BreadcrumbSeparator className="hidden md:block" />}
            </Fragment>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}

export default Breadcrumbs
