import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { flexRender, type Table } from '@tanstack/react-table'
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/next/components/ui/table'
import { But<PERSON> } from '@/next/components/ui/button'
import { ChevronRight, ChevronDown, Loader2 } from 'lucide-react'
import { cn } from '@/next/lib/utils'
import { useDataTable, UseDataTableProps } from '@/next/hooks/use-data-table'
import { StickyTable } from './sticky_table'
import { DataTablePagination } from '@/next/components/data-table/data-table-pagination'
import { getCommonPinningStyles } from '@/next/lib/data-table'

// Base node interface for tree data
export interface TreeNode {
  hasChildren?: boolean
  isExpanded?: boolean
  isLoading?: boolean
  [key: string]: any
}

// Function to get node keys from node
export type GetNodeKeysFunction<T> = (node: T) => string[]

// Function to generate node ID from keys
export type GetNodeIdFunction = (keys: string[]) => string

// Additional props for actions
export interface TreeTableProps<TData extends TreeNode> extends React.ComponentProps<'div'> {
  onExpandRow?: (node: TData) => Promise<TData[]>
  getRowCanExpand?: (row: TData) => boolean
  indentSize?: number
  expandColumn?: string
  className?: string
  children?: React.ReactNode
  getNodeKeys: GetNodeKeysFunction<TData>
  getNodeId?: GetNodeIdFunction
  table: Table<TData>
  sticky?: boolean
  pageSizeOptions?: number[]
}

// Default function to generate node ID from keys
const defaultGetNodeId = (keys: string[]): string => keys.join('|')

export function TreeTable<TData extends TreeNode>({
  onExpandRow,
  getRowCanExpand,
  indentSize = 24,
  expandColumn,
  className,
  children,
  getNodeKeys,
  getNodeId = defaultGetNodeId,
  table,
  sticky = false,
  pageSizeOptions = [10, 20, 30, 40, 50],
  ...props
}: TreeTableProps<TData>) {
  // Extract treeOptions from the table if available (from useTreeTable hook)
  const treeOptions = (table as any).treeOptions || {}

  // Local state for expanded and loading rows if not provided externally
  const [localExpandedRows, setLocalExpandedRows] = useState<Record<string, boolean>>({})
  const [localLoadingRows, setLocalLoadingRows] = useState<Record<string, boolean>>({})

  // Use either the props or the values from treeOptions or local state
  const expandedRows = treeOptions.expandedRows || localExpandedRows
  const setExpandedRows = treeOptions.setExpandedRows || setLocalExpandedRows
  const loadingRows = treeOptions.loadingRows || localLoadingRows
  const setLoadingRows = treeOptions.setLoadingRows || setLocalLoadingRows

  const effectiveExpandColumn = expandColumn || treeOptions.expandColumn
  const effectiveGetNodeKeys = getNodeKeys || treeOptions.getNodeKeys
  const effectiveGetNodeId = getNodeId || treeOptions.getNodeId || defaultGetNodeId

  // Function to get node ID from a node
  const getNodeIdFromNode = useCallback(
    (node: TData): string => {
      const keys = effectiveGetNodeKeys(node)
      return effectiveGetNodeId(keys)
    },
    [effectiveGetNodeKeys, effectiveGetNodeId]
  )

  // Get parent ID from node keys
  const getParentId = useCallback(
    (node: TData): string | null => {
      const keys = effectiveGetNodeKeys(node)
      if (keys.length <= 1) return null // No parent for root nodes

      const parentKeys = keys.slice(0, -1)
      return effectiveGetNodeId(parentKeys)
    },
    [effectiveGetNodeKeys, effectiveGetNodeId]
  )

  // Build a tree structure from the flat rows
  const hierarchyMap = useMemo(() => {
    const rows = table.getRowModel().rows
    const nodeMap: Record<string, { node: TData; children: string[] }> = {}
    const rootNodes: string[] = []

    // First pass: map all nodes
    rows.forEach((row) => {
      const node = row.original as TData
      const nodeId = getNodeIdFromNode(node)
      nodeMap[nodeId] = { node, children: [] }
    })

    // Second pass: build parent-child relationships
    rows.forEach((row) => {
      const node = row.original as TData
      const nodeId = getNodeIdFromNode(node)
      const parentId = getParentId(node)

      if (parentId && nodeMap[parentId]) {
        // Add this node as a child of its parent
        nodeMap[parentId].children.push(nodeId)
      } else {
        // This is a root node
        rootNodes.push(nodeId)
      }
    })

    return { nodeMap, rootNodes }
  }, [table.getRowModel().rows, getNodeIdFromNode, getParentId])

  // Function to handle row expansion
  const handleToggleRow = useCallback(
    async (node: TData) => {
      const nodeId = getNodeIdFromNode(node)

      // Toggle expansion state locally
      const newExpandedRows = { ...expandedRows }
      const isCurrentlyExpanded = !!expandedRows[nodeId]

      if (isCurrentlyExpanded) {
        // If currently expanded, collapse it
        delete newExpandedRows[nodeId]
        setExpandedRows(newExpandedRows)
      } else {
        // If currently collapsed, expand it
        newExpandedRows[nodeId] = true
        setExpandedRows(newExpandedRows)

        // Call handler to load children if needed
        if (
          (node.hasChildren || (getRowCanExpand && getRowCanExpand(node))) &&
          (treeOptions.onExpandRow || onExpandRow)
        ) {
          // Mark as loading
          const newLoadingRows = { ...loadingRows, [nodeId]: true }
          setLoadingRows(newLoadingRows)

          try {
            // Call handler to load children
            if (treeOptions.onExpandRow) {
              await treeOptions.onExpandRow(node)
            } else if (onExpandRow) {
              await onExpandRow(node)
            }
          } finally {
            // Remove loading state regardless of success/failure
            const updatedLoadingRows = { ...loadingRows }
            delete updatedLoadingRows[nodeId]
            setLoadingRows(updatedLoadingRows)
          }
        }
      }
    },
    [
      treeOptions.onExpandRow,
      onExpandRow,
      expandedRows,
      loadingRows,
      getNodeIdFromNode,
      getRowCanExpand,
      setExpandedRows,
      setLoadingRows,
    ]
  )

  // Default function to determine if a row can expand
  const canExpand = useCallback(
    (row: TData) => {
      if (treeOptions.canExpand) {
        return treeOptions.canExpand(row)
      } else if (getRowCanExpand) {
        return getRowCanExpand(row)
      }
      return row.hasChildren ?? false
    },
    [treeOptions.canExpand, getRowCanExpand]
  )

  // Get node level based on keys
  const getNodeLevel = useCallback(
    (node: TData): number => {
      if (treeOptions.getNodeLevel) {
        return treeOptions.getNodeLevel(node)
      }

      if (!effectiveGetNodeKeys) return 0

      const keys = effectiveGetNodeKeys(node)
      return keys.length - 1 // Level is 0-based
    },
    [treeOptions.getNodeLevel, effectiveGetNodeKeys]
  )

  // Function to get ordered rows considering hierarchy
  const rows = useMemo(() => {
    const { nodeMap, rootNodes } = hierarchyMap
    const orderedNodes: TData[] = []

    // Recursive function to traverse the tree
    const traverseNode = (nodeId: string, level: number) => {
      if (!nodeMap[nodeId]) return // Skip if node doesn't exist

      const { node, children } = nodeMap[nodeId]
      orderedNodes.push(node)

      // Only process children if node is expanded
      if (expandedRows[nodeId] && children.length > 0) {
        children.forEach((childId) => traverseNode(childId, level + 1))
      }
    }

    // Start traversal from root nodes
    rootNodes.forEach((nodeId) => traverseNode(nodeId, 0))

    return orderedNodes
  }, [hierarchyMap, expandedRows])

  // Add expansion controls to cells
  const enhanceColumnWithExpandControl = useCallback(
    (columnId: string, cell: any, props: any) => {
      const { row } = props
      const rowNode = row.original as TData
      const nodeId = getNodeIdFromNode(rowNode)
      const rowCanExpand = canExpand(rowNode)
      const isExpanded = !!expandedRows[nodeId]
      const isRowLoading = !!loadingRows[nodeId]
      const level = getNodeLevel(rowNode)

      // For debugging
      // console.log('NodeId:', nodeId, 'isExpanded:', isExpanded)

      // Calculate indent based on level
      const paddingLeft = level * indentSize

      if (
        columnId === effectiveExpandColumn ||
        (!effectiveExpandColumn && columnId === table.getAllColumns()[0].id)
      ) {
        return (
          <div className="flex items-center" style={{ paddingLeft: `${paddingLeft}px` }}>
            {rowCanExpand && (
              <Button
                size="sm"
                variant="ghost"
                className="mr-1 h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation()
                  handleToggleRow(rowNode)
                }}
                disabled={isRowLoading}
              >
                {isRowLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                ) : isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}
            {cell}
          </div>
        )
      }

      return cell
    },
    [
      table,
      effectiveExpandColumn,
      expandedRows,
      loadingRows,
      canExpand,
      indentSize,
      getNodeLevel,
      handleToggleRow,
      getNodeIdFromNode,
    ]
  )

  // Check if pagination should be shown
  const isPaginationVisible = table.getPageCount() > 0

  return (
    <div className={cn(className, 'flex w-full flex-col gap-2.5 overflow-auto')} {...props}>
      {children}

      <StickyTable sticky={sticky} watch={[rows]}>
        <UITable>
          <TableHeader className={cn({ 'bg-background': sticky })}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...getCommonPinningStyles({ column: header.column }),
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {rows.length ? (
              rows.map((rowData, index) => {
                // Find the corresponding row from the table
                // Use getNodeIdFromNode for comparison to ensure correct matching
                const nodeId = getNodeIdFromNode(rowData)
                const row = table
                  .getRowModel()
                  .rows.find((r) => getNodeIdFromNode(r.original as TData) === nodeId)

                if (!row) return null

                return (
                  <TableRow
                    key={`row-${nodeId}-${index}`}
                    data-state={row.getIsSelected() && 'selected'}
                    className={cn(
                      'hover:bg-muted/50 transition-colors',
                      index % 2 === 0 ? 'bg-background' : 'bg-muted/20'
                    )}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        style={{
                          ...getCommonPinningStyles({ column: cell.column }),
                          // Override background để không conflict với row hover
                          background: cell.column.getIsPinned()
                            ? getCommonPinningStyles({ column: cell.column }).background
                            : 'inherit',
                        }}
                      >
                        {enhanceColumnWithExpandControl(
                          cell.column.id,
                          flexRender(cell.column.columnDef.cell, cell.getContext()),
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                )
              })
            ) : (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </UITable>
      </StickyTable>

      {isPaginationVisible && (
        <div className="flex flex-col gap-2.5">
          <DataTablePagination table={table} pageSizeOptions={pageSizeOptions} />
        </div>
      )}
    </div>
  )
}

export interface UseTreeTableProps<TData extends TreeNode>
  extends Omit<UseDataTableProps<TData>, 'getRowCanExpand'> {
  onExpandRow: TreeTableProps<TData>['onExpandRow']
  indentSize: TreeTableProps<TData>['indentSize']
  expandColumn: TreeTableProps<TData>['expandColumn']
  getNodeKeys: TreeTableProps<TData>['getNodeKeys']
  getNodeId: TreeTableProps<TData>['getNodeId']
  getRowCanExpand: TreeTableProps<TData>['getRowCanExpand']
}

export function useTreeTable<TData extends TreeNode>({
  onExpandRow,
  getRowCanExpand,
  indentSize,
  expandColumn,
  getNodeKeys,
  getNodeId,
  ...props
}: UseTreeTableProps<TData>) {
  const { table } = useDataTable<TData>({ ...props })

  return {
    table,
    getNodeKeys,
    getNodeId,
    onExpandRow,
    indentSize,
    expandColumn,
    getRowCanExpand,
  }
}
