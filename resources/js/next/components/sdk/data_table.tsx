import {
  type Table as TanstackTable,
  AccessorFnColumnDef,
  create<PERSON>olumnHelper,
  flexRender,
  HeaderContext,
} from '@tanstack/react-table'
import type * as React from 'react'
import { ArrowBigDownDash, Funnel, Loader2Icon, Pencil, PencilLine, Trash2 } from 'lucide-react'
import { stringify } from 'csv-stringify/browser/esm'
import { DataTablePagination } from '@/next/components/data-table/data-table-pagination'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/next/components/ui/table'
import { getCommonPinningStyles } from '@/next/lib/data-table'
import { cn } from '@/next/lib/utils'
import { Button } from '@/next/components/ui/button'
import { DataTableColumnHeader } from '@/next/components/data-table/data-table-column-header'
import { Checkbox } from '../ui/checkbox'
import { DisplayColumnDef } from '@tanstack/react-table'
import { StickyTable } from './sticky_table'
import { Too<PERSON><PERSON>, Toolt<PERSON>Content, TooltipTrigger } from '../ui/tooltip'
import { format } from 'date-fns'

const stringifyAsync = async (data: any[]) => {
  return new Promise((resolve, reject) => {
    stringify(data, (err, output) => {
      if (err) reject(err)
      resolve(output)
    })
  })
}

interface DataTableProps<TData> extends React.ComponentProps<typeof Table> {
  table: TanstackTable<TData>
  actionBar?: React.ReactNode
  sticky?: boolean
  pageSizeOptions?: number[]
}

export function DataTable<TData>({
  table,
  actionBar,
  sticky = false,
  children,
  pageSizeOptions = [10, 20, 30, 40, 50],
  ...props
}: DataTableProps<TData>) {
  const isPaginationVisible = table.getPageCount() >= 0

  const rows = table.getRowModel().rows

  return (
    <div className={cn('flex w-full flex-col gap-2.5 overflow-auto')}>
      {children}

      <StickyTable sticky={sticky} watch={[rows]}>
        <Table {...props}>
          <TableHeader className={cn({ 'bg-background': sticky })}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...getCommonPinningStyles({ column: header.column }),
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {rows?.length ? (
              rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      style={{
                        ...getCommonPinningStyles({ column: cell.column }),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </StickyTable>

      {isPaginationVisible && (
        <div className="flex flex-col gap-2.5">
          <DataTablePagination table={table} pageSizeOptions={pageSizeOptions} />
          {actionBar && table.getFilteredSelectedRowModel().rows.length > 0 && actionBar}
        </div>
      )}
    </div>
  )
}

export function tableHeader<T>(
  name: string,
  {
    bgcolor,
    className,
    ...props
  }: Omit<React.ComponentProps<typeof DataTableColumnHeader>, 'column' | 'title'> & {
    bgcolor?: string
  } = {}
) {
  return ({ column }: HeaderContext<T, unknown>) => {
    return (
      <>
        {bgcolor && <div className={cn(bgcolor, 'absolute inset-0 z-0')} />}
        <DataTableColumnHeader
          column={column}
          title={name}
          className={cn(className, {
            'z-10 relative h-full bg-transparent! outline-none! shadow-none! focus:outline-none focus:ring-0 focus:shadow-none hover:text-muted-foreground transition-all':
              bgcolor,
          })}
          {...props}
        />
      </>
    )
  }
}

const columnHelper = createColumnHelper<any>()

export function tableSelect<T>(
  options: Partial<DisplayColumnDef<T, string>> = {}
): AccessorFnColumnDef<T, string> {
  return columnHelper.accessor(() => 'select', {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    size: 32,
    enableSorting: false,
    enableHiding: true,
    ...options,
  })
}

export function TableCellButton(props: React.ComponentProps<typeof Button>) {
  return <Button size="sm" className={cn('h-8 lg:flex')} {...props} />
}

export function TableCellEditButton(props: React.ComponentProps<typeof Button>) {
  return (
    <TableCellButton variant="link" {...props}>
      <PencilLine />
      Edit
    </TableCellButton>
  )
}

export function TableCellDeleteButton(props: React.ComponentProps<typeof Button>) {
  return (
    <TableCellButton variant="link" {...props}>
      <Trash2 />
      Delete
    </TableCellButton>
  )
}

export function DataTableFilterSubmitButton({
  loading,
  ...props
}: React.ComponentProps<typeof Button> & { loading?: boolean }) {
  return (
    <Button size="sm" type="button" className="border-dashed" {...props}>
      {loading ? <Loader2Icon className="animate-spin" /> : <Funnel />}
      {loading ? 'Appling...' : 'Apply'}
    </Button>
  )
}

export function DataTableToolbarButton({
  children,
  className,
  ...props
}: React.ComponentProps<typeof Button>) {
  return (
    <Button variant="outline" size="sm" className={className} {...props}>
      {children}
    </Button>
  )
}

export function EditableTableCell({
  className,
  children,
  ...props
}: React.ComponentProps<'button'>) {
  return (
    <div className="flex gap-1">
      {children}

      <button
        className={cn(
          'cursor-pointer hover:bg-muted rounded transition-colors self-start',
          className
        )}
        {...props}
      >
        <Pencil className="w-3 h-3 text-muted-foreground hover:text-foreground border-b-1 border-b-muted-foreground" />
      </button>
    </div>
  )
}

export function DataTableCsvDownloadButton<T>({
  table,
  ...props
}: {
  table: TanstackTable<T>
} & React.ComponentProps<typeof DataTableToolbarButton>) {
  const handleDownload = async () => {
    const headers = table.getFlatHeaders()
    const rows = table.getRowModel().flatRows
    const columns = headers
      .map((header) => {
        const column = table.getAllFlatColumns().find((c) => c.id === header.id)
        return column?.getIsVisible() ? column : null
      })
      .filter(Boolean)

    const headerRow = columns.map((column) => {
      return column!.columnDef.meta!.label
    })

    const dataRows = rows.map((row) => {
      return columns.map((col) => {
        return row.getValue(col!.id)
      })
    })

    const csvRows = [headerRow, ...dataRows]

    const csvBlob = new Blob([(await stringifyAsync(csvRows)) as string], {
      type: 'text/csv;charset=utf-8;',
    })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(csvBlob)
    link.download = `export-${format(new Date(), 'yyyyMMddHHmmss')}.csv`
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <DataTableToolbarButton {...props} onClick={handleDownload}>
          <ArrowBigDownDash />
        </DataTableToolbarButton>
      </TooltipTrigger>
      <TooltipContent>Download as CSV</TooltipContent>
    </Tooltip>
  )
}
