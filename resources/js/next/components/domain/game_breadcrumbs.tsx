import { routes } from '@/components/location'
import { BreadcrumbItem } from '@/next/stores/use_layout_store'

export function makeGameBreadcrumbs(game: { name?: string; id?: string }, currentPageName: string) {
  return [
    { title: 'All Games', url: routes.dash.games.index() },
    { title: game.name, url: routes.dash.games.show(game.id) },
    { title: currentPageName },
  ] as BreadcrumbItem[]
}
