import { ViewPresetAttributesFragment } from '@/graphql'
import { Button } from '@/next/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/next/components/ui/popover'
import { Pencil, Trash2 } from 'lucide-react'
import { cn } from '@/next/lib/utils'
import { ReactNode } from '@/next/components/sdk/types'
import {
  FloatingRoute,
  FloatingRouteContent,
  useFloatingRoute,
} from '@/next/components/sdk/floating_route'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { useHashState } from '@/next/components/sdk/hash_link'
import { parseAsInteger, useQueryState } from 'nuqs'
import ViewPresetConfigMap from '#configmaps/dashboard/view_preset'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { useForm } from 'react-hook-form'
import { Form } from '@/next/components/ui/form'
import { useEffect, useMemo, useState } from 'react'
import { useMutation } from '@/next/components/sdk/graphql'
import { graphql } from '@/gql'
import { Input } from '../ui/input'
import { ConfirmDialog } from '@/next/components/sdk/dialog/confirm_dialog'
import { ConfirmationPopover } from '@/next/components/sdk/confirmation_popover'
import { useTranslation } from 'react-i18next'

const createViewPresetMutation = graphql(`
  mutation ViewPresetEditor_CreateViewPreset($form: CreateViewPresetForm!) {
    createViewPreset(form: $form) {
      ...ViewPresetAttributes
    }
  }
`)

const deleteViewPresetMutation = graphql(`
  mutation ViewPresetSelection_DeleteViewPreset($where: DeleteViewPresetsWhere!) {
    deleteViewPresets(where: $where)
  }
`)

export function ViewPresetSelection({
  title = 'Select view',
  presets,
  onEdit,
  onDelete,
  onSelect,
}: {
  presets: ViewPresetAttributesFragment[]
  title?: ReactNode
  onEdit?: (preset: ViewPresetAttributesFragment) => void
  onDelete?: (preset: ViewPresetAttributesFragment) => void
  onSelect?: (preset: ViewPresetAttributesFragment) => void
}) {
  const [navigate] = useFloatingRoute()
  const [, setPreset] = useQueryState('preset', parseAsInteger)
  const [deleteViewPreset] = useMutation(deleteViewPresetMutation, {
    toastOnSuccess: true,
    successMessage: 'View deleted successfully',
  })

  const handleEdit = (preset: ViewPresetAttributesFragment) => {
    onEdit?.(preset)
    navigate('preset', { id: preset.id.toString() })
  }

  const handleDelete = async (preset: ViewPresetAttributesFragment) => {
    await deleteViewPreset({
      where: {
        ids: [preset.id],
      },
    })
    onDelete?.(preset)
  }

  const handleSelect = (preset: ViewPresetAttributesFragment) => {
    onSelect?.(preset)
    setPreset(preset.id)
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm">
          {title}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]">
        <div className="mt-2 flex flex-col gap-2">
          {presets.map((preset) => (
            <div
              key={preset.id}
              className={cn(
                'flex items-center rounded border',
                'hover:bg-accent hover:text-accent-foreground'
              )}
            >
              <Button
                variant="ghost"
                className="flex-1 justify-start rounded-none px-3 py-2 hover:bg-transparent"
                onClick={() => handleSelect(preset)}
              >
                <span className="text-sm">{preset.name}</span>
              </Button>
              <div className="flex items-center border-l">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEdit(preset)}
                  className="size-8 rounded-none hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400"
                >
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">Edit preset</span>
                </Button>
                <ConfirmationPopover
                  title={`Delete ${preset.name}`}
                  confirmText="Are you sure you want to delete this view? This action cannot be undone."
                  onConfirm={() => handleDelete(preset)}
                  trigger={
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-8 rounded-none hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete preset</span>
                    </Button>
                  }
                />
              </div>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}

export function ViewPresetEditor({
  presets,
  viewPresetConfigMap,
  onSave,
}: {
  presets?: ViewPresetAttributesFragment[]
  viewPresetConfigMap?: ViewPresetConfigMap
  onSave?: () => void
}) {
  const { t } = useTranslation()
  const [id] = useHashState('id', parseAsInteger)
  const [_, closeFloatingRoute] = useFloatingRoute()
  const preset = presets?.find((p) => p.id === id)
  const [selectedNonCohortAttributes, setSelectedNonCohortAttributes] = useState<string[]>([])
  const [saveViewDialogOpen, setSaveViewDialogOpen] = useState(false)
  const [viewName, setViewName] = useState('')

  const [createViewPreset, { loading }] = useMutation(createViewPresetMutation, {
    onSuccess: () => {
      setSaveViewDialogOpen(false)
      onSave?.()
      closeFloatingRoute()
    },
  })

  // Calculate default values before form initialization
  const defaultValues = useMemo(() => {
    if (!preset || !viewPresetConfigMap) return {}

    const cohortAttributes = viewPresetConfigMap.attributes.filter((p) => p.isCohort)
    const nonCohortAttributes = viewPresetConfigMap.attributes.filter((p) => !p.isCohort)

    // Set initial selected non-cohort attributes
    if (preset) {
      const selected = nonCohortAttributes
        .filter((attr) => preset.attributes.find((a) => a.name === attr.name))
        .map((attr) => attr.name)
      setSelectedNonCohortAttributes(selected)
    }

    return cohortAttributes.reduce(
      (acc, attr) => {
        const presetAttr = preset.attributes.find((a) => a.name === attr.name)
        acc[attr.name] = presetAttr?.cohortDays?.map(String) || []
        return acc
      },
      {} as Record<string, any>
    )
  }, [preset, viewPresetConfigMap])

  const form = useForm({
    defaultValues,
  })

  const { control, handleSubmit, reset } = form

  useEffect(() => {
    if (preset && viewPresetConfigMap) {
      const newDefaultValues = viewPresetConfigMap.attributes
        .filter((p) => p.isCohort)
        .reduce(
          (acc, attr) => {
            const presetAttr = preset.attributes.find((a) => a.name === attr.name)
            acc[attr.name] = presetAttr?.cohortDays?.map(String) || []
            return acc
          },
          {} as Record<string, any>
        )

      reset(newDefaultValues)

      // Reset selected non-cohort attributes
      const nonCohortAttributes = viewPresetConfigMap.attributes.filter((p) => !p.isCohort)
      const selected = nonCohortAttributes
        .filter((attr) => preset.attributes.find((a) => a.name === attr.name))
        .map((attr) => attr.name)
      setSelectedNonCohortAttributes(selected)
    }
  }, [preset, viewPresetConfigMap, reset])

  if (!id || !preset || !viewPresetConfigMap) {
    return null
  }

  const nonCohortAttributes = viewPresetConfigMap.attributes.filter((p) => !p.isCohort)
  const cohortAttributes = viewPresetConfigMap.attributes.filter((p) => p.isCohort)

  const handleNonCohortAttributeToggle = (attrName: string) => {
    setSelectedNonCohortAttributes((prev) =>
      prev.includes(attrName) ? prev.filter((name) => name !== attrName) : [...prev, attrName]
    )
  }

  const handleFormSubmit = async (values: any) => {
    const attributes = []

    for (const attrName of selectedNonCohortAttributes) {
      attributes.push({
        name: attrName,
        cohortDays: [],
      })
    }

    for (const [attrName, days] of Object.entries(values)) {
      if (Array.isArray(days) && days.length > 0) {
        attributes.push({
          name: attrName,
          cohortDays: days.map((day) => parseInt(day)),
        })
      }
    }

    try {
      await createViewPreset({
        form: {
          name: viewName,
          pageId: viewPresetConfigMap?.pageId || '',
          attributes,
        },
      })
    } catch (error) {
      console.error('Error creating view preset:', error)
    }
  }

  return (
    <FloatingRoute id="preset">
      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            setSaveViewDialogOpen(true)
          }}
        >
          <DialogHeader>
            <DialogTitle>Edit view</DialogTitle>
            <DialogDescription>{preset.name}</DialogDescription>
          </DialogHeader>

          <FloatingRouteContent>
            <div className="flex flex-col gap-3">
              <div className="flex gap-3 flex-col pt-3 overflow-auto">
                <div className="flex gap-2 flex-wrap">
                  {nonCohortAttributes.map((attr) => {
                    const isSelected = selectedNonCohortAttributes.includes(attr.name)
                    return (
                      <Button
                        type="button"
                        className={cn('flex-1', isSelected && 'bg-blue-500 text-white')}
                        key={attr.name}
                        variant={isSelected ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handleNonCohortAttributeToggle(attr.name)}
                      >
                        {t(`campaignMetrics.${attr.name}`, attr.name)}
                      </Button>
                    )
                  })}
                </div>

                <div className="flex gap-2 flex-wrap">
                  {cohortAttributes.map((a) => {
                    return (
                      <FormMultiSelectField
                        className="flex-1"
                        key={a.name}
                        control={control}
                        name={a.name}
                        label={t(`campaignMetrics.${a.name}`, a.name)}
                        options={getCohortDays(a.cohortDay).map((d) => ({
                          label: `D${d}`,
                          value: `${d}`,
                        }))}
                      />
                    )
                  })}
                </div>
              </div>

              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Save as new view'}
              </Button>
            </div>
          </FloatingRouteContent>
        </form>
      </Form>

      <ConfirmDialog
        open={saveViewDialogOpen}
        onOpenChange={setSaveViewDialogOpen}
        title="Save as new view"
        description={
          <div className="flex flex-col gap-4 py-4">
            <Input
              placeholder="View name"
              value={viewName}
              onChange={(e) => setViewName(e.target.value)}
            />
          </div>
        }
        confirmText="Save"
        isLoading={loading}
        onConfirm={() => handleSubmit(handleFormSubmit)()}
      />
    </FloatingRoute>
  )
}

function getCohortDays(cohortDay: string) {
  const days = cohortDay.split(',').map((d) => d.trim())
  return days.flatMap((d) => {
    if (d.includes('-')) {
      const [start, end] = d.split('-').map(Number)
      return Array.from({ length: end - start + 1 }, (_, i) => start + i)
    }

    return [parseInt(d)]
  })
}
