import { useServerProp } from '@/components/ssr'
import { createTransmit } from '@/next/api/sse'
import { errorToast } from '@/next/components/sdk/toast/error_toast'
import { infoToast } from '@/next/components/sdk/toast/info_toast'
import { successToast } from '@/next/components/sdk/toast/success_toast'
import { warningToast } from '@/next/components/sdk/toast/warning_toast'
import { Transmit } from '@adonisjs/transmit-client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export function ServerSideEventNotification() {
  const user = useServerProp((s) => s.user)
  const { t } = useTranslation()

  const [transmit, setTransmit] = useState<Transmit>()
  const [subscription, setSubscription] = useState<ReturnType<Transmit['subscription']> | null>(
    null
  )

  useEffect(() => {
    if (subscription) {
      subscription.create()
    }
  }, [subscription])

  useEffect(() => {
    if (user && user.id) {
      setTransmit(createTransmit())
    }

    return () => {
      transmit?.close()
      setTransmit(undefined)
    }
  }, [user, setTransmit])

  useEffect(() => {
    if (!transmit) {
      return
    }

    const sub = transmit.subscription(`global/notifications`)

    const stopListening = sub.onMessage(
      (notifications: Array<{ message: string; type: string; code: string }>) => {
        notifications.forEach((notification) => {
          switch (notification.type) {
            case 'success':
              successToast({
                title: t(`sse.global/notifications.${notification.code}.title`),
                message: t(`sse.global/notifications.${notification.code}.message`),
              })
              break

            case 'error':
              errorToast({
                title: t(`sse.global/notifications.${notification.code}.title`),
                message: t(`sse.global/notifications.${notification.code}.message`),
              })
              break

            case 'warn':
              warningToast({
                title: t(`sse.global/notifications.${notification.code}.title`),
                message: t(`sse.global/notifications.${notification.code}.message`),
              })
              break

            case 'info':
            default:
              infoToast({
                title: t(`sse.global/notifications.${notification.code}.title`),
                message: t(`sse.global/notifications.${notification.code}.message`),
              })
              break
          }
        })
      }
    )

    setSubscription(sub)

    return () => {
      stopListening()
    }
  }, [transmit, setSubscription])

  return null
}
