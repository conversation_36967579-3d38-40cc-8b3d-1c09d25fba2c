import { routes } from '@/components/location'
import { BanknoteArrowUp, Bar<PERSON>hart, FlaskConical, Sigma } from 'lucide-react'
import { SidebarItem } from '@/next/components/sdk/layout/use_main_sidebar'
import { AclSubject } from '#config/enums'

export const makeAclSidebarSubItems = (): SidebarItem[] => [
  {
    title: 'Game Metrics ACL',
    url: routes.dash.acls.show('game-metric'),
    icon: Sigma,
  },
  {
    title: 'Route ACL',
    url: routes.dash.acls.show(AclSubject.Route),
    icon: Bar<PERSON><PERSON>,
  },
  {
    title: 'Game Metric V2 ACL',
    url: routes.dash.acls.show('game-metric-v2'),
    icon: FlaskConical,
  },
  {
    title: 'Game Revenue ACL',
    url: routes.dash.acls.show('game-revenue'),
    icon: BanknoteArrowUp,
  },
]
