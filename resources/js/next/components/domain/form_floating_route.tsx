import { FloatingRouteContent } from '@/next/components/sdk/floating_route'
import {
  SimpleForm,
  SimpleFormContentProps,
  SimpleFormRootProps,
} from '@/next/components/sdk/form/simple_form'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { cn } from '@/next/lib/utils'
import { FieldValues } from 'react-hook-form'
import { Fragment, Children, isValidElement, ReactNode } from 'react'

type ReactFragmentElement = React.ReactElement<{ children: ReactNode }, typeof Fragment>

export function FormFloatingRouteHeader({
  title,
  description,
}: {
  title: string
  description?: string | ReactNode
}) {
  return (
    <DialogHeader>
      <DialogTitle>{title}</DialogTitle>
      {description && <DialogDescription>{description}</DialogDescription>}
    </DialogHeader>
  )
}

export function FormFloatingRouteContent<T extends FieldValues>({
  className,
  children,
  slots,
  ...props
}: SimpleFormRootProps<T> & { slots?: SimpleFormContentProps['slots'] }) {
  const fields = Children.toArray(children).flatMap((child) => {
    if (isValidElement(child) && child.type !== Fragment && typeof child.type === 'function') {
      return Children.toArray((child as any).type(child.props)).flatMap((child) => {
        if (isValidElement(child) && child.type === Fragment) {
          return Children.toArray((child as ReactFragmentElement).props.children)
        }
        return child
      })
    }
    return child
  })

  return (
    <FloatingRouteContent>
      <SimpleForm className={cn(className, 'flex flex-col gap-6')} {...props}>
        <div className="flex flex-col gap-4 overflow-auto">
          <SimpleForm.Content slots={slots}>{fields}</SimpleForm.Content>
        </div>
      </SimpleForm>
    </FloatingRouteContent>
  )
}
