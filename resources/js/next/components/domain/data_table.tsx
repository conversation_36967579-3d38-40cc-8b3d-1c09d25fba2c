import {
  formatCurrency,
  formatDuration,
  formatNumber,
  formatPercentage,
} from '@/components/typography'
import { cn } from '@/next/lib/utils'
import { ComponentProps, useMemo } from 'react'

export type TableCellHighlightProps = ComponentProps<'div'> & {
  value: number
  variant?: 'simple' | 'custom'
  type: 'percentage' | 'number' | 'currency' | 'duration'
  decimals?: number
}

export function TableCellHighlight({
  value,
  variant = 'simple',
  type,
  decimals = 2,
  className,
  children,
  ...props
}: TableCellHighlightProps) {
  const formattedValue = useMemo(() => {
    switch (type) {
      case 'percentage':
        return formatPercentage(value)
      case 'number':
        return formatNumber(value, decimals)
      case 'currency':
        return formatCurrency(value, decimals)
      case 'duration':
        return formatDuration(value)
      default:
        return value
    }
  }, [value, type, decimals])

  return (
    <>
      <div
        className={cn(
          'absolute inset-0',
          {
            'bg-green-400': value > 0 && variant === 'simple',
            'bg-red-400': value < 0 && variant === 'simple',
          },
          className
        )}
      />

      <div className={cn('relative z-10')} {...props}>
        {formattedValue}
        {children}
      </div>
    </>
  )
}
