import { routes } from '@/components/location'
import {
  BanknoteArrowDown,
  BanknoteArrowUp,
  FlaskConical,
  House,
  MessagesSquare,
  Palette,
  Rocket,
  Sigma,
  TrendingDown,
  TrendingUpDown,
} from 'lucide-react'
import { ComponentProps } from 'react'
import { SidebarItem } from '@/next/components/sdk/layout/use_main_sidebar'

function SigmaV2({ ...props }: ComponentProps<typeof Sigma>) {
  return (
    <>
      <Sigma {...props} />
      <div className="absolute left-5.5 bottom-4">
        <span className="text-[10px]">2</span>
      </div>
    </>
  )
}

export const makeGameSidebarSubItems = (gameId: string): SidebarItem[] => [
  {
    title: 'Campaign Performance',
    icon: TrendingUpDown,
    url: routes.dash.games.campaignMetrics(gameId),
  },
  {
    title: 'Firebase A/B Testing',
    icon: FlaskConical,
    url: routes.dash.games.firebaseExperiments(gameId),
  },
  {
    title: 'Release Intel',
    icon: Rocket,
    url: routes.dash.games.releaseMetrics(gameId),
  },
  {
    title: 'Game Metrics',
    icon: Sigma,
    url: routes.dash.gameMetrics.index(gameId),
  },
  {
    title: 'Game Metrics V2',
    icon: SigmaV2 as any,
    url: routes.dash.gameMetricsNext.index(gameId),
  },
  {
    title: 'Cost Explorer',
    icon: BanknoteArrowDown,
    url: routes.dash.games.costs(gameId),
  },
  {
    title: 'Revenue Explorer',
    icon: BanknoteArrowUp,
    url: routes.dash.games.revenues(gameId),
  },
  {
    title: 'Level Drop Explorer',
    icon: TrendingDown,
    url: routes.dash.games.levelDrops(gameId),
  },
  {
    title: 'Game Review',
    icon: MessagesSquare,
    url: routes.dash.gameReviews.index(gameId),
  },
  {
    title: 'Product Metrics',
    icon: House,
    url: routes.dash.games.productMetrics(gameId),
  },
  {
    title: 'Creative Performance',
    icon: Palette,
    url: routes.dash.games.creativeMetrics(gameId),
  },
]
