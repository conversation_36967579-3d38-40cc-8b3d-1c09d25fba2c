/// <reference path="../../../adonisrc.ts" />
/// <reference path="../../../config/inertia.ts" />

import '../../css/next.css'
import { createRoot } from 'react-dom/client'
import { createInertiaApp } from '@inertiajs/react'
import { resolvePageComponent } from '@adonisjs/inertia/helpers'

import { ServerPropsContextProvider } from '@/components/ssr'

import { ReactRoot } from './react'
import { ServerSideEventNotification } from '@/next/components/domain/server_side_event_notification'

const appName = import.meta.env.VITE_APP_NAME || 'Mirai Studio Toolkit'

createInertiaApp({
  progress: { color: '#5468FF' },

  title: (title) => `${title} | ${appName}`,

  resolve: async (name) => {
    const { default: Page } = (await resolvePageComponent(
      `./pages/${name}.tsx`,
      import.meta.glob('./pages/**/*.tsx')
    )) as any

    return {
      default: (props: any) => {
        return (
          <ServerPropsContextProvider value={props}>
            <Page {...props} />

            <ServerSideEventNotification />
          </ServerPropsContextProvider>
        )
      },
    }
  },

  setup({ el, App, props }) {
    createRoot(el).render(
      <ReactRoot>
        <App {...props} />
      </ReactRoot>
    )
  },
})
