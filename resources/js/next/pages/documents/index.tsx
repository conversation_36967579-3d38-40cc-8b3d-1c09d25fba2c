import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { DataTable } from '@/next/components/data-table/data-table'
import { DataTableColumnHeader } from '@/next/components/data-table/data-table-column-header'
import { createColumnHelper, getCoreRowModel, useReactTable } from '@tanstack/react-table'
import { useMemo } from 'react'
import DocumentConfigMap from '#configmaps/general/document'
import { useConfigMap } from '@/next/components/sdk/config_map'

const columnHelper = createColumnHelper<DocumentConfigMap>()

export default function Page() {
  const [[documentConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.document'], []))

  const columns = [
    columnHelper.accessor('name', {
      header: ({ column }) => <DataTableColumnHeader column={column} title="Doc Name" />,
      cell: ({ row }) => {
        const document = row.original
        return (
          <a href={`/documents/${document.id}/edit`} className="text-primary hover:underline">
            {document.name}
          </a>
        )
      },
    }),
  ]

  const table = useReactTable({
    data: documentConfigMapCollection,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <BaseLayout>
      <PageConfig title="Documents" breadcrumbs={['Documents']} />

      <div className=" py-6">
        <DataTable table={table} />
      </div>
    </BaseLayout>
  )
}
