import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { useQuery, useMutation } from '@tanstack/react-query'
import { toolkitApi } from '@/components/toolkit-api'
import { Button } from '@/next/components/ui/button'
import { createParamsStore } from '@/components/location/params'
import { object, string } from 'yup'
import { WYSIWYG, useEditorControl } from '@/components/wysiwyg'
import { useCsrf } from '@/components/csrf'
import { toast } from 'sonner'
import { Loading } from '@/next/components/sdk/loading'

export const { useParams, useSetParams, useParam } = createParamsStore<{
  docId: string
}>('/documents/:docId/edit', {
  parse: object({
    docId: string().required(),
  }),
})

export default function Page() {
  const { docId } = useParams()
  const editorControl = useEditorControl()
  const csrf = useCsrf()

  const getDocumentQuery = useQuery({
    queryKey: ['document', docId],
    queryFn: async () => {
      return toolkitApi.getDocument(docId)
    },
  })

  const { data, isLoading } = getDocumentQuery

  const updateDocumentMutation = useMutation({
    mutationFn: async () => {
      await toolkitApi.updateDocument({
        ...csrf.getField(),
        content: editorControl.getContent(),
        id: docId,
      })
    },
    onSuccess: () => {
      toast.success('Document updated successfully')
    },
    mutationKey: ['document', docId],
  })

  return (
    <BaseLayout>
      <PageConfig
        title="Edit Document"
        breadcrumbs={[
          { title: 'Dashboard', url: '/dashboard' },
          { title: 'Documents', url: '/documents' },
          { title: `${docId}` },
        ]}
      />
      <Loading loading={isLoading}>
        <div className="py-6">
          <div className="space-y-4">
            <style>
              {`
                .ck-editor__editable {
                  min-height: calc(100vh - 400px);
                }
              `}
            </style>
            <WYSIWYG control={editorControl} config={{ initialData: data?.data?.content }} />
            <Button
              onClick={() => updateDocumentMutation.mutateAsync()}
              disabled={updateDocumentMutation.isPending}
              className="mt-4"
            >
              {updateDocumentMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </Loading>
    </BaseLayout>
  )
}
