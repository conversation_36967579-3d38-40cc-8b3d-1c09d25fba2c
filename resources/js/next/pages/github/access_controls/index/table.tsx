import { GithubAclRepositoryOutsideCollaboratorConfigMap } from '#configmaps/github/github_access_control'
import { useConfigMap } from '@/next/components/sdk/config_map'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/next/components/ui/accordion'
import { useEffect, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/next/components/ui/table'
import { useUpdateGitHubAcl, useUpdateGitHubTeam } from '@/components/toolkit-api'
import { IconButton } from '@/next/components/sdk/button'
import { successToast } from '@/next/components/sdk/toast/success_toast'
import { errorToast } from '@/next/components/sdk/toast/error_toast'

export function GitHubTeamACLTable() {
  const [[teamConfigMapCollection, memberConfigMapCollection]] = useConfigMap(
    useMemo(() => ['cmap.gh.team', 'cmap.gh.member'], [])
  )

  const updateTeam = useUpdateGitHubTeam()

  useEffect(() => {
    if (updateTeam.isSuccess) {
      successToast({
        title: 'Teams ACL updated',
        message: 'Teams ACL updated successfully',
      })
    }
  }, [updateTeam.isSuccess])

  useEffect(() => {
    if (updateTeam.error) {
      errorToast({
        title: 'Teams ACL update failed',
        message: updateTeam.error.message ?? 'Unknown error',
      })
    }
  }, [updateTeam.error])

  return (
    <>
      <div className="flex justify-end">
        <IconButton
          size="sm"
          className="mb-2"
          onClick={() => updateTeam.mutateAsync([])}
          disabled={updateTeam.isPending}
          loading={updateTeam.isPending}
        >
          Update All Teams ACL
        </IconButton>
      </div>

      <Accordion type="single" collapsible>
        {teamConfigMapCollection.map((teamConfigMap) => {
          return (
            <AccordionItem value={teamConfigMap.name} key={teamConfigMap.name}>
              <AccordionTrigger>{teamConfigMap.name}</AccordionTrigger>
              <AccordionContent>
                <IconButton
                  size="sm"
                  className="mb-2"
                  variant="outline"
                  onClick={() => updateTeam.mutateAsync([teamConfigMap.name])}
                  disabled={updateTeam.isPending}
                  loading={updateTeam.isPending}
                >
                  Update {teamConfigMap.name} ACL
                </IconButton>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Member</TableHead>
                      <TableHead>GitHub Username</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {teamConfigMap.members.map((member) => {
                      const memberConfigMap = memberConfigMapCollection.find(
                        (m) => m.fullName === member.fullName
                      )

                      return (
                        <TableRow key={member.fullName}>
                          <TableCell>{member.fullName}</TableCell>
                          <TableCell>{memberConfigMap?.username ?? 'Please check again'}</TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </AccordionContent>
            </AccordionItem>
          )
        })}
      </Accordion>
    </>
  )
}

export function GitHubRepoACLTable() {
  const [[aclConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.gh.acl'], []))

  const updateAcl = useUpdateGitHubAcl()

  useEffect(() => {
    if (updateAcl.isSuccess) {
      successToast({
        title: 'ACLs updated',
        message: 'ACLs updated successfully',
      })
    }
  }, [updateAcl.isSuccess])

  useEffect(() => {
    if (updateAcl.error) {
      errorToast({
        title: 'ACLs update failed',
        message: updateAcl.error.message ?? 'Unknown error',
      })
    }
  }, [updateAcl.error])

  return (
    <>
      <div className="flex justify-end">
        <IconButton
          size="sm"
          className="mb-2"
          onClick={() => updateAcl.mutateAsync([])}
          disabled={updateAcl.isPending}
          loading={updateAcl.isPending}
        >
          Update All ACLs
        </IconButton>
      </div>

      <Accordion type="single" collapsible>
        {aclConfigMapCollection.map((aclConfigMap) => {
          return (
            <AccordionItem value={aclConfigMap.id} key={aclConfigMap.id}>
              <AccordionTrigger>{aclConfigMap.id}</AccordionTrigger>
              <AccordionContent>
                <IconButton
                  size="sm"
                  className="mb-2"
                  variant="outline"
                  onClick={() => updateAcl.mutateAsync([aclConfigMap.id])}
                  disabled={updateAcl.isPending}
                  loading={updateAcl.isPending}
                >
                  Update {aclConfigMap.id} ACL
                </IconButton>

                <div className="space-y-4 flex gap-4">
                  <div className="flex-3">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Repository</TableHead>
                          <TableHead>Outside Collaborators</TableHead>
                          <TableHead>Team Overrides</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {aclConfigMap.repositories.map((repo) => (
                          <TableRow key={repo.name}>
                            <TableCell className="font-medium">{repo.name}</TableCell>
                            <TableCell>
                              {repo.outsideCollaborators
                                .map((c) => `${c.fullName} (${c.permission})`)
                                .join(', ')}
                            </TableCell>
                            <TableCell>
                              {repo.teams.map((t) => `${t.name} (${t.permission})`).join(', ')}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="flex-2">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Team</TableHead>
                          <TableHead>Permission</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {aclConfigMap.teams.map((team) => (
                          <TableRow key={team.name}>
                            <TableCell>{team.name}</TableCell>
                            <TableCell>{team.defaultPermission}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          )
        })}
      </Accordion>
    </>
  )
}

export function GitHubOutsideCollaboratorsACLTable() {
  const [[aclConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.gh.acl'], []))

  const repositoryCollaboratorConfigMaps = aclConfigMapCollection
    .map((x) => x)
    .flatMap((aclConfigMap) => aclConfigMap.repositories.map((x) => x))
    .reduce((result, aclRepositoryConfigMap) => {
      if (aclRepositoryConfigMap.outsideCollaborators.length === 0) {
        return result
      }

      if (!result.has(aclRepositoryConfigMap.name)) {
        result.set(aclRepositoryConfigMap.name, [])
      }

      aclRepositoryConfigMap.outsideCollaborators.forEach((collaborator) => {
        result.get(aclRepositoryConfigMap.name)!.push(collaborator)
      })

      return result
    }, new Map<string, GithubAclRepositoryOutsideCollaboratorConfigMap[]>())

  return (
    <>
      <Accordion type="single" collapsible>
        {Array.from(repositoryCollaboratorConfigMaps.entries()).map(([repoName, collaborators]) => {
          return (
            <AccordionItem value={repoName} key={repoName}>
              <AccordionTrigger>{repoName}</AccordionTrigger>
              <AccordionContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Collaborator</TableHead>
                      <TableHead>Permission</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {collaborators.map((collaborator) => (
                      <TableRow key={collaborator.fullName}>
                        <TableCell>{collaborator.fullName}</TableCell>
                        <TableCell>{collaborator.permission}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </AccordionContent>
            </AccordionItem>
          )
        })}
      </Accordion>
    </>
  )
}
