import { useConfigMap } from '@/next/components/sdk/config_map'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { PageConfig } from '@/next/components/sdk/page_config'
import { GitHubRepoACLTable, GitHubTeamACLTable } from './index/table'
import { useMemo } from 'react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/next/components/ui/tabs'
import { githubSidebarSubItems } from '@/next/components/domain/github_sidebar'

export default function Page() {
  const [, getConfigMaps] = useConfigMap(
    useMemo(() => ['cmap.gh.acl', 'cmap.gh.member', 'cmap.gh.team'], [])
  )

  return (
    <BaseLayout>
      <PageConfig
        breadcrumbs={['GitHub ACL']}
        sidebarSubItems={githubSidebarSubItems}
        title="GitHub ACL"
      />

      <QueryState query={getConfigMaps}>
        <Tabs defaultValue="team">
          <TabsList>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="acl">ACL</TabsTrigger>
            <TabsTrigger value="outsider">Outside Collaborators</TabsTrigger>
          </TabsList>
          <TabsContent value="team">
            <GitHubTeamACLTable />
          </TabsContent>
          <TabsContent value="acl">
            <GitHubRepoACLTable />
          </TabsContent>
        </Tabs>
      </QueryState>
    </BaseLayout>
  )
}
