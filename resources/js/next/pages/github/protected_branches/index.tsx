import { githubSidebarSubItems } from '@/next/components/domain/github_sidebar'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useMemo } from 'react'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { ProtectedBranchTable } from '@/next/pages/github/protected_branches/index/table'

export default function Page() {
  const [, getConfigMaps] = useConfigMap(useMemo(() => ['cmap.gh.pbranch', 'cmap.gh.ruleset'], []))

  return (
    <BaseLayout>
      <PageConfig
        breadcrumbs={['Protected Branches']}
        sidebarSubItems={githubSidebarSubItems}
        title="GitHub Protected Branches"
      />

      <QueryState query={getConfigMaps}>
        <ProtectedBranchTable />
      </QueryState>
    </BaseLayout>
  )
}
