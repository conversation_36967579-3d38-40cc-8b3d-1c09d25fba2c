import { useConfigMap } from '@/next/components/sdk/config_map'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/next/components/ui/accordion'
import {
  Table,
  TableCell,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@/next/components/ui/table'
import { useEffect, useMemo } from 'react'
import { IconButton } from '@/next/components/sdk/button'
import { useUpdateGitHubProtectedBranch } from '@/components/toolkit-api'
import { successToast } from '@/next/components/sdk/toast/success_toast'
import { errorToast } from '@/next/components/sdk/toast/error_toast'

export function ProtectedBranchTable() {
  const [[protectedBranchConfigMapCollection, rulesetConfigMapCollection]] = useConfigMap(
    useMemo(() => ['cmap.gh.pbranch', 'cmap.gh.ruleset'], [])
  )

  const updateProtectedBranch = useUpdateGitHubProtectedBranch()

  useEffect(() => {
    if (updateProtectedBranch.isSuccess) {
      successToast({
        title: 'Protected branch updated',
        message: 'Protected branch updated successfully',
      })
    }
  }, [updateProtectedBranch.isSuccess])

  useEffect(() => {
    if (updateProtectedBranch.error) {
      errorToast({
        title: 'Protected branch update failed',
        message: updateProtectedBranch.error.message ?? 'Unknown error',
      })
    }
  }, [updateProtectedBranch.error])

  return (
    <>
      <Accordion type="single" collapsible>
        {protectedBranchConfigMapCollection.map((protectedBranchConfigMap) => {
          const rulesetConfigMap = protectedBranchConfigMap.rulesetId
            ? rulesetConfigMapCollection.find((r) => r.id === protectedBranchConfigMap.rulesetId)!
            : protectedBranchConfigMap.inlineRulesets[0]!

          return (
            <AccordionItem
              key={protectedBranchConfigMap.repository}
              value={protectedBranchConfigMap.repository}
            >
              <AccordionTrigger>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">{`<${rulesetConfigMap.id}>`}</span>
                  <span className="text-sm">{protectedBranchConfigMap.repository}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <IconButton
                  variant="outline"
                  className="mb-2"
                  loading={updateProtectedBranch.isPending}
                  disabled={updateProtectedBranch.isPending}
                  onClick={() =>
                    updateProtectedBranch.mutateAsync([protectedBranchConfigMap.repository])
                  }
                >
                  Update {protectedBranchConfigMap.repository} rules
                </IconButton>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Branch</TableHead>
                      <TableHead>Rules</TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {rulesetConfigMap.branches.map((branch) => (
                      <TableRow key={branch.name}>
                        <TableCell>{branch.name}</TableCell>
                        <TableCell>
                          {branch.rules.map((rule) => (
                            <div key={rule.rule}>
                              <span className="text-muted-foreground">{rule.rule} = </span>
                              <span>{rule.value}</span>
                            </div>
                          ))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </AccordionContent>
            </AccordionItem>
          )
        })}
      </Accordion>
    </>
  )
}
