'use client'

import { But<PERSON> } from '@/next/components/ui/button'
import { Card, CardContent } from '@/next/components/ui/card'
import { Link } from '@inertiajs/react'
import { Home, RefreshCcw } from 'lucide-react'

interface ErrorPageProps {
  status: string | number
  title: string
  explanation: string
}

export default function ErrorPage({ status, title, explanation }: ErrorPageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Floating background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-3/4 w-16 h-16 bg-pink-200 dark:bg-pink-800 rounded-full opacity-20 animate-pulse delay-500"></div>
        </div>

        {/* Main error content */}
        <div className="relative z-10 animate-in fade-in-50 slide-in-from-bottom-10 duration-1000">
          <Card className="border-0 shadow-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
            <CardContent className="p-8 text-center">
              {/* Animated status code */}
              <div className="mb-6">
                <div className="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500 animate-in zoom-in-50 duration-1000 delay-300">
                  {status}
                </div>
                <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-orange-500 mx-auto mt-4 rounded-full animate-in slide-in-from-left-full duration-1000 delay-500"></div>
              </div>

              {/* Title with animation */}
              <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4 animate-in fade-in-0 slide-in-from-bottom-4 duration-1000 delay-700">
                {title}
              </h1>

              {/* Explanation with animation */}
              <p className="text-slate-600 dark:text-slate-400 mb-8 leading-relaxed animate-in fade-in-0 slide-in-from-bottom-4 duration-1000 delay-900">
                {explanation}
              </p>

              {/* Action buttons with staggered animation */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  className="animate-in fade-in-0 slide-in-from-bottom-4 duration-1000 delay-1100 hover:scale-105 transition-transform"
                  asChild
                >
                  <Link href="/">
                    <Home className="w-4 h-4 mr-2" />
                    Go Home
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="animate-in fade-in-0 slide-in-from-bottom-4 duration-1000 delay-1300 hover:scale-105 transition-transform"
                >
                  <RefreshCcw className="w-4 h-4 mr-2" />
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Floating particles animation */}
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: 6 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-slate-300 dark:bg-slate-600 rounded-full opacity-30 animate-bounce"
              style={{
                left: `${Math.floor(Math.random() * 100)}%`,
                top: `${Math.floor(Math.random() * 100)}%`,
                animationDelay: `${Math.floor(Math.random() * 2)}s`,
                animationDuration: `${2 + Math.floor(Math.random() * 2)}s`,
              }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  )
}
