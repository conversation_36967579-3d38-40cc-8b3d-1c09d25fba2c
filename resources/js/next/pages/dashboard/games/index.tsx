import { graphql } from '@/gql'
import { useQuery } from '@/next/components/sdk/graphql'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { PageConfig } from '@/next/components/sdk/page_config'

import { GameListTable } from './index/table'
import { FloatingRoute } from '@/next/components/sdk/floating_route'
import { GameRoles } from '@/next/pages/dashboard/games/index/roles'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useMemo } from 'react'
import { GameMembers } from '@/next/pages/dashboard/games/index/members'
import { useGetAllUsers } from '@/next/api/use_get_all_users'
import { DataTableFilterSubmitButton } from '@/next/components/sdk/data_table'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

const getInitialDataQuery = graphql(`
  query GamesIndex_GetInitialData($where: GamesWhere!) {
    games(where: $where, offset: { page: 1, perPage: 9999 }) {
      collection {
        ...GameAttributes
      }
    }
  }
`)

export default function Page() {
  const [, getConfigMap] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))

  const filterForm = useDataTableFilterForm({
    users: {
      variant: 'multiSelect',
      defaultValue: [],
    },
    tags: {
      variant: 'multiSelect',
      defaultValue: [],
    },
    name: {
      variant: 'text',
      defaultValue: '',
    },
  })

  const filterFormValues = filterForm.watch()

  const getInitialData = useQuery(getInitialDataQuery, {
    where: { userIds: filterFormValues.users },
  })

  const getUsers = useGetAllUsers()

  const onFilterSubmit = () => {
    filterForm.syncValues('users')
  }

  return (
    <BaseLayout>
      <PageConfig title="All Games" breadcrumbs={['All Games']} />

      <QueryState query={[getInitialData, getConfigMap, getUsers]}>
        <GameListTable
          collection={getInitialData.data?.games?.collection!}
          listUsers={getUsers.data?.users.collection}
          form={filterForm}
          slots={{
            beforeFilter: (
              <DataTableFilterSubmitButton
                onClick={onFilterSubmit}
                disabled={getUsers.loading}
                loading={getUsers.loading}
              />
            ),
          }}
        />

        <FloatingRoute id="roles" className="sm:max-w-[650px]">
          <GameRoles listUsers={getUsers.data?.users.collection} />
        </FloatingRoute>

        <FloatingRoute id="members" className="sm:max-w-[650px]">
          <GameMembers
            onFinish={() => {
              getInitialData.refetch()
            }}
            listUsers={getUsers.data?.users.collection}
          />
        </FloatingRoute>
      </QueryState>
    </BaseLayout>
  )
}
