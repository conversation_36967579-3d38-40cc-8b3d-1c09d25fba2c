import { routes } from '@/components/location'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { useMemo } from 'react'
import { useQuery } from '@/next/components/sdk/graphql'
import {
  getMetricsQuery,
  getInitDataQuery,
  generateAclFromAttributes,
  getAggregationQuery,
} from './index/util'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { format, subDays } from 'date-fns'
import { parseAsInteger, parseAsString, useQueryState, useQueryStates } from 'nuqs'

import { GameMetricsTable } from './index/table'
import { useServerProp } from '@/components/ssr'
import { FloatingRoute } from '@/next/components/sdk/floating_route'
import {
  DataTableFilterSubmitButton,
  DataTableToolbarButton,
} from '@/next/components/sdk/data_table'
import { EditMetric } from './index/edit_metric'
import { ChartNoAxesCombined, Table } from 'lucide-react'
import Game from '#models/game'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { Chart } from './index/chart'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'
import { Button } from '@/next/components/ui/button'
import {
  Menubar,
  MenubarItem,
  MenubarContent,
  MenubarTrigger,
  MenubarMenu,
} from '@/next/components/ui/menubar'
import { router } from '@inertiajs/react'
import { useTranslation } from 'react-i18next'

export default function Page() {
  const { id: gameId, name: gameName }: Game = useServerProp((s) => s.game)

  const sidebarSubItems = useMemo(() => makeGameSidebarSubItems(gameId), [gameId])

  const [view] = useQueryState('view', parseAsString.withDefault('table'))

  return (
    <BaseLayout>
      <PageConfig
        metabase
        title={gameName}
        breadcrumbs={[
          {
            title: 'All Games',
            url: routes.dash.games.index(),
          },
          {
            title: gameName,
            url: routes.dash.gameMetrics.index(gameId),
          },
          'Game Metrics',
        ]}
        sidebarSubItems={sidebarSubItems}
      />
      {view === 'table' ? <TableView /> : <ChartView />}
    </BaseLayout>
  )
}

function ChartView() {
  const [, getConfigMap] = useConfigMap(useMemo(() => ['cmap.dash.metabasechart'], []))
  const [, setView] = useQueryState('view')

  return (
    <QueryState query={[getConfigMap]}>
      <Chart
        slots={{
          toolbar: (
            <DataTableToolbarButton onClick={() => setView('table')}>
              <Table />
            </DataTableToolbarButton>
          ),
        }}
      />
    </QueryState>
  )
}

function TableView() {
  const gameId = useServerProp((s) => s.game.id)
  const { t } = useTranslation()
  const getInitData = useQuery(getInitDataQuery, { gameId }, { toastOnError: true })
  const dashboardList = ['ua', 'monet']
  const [pagination] = useQueryStates({
    page: parseAsInteger.withDefault(1),
    perPage: parseAsInteger.withDefault(200),
  })

  const filterForm = useDataTableFilterForm({
    date: {
      variant: 'dateRange',
      defaultValue: [subDays(new Date(), 30), new Date()],
    },
  })

  const filterValues = filterForm.watch()

  const acl = useMemo(() => {
    if (!getInitData.data?.attributes?.collection) return {}
    return generateAclFromAttributes(getInitData.data.attributes.collection)
  }, [getInitData.data])

  const queryOptions = useMemo(
    () => ({
      gameId,
      page: pagination.page,
      perPage: pagination.perPage,
      dateGte: format(filterValues.date[0], 'yyyy-MM-dd'),
      dateLte: format(filterValues.date[1] || filterValues.date[0], 'yyyy-MM-dd'),
      ...acl,
    }),
    [gameId, JSON.stringify(filterValues), acl]
  )

  const getMetrics = useQuery(getMetricsQuery, queryOptions, {
    skip: !getInitData.data,
    toastOnError: true,
  })

  const getAggregation = useQuery(getAggregationQuery, queryOptions, {
    skip: !getInitData.data,
    toastOnError: true,
  })

  const onFilterSubmit = () => {
    filterForm.syncValues('date')
  }

  return (
    <QueryState query={[getInitData, getMetrics]}>
      <QueryState query={[getAggregation]}>
        <GameMetricsTable
          filterForm={filterForm}
          metrics={getMetrics.data?.gameMetrics.collection!}
          summary={getAggregation.data?.summary!}
          total={getAggregation.data?.total!}
          attrs={getInitData.data?.attributes?.collection!}
          pageInfo={getMetrics.data?.gameMetrics.pageInfo!}
          slots={{
            beforeFilter: (
              <DataTableFilterSubmitButton
                onClick={onFilterSubmit}
                disabled={getMetrics.loading}
                loading={getMetrics.loading}
              />
            ),
            toolbar: (
              <Menubar className="border-none">
                <MenubarMenu>
                  <MenubarTrigger asChild>
                    <Button size="sm" variant="outline">
                      <ChartNoAxesCombined />
                    </Button>
                  </MenubarTrigger>
                  <MenubarContent>
                    {dashboardList.map((dashboard) => (
                      <MenubarItem
                        key={dashboard}
                        onClick={() => {
                          router.visit(
                            routes.dash.games.overview(gameId, `game-overview-${dashboard}`)
                          )
                        }}
                      >
                        {t(`gameMetric.teams.${dashboard}`)} Dashboard
                      </MenubarItem>
                    ))}
                  </MenubarContent>
                </MenubarMenu>
              </Menubar>
            ),
          }}
        />
      </QueryState>

      <FloatingRoute id="edit-metric" className="sm:max-w-[450px]">
        <EditMetric acl={acl} onUpdated={getAggregation.refetch} />
      </FloatingRoute>
    </QueryState>
  )
}
