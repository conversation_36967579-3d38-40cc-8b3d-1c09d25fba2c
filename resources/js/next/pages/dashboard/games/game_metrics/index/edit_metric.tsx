import { useApolloClient } from '@apollo/client'
import { parseAsInteger, parseAsString } from 'nuqs'
import { useMemo } from 'react'
import { useForm } from 'react-hook-form'

import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormTextAreaField } from '@/next/components/sdk/form/form_text_area_field'
import { useHashNavigate, useHashState } from '@/next/components/sdk/hash_link'
import {
  GameMetricIndex_GameMetricAttributesFragment,
  graphql,
  MutationUpdateGameMetricArgs,
} from '@/graphql'

import { gameMetricFragment } from '@/graphql'
import {
  FormFloatingRouteContent,
  FormFloatingRouteHeader,
} from '@/next/components/domain/form_floating_route'
import { IconButton } from '@/next/components/sdk/button'
import { useServerProp } from '@/components/ssr'
import { useMutation } from '@/next/components/sdk/graphql'
import { useTranslation } from 'react-i18next'

const updateGameMetricMutation = graphql(`
  mutation GameMetricEdit_UpdateGameMetric(
    $where: UpdateGameMetricWhere!
    $form: UpdateGameMetricForm!
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewBannerImpsDau: Boolean = false
    $canViewInterImpsDau: Boolean = false
    $canViewRewardImpsDau: Boolean = false
    $canViewAoaImpsDau: Boolean = false
    $canViewMrecImpsDau: Boolean = false
    $canViewAudioImpsDau: Boolean = false
    $canViewAoaAdmobImpsDau: Boolean = false
    $canViewCollapseAdmobImpsDau: Boolean = false
    $canViewNativeAdmobImpsDau: Boolean = false
    $canViewAdaptiveAdmobImpsDau: Boolean = false
    $canViewMrecAdmobImpsDau: Boolean = false
    $canViewAverageSession: Boolean = false
    $canViewPlaytime: Boolean = false
    $canViewUaNote: Boolean = false
    $canViewMonetNote: Boolean = false
    $canViewProductNote: Boolean = false
    $canViewVersionNote: Boolean = false
    $canViewNote: Boolean = false
  ) {
    updateGameMetric(where: $where, form: $form) {
      ...GameMetricIndex_GameMetricAttributes
    }
  }
`)

export function EditMetric({
  acl,
  onUpdated,
}: {
  acl: Record<string, boolean>
  onUpdated: () => void
}) {
  const [metricId] = useHashState('id', parseAsInteger)
  const [metricName] = useHashState('name', parseAsString) as unknown as [
    keyof GameMetricIndex_GameMetricAttributesFragment,
  ]

  const [_, goBack] = useHashNavigate()

  const apollo = useApolloClient()
  const metric = useMemo(() => {
    if (!metricId) {
      return null
    }

    return apollo.readFragment({
      fragment: gameMetricFragment,
      id: `GameMetric:${metricId}`,
      fragmentName: 'GameMetricIndex_GameMetricAttributes',
      variables: acl,
    })
  }, [metricId, acl])

  const [updateGameMetric, updateGameMetricState] = useMutation(updateGameMetricMutation, {
    onSuccess: () => {
      onUpdated()
      goBack()
    },
  })

  if (!metric) {
    return null
  }

  const renderForm = () => {
    switch (metricName) {
      case 'revenue':
      case 'cost':
        return (
          <EditOverrideMetric
            metric={metric}
            metricName={metricName}
            isSubmitting={updateGameMetricState.loading}
            onSubmit={(args) => updateGameMetric({ ...args, ...acl })}
          />
        )
      case 'uaNote':
      case 'monetNote':
      case 'productNote':
      case 'versionNote':
      case 'note':
        return (
          <EditMetadata
            metric={metric}
            metricName={metricName}
            isSubmitting={updateGameMetricState.loading}
            onSubmit={(args) => updateGameMetric({ ...args, ...acl })}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <FormFloatingRouteHeader title="Edit Metric" description={metric.date} />

      {renderForm()}
    </>
  )
}

function EditMetadata({
  metric,
  metricName,
  isSubmitting,
  onSubmit,
}: {
  metric: GameMetricIndex_GameMetricAttributesFragment
  metricName: keyof GameMetricIndex_GameMetricAttributesFragment
  isSubmitting: boolean
  onSubmit: (values: MutationUpdateGameMetricArgs) => any
}) {
  const { t } = useTranslation()
  const form = useForm({
    defaultValues: {
      value: metric[metricName] || '',
    },
  })
  const gameId = useServerProp((s) => s.game.id)

  const getFieldLabel = (fieldName: string) => {
    return t(`gameMetric.${fieldName}`)
  }

  return (
    <FormFloatingRouteContent
      form={form}
      onSubmit={async (data) => {
        return onSubmit({
          where: {
            gameId,
            date: metric.date,
          },
          form: {
            metadata: {
              [metricName]: data.value,
            },
            override: {},
          },
        })
      }}
      slots={{
        after: (
          <IconButton type="submit" loading={isSubmitting} disabled={isSubmitting}>
            Save
          </IconButton>
        ),
      }}
    >
      <FormTextAreaField
        control={form.control}
        name="value"
        label={getFieldLabel(metricName)}
        rows={3}
        placeholder={`Enter ${getFieldLabel(metricName).toLowerCase()}...`}
      />
    </FormFloatingRouteContent>
  )
}

function EditOverrideMetric({
  metric,
  metricName,
  isSubmitting,
  onSubmit,
}: {
  metric: GameMetricIndex_GameMetricAttributesFragment
  metricName: keyof GameMetricIndex_GameMetricAttributesFragment
  isSubmitting: boolean
  onSubmit: (values: MutationUpdateGameMetricArgs) => any
}) {
  const { t } = useTranslation()
  const form = useForm({
    defaultValues: {
      value: metric[metricName] || 0,
    },
  })
  const gameId = useServerProp((s) => s.game.id)

  const getFieldLabel = (fieldName: string) => {
    return t(`gameMetric.${fieldName}`)
  }

  return (
    <FormFloatingRouteContent
      form={form}
      onSubmit={async (data) => {
        return onSubmit({
          where: {
            gameId,
            date: metric.date,
          },
          form: {
            metadata: {},
            override: {
              [metricName]: data.value ? Number(data.value) : null,
            },
          },
        })
      }}
      slots={{
        after: (
          <IconButton type="submit" loading={isSubmitting} disabled={isSubmitting}>
            Save
          </IconButton>
        ),
      }}
    >
      <FormInputField control={form.control} name="value" label={getFieldLabel(metricName)} />
    </FormFloatingRouteContent>
  )
}
