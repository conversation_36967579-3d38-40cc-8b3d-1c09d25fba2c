import { graphql } from '@/gql'
import * as changeCase from 'case-anything'
import { fromPairs } from 'lodash-es'
import { canRead } from '#utils/acl'
import {
  AclGameMetricAttributesFragment,
  GameMetricIndex_GameMetricAttributesFragment,
} from '@/graphql'

export const getInitDataQuery = graphql(`
  query GamesMetricsShow_GetInitData($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }

    attributes(where: { gameId: $gameId, modelName: "GameMetric" }) {
      collection {
        ...ModelAttribute
      }
    }
  }
`)

export const getMetricsQuery = graphql(`
  query GamesMetricsShow_GetMetrics(
    $gameId: ID!
    $page: Int!
    $perPage: Int!
    $dateGte: Date
    $dateLte: Date
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewBannerImpsDau: Boolean = false
    $canViewInterImpsDau: Boolean = false
    $canViewRewardImpsDau: Boolean = false
    $canViewAoaImpsDau: Boolean = false
    $canViewMrecImpsDau: Boolean = false
    $canViewAudioImpsDau: Boolean = false
    $canViewAoaAdmobImpsDau: Boolean = false
    $canViewCollapseAdmobImpsDau: Boolean = false
    $canViewNativeAdmobImpsDau: Boolean = false
    $canViewAdaptiveAdmobImpsDau: Boolean = false
    $canViewMrecAdmobImpsDau: Boolean = false
    $canViewAverageSession: Boolean = false
    $canViewPlaytime: Boolean = false
    $canViewUaNote: Boolean = false
    $canViewMonetNote: Boolean = false
    $canViewProductNote: Boolean = false
    $canViewVersionNote: Boolean = false
    $canViewNote: Boolean = false
  ) {
    gameMetrics(
      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }
      offset: { page: $page, perPage: $perPage }
    ) {
      collection {
        ...GameMetricIndex_GameMetricAttributes
      }

      pageInfo {
        ...PageInfoAttributes
      }
    }
  }
`)

export const getAggregationQuery = graphql(`
  query GameMetricIndex_GetAggregation(
    $gameId: ID!
    $dateGte: Date
    $dateLte: Date
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewBannerImpsDau: Boolean = false
    $canViewInterImpsDau: Boolean = false
    $canViewRewardImpsDau: Boolean = false
    $canViewAoaImpsDau: Boolean = false
    $canViewMrecImpsDau: Boolean = false
    $canViewAudioImpsDau: Boolean = false
    $canViewAoaAdmobImpsDau: Boolean = false
    $canViewCollapseAdmobImpsDau: Boolean = false
    $canViewNativeAdmobImpsDau: Boolean = false
    $canViewAdaptiveAdmobImpsDau: Boolean = false
    $canViewMrecAdmobImpsDau: Boolean = false
    $canViewAverageSession: Boolean = false
    $canViewPlaytime: Boolean = false
  ) {
    summary: aggregateGameMetric(where: { gameId: $gameId }) {
      ...AclGameMetricAttributes
    }

    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {
      ...AclGameMetricAttributes
    }
  }
`)

export type Aggregation = AclGameMetricAttributesFragment
export type GameMetrics = GameMetricIndex_GameMetricAttributesFragment[]

export interface GameMetric extends GameMetricIndex_GameMetricAttributesFragment {
  hierarchy?: string[]
  autogen?: boolean
}

export function generateAclFromAttributes(attrs: any[]) {
  return fromPairs(
    attrs
      .filter((a) => canRead(a.permission))
      .map((a) => [`canView${changeCase.upperCamelCase(a.name)}`, true])
  )
}
