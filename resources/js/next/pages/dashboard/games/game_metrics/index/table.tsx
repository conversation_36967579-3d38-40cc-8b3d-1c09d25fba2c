import { useMemo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { isNil, fromPairs, sumBy } from 'lodash-es'
import { createColumnHelper } from '@tanstack/react-table'
import { useDataTable } from '@/next/hooks/use-data-table'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { formatter } from '#utils/formatter'
import { canRead } from '#utils/acl'
import { ModelAttributeFragment, PageInfoAttributesFragment } from '@/graphql'
import { Aggregation, GameMetrics, GameMetric } from './util'

import {
  DataTableCsvDownloadButton,
  EditableTableCell,
  tableHeader,
} from '@/next/components/sdk/data_table'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import {
  formatCurrency,
  formatNote,
  formatNumber,
  formatPercentage,
  formatDuration,
} from '@/components/typography'
import { TableCellHighlight } from '@/next/components/domain/data_table'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { useServerProp } from '@/components/ssr'

// Helper functions for aggregation
const avgBy = (arr: any[], key: string) => {
  const sum = sumBy(arr, key)
  return arr.length ? sum / arr.length : 0
}

const maxBy = (arr: any[], key: string) => {
  return Math.max(...arr.map((item) => item[key] ?? 0))
}

const minBy = (arr: any[], key: string) => {
  return Math.min(...arr.map((item) => item[key] ?? 0))
}

// Helper functions
export function isAggregationRow(row: GameMetric) {
  // All rows with negative IDs are aggregation rows
  return (row.id as number) < 0
}

// Helper function to check if a row is a root aggregation
export function isRootAggregationRow(row: GameMetric) {
  return row.id === -2 || row.id === -1
}
export function isChildAggregationRow(row: GameMetric) {
  return row.id === -3 || row.id === -4 || row.id === -5 || row.id === -6
}

interface GameMetricTableProps {
  metrics: GameMetrics
  summary: Aggregation
  total: Aggregation
  attrs: ModelAttributeFragment[]
  pageInfo: PageInfoAttributesFragment
  slots: {
    beforeFilter: React.ReactNode
    toolbar: React.ReactNode
  }
  filterForm: UseDataTableFilterFormReturn<any>
}

const columnHelper = createColumnHelper<GameMetric>()

export function GameMetricsTable({
  metrics,
  summary,
  total,
  attrs,
  pageInfo,
  slots: { beforeFilter, toolbar },
  filterForm,
}: Readonly<GameMetricTableProps>) {
  const { t } = useTranslation()
  const [openFloatingRoute] = useFloatingRoute()
  const pageId = useServerProp((s) => s.pageId)

  // Create column helper for type safety

  // Define columns
  const columns = useMemo(() => {
    const baseColumns = [
      columnHelper.accessor('date', {
        id: 'date',
        header: tableHeader('Date', { className: 'max-w-[100px]' }),
        cell: ({ getValue, row }) => {
          const value = getValue()
          if (isRootAggregationRow(row.original)) {
            return <span className="font-bold text-primary">{value}</span>
          } else if (isChildAggregationRow(row.original)) {
            return <span className="font-semibold text-muted-foreground">{value}</span>
          }
          return value
        },
        meta: {
          label: 'Date',
          variant: 'dateRange',
        },
        enableColumnFilter: true,
      }),
      columnHelper.accessor((row) => formatter.day(row.date as any), {
        id: 'day',
        header: () => 'Day',
        cell: ({ getValue }) => getValue(),
        meta: {
          label: 'Day',
        },
      }),
    ]

    // Metric columns
    const metricColumns = [
      // UA Metrics
      columnHelper.accessor('paidInstalls', {
        id: 'paidInstalls',
        header: tableHeader('Paid Installs', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Paid Installs',
        },
      }),
      columnHelper.accessor('organicInstalls', {
        id: 'organicInstalls',
        header: tableHeader('Organic Installs', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => {
          const value = getValue()
          return !isNil(value) ? formatNumber(value) : null
        },
        meta: {
          label: 'Organic Installs',
        },
      }),
      columnHelper.accessor('totalInstalls', {
        id: 'totalInstalls',
        header: tableHeader(t('gameMetric.totalInstalls'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: t('gameMetric.totalInstalls'),
        },
      }),
      columnHelper.accessor('organicPercentage', {
        id: 'organicPercentage',
        header: tableHeader(t('gameMetric.organicPercentage'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.organicPercentage'),
        },
      }),

      columnHelper.accessor('cost', {
        id: 'cost',
        header: tableHeader(t('gameMetric.cost'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatCurrency(getValue() ?? 0)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'cost' })
              }
            >
              {formatCurrency(getValue() ?? 0)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.cost'),
        },
      }),
      columnHelper.accessor('cpi', {
        id: 'cpi',
        header: tableHeader(t('gameMetric.cpi'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0, 4),
        meta: {
          label: t('gameMetric.cpi'),
        },
      }),
      columnHelper.accessor('roas', {
        id: 'roas',
        header: tableHeader(t('gameMetric.roas'), {
          bgcolor: 'bg-green-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.roas'),
        },
      }),
      columnHelper.accessor('revenue', {
        id: 'revenue',
        header: tableHeader(t('gameMetric.revenue'), {
          bgcolor: 'bg-green-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatCurrency(getValue() ?? 0)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'revenue' })
              }
            >
              {formatCurrency(getValue() ?? 0)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.revenue'),
        },
      }),
      columnHelper.accessor('profit', {
        id: 'profit',
        header: tableHeader(t('gameMetric.profit'), {
          bgcolor: 'bg-green-400',
          className: 'whitespace-break-word',
        }),
        cell: ({ getValue }) => <TableCellHighlight value={getValue() ?? 0} type="currency" />,
        meta: {
          label: t('gameMetric.profit'),
        },
      }),

      // Retention Metrics
      columnHelper.accessor('retentionRateDay1', {
        id: 'retentionRateDay1',
        header: tableHeader(t('gameMetric.retentionRateDay1'), {
          bgcolor: 'bg-orange-400',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.retentionRateDay1'),
        },
      }),
      columnHelper.accessor('retentionRateDay3', {
        id: 'retentionRateDay3',
        header: tableHeader(t('gameMetric.retentionRateDay3'), {
          bgcolor: 'bg-orange-400',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.retentionRateDay3'),
        },
      }),
      columnHelper.accessor('retentionRateDay7', {
        id: 'retentionRateDay7',
        header: tableHeader(t('gameMetric.retentionRateDay7'), {
          bgcolor: 'bg-orange-400',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.retentionRateDay7'),
        },
      }),
      columnHelper.accessor('bannerImpsDau', {
        id: 'bannerImpsDau',
        header: tableHeader('Banner ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces h-[60px]',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Banner ImpsDAU',
        },
      }),
      columnHelper.accessor('interImpsDau', {
        id: 'interImpsDau',
        header: tableHeader('Inter ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Inter ImpsDAU',
        },
      }),
      columnHelper.accessor('rewardImpsDau', {
        id: 'rewardImpsDau',
        header: tableHeader('Reward ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Reward ImpsDAU',
        },
      }),
      columnHelper.accessor('aoaImpsDau', {
        id: 'aoaImpsDau',
        header: tableHeader('AOA ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'AOA ImpsDAU',
        },
      }),
      columnHelper.accessor('mrecImpsDau', {
        id: 'mrecImpsDau',
        header: tableHeader('MREC ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'MREC ImpsDAU',
        },
      }),
      columnHelper.accessor('aoaAdmobImpsDau', {
        id: 'aoaAdmobImpsDau',
        header: tableHeader('AOA Admob ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'AOA Admob ImpsDAU',
        },
      }),
      columnHelper.accessor('collapseAdmobImpsDau', {
        id: 'collapseAdmobImpsDau',
        header: tableHeader('Collapse Admob ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Collapse Admob ImpsDAU',
        },
      }),
      columnHelper.accessor('nativeAdmobImpsDau', {
        id: 'nativeAdmobImpsDau',
        header: tableHeader('Native Admob ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Native Admob ImpsDAU',
        },
      }),
      columnHelper.accessor('adaptiveAdmobImpsDau', {
        id: 'adaptiveAdmobImpsDau',
        header: tableHeader('Adaptive Admob ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Adaptive Admob ImpsDAU',
        },
      }),
      columnHelper.accessor('mrecAdmobImpsDau', {
        id: 'mrecAdmobImpsDau',
        header: tableHeader('MREC Admob ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'MREC Admob ImpsDAU',
        },
      }),
      columnHelper.accessor('audioImpsDau', {
        id: 'audioImpsDau',
        header: tableHeader('Audio ImpsDAU', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Audio ImpsDAU',
        },
      }),

      columnHelper.accessor('averageSession', {
        id: 'averageSession',
        header: tableHeader(t('gameMetric.averageSession'), {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: t('gameMetric.averageSession'),
        },
      }),
      columnHelper.accessor('playtime', {
        id: 'playtime',
        header: tableHeader(t('gameMetric.playtime'), {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatDuration(getValue() ?? 0),
        meta: {
          label: t('gameMetric.playtime'),
        },
      }),
    ]

    const noteColumns = [
      columnHelper.accessor('versionNote', {
        id: 'versionNote',
        header: () => t('gameMetric.versionNote'),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', {
                  id: String(row.original.id),
                  name: 'versionNote',
                })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.versionNote'),
        },
      }),
      columnHelper.accessor('uaNote', {
        id: 'uaNote',
        header: () => t('gameMetric.uaNote'),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'uaNote' })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.uaNote'),
        },
      }),
      columnHelper.accessor('monetNote', {
        id: 'monetNote',
        header: () => t('gameMetric.monetNote'),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'monetNote' })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.monetNote'),
        },
      }),
      columnHelper.accessor('productNote', {
        id: 'productNote',
        header: () => t('gameMetric.productNote'),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', {
                  id: String(row.original.id),
                  name: 'productNote',
                })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.productNote'),
        },
      }),
      columnHelper.accessor('note', {
        id: 'note',
        header: () => t('gameMetric.note'),
        cell: ({ getValue, row }) =>
          row.original.id < 0 ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'note' })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.note'),
        },
      }),
    ]

    // Filter columns based on permissions
    return [...baseColumns, ...metricColumns, ...noteColumns].filter((column) => {
      const attr = attrs.find((a) => a.name === column.id)
      return attr ? canRead(attr.permission) : true
    })
  }, [attrs, t, openFloatingRoute])

  // Aggregate function for summary rows
  const aggregate = useCallback(
    (fn: any, _hierarchy: string[], id: number): GameMetric => {
      return {
        id,
        ...fromPairs(
          Object.keys(summary)
            .map((key) => {
              try {
                const val = fn(metrics, key)
                return [key, val]
              } catch (err) {
                console.error(`Error calculating aggregate for ${key}:`, err)
                return null
              }
            })
            .filter((e): e is [string, any] => !!e)
        ),
      } as GameMetric
    },
    [summary, metrics]
  )

  // Prepare rows with summary data
  const rows = useMemo<GameMetric[]>(() => {
    return [
      // Root nodes
      { ...summary, id: -1, date: 'Summary' as any, hierarchy: ['Summary'] } as GameMetric,
      { ...total, id: -2, date: 'Total' as any, hierarchy: ['Total'] } as GameMetric,

      {
        ...aggregate(sumBy, ['Total', 'SUM'], -3),
        date: 'SUM' as any,
        hierarchy: ['Total', 'SUM'],
      },
      {
        ...aggregate(avgBy, ['Total', 'AVG'], -4),
        date: 'AVG' as any,
        hierarchy: ['Total', 'AVG'],
      },
      {
        ...aggregate(maxBy, ['Total', 'MAX'], -5),
        date: 'MAX' as any,
        hierarchy: ['Total', 'MAX'],
      },
      {
        ...aggregate(minBy, ['Total', 'MIN'], -6),
        date: 'MIN' as any,
        hierarchy: ['Total', 'MIN'],
      },

      ...metrics.map((m) => ({ ...m, hierarchy: [m.date?.toString() ?? ''] })),
    ]
  }, [metrics, summary, total, aggregate])

  // Set up data table
  const { table } = useDataTable({
    data: rows,
    columns,
    pageCount: pageInfo.lastPage,
    getRowId: (row) => String(row.id),
    initialState: {
      grouping: [],
      expanded: {},
      pagination: {
        pageIndex: pageInfo.currentPage - 1,
        pageSize: pageInfo.perPage,
      },
      columnPinning: {
        left: ['date'],
      },
    },
    form: filterForm,
    meta: {
      pageId,
    } as any,
  })

  return (
    <TreeTable
      table={table}
      getNodeKeys={(m) => (m as GameMetric).hierarchy ?? [m.date?.toString() ?? '']}
      getNodeId={(keys) => keys.join('|')}
      getRowCanExpand={(row) => {
        return row.id === -2
      }}
      indentSize={24}
      expandColumn="date"
      sticky
      pageSizeOptions={[50, 100, 200]}
    >
      <DataTableToolbar
        table={table}
        slots={{
          beforeFilter: beforeFilter,
        }}
      >
        {toolbar}
        <DataTableCsvDownloadButton table={table} />
      </DataTableToolbar>
    </TreeTable>
  )
}
