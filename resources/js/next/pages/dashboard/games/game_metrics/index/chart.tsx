import { useServerProp } from '@/components/ssr'
import { useGetMetabaseChart } from '@/next/api/use_get_metabase_chart'
import { MetabaseChart } from '@/next/components/metabase/metabase_chart'
import { useMetabaseFilterForm } from '@/next/components/metabase/use_metabase_filter_form'
import { useMetabaseParams } from '@/next/components/metabase/use_metabase_params'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { useMemo } from 'react'

export function Chart({ slots }: { slots: { toolbar: React.ReactNode } }) {
  const [[metabaseChartConfigMapCollection]] = useConfigMap(
    useMemo(() => ['cmap.dash.metabasechart'], [])
  )

  const gameId = useServerProp((s) => s.game.id)

  const configMap = metabaseChartConfigMapCollection.find((cm) => cm.id === 'game-metric')!

  const form = useMetabaseFilterForm({
    configMap,
  })

  const params = useMetabaseParams(form, {
    gameId: [gameId],
    // gameId: ['com.mirai.badstudent.school.prankteacher.prankster'],
  })

  const getMetabaseChart = useGetMetabaseChart({
    id: configMap.id,
    params,
  })

  return (
    <QueryState query={getMetabaseChart}>
      <MetabaseChart url={getMetabaseChart.data?.metabaseChart?.url!} form={form}>
        {slots.toolbar}
      </MetabaseChart>
    </QueryState>
  )
}
