import Game from '#models/game'
import { useServerProp } from '@/components/ssr'
import { makeGameBreadcrumbs } from '@/next/components/domain/game_breadcrumbs'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { useQuery } from '@/next/components/sdk/graphql'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { getAggregationQuery, getInitialDataQuery, getMetricsQuery } from './index/util'
import { format, subDays } from 'date-fns'
import { useMemo } from 'react'
import { canRead } from '#utils/acl'
import * as changeCase from 'case-anything'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { GameMetricsTable } from './index/table'
import {
  DataTableFilterSubmitButton,
  DataTableToolbarButton,
} from '@/next/components/sdk/data_table'
import { Repeat } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/next/components/ui/tooltip'
import { routes } from '@/components/location'
import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

export default function Page() {
  const game: Game = useServerProp((s) => s.game)
  const [queryParams] = useQueryStates({
    view: parseAsString.withDefault('impsDau'),
    page: parseAsInteger.withDefault(1),
    perPage: parseAsInteger.withDefault(200),
  })

  const filterForm = useDataTableFilterForm({
    date: {
      variant: 'dateRange',
      defaultValue: [subDays(new Date(), 30), new Date()],
    },
    view: {
      variant: 'select',
      defaultValue: 'impsDau',
    },
  })

  const getInitialData = useQuery(getInitialDataQuery, {
    gameId: game.id,
  })

  const acl = useMemo(() => {
    if (!getInitialData.data) {
      return {}
    }

    return Object.assign(
      Object.fromEntries(
        (getInitialData.data?.attributes?.collection || []).map((a) => {
          return [`canView${changeCase.pascalCase(a.name)}`, canRead(a.permission)]
        })
      ),
      Object.fromEntries(
        (getInitialData.data?.gameRevenueAttributes?.collection || []).map((a) => {
          return [`canViewGameRevenue${changeCase.pascalCase(a.name)}`, canRead(a.permission)]
        })
      )
    )
  }, [getInitialData.data])

  const filterFormValues = filterForm.watch()

  const queryVariables = useMemo(() => {
    return {
      gameId: game.id,
      dateFrom: format(filterFormValues.date[0], 'yyyy-MM-dd'),
      dateTo: format(filterFormValues.date[1], 'yyyy-MM-dd'),
      page: queryParams.page,
      perPage: queryParams.perPage,
    }
  }, [game.id, filterFormValues, queryParams.page, queryParams.perPage])

  const getMetrics = useQuery(
    getMetricsQuery,
    {
      ...queryVariables,
      ...acl,
    },
    {
      skip: !getInitialData.data,
    }
  )

  const getAggregation = useQuery(
    getAggregationQuery,
    {
      ...queryVariables,
      ...acl,
    },
    {
      skip: !getInitialData.data,
    }
  )

  const onFilterSubmit = () => {
    filterForm.syncValues('date')
  }

  return (
    <BaseLayout>
      <PageConfig
        title={`Game Metrics V2 | ${game.name}`}
        breadcrumbs={makeGameBreadcrumbs(game, 'Game Metrics V2')}
        sidebarSubItems={makeGameSidebarSubItems(game.id)}
      />

      <QueryState query={[getInitialData, getMetrics, getAggregation]}>
        <GameMetricsTable
          view={queryParams.view}
          revenueAttrs={getInitialData.data?.gameRevenueAttributes?.collection!}
          attrs={getInitialData.data?.attributes?.collection!}
          adTypes={getInitialData.data?.adTypes?.collection!}
          metrics={getMetrics.data?.v2?.gameMetrics?.collection!}
          pageInfo={getMetrics.data?.v2?.gameMetrics?.pageInfo!}
          summary={getAggregation.data?.v2?.summary!}
          total={getAggregation.data?.v2?.total!}
          filterForm={filterForm}
          slots={{
            toolbar: (
              <Tooltip>
                <TooltipTrigger>
                  <DataTableToolbarButton asChild>
                    <a href={routes.partner.games.metrics(game.id)}>
                      <Repeat />
                    </a>
                  </DataTableToolbarButton>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Switch to partner view</p>
                </TooltipContent>
              </Tooltip>
            ),
            beforeFilter: (
              <DataTableFilterSubmitButton
                onClick={onFilterSubmit}
                disabled={getMetrics.loading}
                loading={getMetrics.loading}
              />
            ),
          }}
        />
      </QueryState>
    </BaseLayout>
  )
}
