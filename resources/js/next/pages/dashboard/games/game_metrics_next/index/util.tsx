import { graphql } from '@/gql'

export const getInitialDataQuery = graphql(`
  query GameMetricNextIndex_GetInitData($gameId: ID!) {
    adTypes {
      collection {
        ...AdTypeAttributes
      }
    }

    attributes(where: { gameId: $gameId, modelName: "GameMetricV2" }) {
      collection {
        ...ModelAttribute
      }
    }

    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: "GameRevenue" }) {
      collection {
        ...ModelAttribute
      }
    }
  }
`)

export const gameMetricAttributesFragment = graphql(`
  fragment GameMetricNextIndex_GameMetricAttributes on V2GameMetric {
    date
    gameId
    ...AclGameMetricV2Attributes

    uaNote @include(if: $canViewUaNote)
    monetNote @include(if: $canViewMonetNote)
    productNote @include(if: $canViewProductNote)
    versionNote @include(if: $canViewVersionNote)
    note @include(if: $canViewNote)
  }
`)

export const getMetricsQuery = graphql(`
  query GameMetricNextIndex_GetMetrics(
    $gameId: ID!
    $page: Int!
    $perPage: Int!
    $dateFrom: Date!
    $dateTo: Date!
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewDailyActiveUsers: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewSessions: Boolean = false
    $canViewPlaytime: Boolean = false
    $canViewUaNote: Boolean = false
    $canViewMonetNote: Boolean = false
    $canViewProductNote: Boolean = false
    $canViewVersionNote: Boolean = false
    $canViewNote: Boolean = false
    $canViewGameRevenueRevenue: Boolean = false
    $canViewGameRevenueArpDau: Boolean = false
    $canViewGameRevenueEcpm: Boolean = false
    $canViewGameRevenueImpsDau: Boolean = false
  ) {
    v2 {
      gameMetrics(
        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }
        offset: { page: $page, perPage: $perPage }
      ) {
        collection {
          ...GameMetricNextIndex_GameMetricAttributes
        }

        pageInfo {
          ...PageInfoAttributes
        }
      }
    }
  }
`)

export const getAggregationQuery = graphql(`
  query GameMetricNextIndex_GetAggregation(
    $gameId: ID!
    $dateFrom: Date!
    $dateTo: Date!
    $canViewPaidInstalls: Boolean = false
    $canViewOrganicInstalls: Boolean = false
    $canViewOrganicPercentage: Boolean = false
    $canViewTotalInstalls: Boolean = false
    $canViewCost: Boolean = false
    $canViewCpi: Boolean = false
    $canViewRoas: Boolean = false
    $canViewRevenue: Boolean = false
    $canViewProfit: Boolean = false
    $canViewDailyActiveUsers: Boolean = false
    $canViewRetentionRateDay1: Boolean = false
    $canViewRetentionRateDay3: Boolean = false
    $canViewRetentionRateDay7: Boolean = false
    $canViewSessions: Boolean = false
    $canViewPlaytime: Boolean = false
    $canViewGameRevenueRevenue: Boolean = false
    $canViewGameRevenueArpDau: Boolean = false
    $canViewGameRevenueEcpm: Boolean = false
    $canViewGameRevenueImpsDau: Boolean = false
  ) {
    v2 {
      summary: aggregateGameMetrics(id: "summary", where: { gameId: $gameId }) {
        ...AclGameMetricV2Attributes
      }

      total: aggregateGameMetrics(
        id: "total"
        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }
      ) {
        ...AclGameMetricV2Attributes
      }
    }
  }
`)
