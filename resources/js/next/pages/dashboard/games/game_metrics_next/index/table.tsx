import { useMemo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { isNil, sumBy, orderBy } from 'lodash-es'
import { createColumnHelper } from '@tanstack/react-table'
import { useDataTable } from '@/next/hooks/use-data-table'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { formatter } from '#utils/formatter'
import { canRead } from '#utils/acl'
import {
  AclGameMetricV2AttributesFragment,
  AdTypeAttributesFragment,
  GameMetricNextIndex_GameMetricAttributesFragment,
  ModelAttributeFragment,
  PageInfoAttributesFragment,
} from '@/graphql'
import {
  DataTableCsvDownloadButton,
  EditableTableCell,
  tableHeader,
} from '@/next/components/sdk/data_table'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import {
  formatCurrency,
  formatNote,
  formatNumber,
  formatPercentage,
  formatDuration,
} from '@/components/typography'
import { TableCellHighlight } from '@/next/components/domain/data_table'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { safeDivide } from '#utils/math'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { useServerProp } from '@/components/ssr'

// Helper functions for aggregation
const avgBy = (arr: any[], key: string) => {
  const sum = sumBy(arr, key)
  return arr.length ? sum / arr.length : 0
}

const maxBy = (arr: any[], key: string) => {
  return Math.max(...arr.map((item) => item[key] ?? 0))
}

const minBy = (arr: any[], key: string) => {
  return Math.min(...arr.map((item) => item[key] ?? 0))
}

interface GameMetric extends GameMetricNextIndex_GameMetricAttributesFragment {
  hierarchy?: string[]
}

// Helper functions
export function isAggregationRow(row: GameMetric) {
  return ['summary', 'total', 'sum', 'avg', 'max', 'min'].includes(row.id)
}

interface GameMetricsTableProps {
  metrics: GameMetric[]
  adTypes: AdTypeAttributesFragment[]
  summary: AclGameMetricV2AttributesFragment
  total: AclGameMetricV2AttributesFragment
  attrs: ModelAttributeFragment[]
  revenueAttrs: ModelAttributeFragment[]
  pageInfo: PageInfoAttributesFragment
  view: string
  slots?: {
    beforeFilter?: React.ReactNode
    toolbar?: React.ReactNode
  }
  filterForm: UseDataTableFilterFormReturn<any>
}

const columnHelper = createColumnHelper<GameMetric>()

export function GameMetricsTable({
  metrics,
  adTypes,
  summary,
  total,
  attrs,
  revenueAttrs,
  view,
  pageInfo,
  slots: { beforeFilter, toolbar } = {},
  filterForm,
}: Readonly<GameMetricsTableProps>) {
  const { t } = useTranslation()
  const [openFloatingRoute] = useFloatingRoute()
  const pageId = useServerProp((s) => s.pageId)

  const columns = useMemo(() => {
    const baseColumns = [
      columnHelper.accessor('date', {
        id: 'date',
        header: tableHeader('Date', { className: 'max-w-[100px]' }),
        meta: {
          label: 'Date',
          variant: 'dateRange',
        },
        enableColumnFilter: true,
      }),
      columnHelper.accessor((row) => formatter.day(row.date as any), {
        id: 'day',
        header: () => 'Day',
        cell: ({ getValue }) => getValue(),
        meta: {
          label: 'Day',
        },
      }),
      columnHelper.accessor(() => 'view', {
        id: 'view',
        header: 'View',
        enableHiding: false,
        meta: {
          label: 'View',
          options: [
            { label: 'ImpsDau', value: 'impsDau' },
            { label: 'eCPM', value: 'ecpm' },
            { label: 'ARPDAU', value: 'arpDau' },
            { label: '% Revenue', value: 'revenue' },
          ].filter((e) => revenueAttrs.some((a) => a.name === e.value && canRead(a.permission))),
        },
      }),
    ]

    // Core metric columns
    const metricColumns = [
      // UA Metrics
      columnHelper.accessor('paidInstalls', {
        id: 'paidInstalls',
        header: tableHeader('Paid Installs', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Paid Installs',
        },
      }),
      columnHelper.accessor('organicInstalls', {
        id: 'organicInstalls',
        header: tableHeader('Organic Installs', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => {
          const value = getValue()
          return !isNil(value) ? formatNumber(value) : null
        },
        meta: {
          label: 'Organic Installs',
        },
      }),
      columnHelper.accessor('totalInstalls', {
        id: 'totalInstalls',
        header: tableHeader(t('gameMetric.totalInstalls'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: t('gameMetric.totalInstalls'),
        },
      }),
      columnHelper.accessor('organicPercentage', {
        id: 'organicPercentage',
        header: tableHeader(t('gameMetric.organicPercentage'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.organicPercentage'),
        },
      }),
      columnHelper.accessor('cost', {
        id: 'cost',
        header: tableHeader(t('gameMetric.cost'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatCurrency(getValue() ?? 0)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'cost' })
              }
            >
              {formatCurrency(getValue() ?? 0)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.cost'),
        },
      }),
      columnHelper.accessor('cpi', {
        id: 'cpi',
        header: tableHeader(t('gameMetric.cpi'), {
          bgcolor: 'bg-cyan-300',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0, 4),
        meta: {
          label: t('gameMetric.cpi'),
        },
      }),
      columnHelper.accessor('roas', {
        id: 'roas',
        header: tableHeader(t('gameMetric.roas'), {
          bgcolor: 'bg-green-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.roas'),
        },
      }),
      columnHelper.accessor('revenue', {
        id: 'revenue',
        header: tableHeader(t('gameMetric.revenue'), {
          bgcolor: 'bg-green-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatCurrency(getValue() ?? 0)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'revenue' })
              }
            >
              {formatCurrency(getValue() ?? 0)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.revenue'),
        },
      }),
      columnHelper.accessor('profit', {
        id: 'profit',
        header: tableHeader(t('gameMetric.profit'), {
          bgcolor: 'bg-green-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => <TableCellHighlight value={getValue() ?? 0} type="currency" />,
        meta: {
          label: t('gameMetric.profit'),
        },
      }),
      columnHelper.accessor('dailyActiveUsers', {
        id: 'dailyActiveUsers',
        header: tableHeader('DAU', {
          bgcolor: 'bg-yellow-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'DAU',
        },
      }),

      // Retention Metrics
      columnHelper.accessor('retentionRateDay1', {
        id: 'retentionRateDay1',
        header: tableHeader(t('gameMetric.retentionRateDay1'), {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.retentionRateDay1'),
        },
      }),
      columnHelper.accessor('retentionRateDay3', {
        id: 'retentionRateDay3',
        header: tableHeader(t('gameMetric.retentionRateDay3'), {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.retentionRateDay3'),
        },
      }),
      columnHelper.accessor('retentionRateDay7', {
        id: 'retentionRateDay7',
        header: tableHeader(t('gameMetric.retentionRateDay7'), {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatPercentage(getValue() ?? 0),
        meta: {
          label: t('gameMetric.retentionRateDay7'),
        },
      }),

      // Ad Performance columns based on view
      ...orderBy(adTypes, (a) => a.order, 'asc')
        .flatMap((adType) => {
          const cols = ['impsDau', 'ecpm', 'arpDau'].map((v) =>
            columnHelper.accessor(
              (row) => {
                const adPerformance = row.adPerformances.find((e) => e.adType === adType.id)
                return adPerformance?.[v as 'impsDau' | 'arpDau' | 'ecpm'] ?? 0
              },
              {
                id: `adPerformances.${adType.id}.${v}`,
                header: tableHeader(`${adType.name} ${t(`gameRevenue.${v}`)}`, {
                  bgcolor: 'bg-orange-400',
                  className: 'whitespace-break-spaces',
                }),
                cell: ({ getValue }) => formatNumber(getValue() ?? 0, v === 'arpDau' ? 3 : 2),
                meta: {
                  label: `${adType.name} ${t(`gameRevenue.${v}`)}`,
                },
              }
            )
          )

          cols.push(
            columnHelper.accessor(
              (row) => {
                const adPerformance = row.adPerformances.find((e) => e.adType === adType.id)
                return adPerformance?.revenue ?? 0
              },
              {
                id: `adPerformances.${adType.id}.revenue`,
                header: tableHeader(`${adType.name} ${t('gameRevenue.revenue')}`, {
                  bgcolor: 'bg-orange-400',
                  className: 'whitespace-break-spaces',
                }),
                cell: ({ getValue, row }) =>
                  formatPercentage(safeDivide(getValue() ?? 0, row.original.revenue ?? 0)),
                meta: {
                  label: `${adType.name} ${t('gameRevenue.revenue')}`,
                },
              }
            )
          )

          return cols
        })
        .filter((def) => def.id!.endsWith(view)),

      columnHelper.accessor('sessions', {
        id: 'sessions',
        header: tableHeader('Sessions', {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatNumber(getValue() ?? 0),
        meta: {
          label: 'Sessions',
        },
      }),
      columnHelper.accessor('playtime', {
        id: 'playtime',
        header: tableHeader(t('gameMetric.playtime'), {
          bgcolor: 'bg-orange-400',
          className: 'whitespace-break-spaces',
        }),
        cell: ({ getValue }) => formatDuration(getValue() ?? 0),
        meta: {
          label: t('gameMetric.playtime'),
        },
      }),
    ]

    const noteColumns = [
      columnHelper.accessor('versionNote', {
        id: 'versionNote',
        header: t('gameMetric.versionNote'),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', {
                  id: String(row.original.id),
                  name: 'versionNote',
                })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.versionNote'),
        },
      }),
      columnHelper.accessor('uaNote', {
        id: 'uaNote',
        header: t('gameMetric.uaNote'),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'uaNote' })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.uaNote'),
        },
      }),
      columnHelper.accessor('monetNote', {
        id: 'monetNote',
        header: t('gameMetric.monetNote'),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'monetNote' })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.monetNote'),
        },
      }),
      columnHelper.accessor('productNote', {
        id: 'productNote',
        header: t('gameMetric.productNote'),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', {
                  id: String(row.original.id),
                  name: 'productNote',
                })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.productNote'),
        },
      }),
      columnHelper.accessor('note', {
        id: 'note',
        header: t('gameMetric.note'),
        cell: ({ getValue, row }) =>
          isAggregationRow(row.original) ? (
            formatNote(getValue(), 30)
          ) : (
            <EditableTableCell
              onClick={() =>
                openFloatingRoute('edit-metric', { id: String(row.original.id), name: 'note' })
              }
            >
              {formatNote(getValue(), 30)}
            </EditableTableCell>
          ),
        meta: {
          label: t('gameMetric.note'),
        },
      }),
    ]

    // Filter columns based on permissions
    return baseColumns.concat(
      [...metricColumns, ...noteColumns].filter((column) => {
        const attr = attrs.find((a) => a.name === column.id)
        return attr ? canRead(attr.permission) : true
      }) as any
    )
  }, [attrs, t, openFloatingRoute, adTypes, view])

  // Aggregate function for summary rows
  const aggregate = useCallback(
    (fn: any, _hierarchy: string[], id: string): GameMetric => {
      const entries = Object.entries(total).map(([key, value]) => {
        if (key === 'adPerformances') {
          return [
            key,
            adTypes.map((adType) => {
              const adPerformances = metrics.flatMap((m) =>
                m.adPerformances.filter((ap) => ap.adType === adType.id)
              )

              return {
                adType: adType.id,
                impsDau: fn(adPerformances, 'impsDau'),
                revenue: fn(adPerformances, 'revenue'),
                ecpm: fn(adPerformances, 'ecpm'),
                arpDau: fn(adPerformances, 'arpDau'),
                impressionCount: fn(adPerformances, 'impressionCount'),
                dailyActiveUserCount: fn(adPerformances, 'dailyActiveUserCount'),
              }
            }),
          ]
        }
        try {
          return [key, fn(metrics, key)]
        } catch (err) {
          return [key, value]
        }
      })

      return {
        ...Object.fromEntries(entries),
        id,
        date: id,
        gameId: '',
        hierarchy: ['TOTAL', id],
      } as GameMetric
    },
    [total, metrics, adTypes]
  )

  // Prepare rows with summary data
  const rows = useMemo<GameMetric[]>(() => {
    return [
      // Root nodes
      {
        ...summary,
        date: 'Summary',
        hierarchy: ['Summary'],
      } as GameMetric,
      {
        ...total,
        date: 'Total',
        hierarchy: ['Total'],
      } as GameMetric,

      {
        ...aggregate(sumBy, ['Total', 'SUM'], 'sum'),
        date: 'SUM',
        hierarchy: ['Total', 'SUM'],
      },
      {
        ...aggregate(avgBy, ['Total', 'AVG'], 'avg'),
        date: 'AVG',
        hierarchy: ['Total', 'AVG'],
      },
      {
        ...aggregate(maxBy, ['Total', 'MAX'], 'max'),
        date: 'MAX',
        hierarchy: ['Total', 'MAX'],
      },
      {
        ...aggregate(minBy, ['Total', 'MIN'], 'min'),
        date: 'MIN',
        hierarchy: ['Total', 'MIN'],
      },
      ...metrics,
    ]
  }, [metrics, summary, total, aggregate])

  // Set up data table
  const { table } = useDataTable({
    data: rows,
    columns,
    pageCount: pageInfo.lastPage,
    getRowId: (row) => String(row.id),
    initialState: {
      grouping: [],
      columnVisibility: {
        view: false,
      },
      columnPinning: {
        left: ['date'],
      },
      expanded: {},
      pagination: {
        pageIndex: pageInfo.currentPage - 1,
        pageSize: pageInfo.perPage,
      },
    },
    form: filterForm,
    meta: {
      pageId,
    } as any,
  })

  return (
    <TreeTable
      table={table}
      getNodeKeys={(m) => m.hierarchy ?? [m.date?.toString() ?? '']}
      getNodeId={(keys) => keys.join('|')}
      getRowCanExpand={(row) => {
        return row.id === 'total'
      }}
      indentSize={24}
      expandColumn="date"
      sticky
      pageSizeOptions={[50, 100, 200]}
    >
      <DataTableToolbar
        table={table}
        slots={{
          beforeFilter: beforeFilter,
        }}
      >
        {toolbar}
        <DataTableCsvDownloadButton table={table} />
      </DataTableToolbar>
    </TreeTable>
  )
}
