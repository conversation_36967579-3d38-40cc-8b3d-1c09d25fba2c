import FirebaseExperimentsController from '#controllers/dashboard/game/firebase_experiments_controller'
import { routes } from '@/components/location'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { InferPageProps } from '@adonisjs/inertia/types'

import { FirebaseExprimentsTable } from './index/table'
import { graphql } from '@/gql'
import { useQuery } from '@/next/components/sdk/graphql'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { Filter, FilterOperator, FirebaseExperimentIndex_GetMetricsQueryVariables } from '@/graphql'
import { format, subDays } from 'date-fns'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { useMemo, useRef } from 'react'
import { useGroupFields } from './index/query_state'
import { uniq, uniqBy } from 'lodash-es'

import {
  ViewPresetEditor,
  ViewPresetSelection,
} from '@/next/components/domain/view_preset_selection'
import { Columns2 } from 'lucide-react'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { parseAsString, useQueryState } from 'nuqs'
import { useServerProp } from '@/components/ssr'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'
import { DataTableFilterSubmitButton } from '@/next/components/sdk/data_table'

type PageProps = InferPageProps<FirebaseExperimentsController, 'index'>

graphql(`
  fragment FirebaseExperimentIndex_FirebaseMetric on FirebaseMetric {
    ...FirebaseMetricAttributes

    ads {
      ...FirebaseAdMetricAttributes
    }
  }
`)

const getViewPresetsQuery = graphql(`
  query FirebaseExperimentIndex_GetViewPresets($where: ViewPresetsWhere!) {
    viewPresets(where: $where) {
      collection {
        ...ViewPresetAttributes
      }
    }
  }
`)

const getInitialDataQuery = graphql(`
  query FirebaseExperimentIndex_GetInitialData($gameId: ID!) {
    firebaseExperiments(where: { gameId: $gameId }) {
      collection {
        ...FirebaseExperimentAttributes
      }
    }

    networks(where: { category: FIREBASE }) {
      collection {
        ...NetworkAttributes
      }
    }

    mediations {
      collection {
        ...MediationAttributes
      }
    }
  }
`)

const getVersionVariantsQuery = graphql(`
  query FirebaseExperimentIndex_GetVersionVariants($where: FirebaseVersionVariantsWhere!) {
    firebaseVersionVariants(where: $where) {
      collection {
        ...FirebaseVersionVariantAttributes
      }
    }
  }
`)

const getMetricsQuery = graphql(`
  query FirebaseExperimentIndex_GetMetrics(
    $where: FirebaseMetricsWhere!
    $group: Group!
    $preset: Preset
  ) {
    firebaseMetrics(where: $where, group: $group, preset: $preset) {
      collection {
        ...FirebaseExperimentIndex_FirebaseMetric
      }

      variants {
        ...FirebaseVersionVariantAttributes
      }

      viewPreset {
        ...ViewPresetAttributes
      }
    }
  }
`)

export default function Page({ game: { name: gameName, id: gameId } }: PageProps) {
  const pageId = useServerProp((s) => s.pageId)
  const getInitialData = useQuery(getInitialDataQuery, {
    gameId,
  })
  const [preset] = useQueryState('preset', parseAsString)
  const [groupFields] = useGroupFields()

  const filterForm = useDataTableFilterForm({
    date: {
      variant: 'dateRange',
      defaultValue: [subDays(new Date(), 7), new Date()],
    },
    experiment: {
      variant: 'select',
      defaultValue: 'std',
    },
    variantId: {
      variant: 'multiSelect',
      defaultValue: [],
    },
    base: {
      variant: 'select',
      defaultValue: '',
    },
  })

  const filterFormValues = filterForm.watch()

  const getViewPresets = useQuery(getViewPresetsQuery, { where: { pageId } })

  const [[viewPresetConfigMapCollection], getConfigMaps] = useConfigMap(
    useMemo(() => ['cmap.viewpreset'], [])
  )

  const getMetricsQueryVars = useMemo<FirebaseExperimentIndex_GetMetricsQueryVariables>(() => {
    return {
      group: {
        fields: groupFields.slice(0, 1),
      },
      where: {
        gameId,
        date: {
          operator: FilterOperator.Between,
          values: [
            format(filterFormValues.date[0] || new Date(), 'yyyy-MM-dd'),
            format(filterFormValues.date[1] || new Date(), 'yyyy-MM-dd'),
          ],
        },
        countryCode: {
          operator: FilterOperator.In,
          values: [],
        },
        variantId: {
          operator: FilterOperator.In,
          values: filterFormValues.variantId?.length
            ? uniq([...filterFormValues.variantId, filterFormValues.base].filter(Boolean))
            : filterFormValues.variantId,
        },
        experiment: {
          operator: FilterOperator.In,
          values: [filterFormValues.experiment],
        },
      },
      preset: preset ? { viewPresetId: parseInt(preset) } : undefined,
    }
  }, [groupFields, filterFormValues])

  const getMetrics = useQuery(getMetricsQuery, getMetricsQueryVars)
  const getVersionVariants = useQuery(getVersionVariantsQuery, {
    where: {
      gameId,
      experiment: {
        operator: FilterOperator.In,
        values: [filterFormValues.experiment],
      },
    },
  })
  const reqs = useRef<Record<string, boolean>>({})

  const onExpandRow = (vars: FirebaseExperimentIndex_GetMetricsQueryVariables) => {
    const reqId = vars.group.fields
      .flatMap((m) => (vars.where as unknown as Record<string, Filter>)[m]?.values || [])
      .join('__')

    if (reqs.current[reqId]) {
      return
    }

    return getMetrics
      .fetchMore({
        variables: vars,
        updateQuery: (
          { firebaseMetrics: existing, ...prev },
          { fetchMoreResult: { firebaseMetrics: incoming } }
        ) => {
          return {
            ...prev,
            firebaseMetrics: {
              ...incoming,
              collection: [...existing.collection, ...incoming.collection],
              variants: uniqBy([...existing.variants, ...incoming.variants], 'id'),
            },
          }
        },
      })
      .then(() => {
        reqs.current[reqId] = true
      })
  }

  return (
    <BaseLayout>
      <PageConfig
        title="Firebase A/B Testing"
        breadcrumbs={[
          {
            title: 'All Games',
            url: routes.dash.games.index(),
          },
          {
            title: gameName,
            url: routes.dash.games.campaignMetrics(gameId),
          },
          'Firebase A/B Testing',
        ]}
        sidebarSubItems={makeGameSidebarSubItems(gameId)}
      />

      <QueryState query={[getMetrics, getInitialData, getConfigMaps, getViewPresets]}>
        <FirebaseExprimentsTable
          collection={getMetrics.data?.firebaseMetrics?.collection!}
          networks={getInitialData.data?.networks?.collection!}
          mediations={getInitialData.data?.mediations?.collection!}
          experiments={getInitialData.data?.firebaseExperiments?.collection!}
          variants={getVersionVariants.data?.firebaseVersionVariants?.collection!}
          filterForm={filterForm}
          onExpand={onExpandRow}
          viewPreset={getMetrics.data?.firebaseMetrics?.viewPreset!}
          slots={{
            beforeFilter: (
              <DataTableFilterSubmitButton
                onClick={() => {
                  reqs.current = {}
                  filterForm.syncValues('date', 'experiment', 'variantId', 'base')
                }}
                disabled={getMetrics.loading}
                loading={getMetrics.loading}
              />
            ),
            toolbar: () => (
              <QueryState query={getViewPresets}>
                <ViewPresetSelection
                  presets={getViewPresets.data?.viewPresets?.collection || []}
                  title={
                    <div className="flex items-center gap-2">
                      <Columns2 className="size-4" />
                      <span className="text-sm">Select View</span>
                    </div>
                  }
                  onDelete={() => {
                    getViewPresets.refetch()
                  }}
                  onSelect={(selectedPreset) => {
                    getMetrics.refetch({
                      ...getMetricsQueryVars,
                      preset: { viewPresetId: selectedPreset.id },
                    })
                  }}
                />
              </QueryState>
            ),
          }}
        />

        <ViewPresetEditor
          presets={getViewPresets.data?.viewPresets?.collection}
          viewPresetConfigMap={viewPresetConfigMapCollection?.find((p) => p.pageId === pageId)}
          onSave={() => {
            getViewPresets.refetch()
          }}
        />
      </QueryState>
    </BaseLayout>
  )
}
