import { InferQueryStateType } from '@/next/components/sdk/types'
import { parseAsArrayOf, parseAsString, useQueryState } from 'nuqs'
import { useMemo } from 'react'

export function useGroupFields() {
  return useQueryState(
    'group',
    parseAsArrayOf(parseAsString).withDefault(
      useMemo(() => ['variantId', 'countryCode', 'date'], [])
    )
  )
}

export type QueryValues = {
  groupFields: InferQueryStateType<typeof useGroupFields>
}
