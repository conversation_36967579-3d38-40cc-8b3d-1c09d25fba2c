import {
  NetworkAttributesFragment,
  MediationAttributesFragment,
  FirebaseExperimentAttributesFragment,
  FirebaseMetricAttributesFragment,
  FirebaseVersionVariantAttributesFragment,
  FirebaseExperimentIndex_GetMetricsQueryVariables,
  FilterOperator,
  FirebaseExperimentIndex_FirebaseMetricFragment,
  ViewPresetAttributesFragment,
  AdTypeCategory,
  FirebaseAdMetricAttributesFragment,
} from '@/graphql'
import { DataTableGroupList } from '@/next/components/data-table/data-table-group-list'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { useDataTable } from '@/next/hooks/use-data-table'
import { createColumnHelper, Table } from '@tanstack/react-table'
import { isNil, last, thru, uniq } from 'lodash-es'
import { useCallback, useMemo } from 'react'
import { useGroupFields } from './query_state'
import { format } from 'date-fns'
import { useServerProp } from '@/components/ssr'
import { useTranslation } from 'react-i18next'
import { ReactNode } from '@/next/components/sdk/types'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { sortBy } from 'lodash-es'
import {
  useTreeTableAggregation,
  useTreeTableAggregationState,
} from '@/next/hooks/use_tree_table_aggregation'
import { DataTableGroupHighlightingCell } from '@/next/components/sdk/data_table_group_highlighting'
import { sortVariants } from '@/next/utils/sort_utils'

const columnHelper = createColumnHelper<FirebaseExperimentIndex_FirebaseMetricFragment>()

const processMetricValue = (value: any, attributeName: string) => {
  if (attributeName === 'playtimeMsec' || attributeName === 'playtimeNthDayMsecs') {
    return value / 1000
  }
  return value
}
export function FirebaseExprimentsTable({
  experiments,
  mediations,
  networks,
  collection,
  variants,
  filterForm,
  onExpand,
  viewPreset,
  slots = {},
}: {
  networks: NetworkAttributesFragment[]
  mediations: MediationAttributesFragment[]
  experiments: FirebaseExperimentAttributesFragment[]
  collection: FirebaseExperimentIndex_FirebaseMetricFragment[]
  variants: FirebaseVersionVariantAttributesFragment[]
  filterForm: UseDataTableFilterFormReturn<any>
  onExpand: (vars: FirebaseExperimentIndex_GetMetricsQueryVariables) => any
  viewPreset: ViewPresetAttributesFragment
  slots?: {
    beforeFilter?: ReactNode
    toolbar?:
      | ReactNode
      | ((table: Table<FirebaseExperimentIndex_FirebaseMetricFragment>) => ReactNode)
  }
}) {
  const [groups] = useGroupFields()
  const filterFormValues = filterForm.watch()

  const gameId: string = useServerProp((s) => s.game.id)
  const pageId = useServerProp((s) => s.pageId)
  const { t } = useTranslation()

  const variantsOptions = useMemo(() => {
    return variants
      .filter((v) => v.name)
      .sort(sortVariants)
      .map((v) => ({
        value: v.id,
        label: t(`firebaseVersionVariants.name.${v.name}`, v.name),
      }))
  }, [variants, t])

  const columns = useMemo(() => {
    const dimensionColumns = [
      columnHelper.accessor(() => 'dimension', {
        id: 'dimension',
        header: 'Dimension',
        cell: ({ table, row }) => {
          const grouping = table.getState().grouping
          const variantId = last(grouping.map((g) => row.getValue(g) as string).filter(Boolean))
          const variant = variants.find((v) => v.id === variantId)
          return (
            <span>
              {variant
                ? t(`firebaseVersionVariants.name.${variant.name}`, variant.name)
                : variantId}
            </span>
          )
        },
        meta: {
          label: 'Dimension',
        },
      }),

      columnHelper.accessor('date', {
        id: 'date',
        enableColumnFilter: true,
        enableGrouping: true,
        enableHiding: false,
        meta: {
          label: 'Date',
          variant: 'dateRange',
        },
      }),

      columnHelper.accessor('countryCode', {
        id: 'countryCode',
        enableGrouping: true,
        enableHiding: false,
        meta: {
          label: 'Country',
        },
      }),

      columnHelper.accessor('variantId', {
        id: 'variantId',
        enableGrouping: true,
        enableHiding: false,
        enableColumnFilter: true,
        meta: {
          label: 'Variant',
          variant: 'multiSelect',
          options: variantsOptions,
        },
      }),

      columnHelper.accessor(() => '', {
        id: 'base',
        enableColumnFilter: true,
        enableHiding: false,
        meta: {
          label: 'Base',
          variant: 'select',
          options: variantsOptions,
        },
      }),

      columnHelper.accessor(() => 'experiment', {
        id: 'experiment',
        enableHiding: false,
        enableColumnFilter: true,
        meta: {
          label: 'Experiment',
          variant: 'select',
          options: experiments.map((e) => ({
            value: e.name,
            label: e.name,
          })),
        },
      }),

      columnHelper.accessor(() => 'network', {
        id: 'network',
        enableHiding: false,
        enableColumnFilter: true,
        meta: {
          label: 'Network',
          variant: 'select',
          options: networks.map((e) => ({
            value: e.id,
            label: e.name,
          })),
        },
      }),

      columnHelper.accessor(() => 'mediation', {
        id: 'mediation',
        enableHiding: false,
        enableColumnFilter: true,
        meta: {
          label: 'Mediation',
          variant: 'select',
          options: mediations.map((e) => ({
            value: e.id,
            label: e.name,
          })),
        },
      }),
    ]

    const metricColumns = [
      columnHelper.accessor('installCount', {
        id: 'installCount',
        header: 'Installs',
        enableSorting: true,
        cell: ({ cell, table, row }) => (
          <DataTableGroupHighlightingCell
            baselineComparison
            table={table}
            cell={cell}
            row={row}
            type="number"
          />
        ),
        meta: {
          label: 'Installs',
        },
      }),
      columnHelper.accessor('dailyActiveUserCount', {
        id: 'dailyActiveUserCount',
        header: 'DAU',
        enableSorting: true,
        cell: ({ cell, table, row }) => (
          <DataTableGroupHighlightingCell
            baselineComparison
            table={table}
            cell={cell}
            row={row}
            type="number"
          />
        ),
        meta: {
          label: 'DAU',
        },
      }),
      columnHelper.accessor('sessionCount', {
        id: 'sessionCount',
        header: 'Sessions',
        enableSorting: true,
        cell: ({ cell, table, row }) => (
          <DataTableGroupHighlightingCell
            baselineComparison
            table={table}
            cell={cell}
            row={row}
            type="number"
          />
        ),
        meta: {
          label: 'Sessions',
        },
      }),
      columnHelper.accessor('retentionRate', {
        id: 'retentionRate',
        header: 'RR',
        enableSorting: true,
        cell: ({ cell, table, row }) => (
          <DataTableGroupHighlightingCell
            baselineComparison
            table={table}
            cell={cell}
            row={row}
            type="percentage"
          />
        ),
        meta: {
          label: 'RR',
        },
      }),
      columnHelper.accessor('sessionCountPerActiveUser', {
        id: 'sessionCountPerActiveUser',
        header: 'Sessions / DAU',
        enableSorting: true,
        cell: ({ cell, table, row }) => (
          <DataTableGroupHighlightingCell
            baselineComparison
            table={table}
            cell={cell}
            row={row}
            type="number"
          />
        ),
        meta: {
          label: 'Sessions/DAU',
        },
      }),
      columnHelper.accessor((m) => m.playtimeMsec / 1000, {
        id: 'playtimeMsec',
        header: 'Playtime (s)',
        enableSorting: true,
        cell: ({ cell, table, row }) => (
          <DataTableGroupHighlightingCell
            baselineComparison
            table={table}
            cell={cell}
            row={row}
            type="duration"
          />
        ),
        meta: {
          label: 'Playtime (s)',
        },
      }),
    ].filter((col) => {
      return viewPreset.attributes.find((e) => !e.isCohort && e.name === col.id)
    })
    const cohortColumns = viewPreset.attributes
      .filter((e) => e.isCohort)
      .flatMap((e) => {
        return e.cohortDays.map((day) => {
          const accessorFn = (row: FirebaseExperimentIndex_FirebaseMetricFragment) => {
            const cohortData = (row as any)[e.name]
            if (typeof cohortData === 'string') {
              const parsedData = JSON.parse(cohortData)
              return parsedData[`D${day}`] ?? 0
            }
            if (cohortData && typeof cohortData === 'object') {
              return cohortData[`D${day}`] ?? cohortData[`${day}`] ?? cohortData[day] ?? 0
            }
            return 0
          }

          const label = t(`firebaseMetric.${e.name}`, e.name, { day })
          return columnHelper.accessor((row) => processMetricValue(accessorFn(row), e.name), {
            id: `${e.name}_D${day}`,
            enableSorting: true,
            header: () => label,
            cell: ({ cell, table, row }) => {
              return (
                <DataTableGroupHighlightingCell
                  baselineComparison
                  table={table}
                  cell={cell}
                  row={row}
                />
              )
            },
            enableHiding: true,
            meta: {
              label: label,
            },
          })
        })
      })

    const adColumnNames = Object.values(AdTypeCategory)
      .flatMap((adType) => {
        const findAdMetric =
          (attr: keyof FirebaseAdMetricAttributesFragment) =>
          (m: FirebaseExperimentIndex_FirebaseMetricFragment) => {
            const adMetric = m.ads?.find((ad) => ad.adTypeCategory === adType)
            return adMetric?.[attr] ?? 0
          }

        return [
          columnHelper.accessor(findAdMetric('adRevGrossAmount'), {
            id: `${adType}_ad_adRevGrossAmount`,
            enableSorting: true,
            header: t(`firebaseAdMetric.adRevGrossAmount`, {
              category: t(`firebaseAdMetric.category.${adType}`),
            }),
            cell: ({ cell, table, row }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="currency"
              />
            ),
            meta: {
              label: t(`firebaseAdMetric.adRevGrossAmount`, {
                category: t(`firebaseAdMetric.category.${adType}`),
              }),
            },
          }),

          columnHelper.accessor(findAdMetric('impressionCount'), {
            id: `${adType}_ad_impressionCount`,
            enableSorting: true,
            header: t(`firebaseAdMetric.impressionCount`, {
              category: t(`firebaseAdMetric.category.${adType}`),
            }),
            cell: ({ cell, table, row }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="number"
              />
            ),
            meta: {
              label: t(`firebaseAdMetric.impressionCount`, {
                category: t(`firebaseAdMetric.category.${adType}`),
              }),
            },
          }),

          columnHelper.accessor(findAdMetric('impressionCountPerActiveUser'), {
            id: `${adType}_ad_impressionCountPerActiveUser`,
            enableSorting: true,
            header: t(`firebaseAdMetric.impressionCountPerActiveUser`, {
              category: t(`firebaseAdMetric.category.${adType}`),
            }),
            cell: ({ cell, table, row }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="number"
              />
            ),
            meta: {
              label: t(`firebaseAdMetric.impressionCountPerActiveUser`, {
                category: t(`firebaseAdMetric.category.${adType}`),
              }),
            },
          }),

          columnHelper.accessor(findAdMetric('adRevGrossAmountPerActiveUser'), {
            id: `${adType}_ad_adRevGrossAmountPerActiveUser`,
            enableSorting: true,
            header: t(`firebaseAdMetric.adRevGrossAmountPerActiveUser`, {
              category: t(`firebaseAdMetric.category.${adType}`),
            }),
            cell: ({ cell, table, row }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="number"
              />
            ),
            meta: {
              label: t(`firebaseAdMetric.adRevGrossAmountPerActiveUser`, {
                category: t(`firebaseAdMetric.category.${adType}`),
              }),
            },
          }),
        ]
      })
      .filter((col) => {
        return viewPreset.attributes.find((e) => !e.isCohort && col.id!.endsWith(e.name))
      })
    const arrangedColumns = sortBy([...metricColumns, ...cohortColumns], (col: any) => {
      const baseName = col.id!.includes('_D') ? col.id!.split('_D')[0] : col.id!
      return viewPreset.attributes.findIndex((e) => e.name === baseName)
    })

    return [...dimensionColumns, ...arrangedColumns, ...adColumnNames]
  }, [experiments, networks, mediations, variants, viewPreset, t, variantsOptions])

  const aggregationColumns = useMemo(() => {
    const metricColumns = [
      { id: 'installCount' },
      { id: 'dailyActiveUserCount' },
      { id: 'sessionCount' },
      { id: 'retentionRate' },
      { id: 'sessionCountPerActiveUser' },
      { id: 'playtimeMsec' },
    ].filter((col) => {
      return viewPreset.attributes.find((e) => !e.isCohort && e.name === col.id)
    })
    const cohortColumns = viewPreset.attributes
      .filter((e) => e.isCohort)
      .flatMap((e) => {
        return e.cohortDays.map((day) => {
          return { id: `${e.name}_D${day}` }
        })
      })

    const adColumnNames = Object.values(AdTypeCategory)
      .flatMap((adType) => {
        return [
          { id: `${adType}_ad_adRevGrossAmount` },
          { id: `${adType}_ad_impressionCount` },
          { id: `${adType}_ad_impressionCountPerActiveUser` },
          { id: `${adType}_ad_adRevGrossAmountPerActiveUser` },
        ]
      })
      .filter((col) => {
        return viewPreset.attributes.find((e) => !e.isCohort && col.id!.endsWith(e.name))
      })

    return [...metricColumns, ...cohortColumns, ...adColumnNames]
  }, [viewPreset])

  const baseVariant = filterForm.watch('base')

  const { aggregations, getGroupAggregation, getBaselineAggregation } =
    useTreeTableAggregationState()

  const { table } = useDataTable({
    columns,
    data: collection,
    pageCount: -1,
    getRowId: (row) => [row.variantId, row.countryCode, row.date].join('__'),
    initialState: {
      grouping: groups,
      columnPinning: {
        left: ['dimension'],
      },
      columnVisibility: {
        experiment: false,
        network: false,
        mediation: false,
        countryCode: false,
        variantId: false,
        date: false,
        base: false,
      },
    },
    form: filterForm,
    meta: {
      aggregations,
      getGroupAggregation,
      aggregationColumns,
      baselineColumn: 'variantId',
      baselineValue: baseVariant,
      getBaselineAggregation,
      pageId: `${pageId}:v2`,
    },
  })
  useTreeTableAggregation(table)

  const onExpandRow = useCallback(
    (m: FirebaseMetricAttributesFragment) => {
      const groupValues = Object.fromEntries(groups.map((g) => [g, m[g as keyof typeof m]]))
      return onExpand({
        where: {
          date: thru(groupValues.date, (val: Date | undefined) =>
            isNil(val)
              ? {
                  operator: FilterOperator.Between,
                  values: [
                    format(filterFormValues.date[0], 'yyyy-MM-dd'),
                    format(filterFormValues.date[1], 'yyyy-MM-dd'),
                  ],
                }
              : {
                  operator: FilterOperator.Between,
                  values: [format(val!, 'yyyy-MM-dd'), format(val!, 'yyyy-MM-dd')],
                }
          ),
          countryCode: thru(groupValues.countryCode, (val: string | undefined) =>
            isNil(val)
              ? filterFormValues.countryCode
              : {
                  operator: FilterOperator.In,
                  values: [val],
                }
          ),
          variantId: thru(groupValues.variantId, (val: string | undefined) =>
            isNil(val)
              ? filterFormValues.variantId
              : {
                  operator: FilterOperator.In,
                  values: uniq([val, baseVariant].filter(Boolean)),
                }
          ),
          experiment: {
            operator: FilterOperator.Eq,
            values: [filterFormValues.experiment],
          },
          gameId,
        },
        group: {
          fields: groups.slice(0, groups.findIndex((g) => isNil(groupValues[g])) + 1),
        },
        preset: {
          viewPresetId: viewPreset.id < 0 ? undefined : viewPreset.id,
        },
      })
    },
    [groups, onExpand, filterFormValues, gameId, baseVariant]
  )

  return (
    <TreeTable
      table={table}
      getNodeKeys={(m) => groups.map((g) => m[g as keyof typeof m]).filter((val) => !isNil(val))}
      getNodeId={(keys) => keys.join('|')}
      getRowCanExpand={(m) => groups.map((g) => m[g as keyof typeof m]).some((v) => isNil(v))}
      expandColumn="dimension"
      onExpandRow={onExpandRow}
      sticky
    >
      <DataTableToolbar table={table} slots={{ beforeFilter: slots.beforeFilter }}>
        {slots.toolbar?.(table)}
        <DataTableGroupList table={table} />
      </DataTableToolbar>
    </TreeTable>
  )
}
