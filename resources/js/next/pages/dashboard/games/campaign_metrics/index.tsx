import CampaignMetricsController from '#controllers/dashboard/game/campaign_metrics_controller'
import { routes } from '@/components/location'
import { graphql } from '@/gql'
import { Filter, FilterOperator, GamesCampaignMetricsIndex_GetDataQueryVariables } from '@/graphql'
import { useQuery } from '@/next/components/sdk/graphql'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { PageConfig } from '@/next/components/sdk/page_config'
import { InferPageProps } from '@adonisjs/inertia/types'
import { Columns2 } from 'lucide-react'
import { useEffect, useMemo, useRef } from 'react'
import { parseAsArrayOf, parseAsString, useQueryState } from 'nuqs'

import { CampaignMetricsTable } from './index/table'
import {
  ViewPresetEditor,
  ViewPresetSelection,
} from '@/next/components/domain/view_preset_selection'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { format, subDays } from 'date-fns'
import { useServerProp } from '@/components/ssr'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'
import { DataTableFilterSubmitButton } from '@/next/components/sdk/data_table'

type PageProps = InferPageProps<CampaignMetricsController, 'index'>

const getViewPresetsQuery = graphql(`
  query GamesCampaignMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {
    viewPresets(where: $where) {
      collection {
        ...ViewPresetAttributes
      }
    }
  }
`)

const getMetricsQuery = graphql(`
  query GamesCampaignMetricsIndex_GetData(
    $where: AdMetricsWhere!
    $group: Group!
    $preset: Preset
  ) {
    adMetrics(where: $where, group: $group, preset: $preset) {
      collection {
        ...AdMetricAttributes
      }
      agencies {
        ...AdAgencyAttributes
      }
      ads {
        ...AdAttributes
      }
      adGroups {
        ...AdGroupAttributes
      }
      campaigns {
        ...CampaignAttributes
      }
      viewPreset {
        ...ViewPresetAttributes
      }
    }
  }
`)

// const convertFilterToGraphQLFilter = (filter: any): Filter | null => {
//   const operatorMap: Record<string, FilterOperator> = {
//     eq: FilterOperator.Eq,
//     ne: FilterOperator.Ne,
//     gt: FilterOperator.Gt,
//     gte: FilterOperator.Gte,
//     lt: FilterOperator.Lt,
//     lte: FilterOperator.Lte,
//     isbetween: FilterOperator.Between,
//   }

//   if (filter.operator.toLowerCase() === 'isbetween') {
//     const values = [filter.value].flat()
//     if (
//       values.length === 2 &&
//       values.every((v: any) => v !== '' && v !== undefined && v !== null)
//     ) {
//       return {
//         operator: FilterOperator.Between,
//         values: values,
//       }
//     }
//     return null
//   }

//   return {
//     operator: operatorMap[filter.operator.toLowerCase()] || FilterOperator.Eq,
//     values: Array.isArray(filter.value) ? filter.value : [filter.value],
//   }
// }

export default function Page({ game: { name: gameName, id: gameId } }: Readonly<PageProps>) {
  const reqs = useRef<Record<string, boolean>>({})

  const [group] = useQueryState('group', parseAsArrayOf(parseAsString).withDefault(['agencyId']))
  const [preset] = useQueryState('preset', parseAsString)
  // const [filters] = useQueryState('filters', getFiltersStateParser().withDefault([]))
  const pageId = useServerProp((s) => s.pageId)
  const sidebarSubItems = useMemo(() => makeGameSidebarSubItems(gameId), [gameId])

  const filterForm = useDataTableFilterForm({
    date: {
      variant: 'dateRange',
      defaultValue: [subDays(new Date(), 7), new Date()],
    },
  })

  const filterFormValues = filterForm.watch()

  // const processedFilters = useMemo(() => {
  //   const result: Record<string, Filter> = {}

  //   filters.forEach((filter) => {
  //     if (filter.value && filter.id) {
  //       const graphqlFilter = convertFilterToGraphQLFilter(filter)
  //       if (graphqlFilter) {
  //         result[filter.id] = graphqlFilter
  //       }
  //     }
  //   })

  //   return result
  // }, [JSON.stringify(filters)])

  // const apolloClient = useApolloClient()

  const queryVariables = useMemo(
    () => ({
      where: {
        gameId,
        date: {
          operator: FilterOperator.Between,
          values: [
            format(filterFormValues.date[0] || subDays(new Date(), 7), 'yyyy-MM-dd'),
            format(filterFormValues.date[1] || new Date(), 'yyyy-MM-dd'),
          ],
        },
      },
      group: {
        fields: group.slice(0, 1),
      },
      preset: preset ? { viewPresetId: parseInt(preset) } : undefined,
    }),
    [gameId, filterFormValues, group, preset]
  )

  useEffect(() => {}, [group])

  const getMetrics = useQuery(getMetricsQuery, queryVariables)

  const getViewPresets = useQuery(getViewPresetsQuery, { where: { pageId } })

  const [[viewPresetConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.viewpreset'], []))

  const onRowExpand = (vars: GamesCampaignMetricsIndex_GetDataQueryVariables) => {
    const reqId = vars.group.fields
      .flatMap((m) => (vars.where as unknown as Record<string, Filter>)[m]?.values || [])
      .join('__')

    if (reqs.current[reqId]) {
      return
    }

    return getMetrics
      .fetchMore({
        variables: vars,
        updateQuery: (
          { adMetrics: existing, ...prev },
          { fetchMoreResult: { adMetrics: incoming } }
        ) => {
          return {
            ...prev,
            adMetrics: {
              ...incoming,
              collection: [...existing.collection, ...incoming.collection],
              agencies: [...existing.agencies, ...incoming.agencies],
              ads: [...existing.ads, ...incoming.ads],
              adGroups: [...existing.adGroups, ...incoming.adGroups],
              campaigns: [...existing.campaigns, ...incoming.campaigns],
            },
          }
        },
      })
      .then(() => {
        reqs.current[reqId] = true
      })
  }

  return (
    <BaseLayout>
      <PageConfig
        title={gameName}
        breadcrumbs={[
          {
            title: 'All Games',
            url: routes.dash.games.index(),
          },
          {
            title: gameName,
            url: routes.dash.games.campaignMetrics(gameId),
          },
          'Campaign Performance',
        ]}
        sidebarSubItems={sidebarSubItems}
      />

      <QueryState query={getMetrics}>
        <CampaignMetricsTable
          data={getMetrics.data?.adMetrics!}
          onRowExpand={onRowExpand}
          filterForm={filterForm}
          queryVariables={queryVariables}
          slots={{
            beforeFilter: (
              <DataTableFilterSubmitButton
                onClick={() => {
                  reqs.current = {}
                  filterForm.syncValues('date')
                }}
              />
            ),
            toolbar: () => (
              <QueryState query={getViewPresets}>
                <ViewPresetSelection
                  presets={getViewPresets.data?.viewPresets?.collection || []}
                  title={
                    <div className="flex items-center gap-2">
                      <Columns2 className="size-4" />
                      <span className="text-sm">Select View</span>
                    </div>
                  }
                  onDelete={() => {
                    getViewPresets.refetch()
                  }}
                  onSelect={(selectedPreset) => {
                    getMetrics.refetch({
                      ...queryVariables,
                      preset: { viewPresetId: selectedPreset.id },
                    })
                  }}
                />
              </QueryState>
            ),
          }}
        />
      </QueryState>

      <ViewPresetEditor
        presets={getViewPresets.data?.viewPresets?.collection}
        viewPresetConfigMap={viewPresetConfigMapCollection?.find((p) => p.pageId === pageId)}
        onSave={() => {
          getViewPresets.refetch()
        }}
      />
    </BaseLayout>
  )
}
