import {
  AdMetricAttributes<PERSON>ragment,
  FilterOperator,
  GamesCampaignMetricsIndex_GetDataQuery,
  GamesCampaignMetricsIndex_GetDataQueryVariables,
} from '@/graphql'
import { createColumnHelper, Table } from '@tanstack/react-table'
import { useCallback, useMemo } from 'react'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { isNil, last, thru } from 'lodash-es'
import { DataTableGroupList } from '@/next/components/data-table/data-table-group-list'
import { useDataTable } from '@/next/hooks/use-data-table'
import { useServerProp } from '@/components/ssr'
import { format } from 'date-fns'
import { ReactNode } from '@/next/components/sdk/types'
import { useTranslation } from 'react-i18next'
import { tableHeader } from '@/next/components/sdk/data_table'
import { parseAsArrayOf, parseAsJson, useQueryState } from 'nuqs'
import z from 'zod'
import { sortCollectionWithState } from '@/next/utils/sort_utils'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import {
  AggregationColumnDef,
  useTreeTableAggregation,
  useTreeTableAggregationState,
} from '@/next/hooks/use_tree_table_aggregation'
import { DataTableGroupHighlightingCell } from '@/next/components/sdk/data_table_group_highlighting'

export const getAttributeType = (attributeName: string) => {
  if (
    attributeName === 'adRevGrossAmount' ||
    attributeName === 'adCostNonTaxAmount' ||
    attributeName === 'adRevNthDayGrossAmounts'
  ) {
    return 'currency'
  } else if (
    attributeName === 'roas' ||
    attributeName === 'ctr' ||
    attributeName === 'cvr' ||
    attributeName === 'retentionRate' ||
    attributeName === 'roasNthDayRates' ||
    attributeName === 'retentionNthDayRates'
  ) {
    return 'percentage'
  }

  return 'number'
}

const columnHelper = createColumnHelper<AdMetricAttributesFragment>()

const schema = z.object({
  id: z.string(),
  desc: z.boolean(),
})

export function CampaignMetricsTable({
  data: { collection, agencies, campaigns, ads, adGroups, viewPreset },
  queryVariables,
  slots,
  filterForm,
  ...props
}: {
  data: GamesCampaignMetricsIndex_GetDataQuery['adMetrics']
  onRowExpand: (vars: GamesCampaignMetricsIndex_GetDataQueryVariables) => any
  filterForm: UseDataTableFilterFormReturn<any>
  queryVariables: GamesCampaignMetricsIndex_GetDataQueryVariables
  slots: {
    toolbar: ReactNode | ((table: Table<AdMetricAttributesFragment>) => ReactNode)
    beforeFilter: ReactNode
  }
}) {
  const { t } = useTranslation()
  const gameId = useServerProp((s) => s.game.id)
  const pageId = useServerProp((s) => s.pageId)
  const [sort] = useQueryState('sort', parseAsArrayOf(parseAsJson(schema.parse)).withDefault([]))

  const columns = useMemo(() => {
    const baseColumns: any[] = [
      columnHelper.accessor((m) => agencies.find((a) => a.id === m.agencyId)?.name, {
        id: 'agencyId',
        enableHiding: false,
        enableGrouping: true,
        header: () => 'Agency',
        meta: {
          label: 'Agency',
          variant: 'text',
        },
      }),
      columnHelper.accessor((m) => campaigns.find((c) => c.id === m.campaignId)?.name, {
        id: 'campaignId',
        enableHiding: false,
        enableGrouping: true,
        header: () => 'Campaign',
        meta: {
          label: 'Campaign',
          variant: 'text',
        },
      }),
      columnHelper.accessor((m) => adGroups.find((g) => g.id === m.groupId)?.name, {
        id: 'groupId',
        enableHiding: false,
        enableGrouping: true,
        header: () => 'Ad Group',
        meta: {
          label: 'Ad Group',
          variant: 'text',
        },
      }),
      columnHelper.accessor((m) => ads.find((a) => a.id === m.adId)?.name, {
        id: 'adId',
        enableHiding: false,
        enableGrouping: true,
        header: () => 'Ad',
        meta: {
          label: 'Ad',
          variant: 'text',
        },
      }),
      columnHelper.accessor('countryCode', {
        enableHiding: false,
        enableGrouping: true,
        header: () => 'Country',
        meta: {
          label: 'Country',
          variant: 'text',
        },
      }),
      columnHelper.accessor(() => 'Dimension', {
        id: 'dimension',
        header: () => 'Dimension',
        cell: ({ row, table }) => {
          const grouping = table.getState().grouping
          return <span>{last(grouping.map((g) => row.getValue(g) as string).filter(Boolean))}</span>
        },
        meta: {
          label: 'Dimension',
        },
      }),
    ]

    const metricColumns = viewPreset?.attributes?.flatMap((attr): any => {
      if (!attr.isCohort) {
        const translatedName = t(`campaignMetrics.${attr.name}`, attr.name)

        return [
          columnHelper.accessor((row) => row[attr.name as keyof AdMetricAttributesFragment], {
            id: attr.name,
            header: tableHeader(translatedName),

            cell: ({ cell, table, row }) => {
              return <DataTableGroupHighlightingCell table={table} cell={cell} row={row} />
            },
            enableSorting: true,
            meta: {
              label: translatedName,
              variant: attr.name.includes('rate') || attr.name === 'roas' ? 'range' : 'number',
            },
          }),
        ]
      } else {
        return attr.cohortDays.map((day) => {
          const accessorFn = (row: AdMetricAttributesFragment) => {
            const cohortData = (row as any)[attr.name]
            if (typeof cohortData === 'string') {
              const parsedData = JSON.parse(cohortData)
              return parsedData[`D${day}`] ?? 0
            }
            if (cohortData && typeof cohortData === 'object') {
              return cohortData[`D${day}`] ?? cohortData[`${day}`] ?? cohortData[day] ?? 0
            }

            return 0
          }

          const label = t(`campaignMetrics.${attr.name}`, attr.name, { day: day })

          return columnHelper.accessor(accessorFn, {
            id: `${attr.name}_D${day}`,
            header: () => label,
            cell: ({ cell, table, row }) => {
              return (
                <DataTableGroupHighlightingCell
                  table={table}
                  cell={cell}
                  row={row}
                  type={getAttributeType(attr.name)}
                />
              )
            },
            enableSorting: true,
            meta: {
              label: label,
            },
          })
        })
      }
    })

    const dateColumn = columnHelper.accessor((m) => m.date && format(m.date, 'yyyy-MM-dd'), {
      id: 'date',
      enableHiding: false,
      enableGrouping: true,
      header: () => 'Date',
      meta: {
        label: 'Date',
      },
    })

    return [...baseColumns, ...metricColumns, dateColumn]
  }, [campaigns, agencies, viewPreset, t])

  const aggregationColumns = useMemo(() => {
    return viewPreset?.attributes?.flatMap((attr): AggregationColumnDef[] => {
      if (!attr.isCohort) {
        return [
          {
            id: attr.name,
          },
        ]
      } else {
        return attr.cohortDays.map((day) => {
          return {
            id: `${attr.name}_D${day}`,
          }
        })
      }
    })
  }, [viewPreset])

  const rows = useMemo(() => {
    return sortCollectionWithState(collection, sort)
  }, [collection, sort])

  const { aggregations, getGroupAggregation } = useTreeTableAggregationState()

  const { table } = useDataTable({
    data: rows,
    columns,
    pageCount: -1,
    getRowId: (row) =>
      [row.agencyId, row.campaignId, row.date, row.adId, row.groupId, row.countryCode].join('__'),
    initialState: {
      grouping: ['agencyId', 'campaignId', 'date', 'countryCode'],
      columnVisibility: {
        agencyId: false,
        campaignId: false,
        groupId: false,
        adId: false,
        date: false,
        countryCode: false,
      },
      columnPinning: {
        left: ['dimension'],
      },
    },
    form: filterForm,
    meta: {
      aggregationColumns,
      aggregations,
      getGroupAggregation,
      pageId,
    },
  })

  useTreeTableAggregation(table)

  const grouping = table.getState().grouping

  const onRowExpand = useCallback(
    (m: AdMetricAttributesFragment) => {
      const groupValues = Object.fromEntries(grouping.map((g) => [g, m[g as keyof typeof m]]))

      const vars: GamesCampaignMetricsIndex_GetDataQueryVariables = {
        where: {
          gameId,
          agencyId: thru(groupValues.agencyId, (val: number | undefined) =>
            isNil(val)
              ? undefined
              : {
                  operator: FilterOperator.Eq,
                  values: [val],
                }
          ),
          campaignId: thru(groupValues.campaignId, (val: number | undefined) =>
            isNil(val)
              ? undefined
              : {
                  operator: FilterOperator.Eq,
                  values: [val],
                }
          ),
          adId: thru(groupValues.adId, (val: number | undefined) =>
            isNil(val)
              ? undefined
              : {
                  operator: FilterOperator.Eq,
                  values: [val],
                }
          ),
          groupId: thru(groupValues.groupId, (val: number | undefined) =>
            isNil(val)
              ? undefined
              : {
                  operator: FilterOperator.Eq,
                  values: [val],
                }
          ),
          date: thru(groupValues.date, (val: Date | undefined) =>
            isNil(val)
              ? queryVariables.where.date
              : {
                  operator: FilterOperator.Between,
                  values: [format(val!, 'yyyy-MM-dd'), format(val!, 'yyyy-MM-dd')],
                }
          ),
        },
        group: {
          fields: grouping.slice(0, grouping.findIndex((g) => isNil(groupValues[g])) + 1),
        },
        preset: viewPreset.id > 0 ? { viewPresetId: viewPreset.id } : undefined,
      }

      return props.onRowExpand(vars)
    },
    [props.onRowExpand, grouping]
  )

  return (
    <TreeTable
      table={table}
      onExpandRow={onRowExpand}
      getNodeKeys={(m) => grouping.map((g) => m[g as keyof typeof m]).filter((val) => !isNil(val))}
      getNodeId={(keys) => keys.join('|')}
      getRowCanExpand={(m) => grouping.map((g) => m[g as keyof typeof m]).some((v) => isNil(v))}
      indentSize={24}
      expandColumn="dimension"
      sticky
    >
      <DataTableToolbar table={table} slots={{ beforeFilter: slots.beforeFilter }}>
        {slots.toolbar?.(table)}
        {/* <DataTableFilterList table={table} /> */}
        <DataTableGroupList table={table} />
      </DataTableToolbar>
    </TreeTable>
  )
}
