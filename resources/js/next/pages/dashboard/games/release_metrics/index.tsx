import ReleaseMetricsController from '#controllers/dashboard/game/release_metrics_controller'
import { graphql } from '@/gql'
import { makeGameBreadcrumbs } from '@/next/components/domain/game_breadcrumbs'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { useQuery } from '@/next/components/sdk/graphql'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { ReleaseMetricTable } from '@/next/pages/dashboard/games/release_metrics/index/table'
import { InferPageProps } from '@adonisjs/inertia/types'
import { parseAsArrayOf, parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useMemo, useRef } from 'react'
import {
  Filter,
  FilterOperator,
  ReleaseMetricIndex_GetDataQueryVariables,
  ViewPresetAttributesFragment,
} from '@/graphql'
import { format, subDays } from 'date-fns'
import { DataTableFilterSubmitButton } from '@/next/components/sdk/data_table'
import { useForm } from 'react-hook-form'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'
import { uniq, uniqBy } from 'lodash-es'

graphql(`
  fragment ReleaseMetricIndex_ReleaseMetricAttributes on ReleaseMetric {
    ...ReleaseMetricAttributes

    ads {
      ...ReleaseAdMetricAttributes
    }
  }
`)

const getMetricsQuery = graphql(`
  query ReleaseMetricIndex_GetData($where: ReleaseMetricsWhere!, $group: Group, $preset: Preset) {
    releaseMetrics(where: $where, group: $group, preset: $preset) {
      collection {
        ...ReleaseMetricIndex_ReleaseMetricAttributes
      }
      viewPreset {
        ...ViewPresetAttributes
      }
    }
  }
`)

const getVersionsQuery = graphql(`
  query ReleaseMetricIndex_GetVersion($where: ReleaseVersionsWhere!) {
    releaseVersions(where: $where) {
      collection {
        version
      }
    }
  }
`)

export default function Page({ game }: InferPageProps<ReleaseMetricsController, 'index'>) {
  const getVersions = useQuery(getVersionsQuery, { where: { gameId: game.id } })

  const [group] = useQueryState(
    'group',
    parseAsArrayOf(parseAsString).withDefault(useMemo(() => ['version', 'countryCode', 'date'], []))
  )

  const [preset, setPreset] = useQueryState('preset', parseAsInteger)

  const groupForm = useForm({
    defaultValues: useMemo(() => ({ group }), []),
  })

  const filterForm = useDataTableFilterForm({
    date: {
      variant: 'dateRange',
      defaultValue: [subDays(new Date(), 7), new Date()],
    },
    version: {
      variant: 'multiSelect',
      defaultValue: [],
    },
    base: {
      variant: 'select',
      defaultValue: '',
    },
  })

  const { immediateValues } = filterForm

  const filterFormValues = filterForm.watch()
  const groupFormValues = groupForm.watch()

  const queryVariables = useMemo(
    () => ({
      where: {
        gameId: game.id,
        date: {
          operator: FilterOperator.Between,
          values: [
            format(filterFormValues.date[0], 'yyyy-MM-dd'),
            format(filterFormValues.date[1], 'yyyy-MM-dd'),
          ],
        },
        version: {
          operator: FilterOperator.In,
          values: filterFormValues.version?.length
            ? uniq([...filterFormValues.version, filterFormValues.base].filter(Boolean))
            : filterFormValues.version,
        },
      },
      group: {
        fields: groupFormValues.group.slice(0, 1),
      },
      preset: preset ? { viewPresetId: preset } : undefined,
    }),
    [filterFormValues, groupFormValues, preset]
  )

  const getMetrics = useQuery(getMetricsQuery, queryVariables)

  const onFilterSubmit = () => {
    reqs.current = {}
    filterForm.setValue('date', immediateValues.date)
    filterForm.setValue('version', immediateValues.version)
    filterForm.setValue('base', immediateValues.base)
    groupForm.setValue('group', group)
  }

  const onPresetSelect = (preset: ViewPresetAttributesFragment) => {
    reqs.current = {}
    setPreset(preset.id)
  }

  const reqs = useRef<Record<string, boolean>>({})

  const onRowExpand = (vars: ReleaseMetricIndex_GetDataQueryVariables) => {
    const reqId = vars
      .group!.fields.flatMap(
        (m) => (vars.where as unknown as Record<string, Filter>)[m]?.values || []
      )
      .join('__')

    if (reqs.current[reqId]) {
      return
    }

    return getMetrics
      .fetchMore({
        variables: vars,
        updateQuery: (
          { releaseMetrics: existing, ...prev },
          { fetchMoreResult: { releaseMetrics: incoming } }
        ) => {
          return {
            ...prev,
            releaseMetrics: {
              ...incoming,
              collection: uniqBy([...existing.collection, ...incoming.collection], (e) =>
                vars.group!.fields.map((f) => e[f as keyof typeof e])
              ),
            },
          }
        },
      })
      .then(() => {
        reqs.current[reqId] = true
      })
  }

  return (
    <BaseLayout>
      <PageConfig
        title="Release Intel"
        breadcrumbs={makeGameBreadcrumbs(game, 'Release Intel')}
        sidebarSubItems={makeGameSidebarSubItems(game.id)}
      />

      <QueryState query={getMetrics}>
        <ReleaseMetricTable
          collection={getMetrics.data?.releaseMetrics?.collection!}
          viewPreset={getMetrics.data?.releaseMetrics?.viewPreset!}
          onRowExpand={onRowExpand}
          queryVariables={queryVariables}
          versions={getVersions.data?.releaseVersions?.collection!}
          onPresetSelect={onPresetSelect}
          filterForm={filterForm}
          slots={{
            beforeFilter: (
              <DataTableFilterSubmitButton
                onClick={onFilterSubmit}
                loading={getMetrics.loading}
                disabled={getMetrics.loading}
              />
            ),
          }}
        />
      </QueryState>
    </BaseLayout>
  )
}
