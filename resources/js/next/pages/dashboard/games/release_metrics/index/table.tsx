import { createColumnHelper } from '@tanstack/react-table'
import { ComponentProps, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { isNil, last, orderBy, thru, uniq } from 'lodash-es'
import { graphql } from '@/gql'
import {
  AdTypeCategory,
  FilterOperator,
  ReleaseAdMetricAttributesFragment,
  ReleaseMetricAttributesFragment,
  ReleaseMetricIndex_GetDataQueryVariables,
  ReleaseMetricIndex_ReleaseMetricAttributesFragment,
  ViewPresetAttributesFragment,
} from '@/graphql'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { useDataTable } from '@/next/hooks/use-data-table'
import { DataTableGroupList } from '@/next/components/data-table/data-table-group-list'
import {
  ViewPresetEditor,
  ViewPresetSelection,
} from '@/next/components/domain/view_preset_selection'
import { Columns2 } from 'lucide-react'
import { useServerProp } from '@/components/ssr'
import { format } from 'date-fns'

import { semverComparator } from '#utils/semver'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useQuery } from '@/next/components/sdk/graphql'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import z from 'zod'
import { parseAsArrayOf, parseAsJson, useQueryState } from 'nuqs'
import { tableHeader } from '@/next/components/sdk/data_table'
import { sortCollectionWithState } from '@/next/utils/sort_utils'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { DataTableGroupHighlightingCell } from '@/next/components/sdk/data_table_group_highlighting'
import {
  useTreeTableAggregation,
  useTreeTableAggregationState,
} from '@/next/hooks/use_tree_table_aggregation'

const columnHelper = createColumnHelper<ReleaseMetricIndex_ReleaseMetricAttributesFragment>()

const getViewPresetsQuery = graphql(`
  query GamesReleaseMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {
    viewPresets(where: $where) {
      collection {
        ...ViewPresetAttributes
      }
    }
  }
`)

export const getAttributeType = (attributeName: string) => {
  if (
    attributeName === 'adRevGrossAmount' ||
    attributeName === 'adCostNonTaxAmount' ||
    attributeName === 'adRevNthDayGrossAmounts'
  ) {
    return 'currency'
  } else if (attributeName === 'retentionRate' || attributeName === 'retentionNthDayRates') {
    return 'percentage'
  } else if (attributeName === 'playtimeMsec' || attributeName === 'playtimeNthDayMsecs') {
    return 'duration'
  }

  return 'number'
}

export const getDecimalsForAttribute = (attributeName: string) => {
  if (attributeName === 'activeUserNthDayCounts' || attributeName === 'dailyActiveUserCount') {
    return 0
  } else if (
    attributeName === 'sessionCountPerActiveUser' ||
    attributeName === 'adRevGrossAmountPerActiveUser' ||
    attributeName === 'impressionCountPerActiveUser'
  ) {
    return 3
  }
  return undefined
}

const processMetricValue = (value: any, attributeName: string) => {
  if (attributeName === 'playtimeMsec' || attributeName === 'playtimeNthDayMsecs') {
    return value / 1000
  }
  return value
}

const schema = z.object({
  id: z.string(),
  desc: z.boolean(),
})

export function ReleaseMetricTable({
  collection,
  viewPreset,
  versions,
  slots,
  queryVariables,
  onPresetSelect,
  filterForm,
  ...props
}: {
  collection: ReleaseMetricIndex_ReleaseMetricAttributesFragment[]
  queryVariables: ReleaseMetricIndex_GetDataQueryVariables
  versions: any[]
  viewPreset: ViewPresetAttributesFragment
  slots: ComponentProps<typeof DataTableToolbar>['slots']
  onPresetSelect: (preset: ViewPresetAttributesFragment) => void
  filterForm: UseDataTableFilterFormReturn<any>
  onRowExpand: (vars: ReleaseMetricIndex_GetDataQueryVariables) => any
}) {
  const { t } = useTranslation()
  const gameId = useServerProp((s) => s.game.id)
  const [[viewPresetConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.viewpreset'], []))
  const pageId = useServerProp((s) => s.pageId)
  const [sort] = useQueryState('sort', parseAsArrayOf(parseAsJson(schema.parse)).withDefault([]))
  const getViewPresets = useQuery(getViewPresetsQuery, { where: { pageId } })

  const versionsOptions = useMemo(() => {
    return versions
      .filter((v) => v.version && v.version !== '0.0.0')
      .sort((a, b) => semverComparator(b.version, a.version))
      .map((v) => ({ value: v.version, label: v.version }))
  }, [versions])

  const viewMetricColumns = useMemo(() => {
    return viewPreset.attributes.flatMap((attr): string[] => {
      const generateName = (name: string) => {
        if (attr.isCohort) {
          return attr.cohortDays.map((day) => `${name}_D${day}`)
        }

        return [name]
      }

      if (attr.name.startsWith('ad_')) {
        return Object.values(AdTypeCategory).flatMap((adType) => {
          return generateName(`${adType}_${attr.name}`)
        })
      }
      return generateName(attr.name)
    })
  }, [viewPreset])

  const columns = useMemo(() => {
    const dimensionColumns = [
      columnHelper.accessor(() => 'Dimension', {
        id: 'dimension',
        header: tableHeader('Dimension'),
        cell: ({ table, row }) => {
          const grouping = table.getState().grouping
          return <span>{last(grouping.map((g) => row.getValue(g) as string).filter(Boolean))}</span>
        },
        meta: {
          label: 'Dimension',
        },
      }),

      columnHelper.accessor('version', {
        id: 'version',
        enableColumnFilter: true,
        enableHiding: false,
        enableGrouping: true,
        meta: {
          label: 'Version',
          options: versionsOptions,
        },
      }),
      columnHelper.accessor('countryCode', {
        id: 'countryCode',
        enableColumnFilter: true,
        enableHiding: false,
        enableGrouping: true,
        meta: {
          label: 'Country',
        },
      }),

      columnHelper.accessor('date', {
        id: 'date',
        enableColumnFilter: true,
        enableGrouping: true,
        enableHiding: false,
        meta: {
          label: 'Date',
          variant: 'dateRange',
        },
      }),
      columnHelper.accessor(() => '', {
        id: 'base',
        enableColumnFilter: true,
        enableHiding: false,
        meta: {
          label: 'Base',
          options: versionsOptions,
        },
      }),
    ]

    const metricColumns = viewPreset?.attributes.flatMap((attr): any => {
      if (!attr.isCohort && !attr.name.startsWith('ad_')) {
        const translatedName = t(`releaseMetric.${attr.name}`, attr.name)
        return [
          columnHelper.accessor(
            (row) =>
              processMetricValue(
                row[attr.name as keyof ReleaseMetricAttributesFragment],
                attr.name
              ),
            {
              id: attr.name,
              header: tableHeader(translatedName),
              cell: ({ cell, table, row }) => {
                return (
                  <DataTableGroupHighlightingCell
                    baselineComparison
                    table={table}
                    cell={cell}
                    row={row}
                    type={getAttributeType(attr.name)}
                    decimals={getDecimalsForAttribute(attr.name)}
                  />
                )
              },
              enableHiding: true,
              meta: {
                label: translatedName,
                variant: attr.name.includes('rate') || attr.name === 'roas' ? 'range' : 'number',
              },
            }
          ),
        ]
      } else {
        return attr.cohortDays.map((day) => {
          const accessorFn = (row: ReleaseMetricAttributesFragment) => {
            const cohortData = (row as any)[attr.name]
            if (typeof cohortData === 'string') {
              const parsedData = JSON.parse(cohortData)
              return parsedData[`D${day}`] ?? 0
            }
            if (cohortData && typeof cohortData === 'object') {
              return cohortData[`D${day}`] ?? cohortData[`${day}`] ?? cohortData[day] ?? 0
            }

            return 0
          }

          const label = t(`releaseMetric.${attr.name}`, attr.name, { day })

          return columnHelper.accessor((row) => processMetricValue(accessorFn(row), attr.name), {
            id: `${attr.name}_D${day}`,
            enableSorting: true,
            header: () => label,
            cell: ({ cell, table, row }) => {
              return (
                <DataTableGroupHighlightingCell
                  baselineComparison
                  table={table}
                  cell={cell}
                  row={row}
                  type={getAttributeType(attr.name)}
                  decimals={getDecimalsForAttribute(attr.name)}
                />
              )
            },
            enableHiding: true,
            meta: {
              label: label,
            },
          })
        })
      }
    })

    const adColumns = Object.values(AdTypeCategory)
      .flatMap((adType) => {
        const findAdMetric =
          (attr: keyof ReleaseAdMetricAttributesFragment) =>
          (row: ReleaseMetricIndex_ReleaseMetricAttributesFragment) => {
            const adMetric = row.ads?.find((a) => a.adTypeCategory === adType)
            return adMetric?.[attr] ?? 0
          }

        return [
          columnHelper.accessor(findAdMetric('adRevGrossAmount'), {
            id: `${adType}_ad_adRevGrossAmount`,
            enableSorting: true,
            header: tableHeader(t(`adTypeCategory.${adType}`) + ' Rev'),
            meta: {
              label: t(`adTypeCategory.${adType}`) + ' Rev',
            },
            cell: ({ table, row, cell }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="currency"
              />
            ),
          }),

          columnHelper.accessor(findAdMetric('impressionCount'), {
            id: `${adType}_ad_impressionCount`,
            enableSorting: true,
            enableHiding: true,
            header: t(`adTypeCategory.${adType}`) + ' Imps',
            meta: {
              label: t(`adTypeCategory.${adType}`) + ' Imps',
            },
            cell: ({ table, row, cell }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="number"
              />
            ),
          }),

          columnHelper.accessor(findAdMetric('impressionCountPerActiveUser'), {
            id: `${adType}_ad_impressionCountPerActiveUser`,
            enableSorting: true,
            enableHiding: true,
            header: t(`adTypeCategory.${adType}`) + ' ImpsDAU',
            meta: {
              label: t(`adTypeCategory.${adType}`) + ' ImpsDAU',
            },
            cell: ({ table, row, cell }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="number"
              />
            ),
          }),

          columnHelper.accessor(findAdMetric('adRevGrossAmountPerActiveUser'), {
            id: `${adType}_ad_adRevGrossAmountPerActiveUser`,
            enableSorting: true,
            header: t(`adTypeCategory.${adType}`) + ' ArpDAU',
            meta: {
              label: t(`adTypeCategory.${adType}`) + ' ArpDAU',
            },
            cell: ({ table, row, cell }) => (
              <DataTableGroupHighlightingCell
                baselineComparison
                table={table}
                cell={cell}
                row={row}
                type="number"
                decimals={3}
              />
            ),
          }),
        ]
      })
      .filter((col) => {
        return viewPreset.attributes.find((e) => !e.isCohort && col.id!.endsWith(e.name))
      })

    return [...dimensionColumns, ...metricColumns, ...adColumns]
  }, [collection, sort, viewMetricColumns])

  const baseVersion = filterForm.watch('base')

  const rows = useMemo(() => {
    const customSortCollection = sortCollectionWithState(collection, sort)
    if (!baseVersion) return customSortCollection

    return orderBy(customSortCollection, (e) => e.version === baseVersion, 'desc')
  }, [collection, JSON.stringify(sort), baseVersion])

  const { aggregations, getGroupAggregation, getBaselineAggregation } =
    useTreeTableAggregationState()

  const aggregationColumns = useMemo(() => {
    return viewMetricColumns.map((col) => ({ id: col }))
  }, [viewMetricColumns])

  const { table } = useDataTable({
    columns: columns,
    data: rows,
    getRowId: (row) => [row.version, row.countryCode, row.date].join('__'),
    pageCount: -1,
    initialState: {
      grouping: ['version', 'countryCode', 'date'],
      columnVisibility: {
        version: false,
        countryCode: false,
        date: false,
        base: false,
      },
      columnPinning: {
        left: ['dimension'],
      },
    },
    form: filterForm,
    meta: {
      aggregations,
      getGroupAggregation,
      aggregationColumns,
      baselineColumn: 'version',
      baselineValue: baseVersion,
      getBaselineAggregation,
      pageId,
    },
  })
  useTreeTableAggregation(table)
  const grouping = table.getState().grouping

  const onExpandRow = useCallback(
    (m: ReleaseMetricAttributesFragment) => {
      const groupValues = Object.fromEntries(grouping.map((g) => [g, m[g as keyof typeof m]]))

      const vars: ReleaseMetricIndex_GetDataQueryVariables = {
        where: {
          gameId,
          date: thru(groupValues.date, (val: Date | undefined) =>
            isNil(val)
              ? queryVariables.where.date
              : {
                  operator: FilterOperator.Between,
                  values: [format(val!, 'yyyy-MM-dd'), format(val!, 'yyyy-MM-dd')],
                }
          ),
          countryCode: thru(groupValues.countryCode, (val: string | undefined) =>
            isNil(val)
              ? undefined
              : {
                  operator: FilterOperator.In,
                  values: [val],
                }
          ),
          version: thru(groupValues.version, (val: string | undefined) =>
            isNil(val)
              ? undefined
              : {
                  operator: FilterOperator.In,
                  values: uniq([val, baseVersion].filter(Boolean)),
                }
          ),
        },
        group: {
          fields: grouping.slice(0, grouping.findIndex((g) => isNil(groupValues[g])) + 1),
        },
        preset: viewPreset.id > 0 ? { viewPresetId: viewPreset.id } : undefined,
      }

      return props.onRowExpand(vars)
    },
    [grouping]
  )

  return (
    <TreeTable
      table={table}
      getNodeKeys={(m) => grouping.map((g) => m[g as keyof typeof m]).filter((val) => !isNil(val))}
      getNodeId={(keys) => keys.join('|')}
      getRowCanExpand={(m) => grouping.map((g) => m[g as keyof typeof m]).some((v) => isNil(v))}
      sticky
      onExpandRow={onExpandRow}
      expandColumn="dimension"
    >
      <DataTableToolbar table={table} slots={slots}>
        <QueryState query={getViewPresets}>
          <ViewPresetSelection
            presets={getViewPresets.data?.viewPresets?.collection || []}
            title={
              <div className="flex items-center gap-2">
                <Columns2 className="size-4" />
                <span className="text-sm">Select View</span>
              </div>
            }
            onSelect={onPresetSelect}
            onDelete={() => {
              getViewPresets.refetch()
            }}
          />
          <ViewPresetEditor
            presets={getViewPresets.data?.viewPresets?.collection || []}
            viewPresetConfigMap={viewPresetConfigMapCollection?.find((p) => p.pageId === pageId)}
            onSave={() => {
              getViewPresets.refetch()
            }}
          />
        </QueryState>
        <DataTableGroupList table={table} />
      </DataTableToolbar>
    </TreeTable>
  )
}
