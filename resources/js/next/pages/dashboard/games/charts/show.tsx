import Game from '#models/game'
import { useServerProp } from '@/components/ssr'
import { makeGameBreadcrumbs } from '@/next/components/domain/game_breadcrumbs'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { useMetabaseParams } from '@/next/components/metabase/use_metabase_params'
import { useMetabaseFilterForm } from '@/next/components/metabase/use_metabase_filter_form'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { PageConfig } from '@/next/components/sdk/page_config'
import { useMemo } from 'react'
import { useGetMetabaseChart } from '@/next/api/use_get_metabase_chart'
import { MetabaseChart } from '@/next/components/metabase/metabase_chart'

export default function Page({ game }: { game: Game; chartId: string }) {
  const [, getConfigMaps] = useConfigMap(useMemo(() => ['cmap.dash.metabasechart'], []))

  return (
    <BaseLayout>
      <PageConfig
        title="Game Overview"
        breadcrumbs={makeGameBreadcrumbs(game, 'Game Overview')}
        sidebarSubItems={makeGameSidebarSubItems(game.id)}
        metabase
      />

      <QueryState query={[getConfigMaps]}>
        <Chart />
      </QueryState>
    </BaseLayout>
  )
}

function Chart() {
  const [[metabaseChartConfigMapCollection]] = useConfigMap(
    useMemo(() => ['cmap.dash.metabasechart'], [])
  )

  const chartId: string = useServerProp((s) => s.chartId)
  const gameId: string = useServerProp((s) => s.game.id)

  const configMap = metabaseChartConfigMapCollection.find((cm) => cm.id === chartId)!

  const form = useMetabaseFilterForm({
    configMap,
  })

  const params = useMetabaseParams(form, {
    gameId: [gameId],
    // gameId: ['com.mirai.badstudent.school.prankteacher.prankster'],
  })

  const getMetabaseChart = useGetMetabaseChart({
    id: configMap.id,
    params,
  })

  return (
    <QueryState query={getMetabaseChart}>
      <MetabaseChart url={getMetabaseChart.data?.metabaseChart?.url!} form={form} />
    </QueryState>
  )
}
