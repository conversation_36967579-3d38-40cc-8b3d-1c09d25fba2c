import {
  GameAgencyCostAttributesFragment,
  GameCostAttributesFragment,
  GameCostsOverview_GetDataQuery,
} from '@/graphql'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { ReactNode, useMemo } from 'react'
import { createColumnHelper, Table } from '@tanstack/react-table'
import { graphql } from '@/gql'
import { useGraphql } from '@/components/toolkit-api'
import { groupBy, orderBy, sumBy } from 'lodash-es'
import { useDataTable } from '@/next/hooks/use-data-table'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { EditableTableCell, tableHeader } from '@/next/components/sdk/data_table'
import { formatCurrency } from '@/components/typography'
import { format } from 'date-fns'
import { sortCollectionWithState } from '@/next/utils/sort_utils'
import { parseAsArrayOf, parseAsJson, useQueryState } from 'nuqs'
import z from 'zod'
import { AdNetworkInputType } from '#config/enums'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'

const getAdNetworksQuery = graphql(`
  query GameCostsOverview_GetAdNetworks {
    adNetworks {
      collection {
        ...GameCostIndexOverview_AdNetworkAttributes
      }
    }
  }
`)

export type DailySpend = {
  id: string
  date: string
  spends: GameCostAttributesFragment[]
  preTaxAmount: number
  totalAmount: number
  autogen: boolean
  mmpAmount: number
  taxAmount: number
}

const schema = z.object({
  id: z.string(),
  desc: z.boolean(),
})
const columnHelper = createColumnHelper<DailySpend>()

export function GameCostsOverviewTable({
  getOverviewData,
  filterForm,
  slots: { beforeFilter } = {},
}: {
  getOverviewData: GameCostsOverview_GetDataQuery
  filterForm: UseDataTableFilterFormReturn<any>
  slots?: {
    beforeFilter?: ReactNode
    toolbar?: ReactNode | ((table: Table<GameAgencyCostAttributesFragment>) => ReactNode)
  }
}) {
  const networks = useGraphql(getAdNetworksQuery)
  const [sort] = useQueryState('sort', parseAsArrayOf(parseAsJson(schema.parse)).withDefault([]))
  const [openFloatingRoute] = useFloatingRoute()

  const dailySpends = useMemo<DailySpend[]>(() => {
    if (!getOverviewData?.gameCosts?.collection) return []

    const spends = getOverviewData?.gameCosts.collection as GameCostAttributesFragment[]
    const dailies = orderBy(
      Object.entries(groupBy(spends, (s) => s.date)).map((entry) => ({
        id: entry[0],
        date: entry[0],
        spends: entry[1],
        preTaxAmount: sumBy(entry[1], (s) => s.preTaxAmount || 0),
        totalAmount: sumBy(entry[1], (s) => s.totalAmount || 0),
        autogen: false,
        mmpAmount: entry[1][0]?.mmpAmount || 0,
        taxAmount: entry[1][0]?.taxAmount || 0,
      })),
      (g) => new Date(g.date),
      'desc' as any
    )

    return dailies
  }, [getOverviewData.gameCosts.collection])

  const rows = useMemo(() => {
    const aggregation = getOverviewData.gameCosts.meta?.aggregation
    return [
      {
        id: 'SUMMARY',
        date: 'Summary',
        preTaxAmount: aggregation?.preTaxAmount || 0,
        totalAmount: aggregation?.totalAmount || 0,
        spends: [],
        autogen: false,
        mmpAmount: aggregation?.mmpAmount || 0,
      },
      ...dailySpends,
    ]
  }, [dailySpends, getOverviewData.gameCosts.meta?.aggregation])

  const columns = useMemo(() => {
    const baseColumns = [
      columnHelper.accessor('date', {
        id: 'date',
        header: tableHeader('Date'),
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue()}</div>
        },
        meta: {
          label: 'Date',
          placeholder: 'Search dates...',
          variant: 'text',
        },
        enableColumnFilter: true,
        enableSorting: true,
        enableHiding: true,
      }),
      columnHelper.accessor('date', {
        id: 'day',
        header: tableHeader('Day'),
        cell: ({ getValue, row }) => {
          const date = getValue()
          const isSummary = row.original.id === 'SUMMARY'
          if (isSummary) return null
          return <div className="text-xs">{format(new Date(date), 'EEE')}</div>
        },
        meta: {
          label: 'Day',
        },
        enableHiding: true,
        enableSorting: false,
      }),

      columnHelper.accessor('totalAmount', {
        id: 'totalAmount',
        header: tableHeader('Total Cost', {
          bgcolor: 'bg-cyan-400',
          className: 'text-center',
        }),
        cell: ({ getValue, row }) => {
          const totalAmount = getValue()
          const mmpAmount = row.original.mmpAmount || 0
          return formatCurrency(totalAmount + mmpAmount)
        },
        meta: {
          label: 'Total Cost',
        },
        enableSorting: true,
        enableHiding: true,
      }),
      columnHelper.accessor('preTaxAmount', {
        id: 'preTaxAmount',
        header: tableHeader('Cost (non-Tax)', { bgcolor: 'bg-cyan-400' }),
        cell: ({ getValue, row }) => {
          const preTaxAmount = getValue()
          const mmpAmount = row.original.mmpAmount || 0
          return formatCurrency(preTaxAmount + mmpAmount)
        },
        meta: {
          label: 'Cost (non-Tax)',
        },
        enableSorting: true,
        enableHiding: true,
      }),
      columnHelper.accessor('mmpAmount', {
        id: 'mmp',
        header: tableHeader('MMP', { bgcolor: 'bg-cyan-400' }),
        cell: ({ getValue }) => formatCurrency(getValue() || 0),
        meta: {
          label: 'MMP',
        },
        enableSorting: true,
        enableHiding: true,
      }),
      columnHelper.accessor(() => '', {
        id: 'view',
        enableHiding: false,
        meta: {
          label: 'View Table',
          variant: 'select',
          options: [
            { label: 'Overview', value: 'overview' },
            { label: 'Comparison', value: 'comparison' },
          ],
        },
      }),
    ]
    // Add ad network columns
    const adNetworks = networks.data?.adNetworks?.collection || []
    const networkColumns = adNetworks.map((network) =>
      columnHelper.accessor(
        (row) => {
          if (row.id === 'SUMMARY') return null
          const networkSpend = row.spends.find((s) => s.network?.id === network.id)
          return networkSpend?.preTaxAmount || 0
        },
        {
          id: network.id.toString(),
          header: tableHeader(network.name, {
            bgcolor: network.inputType === 'Auto' ? 'bg-orange-400' : 'bg-green-400',
            className: 'whitespace-break-spaces',
          }),
          cell: ({ getValue, row }) => {
            const value = getValue()
            if (row.original.id === 'SUMMARY') return null
            const formattedValue = value ? formatCurrency(value) : null

            if (network.inputType === AdNetworkInputType.Auto) {
              return formattedValue
            }

            return (
              <EditableTableCell
                onClick={() =>
                  openFloatingRoute('edit', {
                    date: row.original.date,
                    networkId: network.id.toString(),
                  })
                }
              >
                {formattedValue}
              </EditableTableCell>
            )
          },
          meta: {
            label: network.name,
          },
          enableSorting: true,
        }
      )
    )

    return [
      ...baseColumns,
      ...networkColumns,
      columnHelper.accessor(() => '', {
        id: '_reserved',
        enableHiding: false,
        cell: () => null,
        header: '',
        meta: {
          label: '',
        },
      }),
    ]
  }, [networks.data])

  const sortedRows = useMemo(() => {
    return sortCollectionWithState(rows, sort)
  }, [rows, sort])

  const { table } = useDataTable({
    data: sortedRows as DailySpend[],
    columns,
    pageCount: getOverviewData.gameCosts.meta?.pageInfo?.lastPage || -1,
    getRowId: (row) => row.id,
    initialState: {
      pagination: {
        pageIndex: (getOverviewData.gameCosts.meta?.pageInfo?.currentPage || 1) - 1,
        pageSize: getOverviewData.gameCosts.meta?.pageInfo?.perPage || 10,
      },
      columnVisibility: {
        view: false,
      },
      columnPinning: {
        left: ['date'],
      },
    },
    form: filterForm,
  })

  return (
    <TreeTable
      table={table}
      getNodeKeys={(row) => [row.date]}
      getNodeId={(keys) => keys.join('|')}
      sticky
      pageSizeOptions={[50, 100, 200]}
    >
      <DataTableToolbar table={table} slots={{ beforeFilter }} />
    </TreeTable>
  )
}
