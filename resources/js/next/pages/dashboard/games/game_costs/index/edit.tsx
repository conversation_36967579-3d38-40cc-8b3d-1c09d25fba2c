import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import { useHashState } from '@/next/components/sdk/hash_link'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { useApolloClient } from '@apollo/client'
import { parseAsInteger, parseAsIsoDate } from 'nuqs'
import { adNetworkAttributesFragment } from './util'
import { useMemo } from 'react'
import { FormFloatingRouteContent } from '@/next/components/domain/form_floating_route'
import { useForm } from 'react-hook-form'
import { GameCostIndexOverview_AdNetworkAttributesFragment, graphql } from '@/graphql'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { IconButton } from '@/next/components/sdk/button'
import { isNil } from 'lodash-es'
import { useMutation } from '@/next/components/sdk/graphql'
import { formatDate } from '@/next/lib/format'
import { format } from 'date-fns'
import { useServerProp } from '@/components/ssr'

export function GameCostEdit({ onSuccess }: { onSuccess: () => any }) {
  const [networkId] = useHashState('networkId', parseAsInteger)
  const [date] = useHashState('date', parseAsIsoDate)

  const apollo = useApolloClient()
  const network = useMemo(() => {
    return apollo.readFragment({
      fragment: adNetworkAttributesFragment,
      id: `AdNetwork:${networkId}`,
    })
  }, [networkId, apollo.readFragment])

  if (!network || !date) {
    return null
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle>Edit cost</DialogTitle>
        <DialogDescription className="flex gap-2">
          <span>Date: {formatDate(date)}</span>
          <span>Network: {network.name}</span>
        </DialogDescription>
      </DialogHeader>

      <Form network={network} date={date} onSuccess={onSuccess} />
    </>
  )
}

type FormData = {
  preTaxAmount: string
}

const updateGameCostMutation = graphql(`
  mutation GameCostEdit_UpdateGameCost($form: UpdateGameCostForm!, $where: UpdateGameCostWhere!) {
    updateGameCost(form: $form, where: $where)
  }
`)

function Form({
  network,
  date,
  onSuccess,
}: {
  network: GameCostIndexOverview_AdNetworkAttributesFragment
  date: Date
  onSuccess: () => any
}) {
  const gameId = useServerProp((s) => s.game.id)
  const form = useForm({
    defaultValues: {
      preTaxAmount: 0,
    },
  })

  const [, closeFloatingRoute] = useFloatingRoute()

  const { isSubmitting } = form.formState

  const [updateGameCost] = useMutation(updateGameCostMutation, {
    onSuccess: () => {
      onSuccess()
      closeFloatingRoute()
    },
  })

  const onSubmit = async (data: FormData) => {
    await updateGameCost({
      where: {
        networkId: network.id,
        date: format(date, 'yyyy-MM-dd'),
        gameId: gameId,
      },
      form: {
        preTaxAmount:
          isNil(data.preTaxAmount) || data.preTaxAmount === '' ? null : Number(data.preTaxAmount),
      },
    })
  }

  return (
    <FormFloatingRouteContent
      form={form as any}
      onSubmit={onSubmit}
      slots={{
        after: (
          <IconButton type="submit" loading={isSubmitting} disabled={isSubmitting}>
            Save
          </IconButton>
        ),
      }}
    >
      <FormInputField
        control={form.control}
        name="preTaxAmount"
        label="Amount (Non-Tax)"
        placeholder="Enter non-tax amount"
        type="number"
        hint="Leave it empty to delete the record"
      />
    </FormFloatingRouteContent>
  )
}
