import GameSpend from '#models/game_spend'
import { graphql } from '@/gql'

export type DailySpend = {
  date: string
  spends: GameSpend[]
  preTaxAmount: number
  totalAmount: number
  autogen: boolean
  mmp: number
}

export const getInitDataQuery = graphql(`
  query GameSpendShow_Init($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }
  }
`)

export const comparisonQuery = graphql(`
  query GameCostsComparison_GetData(
    $gameId: ID!
    $dateFrom: Date!
    $dateTo: Date!
    $orderDirection: OrderDirection!
    $page: Int!
    $perPage: Int!
  ) {
    gameAgencyCosts(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
      offset: { page: $page, perPage: $perPage }
      order: { direction: $orderDirection }
    ) {
      collection {
        ...GameAgencyCostAttributes
        date
        metric {
          ...GameAgencyMetricAttributes
        }
      }
      pageInfo {
        ...PageInfoAttributes
      }
    }
  }
`)

export const aggregationQuery = graphql(`
  query GameCostsAggregation_GetData(
    $gameId: ID!
    $dateFrom: Date!
    $dateTo: Date!
    $weeklyDateFrom: Date!
    $weeklyDateTo: Date!
  ) {
    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {
      collection {
        ...GameAgencyCostAttributes
        agency {
          ...AdAgencyAttributes
        }
      }
    }

    total: aggregateGameAgencyCost(
      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }
    ) {
      collection {
        ...GameAgencyCostAttributes
      }
    }

    weekly: aggregateGameAgencyCost(
      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }
    ) {
      collection {
        ...GameAgencyCostAttributes
      }
    }
  }
`)

export const overviewGameCostAttributesFragment = graphql(`
  fragment GameCostIndexOverview_GameCostAttributes on GameCost {
    id
    date
    mmpAmount
    preTaxAmount
    totalAmount
    taxAmount
    network {
      id
      name
    }
  }
`)

export const overviewQuery = graphql(`
  query GameCostsOverview_GetData($where: GameCostsWhere!, $offset: Offset!) {
    gameCosts(where: $where, offset: $offset) {
      collection {
        ...GameCostIndexOverview_GameCostAttributes
      }
      meta {
        aggregation {
          mmpAmount
          totalAmount
          preTaxAmount
        }
        pageInfo {
          ...PageInfoAttributes
        }
      }
    }
  }
`)

export const adNetworkAttributesFragment = graphql(`
  fragment GameCostIndexOverview_AdNetworkAttributes on AdNetwork {
    id
    name
    inputType
  }
`)
