import { formatCurrency } from '@/components/typography'
import {
  GameAgencyCostAttributesFragment,
  GameCostsAggregation_GetDataQuery,
  GameCostsComparison_GetDataQuery,
} from '@/graphql'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { tableHeader } from '@/next/components/sdk/data_table'
import { TreeTable } from '@/next/components/sdk/tree_table'
import { useDataTable } from '@/next/hooks/use-data-table'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'
import { cn } from '@/next/lib/utils'
import { sortCollectionWithState } from '@/next/utils/sort_utils'
import { createColumnHelper, Table } from '@tanstack/react-table'
import { format } from 'date-fns'
import { groupBy, orderBy, sumBy, toPairs } from 'lodash-es'
import { parseAsArrayOf, parseAsJson, useQueryState } from 'nuqs'
import { ReactNode, useMemo } from 'react'
import z from 'zod'

export type AgencyCostRow = {
  id: string
  date: string
  agencies: any[]
  hierarchy?: string[]
  metric: {
    paidInstalls: number
    cost: number
  }
  view?: string
}

const isAggregation = (row: { original: AgencyCostRow }) =>
  Boolean(row.original.hierarchy) ||
  ['Summary', 'Total', 'SUM', 'AVG', 'MAX', 'MIN'].includes(row.original.id)

const schema = z.object({
  id: z.string(),
  desc: z.boolean(),
})

const columnHelper = createColumnHelper<AgencyCostRow>()

export function GameCostsComparisonTable({
  getComparisonData,
  aggregationData,
  filterForm,
  slots: { beforeFilter } = {},
}: {
  getComparisonData: GameCostsComparison_GetDataQuery
  aggregationData: GameCostsAggregation_GetDataQuery
  filterForm: UseDataTableFilterFormReturn<any>
  slots?: {
    beforeFilter?: ReactNode
    toolbar?: ReactNode | ((table: Table<GameAgencyCostAttributesFragment>) => ReactNode)
  }
}) {
  const [sort] = useQueryState('sort', parseAsArrayOf(parseAsJson(schema.parse)).withDefault([]))

  const agencies = useMemo(() => {
    if (!aggregationData?.summary?.collection) return []

    return orderBy(
      aggregationData.summary.collection.map((i: any) => i.agency),
      (agency: any) => {
        const weeklyCost =
          aggregationData?.weekly?.collection?.find((c: any) => c.agencyId === agency.id)
            ?.mediationCost ?? 0
        return weeklyCost
      },
      'desc'
    )
  }, [aggregationData])

  const rows = useMemo<AgencyCostRow[]>(() => {
    if (!getComparisonData?.gameAgencyCosts?.collection) return []

    const costs = getComparisonData.gameAgencyCosts.collection
    const dates = orderBy(
      toPairs(groupBy(costs, 'date')).map(([date, agencies]) => ({
        id: date,
        date,
        agencies,
        metric: agencies[0].metric,
      })),
      'date',
      'desc'
    )

    const makeAggregationRow = (fn: any, id: string): AgencyCostRow => {
      return {
        id,
        date: id,
        hierarchy: ['Total', id],
        agencies: agencies.map((agency) => {
          const agencyCosts = dates.map((date) =>
            date.agencies.find((a) => a.agencyId === agency.id)
          )

          return {
            mediationCost: fn(agencyCosts, 'mediationCost'),
            totalCost: fn(agencyCosts, 'totalCost'),
            varianceRate: fn(agencyCosts, 'varianceRate'),
            agencyId: agency.id,
          }
        }),
        metric: { paidInstalls: 0, cost: 0 },
      }
    }
    return [
      {
        id: 'Summary',
        date: 'Summary',
        agencies: aggregationData.summary.collection,
        metric: { paidInstalls: 0, cost: 0 },
      },
      {
        id: 'Total',
        date: 'Total',
        agencies: aggregationData.total.collection,
        metric: { paidInstalls: 0, cost: 0 },
      },
      makeAggregationRow(sumBy, 'SUM'),
      makeAggregationRow((arr: any[], key: string) => {
        const values = arr.map((item) => item?.[key]).filter(Boolean)
        return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0
      }, 'AVG'),
      makeAggregationRow((arr: any[], key: string) => {
        const values = arr.map((item) => item?.[key]).filter(Boolean)
        return values.length > 0 ? Math.max(...values) : 0
      }, 'MAX'),
      makeAggregationRow((arr: any[], key: string) => {
        const values = arr.map((item) => item?.[key]).filter(Boolean)
        return values.length > 0 ? Math.min(...values) : 0
      }, 'MIN'),
      ...dates,
    ]
  }, [aggregationData, agencies])

  const columns = useMemo(() => {
    const baseColumns = [
      columnHelper.accessor('date', {
        id: 'date',
        header: tableHeader('Date'),
        cell: ({ getValue, row }) => {
          const date = getValue()
          if (isAggregation(row)) return <div className="text-center font-bold">{date}</div>
          const d = new Date(date as string)
          return (
            <div className="text-center">
              {isNaN(d.getTime()) ? (date as string) : format(d, 'yyyy-MM-dd')}
            </div>
          )
        },
        meta: {
          label: 'Date',
          placeholder: 'Search dates...',
          variant: 'text',
        },
        enableColumnFilter: true,
        enableSorting: true,
        enableHiding: false,
      }),

      columnHelper.accessor(() => '', {
        id: 'view',
        enableHiding: false,
        enableColumnFilter: true,
        header: tableHeader('View'),
        cell: ({ getValue }) => {
          return getValue()
        },
        meta: {
          label: 'View',
          placeholder: 'Select view table...',
          variant: 'select',
          options: [
            { label: 'Overview', value: 'overview' },
            { label: 'Comparison', value: 'comparison' },
          ],
        },
      }),
    ]

    const totalColumns = [
      columnHelper.group({
        id: 'total',
        header: tableHeader('Total', {
          bgcolor: 'bg-yellow-100',
          className: 'ml-auto mx-auto pl-px',
        }),

        columns: [
          columnHelper.accessor((row) => (row.hierarchy ? null : row.metric.cost), {
            id: 'totalCost',
            header: tableHeader('from Game Metrics', {
              bgcolor: 'bg-purple-200',
              className: 'whitespace-break-spaces',
            }),
            cell: (info) => {
              const r = info.row.original
              return isAggregation(info.row) ? null : formatCurrency(r.metric.cost)
            },
            meta: {
              label: 'from Game Metrics',
            },
            enableSorting: true,
          }),
          columnHelper.accessor((row) => (row.hierarchy ? null : row.metric.cost), {
            id: 'totalMediationCost',
            header: tableHeader('from MMP', {
              bgcolor: 'bg-purple-200',
              className: 'whitespace-break-spaces',
            }),
            cell: (info) => {
              if (isAggregation(info.row)) return null
              const r = info.row.original
              const value =
                sumBy(r.agencies, (a) => a.mediationCost) + r.metric.paidInstalls * 0.0012
              return formatCurrency(value)
            },
            meta: {
              label: 'from MMP',
            },
            enableSorting: true,
          }),
          columnHelper.accessor((row) => (row.hierarchy ? null : row.metric.cost), {
            id: 'totalAgencyCost',
            header: tableHeader('from Agency', {
              bgcolor: 'bg-purple-200',
              className: 'whitespace-break-spaces',
            }),
            cell: (info) => {
              if (isAggregation(info.row)) return null
              const r = info.row.original
              const value =
                sumBy(r.agencies as any[], (a: any) => a.totalCost || 0) +
                r.metric.paidInstalls * 0.0012
              return formatCurrency(value)
            },
            meta: {
              label: 'from Agency',
            },
            enableSorting: true,
          }),
        ],
      }),
    ]

    const agencyGroupColumns = agencies.map((agency) =>
      columnHelper.group({
        id: `${agency.id}`,
        header: tableHeader(agency.name, {
          bgcolor: 'bg-yellow-100',
          className: 'mx-auto whitespace-break-spaces',
        }),
        columns: [
          columnHelper.accessor(
            (row) => row.agencies.find((a) => a.agencyId === agency.id)?.mediationCost ?? 0,
            {
              id: `${agency.id}.mediationCost`,
              header: tableHeader('from MMP', {
                bgcolor: 'bg-pink-200',
                className: 'whitespace-break-spaces',
              }),
              cell: ({ getValue, row }) => {
                const value = getValue()
                const isAggregation = ['SUM', 'AVG', 'MAX', 'MIN'].includes(row.original.id)
                if (row.original.hierarchy && !isAggregation) return null
                return value ? formatCurrency(value) : null
              },
              meta: {
                label: `${agency.name} - from MMP`,
              },
              enableSorting: true,
            }
          ),
          columnHelper.accessor(
            (row) => row.agencies.find((a) => a.agencyId === agency.id)?.totalCost ?? 0,
            {
              id: `${agency.id}.cost`,
              header: tableHeader('from Agency', {
                bgcolor: 'bg-green-200',
                className: 'whitespace-break-spaces',
              }),
              cell: ({ getValue, row }) => {
                const value = getValue()
                const isAggregation = ['SUM', 'AVG', 'MAX', 'MIN'].includes(row.original.id)
                if (row.original.hierarchy && !isAggregation) return null
                return value ? formatCurrency(value) : null
              },
              meta: {
                label: `${agency.name} - from Agency`,
              },
              enableSorting: true,
            }
          ),
          columnHelper.accessor(
            (row) => row.agencies.find((a) => a.agencyId === agency.id)?.varianceRate ?? 0,
            {
              id: `${agency.id}.varianceRate`,
              header: tableHeader('% difference', {
                bgcolor: 'bg-blue-200',
                className: 'whitespace-break-spaces',
              }),
              cell: ({ getValue, row }) => {
                const value = getValue()
                const isAggregation = ['SUM', 'AVG', 'MAX', 'MIN'].includes(row.original.id)
                if (row.original.hierarchy && !isAggregation) return null
                const diff = Math.abs(value)
                return (
                  <>
                    <div
                      className={cn(
                        'absolute inset-0',
                        {
                          'bg-red-100': diff > 0.025 && diff <= 0.1,
                          'bg-red-300': diff > 0.1 && diff <= 0.2,
                          'bg-red-400 text-white': diff > 0.2,
                        },
                        'text-center'
                      )}
                    />

                    <div className={cn('relative z-10')}>
                      {value ? `${(value * 100).toFixed(3)}%` : null}
                    </div>
                  </>
                )
              },
              meta: {
                label: `${agency.name} - % difference`,
              },
              enableSorting: true,
            }
          ),
        ],
      })
    )

    return [...baseColumns, ...totalColumns, ...agencyGroupColumns]
  }, [agencies, aggregationData])

  const sortedRows = useMemo(() => {
    return sortCollectionWithState(rows, sort)
  }, [rows, sort])

  const { table } = useDataTable({
    columns,
    data: sortedRows,
    pageCount: -1,
    getRowId: (row) => row.id,
    initialState: {
      columnPinning: {
        left: ['dimension'],
      },
      columnVisibility: {
        agencyId: false,
        date: true,
        view: false,
      },
    },
    form: filterForm,
  })
  return (
    <TreeTable
      table={table}
      getNodeKeys={(row) => row.hierarchy || [row.date]}
      getNodeId={(keys) => keys.join('|')}
      getRowCanExpand={(row) => row.id === 'Total'}
      indentSize={24}
      expandColumn="date"
      sticky
      pageSizeOptions={[50, 100, 200]}
    >
      <DataTableToolbar table={table} slots={{ beforeFilter }} />
    </TreeTable>
  )
}
