import GameCostsController from '#controllers/dashboard/game/game_costs_controller'
import { makeGameSidebarSubItems } from '@/next/components/domain/game_sidebar'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { InferPageProps } from '@adonisjs/inertia/types'
import { useGraphql } from '@/components/toolkit-api'
import { aggregationQuery, comparisonQuery, getInitDataQuery, overviewQuery } from './index/util'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { graphql } from '@/gql'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'
import { format, subDays } from 'date-fns'
import { DataTableFilterSubmitButton } from '@/next/components/sdk/data_table'
import { GameCostsComparisonTable } from './index/comparison_table'
import { makeGameBreadcrumbs } from '@/next/components/domain/game_breadcrumbs'
import Game from '#models/game'
import { useServerProp } from '@/components/ssr'
import { GameCostsOverviewTable } from './index/overview_table'
import { OrderDirection } from '#graphql/main'
import {
  GameCostsAggregation_GetDataQuery,
  GameCostsComparison_GetDataQuery,
  GameCostsOverview_GetDataQuery,
} from '@/graphql'
import { parseAsInteger, useQueryState } from 'nuqs'
import { FloatingRoute } from '@/next/components/sdk/floating_route'
import { GameCostEdit } from './index/edit'

type PageProps = InferPageProps<GameCostsController, 'index'>

graphql(`
  fragment GameAgencyCostAttributes on GameAgencyCost {
    totalCost
    mediationCost
    varianceRate
    agencyId
  }
`)

export default function Page({ game: { name: gameName, id: gameId } }: PageProps) {
  const [page] = useQueryState('page', parseAsInteger.withDefault(1))
  const [perPage] = useQueryState('perPage', parseAsInteger.withDefault(50))

  const getInitData = useGraphql(getInitDataQuery, {
    variables: {
      gameId,
    },
  })

  const game: Game = useServerProp((s) => s.game)
  const filterForm = useDataTableFilterForm({
    date: {
      variant: 'dateRange',
      defaultValue: [subDays(new Date(), 30), new Date()],
    },
    view: {
      variant: 'select',
      defaultValue: 'overview',
      options: [
        { label: 'Overview', value: 'overview' },
        { label: 'Comparison', value: 'comparison' },
      ],
    },
  })
  const { immediateValues } = filterForm
  const filterFormValues = filterForm.watch()

  const getAggregationData = useGraphql(aggregationQuery, {
    variables: {
      gameId,
      dateFrom: format(filterFormValues.date[0] || new Date(), 'yyyy-MM-dd'),
      dateTo: format(filterFormValues.date[1] || new Date(), 'yyyy-MM-dd'),
      weeklyDateFrom: format(subDays(new Date(), 6), 'yyyy-MM-dd'),
      weeklyDateTo: format(new Date(), 'yyyy-MM-dd'),
    },
    skip: immediateValues.view === 'overview',
  })

  const getCostsData =
    immediateValues.view === 'comparison'
      ? useGraphql(comparisonQuery, {
          variables: {
            gameId,
            page,
            perPage,
            dateTo: format(filterFormValues.date[1] || new Date(), 'yyyy-MM-dd'),
            dateFrom: format(filterFormValues.date[0] || new Date(), 'yyyy-MM-dd'),
            orderDirection: OrderDirection.Desc,
          },
        })
      : useGraphql(overviewQuery, {
          variables: {
            where: {
              gameId,
              dateFrom: format(filterFormValues.date[0] || new Date(), 'yyyy-MM-dd'),
              dateTo: format(filterFormValues.date[1] || new Date(), 'yyyy-MM-dd'),
            },
            offset: {
              perPage,
              page,
            },
          },
        })

  return (
    <BaseLayout>
      <PageConfig
        title={`Cost Explorer | ${gameName}`}
        breadcrumbs={makeGameBreadcrumbs(game, 'Cost Explorer')}
        sidebarSubItems={makeGameSidebarSubItems(gameId)}
      />

      <QueryState query={[getInitData, getCostsData]}>
        {immediateValues.view === 'comparison' ? (
          <GameCostsComparisonTable
            getComparisonData={getCostsData.data! as GameCostsComparison_GetDataQuery}
            filterForm={filterForm}
            aggregationData={getAggregationData?.data! as GameCostsAggregation_GetDataQuery}
            slots={{
              beforeFilter: (
                <DataTableFilterSubmitButton
                  onClick={() => {
                    filterForm.syncValues('date')
                  }}
                  disabled={getCostsData.loading}
                  loading={getCostsData.loading}
                />
              ),
            }}
          />
        ) : (
          <>
            <GameCostsOverviewTable
              getOverviewData={getCostsData.data! as GameCostsOverview_GetDataQuery}
              filterForm={filterForm}
              slots={{
                beforeFilter: (
                  <DataTableFilterSubmitButton
                    onClick={() => {
                      filterForm.syncValues('date')
                    }}
                    disabled={getCostsData.loading}
                    loading={getCostsData.loading}
                  />
                ),
              }}
            />

            <FloatingRoute id="edit">
              <GameCostEdit
                onSuccess={() => {
                  getCostsData.refetch()
                }}
              />
            </FloatingRoute>
          </>
        )}
      </QueryState>
    </BaseLayout>
  )
}
