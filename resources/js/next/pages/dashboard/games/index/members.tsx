import { parseAs<PERSON>rrayOf, parseAsString } from 'nuqs'

import { useHashNavigate, useHashState } from '@/next/components/sdk/hash_link'
import {
  FormFloatingRouteContent,
  FormFloatingRouteHeader,
} from '@/next/components/domain/form_floating_route'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { useGetAllUsers } from '@/next/api/use_get_all_users'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { useMemo } from 'react'
import { Badge } from '@/next/components/ui/badge'
import { IconButton } from '@/next/components/sdk/button'
import { graphql } from '@/gql'
import { useMutation, useQuery } from '@/next/components/sdk/graphql'
import { uniq, uniqBy } from 'lodash-es'
import { useConfigMap } from '@/next/components/sdk/config_map'

const schema = z.object({
  adds: z.array(z.string()).optional().default([]),
  removes: z.array(z.string()).optional().default([]),
})

const getGameRolesQuery = graphql(`
  query ListGameRoles($gameIds: [ID!]!) {
    gameRoles(gameIds: $gameIds) {
      collection {
        ...GameRolesAttributes
      }
    }
  }
`)

const updateGameRolesMutation = graphql(`
  mutation UpdateGameRoles($forms: [UpdateGameRolesGame!]!) {
    updateGameRoles(forms: $forms) {
      collection {
        ...GameRolesAttributes
      }
    }
  }
`)
export function GameMembers({ onFinish, listUsers }: { onFinish: () => void; listUsers: any }) {
  const [gameIds] = useHashState('ids', parseAsArrayOf(parseAsString))
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const [, closeDialog] = useHashNavigate()

  const form = useForm({
    resolver: zodResolver(schema),
  })

  const getUsers = useGetAllUsers()

  // TODO: fix to use cache for get roles query
  const listRole = useQuery(
    getGameRolesQuery,
    {
      gameIds: gameIds || [],
    },
    {
      skip: !gameIds?.length,
      fetchPolicy: 'no-cache',
    }
  )
  const userRemoveList = useMemo(
    () =>
      uniqBy(
        listRole.data?.gameRoles?.collection?.flatMap(
          (game) =>
            game.roles.flatMap(
              (role) =>
                role.users?.map((u) => ({
                  label: u.email,
                  value: u.email,
                })) || []
            ) || []
        ) || [],
        'value'
      ),
    [listRole.data]
  )

  const userAddList = useMemo(() => {
    return (
      listUsers
        ?.filter((user: any) => user.team)
        ?.map((user: any) => ({
          label: `${user.fullName} (${user.email})`,
          value: user.email,
          roleId: user.team?.roleId,
        })) || []
    )
  }, [listUsers])

  const onSubmit = form.handleSubmit(() => {
    if (!gameIds) return
    if (!listRole.data?.gameRoles.collection) return

    const formValues = form.getValues()
    const currentGames = listRole.data.gameRoles.collection
    const gameUpdates = gameIds.map((gameId) => {
      const game = currentGames.find((g) => g.id === gameId)

      const updatedRoles = roleConfigMapCollection?.map((roleConfig) => {
        const currentRole = game?.roles.find((role) => role.id === roleConfig.id)
        const currentUsers = currentRole?.users?.map((u) => u.email) || []

        const usersToAdd =
          formValues.adds?.filter((email) => {
            const user = userAddList.find((u: any) => u.value === email)
            const alreadyExists = currentUsers.includes(email)
            return user?.roleId === roleConfig.id && !alreadyExists
          }) || []

        return {
          id: roleConfig.id,
          users: uniq([...currentUsers, ...usersToAdd]).filter(
            (email) => !formValues.removes?.includes(email)
          ),
        }
      })

      return {
        gameId,
        roles: updatedRoles,
      }
    })

    updateGameRoles({
      forms: gameUpdates,
    })
  })

  const [updateGameRoles, { loading }] = useMutation(updateGameRolesMutation, {
    onSuccess() {
      onFinish()
      closeDialog()
    },
  })

  return (
    <>
      <FormFloatingRouteHeader
        title="Manage Members"
        description={
          <>
            <span className="flex flex-col gap-2">
              {gameIds?.map((gameId) => (
                <Badge key={gameId} variant="outline">
                  {gameId}
                </Badge>
              ))}
              <span>
                <span className="font-bold">Note:</span> This will update the members for all games.
              </span>
            </span>
          </>
        }
      />
      <FormFloatingRouteContent
        form={form}
        onSubmit={() => {
          onSubmit()
        }}
        slots={{
          after: (
            <IconButton type="submit" disabled={loading} loading={loading}>
              Save
            </IconButton>
          ),
        }}
      >
        <QueryState query={[getUsers]}>
          <FormMultiSelectField
            control={form.control}
            name="adds"
            label="Members to add"
            options={userAddList}
          />

          <FormMultiSelectField
            control={form.control}
            name="removes"
            label="Members to remove"
            options={userRemoveList}
          />
        </QueryState>
      </FormFloatingRouteContent>
    </>
  )
}
