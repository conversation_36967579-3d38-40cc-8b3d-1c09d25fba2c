import { graphql } from '@/gql'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { useQuery, useMutation } from '@/next/components/sdk/graphql'
import { useHashState, useHashNavigate } from '@/next/components/sdk/hash_link'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { Loading } from '@/next/components/sdk/loading'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { Form } from '@/next/components/ui/form'
import { Button } from '@/next/components/ui/button'
import { parseAsString } from 'nuqs'
import { useEffect, useMemo } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'

import { FloatingRouteContent } from '@/next/components/sdk/floating_route'
const getDataQuery = graphql(`
  query GameRoles_GetData($gameId: ID!) {
    game(id: $gameId) {
      ...GameAttributes
    }
  }
`)
const getGameRolesQuery = graphql(`
  query ListGameRoles($gameIds: [ID!]!) {
    gameRoles(gameIds: $gameIds) {
      collection {
        ...GameRolesAttributes
      }
    }
  }
`)

const updateGameRolesMutation = graphql(`
  mutation UpdateGameRoles($forms: [UpdateGameRolesGame!]!) {
    updateGameRoles(forms: $forms) {
      collection {
        ...GameRolesAttributes
      }
    }
  }
`)

export function GameRoles({ listUsers }: any) {
  const [gameId] = useHashState('game_id', parseAsString)
  const [, closeDialog] = useHashNavigate()

  // Get game data with refetch options to ensure fresh data
  const getData = useQuery(
    getDataQuery,
    { gameId: gameId! },
    {
      skip: !gameId,
    }
  )

  // Get role configuration
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))

  // Get users and roles data
  // TODO: fix to use cache for get roles query

  const listRoleQuery = useQuery(
    getGameRolesQuery,
    { gameIds: [gameId!] },
    {
      skip: !gameId,
      fetchPolicy: 'cache-and-network',
    }
  )
  const roles = useMemo(
    () => listRoleQuery.data?.gameRoles.collection[0].roles || [],
    [listRoleQuery.data?.gameRoles.collection[0].roles]
  )

  // Create form with dynamic defaultValues
  const form = useForm<{
    roles: Array<{
      roleId: string
      members: string[]
    }>
  }>({
    defaultValues: {
      roles: [],
    },
  })

  useEffect(() => {
    if (!roleConfigMapCollection) return

    if (roles.length > 0) {
      form.reset({
        roles: roleConfigMapCollection.map((role) => {
          const roleMemberships = roles.filter((r) => r.id === role.id)
          const memberEmails = roleMemberships.flatMap((rm) => rm.users.map((u) => u.email))

          return {
            roleId: role.id,
            members: memberEmails || [],
          }
        }),
      })
    } else {
      // Reset với giá trị rỗng nếu không có dữ liệu roles
      form.reset({
        roles: roleConfigMapCollection.map((role) => ({
          roleId: role.id,
          members: [],
        })),
      })
    }
  }, [roles, roleConfigMapCollection, gameId])

  // Setup field array for dynamic form fields
  const rolesFieldArray = useFieldArray({
    control: form.control,
    name: 'roles',
  })

  // Create update role mutation
  const [updateGameRoles, updateGameRolesState] = useMutation(updateGameRolesMutation, {
    onSuccess: () => {
      closeDialog()
    },
  })

  // Handle form submission
  const onSubmit = form.handleSubmit((values) => {
    if (!gameId) return
    updateGameRoles({
      forms: [
        {
          gameId: gameId,
          roles: values.roles.map((role) => ({
            id: role.roleId,
            users: role.members,
          })),
        },
      ],
    })
  })

  const getRoleOptions = (roleId: string) => {
    return (
      listUsers
        ?.filter((user: any) => user.team?.roleId === roleId)
        .map((u: any) => ({
          label: u.email,
          value: u.email,
        })) || []
    )
  }

  const renderRoleFields = () => {
    if (!roleConfigMapCollection) {
      return null
    }

    if (rolesFieldArray.fields.length > 0) {
      return rolesFieldArray.fields.map((field, index) => {
        const roleConfigMap = roleConfigMapCollection.find((role) => role.id === field.roleId)!
        return (
          <FormMultiSelectField
            data-testid={`role_field_${field.roleId}`}
            key={field.roleId}
            control={form.control}
            name={`roles.${index}.members`}
            label={`${roleConfigMap.group} ${roleConfigMap.name}`}
            options={getRoleOptions(field.roleId)}
          />
        )
      })
    }

    return roleConfigMapCollection.map((roleConfig, index) => (
      <FormMultiSelectField
        key={roleConfig.id}
        control={form.control}
        name={`roles.${index}.members`}
        label={`${roleConfig.group} ${roleConfig.name}`}
        options={getRoleOptions(roleConfig.id)}
      />
    ))
  }
  return (
    <>
      <DialogHeader>
        <DialogTitle data-testid="manage_roles_dialog_title">Manage Roles</DialogTitle>
        <DialogDescription>{gameId}</DialogDescription>
      </DialogHeader>

      <QueryState query={[getData]}>
        <Loading loading={listRoleQuery.loading}>
          <Form {...form}>
            <FloatingRouteContent>
              <form className="flex flex-col gap-2.5 overflow-hidden" onSubmit={onSubmit}>
                <div className="flex flex-col gap-2.5 overflow-auto pr-3">{renderRoleFields()}</div>

                <div className="flex justify-end mt-4">
                  <Button type="submit" disabled={updateGameRolesState.loading}>
                    {updateGameRolesState.loading ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </FloatingRouteContent>
          </Form>
        </Loading>
      </QueryState>
    </>
  )
}
