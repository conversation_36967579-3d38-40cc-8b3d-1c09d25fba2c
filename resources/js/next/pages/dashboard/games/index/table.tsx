import { routes } from '@/components/location'
import { GameAttributesFragment } from '@/graphql'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import { PlatformIcon } from '@/next/components/sdk/platform_icon'
import {
  TableCellButton,
  DataTable,
  tableHeader,
  tableSelect,
} from '@/next/components/sdk/data_table'
import { Badge } from '@/next/components/ui/badge'
import { useDataTable } from '@/next/hooks/use-data-table'
import { Link } from '@inertiajs/react'
import { createColumnHelper } from '@tanstack/react-table'
import { orderBy } from 'lodash-es'
import { Apple, ChevronDown, Handshake, House, Smartphone, Text, UserCog } from 'lucide-react'
import { parseAsArrayOf, parseAsJson, useQueryState } from 'nuqs'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'
import { But<PERSON> } from '@/next/components/ui/button'
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from '@/next/components/ui/menubar'
import { infoToast } from '@/next/components/sdk/toast/info_toast'
import GamesController from '#controllers/dashboard/game/games_controller'
import { InferPageProps } from '@adonisjs/inertia/types'
import { useServerProp } from '@/components/ssr'
import { cn } from '@/next/lib/utils'
import { UseDataTableFilterFormReturn } from '@/next/hooks/use_data_table_filter_form'

const schema = z.object({
  id: z.string(),
  desc: z.boolean(),
})

const columnHelper = createColumnHelper<GameAttributesFragment>()

type PageProps = InferPageProps<GamesController, 'index'>

interface GameListTableProps {
  collection: GameAttributesFragment[]
  listUsers: any
  slots?: {
    beforeFilter?: React.ReactNode
  }
  form: UseDataTableFilterFormReturn<any>
}

export function GameListTable({
  collection,
  listUsers,
  slots: { beforeFilter } = {},
  form,
}: GameListTableProps) {
  const [openFloatingRoute] = useFloatingRoute()
  const { immediateValues } = form
  const { name, tags } = immediateValues as { name: string; tags: string[] }
  const [sort] = useQueryState('sort', parseAsArrayOf(parseAsJson(schema.parse)).withDefault([]))
  const { t } = useTranslation()
  const canUpdateRole = useServerProp((s: PageProps) => s.canUpdateRole)
  const pageId = useServerProp((s) => s.pageId)

  const rows = useMemo(() => {
    const filtered = collection.filter((game) => {
      const isNameMatch = !name || game.name.toLowerCase().includes(name.toLowerCase())
      const isTagMatch =
        tags.length === 0 ||
        tags.some((tag) => {
          switch (tag) {
            case 'inhouse':
              return game.isInhouse
            case 'partner':
              return !game.isInhouse
            case 'android':
              return game.platform === 'android'
            case 'ios':
              return game.platform === 'ios'
            default:
              return false
          }
        })
      return isNameMatch && isTagMatch
    })

    return orderBy(
      filtered,
      sort.map((s) => s.id),
      sort.map((s) => (s.desc ? 'desc' : 'asc'))
    )
  }, [name, tags, sort])

  const columns = useMemo(() => {
    return [
      tableSelect<GameAttributesFragment>(),
      columnHelper.accessor((g) => g.name, {
        id: 'name',
        header: tableHeader('Name'),
        cell: ({ row }) => {
          return (
            <Link
              data-testid={`game_link_${row.index}`}
              href={routes.dash.games.show(row.original.id)}
              className={cn(
                'flex items-center gap-2 p-1 rounded-md transition-all duration-200',
                'hover:bg-blue-50 hover:shadow-sm dark:hover:bg-blue-900/10',
                'hover:scale-[1.02] hover:translate-x-1'
              )}
              name="game_link"
            >
              <PlatformIcon
                platform={row.original.platform}
                className="transition-transform hover:scale-110"
              />
              <span className="font-medium hover:text-blue-600 dark:hover:text-blue-400">
                {row.original.name}
              </span>
            </Link>
          )
        },
        meta: {
          label: 'Title',
          placeholder: 'Search titles or store id...',
          icon: Text,
        },
        enableSorting: true,
      }),
      columnHelper.accessor(
        (g) => [g.isInhouse ? 'inhouse' : 'partner', g.platform.toLowerCase()],
        {
          id: 'tags',
          header: 'Tags',
          cell: ({ row }) => {
            const tags = [
              row.original.isInhouse ? 'inhouse' : 'partner',
              row.original.platform.toLowerCase(),
            ]
            return (
              <div className="flex items-center gap-1">
                {tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            )
          },
          meta: {
            label: 'Tag',
            variant: 'multiSelect',
            options: [
              { label: 'Inhouse', value: 'inhouse', icon: House },
              { label: 'Partner', value: 'partner', icon: Handshake },
              { label: 'Android', value: 'android', icon: Smartphone },
              { label: 'iOS', value: 'ios', icon: Apple },
            ],
          },
          enableColumnFilter: true,
          enableSorting: true,
        }
      ),
      columnHelper.accessor((row) => row.id, {
        id: 'users',
        header: 'Manage Roles',
        cell: ({ row }) => {
          if (!canUpdateRole) {
            return null
          }

          return (
            <TableCellButton
              onClick={() => openFloatingRoute('roles', { game_id: row.original.id })}
              data-testid={`manage_game_roles_${row.index}`}
            >
              Manage
            </TableCellButton>
          )
        },
        meta: {
          label: 'Member',
          options: listUsers
            ?.filter((user: any) => user.team?.roleId)
            .map((u: any) => {
              return {
                label: u.email,
                value: u.id,
              }
            }),
        },
      }),
    ]
  }, [t, canUpdateRole])

  const { table } = useDataTable({
    data: rows,
    columns: columns,
    pageCount: -1,
    getRowId: (row) => row.id,
    initialState: {
      sorting: [{ id: 'name', desc: true }],
    },
    form,
    meta: {
      pageId,
    } as any,
  })

  const onManageMembers = () => {
    const selectedGamesIds = table.getSelectedRowModel().rows.map((row) => row.original.id)
    if (selectedGamesIds.length === 0) {
      infoToast({
        title: 'No game selected',
        message: 'Please select at least one game to manage members',
      })
      return
    }

    openFloatingRoute('members', { ids: selectedGamesIds.join(',') })
  }

  return (
    <DataTable data-testid="game_list_table" table={table} sticky>
      <DataTableToolbar
        table={table}
        slots={{
          beforeFilter: beforeFilter,
        }}
      >
        {canUpdateRole && (
          <Menubar className="border-none">
            <MenubarMenu>
              <MenubarTrigger asChild>
                <Button size="sm" variant="outline">
                  Bulk Actions
                  <ChevronDown />
                </Button>
              </MenubarTrigger>
              <MenubarContent>
                <MenubarItem onClick={onManageMembers}>
                  <UserCog />
                  Manage members
                </MenubarItem>
              </MenubarContent>
            </MenubarMenu>
          </Menubar>
        )}
      </DataTableToolbar>
    </DataTable>
  )
}
