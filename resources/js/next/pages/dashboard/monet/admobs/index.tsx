import { graphql } from '@/gql'
import { PageConfig } from '@/next/components/sdk/page_config'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { AdmobGameList, gameFormSchema } from './index/_admob_game_list'
import { FormProvider } from 'react-hook-form'
import { AdmobDataTable } from './index/_admob_data_table'
import { format } from 'date-fns'
import { groupBy, thru, uniq } from 'lodash-es'
import { useCallback, useMemo } from 'react'
import { Dimension, mergeAdmobMetrics } from './index/_helpers'
import { admob_v1beta } from '@googleapis/admob'
import { useQuery } from '@/next/components/sdk/graphql'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { useQueryForm } from '@/next/components/sdk/form/use_query_form'
import { parseAsIsoDate, parseAsString } from 'nuqs'
import ReactCountryFlag from 'react-country-flag'
import { FormSelectFieldOption } from '@/next/components/sdk/form/form_select_field'

const gameQuery = graphql(`
  query AdmobGamesQuery {
    admobGames {
      collection
    }
  }
`)

const listMetricQuery = graphql(`
  query AdmobMetrics(
    $gameId: String!
    $baselineDateFrom: Date!
    $baselineDateTo: Date!
    $targetDateFrom: Date!
    $targetDateTo: Date!
    $formats: [String!]!
  ) {
    baseline: admobMetrics(
      where: {
        gameId: $gameId
        dateFrom: $baselineDateFrom
        dateTo: $baselineDateTo
        formats: $formats
      }
    ) {
      mediation
      network
    }

    target: admobMetrics(
      where: {
        gameId: $gameId
        dateFrom: $targetDateFrom
        dateTo: $targetDateTo
        formats: $formats
      }
    ) {
      mediation
      network
    }
  }
`)

export default function Page() {
  const listGame = useQuery(gameQuery, undefined, {
    toastOnError: true,
    errorMessage: 'Failed to load games',
  })

  const form = useQueryForm({
    schema: gameFormSchema,
    syncConfig: {
      'gameId': {
        lazy: true,
        queryKey: 'game',
      },
      'baseline.date.from': {
        lazy: true,
        queryKey: 'bdatefrom',
        parser: parseAsIsoDate,
      },
      'baseline.date.to': {
        lazy: true,
        queryKey: 'bdateto',
        parser: parseAsIsoDate,
      },
      'target.date.from': {
        lazy: true,
        queryKey: 'tdatefrom',
        parser: parseAsIsoDate,
      },
      'target.date.to': {
        lazy: true,
        queryKey: 'tdateto',
        parser: parseAsIsoDate,
      },
      'format': {
        lazy: true,
        queryKey: 'format',
        parser: parseAsString.withDefault('native'),
      },
    },
    formOptions: {
      defaultValues: {
        countries: [],
      },
    },
  })

  const baselineVersion = form.watch('baseline.version')
  const targetVersion = form.watch('target.version')

  const {
    formState: { isValid, isSubmitSuccessful },
    queryState,
    setValue,
  } = form

  // Only enable if we have game data, valid form, and either:
  // 1. Have query params (not fresh form) OR
  // 2. Form was submitted successfully
  // For fully check, we need to check all query params, it is just a quick check to prevent most common behaviour
  const hasQueryParams = Boolean(queryState.gameId)
  const isListMetricEnabled = listGame.data && isValid && (hasQueryParams || isSubmitSuccessful)

  const listMetric = useQuery(
    listMetricQuery,
    {
      gameId: queryState.gameId!,
      baselineDateFrom: thru(queryState.baseline?.date?.from, (d) => d && format(d, 'yyyy-MM-dd')),
      baselineDateTo: thru(queryState.baseline?.date?.to, (d) => d && format(d, 'yyyy-MM-dd')),
      targetDateFrom: thru(queryState?.target?.date?.from, (d) => d && format(d, 'yyyy-MM-dd')),
      targetDateTo: thru(queryState?.target?.date?.to, (d) => d && format(d, 'yyyy-MM-dd')),
      formats: [queryState.format!],
    },
    {
      toastOnError: true,
      errorMessage: 'Failed to load metrics',
      skip: !isListMetricEnabled,
    }
  )

  const onSubmit = useCallback(() => {
    setValue('baseline.version', '')
    setValue('target.version', '')
    setValue('countries', [])
  }, [setValue])

  const [baselineData, targetData] = useMemo(() => {
    if (!listMetric.data) {
      return [{}, {}]
    }

    const { baseline, target } = listMetric.data

    // Extract middle elements (rows) by slicing from index 1 to length-1
    const baselineNetworkRows = baseline.network.slice(1, -1).map((e) => e.row!)
    const baselineMediationRows = baseline.mediation.slice(1, -1).map((e) => e.row!)
    const baselineMetrics = mergeAdmobMetrics(baselineNetworkRows, baselineMediationRows)

    const targetNetworkRows = target.network
      .slice(1, -1)
      .map((e) => e.row! as admob_v1beta.Schema$ReportRow)
    const targetMediationRows = target.mediation
      .slice(1, -1)
      .map((e) => e.row! as admob_v1beta.Schema$ReportRow)
    const targetMetrics = mergeAdmobMetrics(targetNetworkRows, targetMediationRows)

    return [
      groupBy(baselineMetrics, (m) => m.dimensionValues!['DATE'].value),
      groupBy(targetMetrics, (m) => m.dimensionValues!['DATE'].value),
    ]
  }, [listMetric.data, baselineVersion, targetVersion])

  const baselineVersionOptions = useMemo(() => {
    return uniq(
      Object.values(baselineData).flatMap((metrics) =>
        metrics.map((m) => m.dimensionValues![Dimension.Version].value!)
      )
    ).map((v) => ({ label: v, value: v }))
  }, [baselineData])

  const targetVersionOptions = useMemo(() => {
    return uniq(
      Object.values(targetData).flatMap((metrics) =>
        metrics.map((m) => m.dimensionValues![Dimension.Version].value!)
      )
    ).map((v) => ({ label: v, value: v }))
  }, [targetData])

  const countryOptions = useMemo(() => {
    return uniq(
      [...Object.values(baselineData), ...Object.values(targetData)].flatMap((metrics) =>
        metrics.map((m) => {
          return m.dimensionValues![Dimension.Country].value!
        })
      )
    ).map(
      (c): FormSelectFieldOption => ({
        label: c,
        value: c,
        render: (value) => (
          <>
            <ReactCountryFlag countryCode={value} />
            {value}
          </>
        ),
      })
    )
  }, [baselineData, targetData])

  return (
    <BaseLayout>
      <PageConfig title="Admob" breadcrumbs={['Admob']} />

      <FormProvider {...form}>
        <QueryState query={listGame} errorMessage="Failed to get game list">
          <AdmobGameList
            collection={listGame.data?.admobGames?.collection!}
            form={form}
            onSubmit={onSubmit}
            countryOptions={countryOptions}
            baselineVersionOptions={baselineVersionOptions}
            targetVersionOptions={targetVersionOptions}
          />
        </QueryState>

        {isListMetricEnabled && (
          <QueryState query={listMetric} errorMessage="Failed to get metrics data">
            <AdmobDataTable baseline={baselineData} target={targetData} />
          </QueryState>
        )}
      </FormProvider>
    </BaseLayout>
  )
}
