'use client'

import { Cartesian<PERSON>rid, <PERSON>, <PERSON><PERSON>hart, XAxis } from 'recharts'

import { Card, CardContent, CardHeader, CardTitle } from '@/next/components/ui/card'
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/next/components/ui/chart'
import { Metric } from './_helpers'
import { format } from 'date-fns'

const chartConfig = {
  ...Object.fromEntries(
    Object.values(Metric).flatMap((metricName, index) => [
      [
        `baselineMetrics.${metricName}`,
        {
          label: metricName,
          color: `var(--chart-${index + 1})`,
        },
      ],
      [
        `targetMetrics.${metricName}`,
        {
          label: metricName,
          color: `var(--chart-${index + 1})`,
        },
      ],
    ])
  ),
} satisfies ChartConfig

console.log(chartConfig)

export function AdmobDataChart({
  rows,
  baselineVersion,
  targetVersion,
}: {
  rows: {
    baselineDate: Date
    targetDate: Date
    baselineMetrics: Record<string, number | undefined>
    targetMetrics: Record<string, number | undefined>
  }[]
  baselineVersion: string | undefined
  targetVersion: string | undefined
}) {
  return (
    <div className="grid grid-cols-5">
      <div className="col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>{baselineVersion} Chart</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer className="min-h-100px" config={chartConfig}>
              <LineChart
                accessibilityLayer
                data={rows}
                margin={{
                  left: 12,
                  right: 12,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="baselineDate"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => format(value, 'yyyy-MM-dd')}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
                {Object.values(Metric).map((metricName, index) => {
                  return (
                    <Line
                      dataKey={`baselineMetrics.${metricName}`}
                      type="monotone"
                      stroke={`var(--chart-${index + 1})`}
                      strokeWidth={2}
                      dot={false}
                    />
                  )
                })}
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <div className="col-span-2 col-start-4">
        <Card>
          <CardHeader>
            <CardTitle>{targetVersion} Chart</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer className="min-h-100px" config={chartConfig}>
              <LineChart
                accessibilityLayer
                data={rows}
                margin={{
                  left: 12,
                  right: 12,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="targetDate"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => format(value, 'yyyy-MM-dd')}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
                {Object.values(Metric).map((metricName, index) => {
                  return (
                    <Line
                      dataKey={`targetMetrics.${metricName}`}
                      type="monotone"
                      stroke={`var(--chart-${index + 1})`}
                      strokeWidth={2}
                      dot={false}
                    />
                  )
                })}
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
