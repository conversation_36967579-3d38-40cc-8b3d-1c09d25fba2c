import { admob_v1beta } from '@googleapis/admob'
import { useMemo } from 'react'
import { sumBy } from 'lodash-es'
import { differenceInDays, addDays, format } from 'date-fns'
import { useFormContext } from 'react-hook-form'
import { createColumnHelper } from '@tanstack/react-table'

import { useDataTable } from '@/next/hooks/use-data-table'
import { DataTable } from '@/next/components/data-table/data-table'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { DataTableSortList } from '@/next/components/data-table/data-table-sort-list'
import { safeDivide } from '#utils/math'
import { formatNumber, formatPercentage } from '@/components/typography'

import { GameFormSchema } from './_admob_game_list'
import { Dimension, formatValue } from './_helpers'
import { NoContent } from '@/next/components/sdk/loading/no_content'
import { AdmobDataChart } from './_admob_data_chart'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

const metrics = [
  ['network', 'CLICKS', 'integerValue'],
  ['network', 'ESTIMATED_EARNINGS', 'microsValue'],
  ['network', 'IMPRESSIONS', 'integerValue'],
  ['network', 'IMPRESSION_CTR', 'doubleValue'],
  ['network', 'MATCHED_REQUESTS', 'integerValue'],
  ['network', 'SHOW_RATE', 'doubleValue'],
  ['mediation', 'OBSERVED_ECPM', 'microsValue'],
]

export function AdmobDataTable({
  baseline,
  target,
}: {
  baseline: Record<string, admob_v1beta.Schema$ReportRow[]>
  target: Record<string, admob_v1beta.Schema$ReportRow[]>
}) {
  const form = useFormContext<GameFormSchema>()

  const baselineDate = form.watch('baseline.date')
  const baselineVersion = form.watch('baseline.version')
  const countries = form.watch('countries')
  const targetDate = form.watch('target.date')
  const targetVersion = form.watch('target.version')

  const baselineRows = useMemo(() => {
    return Object.fromEntries(
      Object.entries(baseline).map(([date, metrics]) => {
        return [
          date,
          metrics.filter((m) => {
            const isCountryMatch =
              countries.length === 0 ||
              // @ts-ignore dynamic metric
              countries.includes(m.dimensionValues![Dimension.Country].value)

            const isVersionMatch =
              baselineVersion === undefined ||
              // @ts-ignore dynamic metric
              m.dimensionValues![Dimension.Version].value === baselineVersion

            return isCountryMatch && isVersionMatch
          }),
        ]
      })
    )
  }, [baselineVersion, countries, baseline])

  const targetRows = useMemo(() => {
    return Object.fromEntries(
      Object.entries(target).map(([date, metrics]) => {
        return [
          date,
          metrics.filter((m) => {
            const isCountryMatch =
              countries.length === 0 ||
              // @ts-ignore dynamic metric
              countries.includes(m.dimensionValues![Dimension.Country].value)

            const isVersionMatch =
              targetVersion === undefined ||
              // @ts-ignore dynamic metric
              m.dimensionValues![Dimension.Version].value === targetVersion

            return isCountryMatch && isVersionMatch
          }),
        ]
      })
    )
  }, [targetVersion, countries, target])

  const tableRows = useMemo(() => {
    const dateCount =
      Math.max(
        Math.abs(differenceInDays(baselineDate.from, baselineDate.to)),
        Math.abs(differenceInDays(targetDate.from, targetDate.to))
      ) + 1

    return Array.from({ length: dateCount }, (_, i) => {
      const rowBaselineDate = addDays(baselineDate.from, i)
      const rowTargetDate = addDays(targetDate.from, i)

      const baselineDayMetrics = baselineRows[format(rowBaselineDate, 'yyyyMMdd')]
      const targetDayMetrics = targetRows[format(rowTargetDate, 'yyyyMMdd')]

      return {
        baselineDate: rowBaselineDate,
        targetDate: rowTargetDate,
        baselineMetrics: Object.fromEntries(
          metrics.map(([_type, metricName, valueType]) => [
            metricName,
            formatValue(
              sumBy(baselineDayMetrics, (m) => {
                // @ts-ignore dynamic metric
                return Number(m.metricValues?.[metricName]?.[valueType] ?? '0')
              }),
              valueType
            ),
          ])
        ),
        targetMetrics: Object.fromEntries(
          metrics.map(([_type, metricName, valueType]) => [
            metricName,
            formatValue(
              sumBy(targetDayMetrics, (m) => {
                // @ts-ignore dynamic metric
                return Number(m.metricValues?.[metricName]?.[valueType] ?? '0')
              }),
              valueType
            ),
          ])
        ),
      }
    })
  }, [baselineDate, targetDate, baselineRows, targetRows])

  const columnHelper = useMemo(() => createColumnHelper<(typeof tableRows)[0]>(), [])

  const { table } = useDataTable({
    data: tableRows,
    form: useDataTableFilterForm({}),
    columns: [
      columnHelper.group({
        header: 'Date',
        columns: [
          {
            accessorFn: (row) => format(row.baselineDate, 'yyyy-MM-dd'),
            header: baselineVersion,
            id: 'baseline.date',
          },
          {
            accessorFn: (row) => format(row.targetDate, 'yyyy-MM-dd'),
            id: 'target.date',
            header: targetVersion,
          },
        ],
      }),
      ...metrics.map(([_type, metricName]) => {
        return columnHelper.group({
          header: metricName,
          columns: [
            {
              accessorFn: (row) => row.baselineMetrics[metricName],
              header: baselineVersion,
              id: `baseline.${metricName}`,
              cell: ({ getValue }) => formatNumber(getValue()),
            },
            {
              accessorFn: (row) => {
                return row.targetMetrics[metricName]
              },
              cell: ({ row, getValue }) => {
                const baselineValue = Number(row.getValue('baseline.' + metricName) || 0)
                const targetValue = Number(row.getValue('target.' + metricName) || 0)
                const diff = safeDivide(targetValue - baselineValue, baselineValue)
                return (
                  <div className="flex items-center gap-1">
                    <span>{formatNumber(getValue())}</span>

                    <span>({diff > 0 ? '⬆️' : '⬇️'}</span>
                    <span>{formatPercentage(Math.abs(diff))})</span>
                  </div>
                )
              },
              header: targetVersion,
              id: `target.${metricName}`,
            },
          ],
        })
      }),
    ],
    pageCount: 1,
  })

  return (
    <NoContent empty={!baselineVersion || !targetVersion} renderEmpty={renderNoVersion}>
      <div className="mb-1">
        <AdmobDataChart
          rows={tableRows}
          baselineVersion={baselineVersion}
          targetVersion={targetVersion}
        />
      </div>

      <DataTable table={table}>
        <DataTableToolbar table={table}>
          <DataTableSortList table={table} />
        </DataTableToolbar>
      </DataTable>
    </NoContent>
  )
}

function renderNoVersion() {
  return <div className="text-center">Please select version</div>
}
