import { admob_v1beta } from '@googleapis/admob'

export enum Dimension {
  App = 'APP',
  AdUnit = 'AD_UNIT',
  Version = 'APP_VERSION_NAME',
  Country = 'COUNTRY',
  Date = 'DATE',
  Format = 'FORMAT',
}

export enum Metric {
  Clicks = 'CLICKS',
  EstimatedEarnings = 'ESTIMATED_EARNINGS',
  Impressions = 'IMPRESSIONS',
  ImpressionCtr = 'IMPRESSION_CTR',
  MatchedRequests = 'MATCHED_REQUESTS',
  ShowRate = 'SHOW_RATE',
  ObservedEcpm = 'OBSERVED_ECPM',
}

export function mergeAdmobMetrics(
  networkRows: admob_v1beta.Schema$ReportRow[],
  mediationRows: admob_v1beta.Schema$ReportRow[]
) {
  const metrics = new Map<string, admob_v1beta.Schema$ReportRow>()

  const dimKeys = Object.values(Dimension)

  networkRows.forEach((row) => {
    const dims = row.dimensionValues!
    const key = dimKeys.map((k) => dims[k].value).join('|')
    metrics.set(key, row)
  })

  mediationRows.forEach((row) => {
    const dims = row.dimensionValues!
    const key = dimKeys.map((k) => dims[k].value).join('|')
    if (metrics.has(key)) {
      const networkRow = metrics.get(key)!
      metrics.set(key, {
        ...row,
        metricValues: {
          ...networkRow.metricValues,
          ...row.metricValues,
        },
      })
    } else {
      metrics.set(key, row)
    }
  })

  return Array.from(metrics.values())
}

export function formatValue(value: number, valueType: string) {
  switch (valueType) {
    case 'integerValue':
      return Number(value)

    case 'microsValue':
      return Number(value) / 1_000_000

    case 'doubleValue':
      return Number(value)

    default:
      break
  }
}
