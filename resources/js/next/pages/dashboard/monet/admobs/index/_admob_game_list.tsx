import type { admob_v1beta } from '@googleapis/admob'
import { UseFormReturn } from 'react-hook-form'
import { z } from 'zod'
import { useMemo } from 'react'

import { Button } from '@/next/components/ui/button'
import { Form } from '@/next/components/ui/form'
import { FormDateRangePickerField } from '@/next/components/sdk/form/form_date_range_picker_field'
import { Card, CardContent, CardHeader, CardTitle } from '@/next/components/ui/card'
import {
  FormSelectField,
  FormSelectFieldOption,
} from '@/next/components/sdk/form/form_select_field'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { PlatformIcon } from '@/next/components/sdk/platform_icon'

export const gameFormSchema = z.object({
  gameId: z.string(),
  format: z.string(),
  baseline: z.object({
    date: z.object({
      from: z.date(),
      to: z.date(),
    }),
    version: z.string().optional(),
  }),
  target: z.object({
    date: z.object({
      from: z.date(),
      to: z.date(),
    }),
    version: z.string().optional(),
  }),
  countries: z.array(z.string()),
})

export type GameFormSchema = z.infer<typeof gameFormSchema>

export function AdmobGameList({
  collection,
  form,
  countryOptions,
  baselineVersionOptions,
  targetVersionOptions,
  onSubmit,
}: {
  collection: NonNullable<admob_v1beta.Schema$ListAppsResponse['apps']>
  form: UseFormReturn<GameFormSchema>
  countryOptions: FormSelectFieldOption[]
  baselineVersionOptions: FormSelectFieldOption[]
  targetVersionOptions: FormSelectFieldOption[]
  onSubmit: (data: GameFormSchema) => void
}) {
  const gameOptions = useMemo(() => {
    return collection.map(
      (game): FormSelectFieldOption => ({
        value: game.appId!,
        label: `${game.manualAppInfo?.displayName!} | ${game.platform?.toLowerCase()}`,
        render: (_value, label) => (
          <div className="flex items-center gap-2">
            <PlatformIcon platform={game.platform!} />
            {label}
          </div>
        ),
      })
    )
  }, [collection])

  const formatOptions = useMemo<FormSelectFieldOption[]>(() => {
    return [
      { label: 'Banner', value: 'banner' },
      {
        label: 'Interstitial',
        value: 'interstitial',
      },
      {
        label: 'Rewarded',
        value: 'rewarded',
      },
      {
        label: 'Native',
        value: 'native',
      },
      {
        label: 'App Open',
        value: 'app_open',
      },
    ]
  }, [])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid auto-rows-min gap-4 md:grid-cols-10">
          <div className="col-span-4">
            <FormSelectField
              control={form.control}
              name="gameId"
              label="Game"
              placeholder="Select game"
              options={gameOptions}
              searchHint="Search game"
              emptyHint="No game found"
            />
          </div>

          <div className="col-span-2">
            <FormSelectField
              control={form.control}
              name="format"
              label="Format"
              placeholder="Select format"
              options={formatOptions}
              searchHint="Search format"
              emptyHint="No format found"
            />
          </div>

          <div className="col-span-4" />

          <div className="col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>Baseline</CardTitle>
              </CardHeader>

              <CardContent>
                <FormDateRangePickerField
                  label="Date"
                  control={form.control}
                  name="baseline.date"
                  className="mb-3"
                />
                <FormSelectField
                  placeholder="Select version"
                  label="Version"
                  control={form.control}
                  name="baseline.version"
                  options={baselineVersionOptions}
                />
              </CardContent>
            </Card>
          </div>

          <div className="col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>Target</CardTitle>
              </CardHeader>

              <CardContent>
                <FormDateRangePickerField
                  label="Date"
                  control={form.control}
                  name="target.date"
                  className="mb-3"
                />
                <FormSelectField
                  placeholder="Select version"
                  label="Version"
                  control={form.control}
                  name="target.version"
                  options={targetVersionOptions}
                />
              </CardContent>
            </Card>
          </div>

          <div className="col-span-2">
            <FormMultiSelectField
              control={form.control}
              name="countries"
              label="Countries"
              placeholder="Select countries"
              options={countryOptions}
              searchHint="Search countries"
              emptyHint="No countries found"
            />
          </div>
        </div>

        <div className="flex items-end justify-start">
          <Button type="submit">Apply</Button>
        </div>
      </form>
    </Form>
  )
}
