import { TableCellButton } from '@/next/components/sdk/data_table'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import { ConfirmationPopover } from '@/next/components/sdk/confirmation_popover'
import { CirclePlus, CheckCheck, Trash2 } from 'lucide-react'
import { AnyHandler } from '@/next/components/sdk/types'

export function BudgetRequestTableToolbar({
  onDelete,
  deleting,
  onProcess,
  processing,
}: {
  onDelete: AnyHandler
  deleting: boolean
  onProcess: AnyHandler
  processing: boolean
}) {
  const [navigate] = useFloatingRoute()

  return (
    <>
      <TableCellButton variant="outline" onClick={() => navigate('create')}>
        <CirclePlus />
        Create
      </TableCellButton>
      <TableCellButton variant="default" onClick={onProcess} disabled={processing}>
        <CheckCheck />
        Process
      </TableCellButton>
      <ConfirmationPopover
        trigger={
          <TableCellButton variant="destructive" disabled={deleting}>
            <Trash2 />
            Delete
          </TableCellButton>
        }
        onConfirm={onDelete}
        isSubmitting={deleting}
        title="Delete selected items"
        confirmText="Are you sure you want to delete the selected items?"
      />
    </>
  )
}
