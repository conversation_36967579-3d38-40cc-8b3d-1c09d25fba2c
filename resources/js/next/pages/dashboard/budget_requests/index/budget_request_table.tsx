import { formatCurrency } from '@/components/typography'
import {
  BudgetRequestAttributesFragment,
  GameAttributesFragment,
  graphql,
  PageInfoAttributesFragment,
  WorkflowAttributesFragment,
  WorkflowStepActionAttributesFragment,
} from '@/graphql'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { DataTable, TableCellButton } from '@/next/components/sdk/data_table'
import { PlatformIcon } from '@/next/components/sdk/platform_icon'
import { tableSelect } from '@/next/components/sdk/data_table'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/next/components/ui/tooltip'
import { useDataTable } from '@/next/hooks/use-data-table'
import { createColumnHelper } from '@tanstack/react-table'
import { format } from 'date-fns'
import { useMemo } from 'react'
import { BudgetRequestTableToolbar } from './budget_request_table_toolbar'
import { PencilLine } from 'lucide-react'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import { useServerProp } from '@/components/ssr'
import { AnyHandler } from '@/next/components/sdk/types'
import { useMutation } from '@/next/components/sdk/graphql'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

const columnHelper = createColumnHelper<BudgetRequestAttributesFragment>()

const updateBudgetRequestsStateMutation = graphql(`
  mutation BudgetRequestIndex_UpdateBudgetRequestsState($forms: [UpdateBudgetRequestStateForm!]!) {
    updateBudgetRequestsState(forms: $forms) {
      ...BudgetRequestAttributes
    }
  }
`)

const deleteBudgetRequestsMutation = graphql(`
  mutation BudgetRequestIndex_DeleteBudgetRequests($ids: [Int!]!) {
    deleteBudgetRequests(where: { ids: $ids })
  }
`)

export enum BudgetRequestStep {
  All = 'all',
  InComplete = 'in-complete',
  Done = 'done',
}

export function BudgetRequestTable({
  collection,
  pageInfo,
  workflows,
  games,
  onMutateSuccess,
  steps,
}: {
  collection: BudgetRequestAttributesFragment[]
  pageInfo: PageInfoAttributesFragment
  workflows: WorkflowAttributesFragment[]
  games: GameAttributesFragment[]
  onMutateSuccess: AnyHandler
  steps: WorkflowStepActionAttributesFragment[]
}) {
  const onSuccess = () => {
    onMutateSuccess()
    table.resetRowSelection()
  }

  const [updateBudgetRequestsState, updateBudgetRequestsStateState] = useMutation(
    updateBudgetRequestsStateMutation,
    {
      onSuccess,
    }
  )

  const [deleteBudgetRequests, deleteBudgetRequestsState] = useMutation(
    deleteBudgetRequestsMutation,
    {
      onSuccess,
    }
  )

  const [navigate] = useFloatingRoute()
  const user = useServerProp((s) => s.user)

  const onAction = (row: BudgetRequestAttributesFragment) => {
    return updateBudgetRequestsState({
      forms: [
        {
          id: row.id,
          stepId: row.stepId + 1,
          lastAction: row.step.action,
        },
      ],
    })
  }

  const onAlternateAction = async (row: BudgetRequestAttributesFragment) => {
    await updateBudgetRequestsState({
      forms: [
        {
          id: row.id,
          stepId: row.stepId,
          lastAction: row.step.alternateAction,
        },
      ],
    })
  }

  const onBatchDelete = async () => {
    await deleteBudgetRequests({
      ids: table.getSelectedRowModel().flatRows.map((r) => r.original.id),
    })
  }

  const onBatchProcess = async () => {
    const requests = table.getSelectedRowModel().flatRows.map((r) => r.original)

    await updateBudgetRequestsState({
      forms: requests.map((r) => ({
        id: r.id,
        stepId: r.stepId + 1,
        lastAction: r.step.action,
      })),
    })
  }

  const columns = useMemo(() => {
    return [
      tableSelect<BudgetRequestAttributesFragment>(),
      columnHelper.accessor((r) => r.workflow!.name, {
        id: 'workflow',
        header: 'Type',
        enableColumnFilter: true,
        meta: {
          label: 'Type',
          variant: 'multiSelect',
          options: workflows.map((w) => ({
            value: w.id.toString(),
            label: w.name,
          })),
        },
      }),
      columnHelper.accessor((r) => (r.stepId >= 0 ? r.step.name : 'Done'), {
        id: 'step',
        header: 'Step',
        enableColumnFilter: true,
        meta: {
          label: 'Step',
          variant: 'select',
          options: [
            {
              label: 'Show All',
              value: BudgetRequestStep.All,
            },
            {
              label: 'Done Only',
              value: BudgetRequestStep.Done,
            },
            {
              label: 'In-Complete Only',
              value: BudgetRequestStep.InComplete,
            },
          ],
        },
      }),
      columnHelper.accessor((r) => r.game!.name, {
        id: 'game',
        header: 'Game',
        cell: ({ row }) => {
          const r = row.original!
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <PlatformIcon platform={r.game.platform} />
                    <span>{r.game.name}</span>
                  </div>
                </TooltipTrigger>

                <TooltipContent>
                  <p>{r.game.id}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )
        },
        enableColumnFilter: true,
        meta: {
          label: 'Game',
          variant: 'multiSelect',
          options: games.map((g) => ({
            value: g.id,
            label: g.name,
          })),
        },
      }),
      columnHelper.accessor('amount', {
        id: 'amount',
        header: 'Amount',
        cell: ({ getValue }) => formatCurrency(getValue()),
        meta: {
          label: 'Amount',
        },
      }),
      columnHelper.accessor('description', {
        id: 'description',
        header: 'Description',
        meta: {
          label: 'Description',
        },
      }),
      columnHelper.accessor('expirationDate', {
        id: 'expirationDate',
        header: 'Expiration Date',
        meta: {
          label: 'Expiration Date',
        },
      }),
      columnHelper.accessor((r) => (r.stepId >= 0 ? r.lastAction : 'Done'), {
        id: 'status',
        header: 'Status',
        enableColumnFilter: true,
        meta: {
          label: 'Status',
          variant: 'multiSelect',
          options: steps.map((s) => ({
            value: s.action,
            label: s.action,
          })),
        },
      }),
      columnHelper.accessor((r) => r.step.assignee.fullName, {
        id: 'pic',
        header: 'PIC',
        meta: {
          label: 'PIC',
        },
      }),
      columnHelper.accessor((r) => format(new Date(r.createdAt), 'yyyy-MM-dd'), {
        id: 'action',
        header: 'Action',
        cell: ({ row }) => {
          const r = row.original!
          return (
            r.stepId >= 0 && (
              <div className="flex gap-2">
                <TableCellButton variant="default" onClick={() => onAction(r)}>
                  {r.step.action}
                </TableCellButton>
                <TableCellButton variant="destructive" onClick={() => onAlternateAction(r)}>
                  {r.step.alternateAction}
                </TableCellButton>
              </div>
            )
          )
        },
        meta: {
          label: 'Action',
        },
      }),
      columnHelper.accessor((r) => r.createdBy.fullName, {
        id: 'createdBy',
        header: 'Created By',
        meta: {
          label: 'Created By',
        },
      }),
      columnHelper.accessor((r) => format(new Date(r.createdAt), 'yyyy-MM-dd'), {
        id: 'cdate',
        header: 'Created At',
        enableColumnFilter: true,
        meta: {
          label: 'Creation Date',
          variant: 'dateRange',
        },
      }),
      columnHelper.accessor(() => 'edit', {
        id: 'edit',
        header: 'Edit',
        meta: {
          label: 'Edit',
        },
        cell: ({ row }) => {
          const r = row.original!
          return (
            r.stepId === 0 &&
            r.createdBy.id === user.id && (
              <TableCellButton
                variant="link"
                onClick={() => navigate('edit', { id: r.id.toString() })}
              >
                <PencilLine />
                Edit
              </TableCellButton>
            )
          )
        },
      }),
    ]
  }, [
    deleteBudgetRequests,
    deleteBudgetRequestsState.loading,
    updateBudgetRequestsState,
    updateBudgetRequestsStateState.loading,
    workflows,
    games,
    steps,
    user,
  ])

  const { table } = useDataTable({
    columns,
    form: useDataTableFilterForm({}),
    data: collection,
    pageCount: pageInfo.lastPage,
    initialState: {
      pagination: {
        pageIndex: pageInfo.currentPage - 1,
        pageSize: pageInfo.perPage,
      },
    },
  })

  return (
    <DataTable table={table} sticky>
      <DataTableToolbar table={table}>
        <BudgetRequestTableToolbar
          deleting={deleteBudgetRequestsState.loading}
          onDelete={onBatchDelete}
          onProcess={onBatchProcess}
          processing={updateBudgetRequestsStateState.loading}
        />
      </DataTableToolbar>
    </DataTable>
  )
}
