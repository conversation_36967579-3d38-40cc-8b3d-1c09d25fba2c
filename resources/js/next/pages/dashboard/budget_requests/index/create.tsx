import { GameAttributesFragment, graphql, WorkflowAttributesFragment } from '@/graphql'
import { FloatingRouteContent, useFloatingRoute } from '@/next/components/sdk/floating_route'
import { FormDatePickerField } from '@/next/components/sdk/form/form_date_picker_field'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { useMutation } from '@/next/components/sdk/graphql'
import { Button } from '@/next/components/ui/button'
import { DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { Form } from '@/next/components/ui/form'
import { zodResolver } from '@hookform/resolvers/zod'
import { format } from 'date-fns'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const schema = z.object({
  description: z.string().trim().optional().nullable(),
  amount: z.preprocess((v) => Number(v), z.number().positive()),
  expirationDate: z.date(),
  workflowId: z.string(),
  gameId: z.string().trim(),
})

const createBudgetRequestMutation = graphql(`
  mutation BudgetRequestIndex_CreateBudgetRequest($form: CreateBudgetRequestForm!) {
    createBudgetRequest(form: $form) {
      ...BudgetRequestAttributes
    }
  }
`)

export function CreateBudgetRequestPage({
  onSuccess,
  workflows,
  games,
}: {
  onSuccess: () => any
  workflows: WorkflowAttributesFragment[]
  games: GameAttributesFragment[]
}) {
  const [_, closeFloatingRoute] = useFloatingRoute()

  const form = useForm({
    defaultValues: {
      description: '',
      amount: 0,
      expirationDate: new Date(),
      workflowId: workflows[0]?.id?.toString(),
      gameId: games[0]?.id,
    },
    resolver: zodResolver(schema),
  })

  const [createBudgetRequest, createBudgetRequestState] = useMutation(createBudgetRequestMutation, {
    onSuccess: () => {
      onSuccess()
      closeFloatingRoute()
    },
  })

  const onSubmit = form.handleSubmit((formData) => {
    return createBudgetRequest({
      form: {
        workflowId: Number(formData.workflowId),
        gameId: formData.gameId,
        description: formData.description,
        amount: formData.amount,
        expirationDate: format(formData.expirationDate, 'yyyy-MM-dd'),
      },
    })
  })

  const amount = form.watch('amount')
  console.log('amount', amount)

  return (
    <>
      <DialogHeader>
        <DialogTitle>Create Budget Request</DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <FloatingRouteContent>
          <form className="flex flex-col gap-4" onSubmit={onSubmit}>
            <div className="flex flex-col gap-4 overflow-auto">
              <FormSelectField
                control={form.control}
                name="workflowId"
                label="Type"
                options={workflows.map((w) => ({
                  value: w.id.toString(),
                  label: w.name,
                }))}
                placeholder="Select request type"
              />

              <FormSelectField
                control={form.control}
                name="gameId"
                label="Game"
                options={games.map((g) => ({
                  value: g.id,
                  label: g.name,
                }))}
                placeholder="Select game"
              />

              <FormInputField control={form.control} name="amount" label="Amount" type="number" />

              <FormInputField control={form.control} name="description" label="Description" />

              <FormDatePickerField
                control={form.control}
                name="expirationDate"
                label="Expiration Date"
                type="date"
              />
            </div>

            <Button
              type="submit"
              disabled={createBudgetRequestState.loading || form.formState.isSubmitting}
            >
              Save
            </Button>
          </form>
        </FloatingRouteContent>
      </Form>
    </>
  )
}
