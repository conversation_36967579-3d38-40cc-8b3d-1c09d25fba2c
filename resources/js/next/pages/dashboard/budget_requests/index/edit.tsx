import { BudgetRequestAttributesFragment, graphql } from '@/graphql'
import { FloatingRouteContent, useFloatingRoute } from '@/next/components/sdk/floating_route'
import { FormDatePickerField } from '@/next/components/sdk/form/form_date_picker_field'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { useMutation } from '@/next/components/sdk/graphql'
import { useHashState } from '@/next/components/sdk/hash_link'
import { Button } from '@/next/components/ui/button'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { Form } from '@/next/components/ui/form'
import { zodResolver } from '@hookform/resolvers/zod'
import { format } from 'date-fns'
import { parseAsInteger } from 'nuqs'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

export function EditBudgetRequestPage({
  collection,
  onSuccess,
}: {
  collection: BudgetRequestAttributesFragment[]
  onSuccess: () => any
}) {
  const [id] = useHashState('id', parseAsInteger)
  const budgetRequest = collection.find((r) => r.id === id)
  const [_, closeFloatingRoute] = useFloatingRoute()

  if (!budgetRequest) {
    return null
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle>Edit Budget Request</DialogTitle>
        <DialogDescription>ID: {id}</DialogDescription>
      </DialogHeader>

      <EditForm
        record={budgetRequest}
        onSuccess={() => {
          onSuccess()
          closeFloatingRoute()
        }}
      />
    </>
  )
}

const schema = z.object({
  description: z.string().trim().optional().nullable(),
  amount: z.preprocess((v) => Number(v), z.number().positive()),
  expirationDate: z.date(),
  workflowName: z.any(),
  gameName: z.any(),
})

const updateBudgetRequestMutation = graphql(`
  mutation BudgetRequestIndex_UpdateBudgetRequests($form: UpdateBudgetRequestForm!) {
    updateBudgetRequests(forms: [$form]) {
      ...BudgetRequestAttributes
    }
  }
`)

function EditForm({
  record,
  onSuccess,
}: {
  record: BudgetRequestAttributesFragment
  onSuccess: () => any
}) {
  const form = useForm({
    defaultValues: {
      workflowName: record.workflow.name,
      description: record.description,
      amount: record.amount,
      expirationDate: new Date(record.expirationDate),
      gameName: record.game.name,
    },
    resolver: zodResolver(schema),
  })

  const [updateBudgetRequest, updateBudgetRequestState] = useMutation(updateBudgetRequestMutation, {
    onSuccess,
  })

  const onSubmit = form.handleSubmit((formData) => {
    return updateBudgetRequest({
      form: {
        id: record.id,
        description: formData.description,
        amount: formData.amount,
        expirationDate: format(formData.expirationDate, 'yyyy-MM-dd'),
      },
    })
  })

  return (
    <Form {...form}>
      <FloatingRouteContent>
        <form className="flex flex-col gap-4" onSubmit={onSubmit}>
          <div className="flex flex-col gap-4 overflow-auto">
            <FormInputField control={form.control} name="workflowName" label="Type" disabled />

            <FormInputField control={form.control} name="gameName" label="Game" disabled />

            <FormInputField control={form.control} name="amount" label="Amount" type="number" />

            <FormInputField control={form.control} name="description" label="Description" />

            <FormDatePickerField
              control={form.control}
              name="expirationDate"
              label="Expiration Date"
              type="date"
            />
          </div>

          <Button
            type="submit"
            disabled={updateBudgetRequestState.loading || form.formState.isSubmitting}
          >
            Save
          </Button>
        </form>
      </FloatingRouteContent>
    </Form>
  )
}
