import { graphql } from '@/gql'
import { useQuery } from '@/next/components/sdk/graphql'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { PageConfig } from '@/next/components/sdk/page_config'
import { addDays, format, subDays } from 'date-fns'
import { BudgetRequestStep, BudgetRequestTable } from './index/budget_request_table'
import { useServerProp } from '@/components/ssr'
import {
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsTimestamp,
  useQueryState,
} from 'nuqs'
import { useDebounce } from '@uidotdev/usehooks'
import { useMemo } from 'react'
import { BudgetRequestIndex_GetBudgetRequestsQueryVariables, FilterOperator } from '@/graphql'
import { FloatingRoute } from '@/next/components/sdk/floating_route'
import { CreateBudgetRequestPage } from './index/create'
import { EditBudgetRequestPage } from './index/edit'
import { optionalFilter } from '@/next/utils/filter_utils'

graphql(`
  fragment WorkflowStepActionAttributes on WorkflowStepAction {
    action
  }
`)

const getInitialDataQuery = graphql(`
  query BudgetRequestIndex_GetInitialData {
    games(where: {}, offset: { page: 1, perPage: 9999 }) {
      collection {
        ...GameAttributes
      }
    }

    workflows {
      collection {
        ...WorkflowAttributes
      }
    }

    workflowStepActions {
      collection {
        ...WorkflowStepActionAttributes
      }
    }
  }
`)

const getBudgetRequestsQuery = graphql(`
  query BudgetRequestIndex_GetBudgetRequests($where: BudgetRequestsWhere!, $offset: Offset) {
    budgetRequests(where: $where, offset: $offset) {
      collection {
        ...BudgetRequestAttributes
      }
      pageInfo {
        ...PageInfoAttributes
      }
    }
  }
`)

export default function Page() {
  const getInitialData = useQuery(getInitialDataQuery)

  const user = useServerProp((s) => s.user)

  const [perPage] = useQueryState('perPage', parseAsInteger.withDefault(50))
  const [page] = useQueryState('page', parseAsInteger.withDefault(1))
  const [workflowIds] = useQueryState('workflow', parseAsArrayOf(parseAsInteger).withDefault([]))
  const [gameIds] = useQueryState('game', parseAsArrayOf(parseAsString).withDefault([]))
  const [lastActions] = useQueryState('status', parseAsArrayOf(parseAsString).withDefault([]))
  const [creationDateRange] = useQueryState(
    'cdate',
    parseAsArrayOf(parseAsTimestamp).withDefault(
      useMemo(() => [subDays(new Date(), 30), addDays(new Date(), 1)], [])
    )
  )
  const [steps] = useQueryState(
    'step',
    parseAsArrayOf(parseAsString).withDefault(useMemo(() => [BudgetRequestStep.InComplete], []))
  )

  const where = useDebounce(
    useMemo<BudgetRequestIndex_GetBudgetRequestsQueryVariables['where']>(() => {
      return {
        createdAt: {
          operator: FilterOperator.Between,
          values: [
            format(creationDateRange[0] || subDays(new Date(), 30), 'yyyy-MM-dd'),
            format(creationDateRange[1] || addDays(new Date(), 1), 'yyyy-MM-dd'),
          ],
        },
        workflowId: optionalFilter({
          operator: FilterOperator.In,
          values: workflowIds,
        }),
        gameId: optionalFilter({
          operator: FilterOperator.In,
          values: gameIds,
        }),
        assigneeId: user.id,
        lastAction: optionalFilter({
          operator: FilterOperator.In,
          values: lastActions,
        }),
        stepId: (function stepParamToFilter() {
          switch (steps[0]) {
            case BudgetRequestStep.Done:
              return {
                operator: FilterOperator.Lt,
                values: [0],
              }

            case BudgetRequestStep.All:
              return undefined

            case BudgetRequestStep.InComplete:
            default:
              return {
                operator: FilterOperator.Gte,
                values: [0],
              }
          }
        })(),
      }
    }, [...steps, user.id, ...workflowIds, ...gameIds, ...lastActions, ...creationDateRange]),
    1000
  )

  console.log(where)

  const vars = useMemo<BudgetRequestIndex_GetBudgetRequestsQueryVariables>(
    () => ({
      where,
      offset: {
        page,
        perPage,
      },
    }),
    [where, page, perPage]
  )

  const getBudgetRequests = useQuery(getBudgetRequestsQuery, vars)

  return (
    <BaseLayout>
      <PageConfig title="Budget Requests" breadcrumbs={['Budget Requests']} />

      <QueryState query={getInitialData}>
        <QueryState query={getBudgetRequests}>
          <BudgetRequestTable
            collection={getBudgetRequests.data?.budgetRequests?.collection!}
            pageInfo={getBudgetRequests.data?.budgetRequests?.pageInfo!}
            workflows={getInitialData.data?.workflows?.collection!}
            games={getInitialData.data?.games?.collection!}
            onMutateSuccess={() => getBudgetRequests.refetch()}
            steps={getInitialData.data?.workflowStepActions?.collection!}
          />
        </QueryState>
      </QueryState>

      {getInitialData.data && (
        <FloatingRoute id="create" className="sm:max-w-[650px]">
          <CreateBudgetRequestPage
            onSuccess={() => getBudgetRequests.refetch()}
            workflows={getInitialData.data?.workflows?.collection!}
            games={getInitialData.data?.games?.collection!}
          />
        </FloatingRoute>
      )}

      {getBudgetRequests.data && (
        <>
          <FloatingRoute id="edit" className="sm:max-w-[650px]">
            <EditBudgetRequestPage
              onSuccess={() => getBudgetRequests.refetch()}
              collection={getBudgetRequests.data?.budgetRequests?.collection!}
            />
          </FloatingRoute>
        </>
      )}
    </BaseLayout>
  )
}
