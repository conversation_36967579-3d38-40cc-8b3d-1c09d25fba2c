import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { createColumnHelper } from '@tanstack/react-table'
import { Button } from '@/next/components/ui/button'
import { Checkbox } from '@/next/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/next/components/ui/table'
import { ModelAttributePresenter } from '#controllers/dashboard/game/model_attribute_presenter'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { AclSubject } from '#config/enums'
import { Form } from '@/next/components/ui/form'
import { useDataTable } from '@/next/hooks/use-data-table'
import { flexRender } from '@tanstack/react-table'
import { makeAclSidebarSubItems } from '@/next/components/domain/acl_sidebar'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { Loading } from '@/next/components/sdk/loading'

import { useMutation, useQuery } from '@/next/components/sdk/graphql'
import { graphql } from '@/gql'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

const aclRouteQuery = graphql(`
  query GetAccessControl($where: AccessControlWhere!) {
    accessControl(where: $where) {
      ...AccessControlAttributes
    }
  }
`)

const updateAccessControlMutation = graphql(`
  mutation UpdateAccessControl($where: AccessControlWhere!, $form: AccessControlUpdateForm!) {
    updateAccessControl(where: $where, form: $form) {
      ...AccessControlAttributes
    }
  }
`)

export default function Page() {
  return (
    <BaseLayout>
      <PageConfig
        title="Route ACL"
        breadcrumbs={['Route ACL']}
        sidebarSubItems={makeAclSidebarSubItems()}
      />
      <RouteAclContent />
    </BaseLayout>
  )
}

function RouteAclContent() {
  const subject = AclSubject.Route
  const columnHelper = createColumnHelper<ModelAttributePresenter>()

  const listAclQuery = useQuery(aclRouteQuery, {
    where: {
      subject,
    },
  })

  // Queries
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const [[settingConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.setting'], []))

  const defaultRoles = useMemo(() => {
    if (!roleConfigMapCollection || !listAclQuery.data) return {}

    const roles: Record<string, Record<string, boolean>> = {}
    for (const role of roleConfigMapCollection) {
      const acl = listAclQuery.data.accessControl.roles.find((a) => a.roleId === role.id)
      if (!acl) continue
      roles[role.id] = {}
      for (const permit of acl.permits) {
        roles[role.id][permit.replace(/\./g, '#')] = true
      }
    }
    return roles
  }, [roleConfigMapCollection, listAclQuery.data])

  const form = useForm({
    defaultValues: { roles: defaultRoles },
  })

  useEffect(() => {
    form.reset({ roles: defaultRoles })
  }, [defaultRoles, form])

  const [updateAclMutation, { loading }] = useMutation(updateAccessControlMutation, {
    onSuccess: () => {
      listAclQuery.refetch()
    },
  })

  const columns = useMemo(() => {
    if (!roleConfigMapCollection || !listAclQuery.data) {
      return []
    }

    return [
      columnHelper.accessor('displayName', {
        header: '',
        cell: (info) => info.getValue(),
      }),
      ...roleConfigMapCollection.map((roleConfigMap) =>
        columnHelper.accessor(
          (row) => {
            const acls = listAclQuery.data
            const rolePermits =
              acls?.accessControl.roles.find((a: any) => a.roleId === roleConfigMap.id)?.permits ??
              []
            return rolePermits.includes(row.name)
          },
          {
            id: roleConfigMap.id,
            header: roleConfigMap.name,
            cell: (info) => {
              const roleId = roleConfigMap.id
              const attrName = info.row.original.name.replace(/\./g, '#')
              const isChecked = form.watch(`roles.${roleId}.${attrName}`) ?? info.getValue()

              return (
                <Checkbox
                  checked={isChecked}
                  onCheckedChange={(checked) => {
                    form.setValue(`roles.${roleId}.${attrName}`, checked as boolean, {
                      shouldDirty: true,
                      shouldTouch: true,
                    })
                  }}
                />
              )
            },
          }
        )
      ),
    ]
  }, [roleConfigMapCollection, listAclQuery.data, form])

  const attrs = useMemo(() => {
    const routes = settingConfigMapCollection?.[0]?.aclAccessibleRoutes ?? []
    return routes.map(
      (route) =>
        ({
          name: route.id,
          displayName: route.name,
        }) as ModelAttributePresenter
    )
  }, [settingConfigMapCollection])

  const { table } = useDataTable({
    data: attrs,
    columns,
    pageCount: 0,
    form: useDataTableFilterForm({}),
  })

  const onSubmit = (values: any) => {
    updateAclMutation({
      where: {
        subject,
      },
      form: {
        roles: Object.entries(values.roles).map(([roleId, attrsToEnabled]) => {
          return {
            id: roleId,
            // TODO: Fix filter of permits to retain previous values.
            // Currently, when updating, only the newly selected values of permits are kept and the previous ones are lost.
            permits: Object.entries(attrsToEnabled as any)
              .filter(([_, enabled]) => enabled)
              .map(([attr]) => attr.replace(/#/g, '.')),
          }
        }),
      },
    })
  }

  return (
    <Loading loading={listAclQuery.loading || roleConfigMapCollection.length === 0}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead rowSpan={2} />
                {(() => {
                  if (!roleConfigMapCollection) return null
                  const groupMap: Record<string, any[]> = {}
                  for (const role of roleConfigMapCollection) {
                    if (!groupMap[role.group]) groupMap[role.group] = []
                    groupMap[role.group].push(role)
                  }
                  return Object.entries(groupMap).map(([groupName, roles]) => (
                    <TableHead
                      key={groupName}
                      colSpan={roles.length}
                      className="text-center border-l border-r"
                    >
                      {groupName}
                    </TableHead>
                  ))
                })()}
              </TableRow>
              <TableRow>
                {roleConfigMapCollection &&
                  roleConfigMapCollection.map((role) => (
                    <TableHead key={role.id} className="text-center border-1">
                      {role.name}
                    </TableHead>
                  ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map((cell, idx) => (
                      <TableCell
                        key={cell.id}
                        className={
                          cell.column.id === 'displayName' || idx === 0 ? '' : 'text-center'
                        }
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          <div className="flex justify-end">
            <Button disabled={loading} type="submit">
              Save Changes
            </Button>
          </div>
        </form>
      </Form>
    </Loading>
  )
}
