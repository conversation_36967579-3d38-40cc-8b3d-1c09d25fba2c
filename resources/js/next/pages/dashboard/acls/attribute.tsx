import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { createColumnHelper } from '@tanstack/react-table'
import { useTranslation } from 'react-i18next'
import * as changeCase from 'case-anything'

import { useGraphql } from '@/components/toolkit-api'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { AclSubject } from '#config/enums'
import { graphql } from '@/gql'
import { ModelAttributeFragment } from '@/graphql'
import { aclAttributeSubject } from '#utils/acl'
import { createParamsStore, routes } from '@/components/location'
import { makeAclSidebarSubItems } from '@/next/components/domain/acl_sidebar'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { object, string } from 'yup'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/next/components/ui/table'
import { Button } from '@/next/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/next/components/ui/select'
import { Form } from '@/next/components/ui/form'
import { useDataTable } from '@/next/hooks/use-data-table'
import { flexRender } from '@tanstack/react-table'
import { Loading } from '@/next/components/sdk/loading'
import { useMutation } from '@/next/components/sdk/graphql'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

const getInitDataQuery = graphql(`
  query Acl_GameMetricAttributes(
    $readSubject: String!
    $writeSubject: String!
    $modelName: String!
  ) {
    attributes(where: { gameId: null, modelName: $modelName }) {
      collection {
        ...ModelAttribute
      }
    }

    read: accessControl(where: { subject: $readSubject }) {
      ...AccessControlAttributes
    }

    write: accessControl(where: { subject: $writeSubject }) {
      ...AccessControlAttributes
    }
  }
`)

const updateAccessControlMutation = graphql(`
  mutation UpdateAccessControl($where: AccessControlWhere!, $form: AccessControlUpdateForm!) {
    updateAccessControl(where: $where, form: $form) {
      ...AccessControlAttributes
    }
  }
`)

const { useParam } = createParamsStore(routes.dash.acls.show(':subject'), {
  parse: object({
    subject: string().required(),
  }),
})

export default function Page() {
  return (
    <BaseLayout>
      <PageConfig
        title="Game Metric Attributes ACL"
        breadcrumbs={['Game Metric Attributes ACL']}
        sidebarSubItems={makeAclSidebarSubItems()}
      />
      <AttributeAclContent />
    </BaseLayout>
  )
}

function AttributeAclContent() {
  const columnHelper = createColumnHelper<ModelAttributeFragment>()

  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const { t } = useTranslation()
  const subjectParam = useParam((p) => p.subject)

  const modelName = useMemo(() => {
    return subjectParam
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('')
  }, [subjectParam])

  const getInitData = useGraphql(getInitDataQuery, {
    variables: {
      readSubject: aclAttributeSubject(AclSubject.AttributeRead, modelName),
      writeSubject: aclAttributeSubject(AclSubject.AttributeWrite, modelName),
      modelName: modelName,
    },
  })

  const [updateAclMutation] = useMutation(updateAccessControlMutation)

  const defaultRoles = useMemo(() => {
    if (!roleConfigMapCollection || !getInitData.data) return {}

    const roles: Record<string, Record<string, string>> = {}
    for (const role of roleConfigMapCollection) {
      const readAcl = getInitData.data.read.roles.find((a) => a.roleId === role.id)
      const writeAcl = getInitData.data.write.roles.find((a) => a.roleId === role.id)
      if (!readAcl && !writeAcl) continue

      roles[role.id] = {}
      const allAttrs = new Set([...(readAcl?.permits ?? []), ...(writeAcl?.permits ?? [])])
      for (const attr of allAttrs) {
        const hasWrite = writeAcl?.permits.includes(attr)
        const hasRead = readAcl?.permits.includes(attr)
        roles[role.id][attr.replace(/\./g, '#')] = hasWrite ? 'rw' : hasRead ? 'r' : 'none'
      }
    }
    return roles
  }, [roleConfigMapCollection, getInitData.data])

  const form = useForm({
    defaultValues: { roles: defaultRoles },
  })

  const columns = useMemo(() => {
    if (!getInitData.data) {
      return []
    }

    const intlScope = changeCase.camelCase(modelName)

    return [
      columnHelper.accessor('name', {
        header: '',
        cell: (info) => t(`${intlScope}.${info.getValue()}`),
      }),
      ...roleConfigMapCollection.map((roleConfigMap: any) =>
        columnHelper.accessor(
          (row) => {
            const readAcls = getInitData.data!.read.roles
            const writeAcls = getInitData.data!.write.roles
            const roleReadAcl = readAcls.find((a) => a.roleId === roleConfigMap.id)!
            const roleWriteAcl = writeAcls.find((a) => a.roleId === roleConfigMap.id)!

            const permission = roleWriteAcl.permits.includes(row.name)
              ? 'rw'
              : roleReadAcl.permits.includes(row.name)
                ? 'r'
                : 'none'

            return permission
          },
          {
            id: roleConfigMap.id,
            header: roleConfigMap.name,
            cell: (info) => {
              const roleId = roleConfigMap.id
              const attrName = info.row.original.name.replace(/\./g, '#')
              const permission = form.watch(`roles.${roleId}.${attrName}`) ?? info.getValue()

              return (
                <Select
                  value={permission}
                  onValueChange={(value) => {
                    form.setValue(`roles.${roleId}.${attrName}`, value, {
                      shouldDirty: true,
                      shouldTouch: true,
                    })
                  }}
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">-</SelectItem>
                    <SelectItem value="r">Read</SelectItem>
                    <SelectItem value="rw">Read & Write</SelectItem>
                  </SelectContent>
                </Select>
              )
            },
          }
        )
      ),
    ]
  }, [roleConfigMapCollection, getInitData.data, form])

  const { table } = useDataTable({
    data: getInitData.data?.attributes.collection ?? [],
    columns,
    pageCount: 0,
    form: useDataTableFilterForm({}),
  })

  // TODO: fix filter to downgrade read & write to read
  const buildRoles = (values: any, type: 'read' | 'write') => {
    const isRead = type === 'read'
    const roles = getInitData.data?.[type].roles || []

    return Object.entries(values.roles).map(([roleId, acls]: [string, any]) => {
      const existing = roles.find((r) => r.roleId === roleId)?.permits || []

      const permitted = Object.entries(acls)
        .filter(([_, perm]) => (isRead ? perm === 'r' || perm === 'rw' : perm === 'rw'))
        .map(([attr]) => attr.replace(/#/g, '.'))

      const toRemove = Object.entries(acls)
        .filter(([_, perm]) => perm === 'none')
        .map(([attr]) => attr.replace(/#/g, '.'))

      const final = Array.from(new Set([...existing, ...permitted])).filter(
        (attr) => !toRemove.includes(attr)
      )

      return { id: roleId, permits: final }
    })
  }

  const onSubmit = (values: any) => {
    Promise.all([
      updateAclMutation({
        where: { subject: aclAttributeSubject(AclSubject.AttributeRead, modelName) },
        form: { roles: buildRoles(values, 'read') },
      }),
      updateAclMutation({
        where: { subject: aclAttributeSubject(AclSubject.AttributeWrite, modelName) },
        form: { roles: buildRoles(values, 'write') },
      }),
    ])
  }

  return (
    <Loading loading={getInitData.loading || roleConfigMapCollection.length === 0}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead rowSpan={2} />
                {(() => {
                  if (!roleConfigMapCollection) return null
                  const groupMap: Record<string, any[]> = {}
                  for (const role of roleConfigMapCollection) {
                    if (!groupMap[role.group]) groupMap[role.group] = []
                    groupMap[role.group].push(role)
                  }
                  return Object.entries(groupMap).map(([groupName, roles]) => (
                    <TableHead
                      key={groupName}
                      colSpan={roles.length}
                      className="text-center border-l border-r"
                    >
                      {groupName}
                    </TableHead>
                  ))
                })()}
              </TableRow>
              <TableRow>
                {roleConfigMapCollection &&
                  roleConfigMapCollection.map((role) => (
                    <TableHead key={role.id} className="text-center border-1">
                      {role.name}
                    </TableHead>
                  ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          <div className="flex justify-end">
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </Form>
    </Loading>
  )
}
