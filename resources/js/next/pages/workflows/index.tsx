import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { Button } from '@/next/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/next/components/ui/card'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/next/components/ui/accordion'
import { Loading } from '@/next/components/sdk/loading'
import { graphql, WorkflowStepAttributesFragment } from '@/graphql'
import { useQuery } from '@/next/components/sdk/graphql'
import { FloatingRoute, useFloatingRoute } from '@/next/components/sdk/floating_route'
import { CreateWorkflowPage } from './index/create'
import { EditWorkflowPage } from './index/edit'
import { useHashState } from '@/next/components/sdk/hash_link'
import { parseAsString } from 'nuqs'

graphql(`
  fragment WorkflowAttributes on Workflow {
    id
    name
    roleId
    steps {
      ...WorkflowStepAttributes
    }
  }
`)

const getInitialDataQuery = graphql(`
  query WorkflowsIndex_GetInitialData {
    workflows {
      collection {
        ...WorkflowAttributes
      }
    }

    users(where: {}, offset: { perPage: 9999 }) {
      collection {
        ...UserPublicProfile
      }
    }
  }
`)

export default function Page() {
  const { data: getInitialData, loading, refetch } = useQuery(getInitialDataQuery)
  const [workflowId] = useHashState('workflow_id', parseAsString.withDefault(''))
  const [navigate] = useFloatingRoute()

  const selectedWorkflow = workflowId
    ? getInitialData?.workflows?.collection?.find((w) => w.id.toString() === workflowId)
    : null

  return (
    <BaseLayout>
      <PageConfig title="Workflows" breadcrumbs={[]} />

      <div className="space-y-4">
        <Button onClick={() => navigate('create')}>Create Workflow</Button>

        <Loading loading={loading}>
          <div className="space-y-4">
            {getInitialData?.workflows?.collection?.map((workflow: any) => (
              <Accordion key={workflow.id} type="single" collapsible>
                <AccordionItem value={workflow.id.toString()}>
                  <AccordionTrigger>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{workflow.name}</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <Card>
                      <CardHeader>
                        <CardTitle>Workflow Steps</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {workflow.steps.map((step: WorkflowStepAttributesFragment) => (
                            <div key={step.name} className="flex items-center gap-4">
                              <div className="flex-1">
                                <div className="font-medium">{step.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {step.assignee.fullName} - {step.action}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="mt-4 flex gap-2">
                          <Button variant="destructive">Delete</Button>
                          <Button
                            onClick={() => {
                              navigate('edit', {
                                workflow_id: workflow.id.toString(),
                              })
                            }}
                          >
                            Edit
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
          </div>
        </Loading>

        {getInitialData?.users && (
          <>
            <FloatingRoute id="create" className="sm:max-w-[600px]">
              <CreateWorkflowPage
                onSuccess={() => {
                  refetch()
                }}
                users={getInitialData.users.collection}
              />
            </FloatingRoute>

            <FloatingRoute id="edit" className="sm:max-w-[600px]">
              <EditWorkflowPage
                onSuccess={() => {
                  refetch()
                }}
                workflows={selectedWorkflow ? [selectedWorkflow] : []}
                users={getInitialData.users.collection}
              />
            </FloatingRoute>
          </>
        )}
      </div>
    </BaseLayout>
  )
}
