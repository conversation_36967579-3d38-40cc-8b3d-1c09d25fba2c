import { CreateWorkflowForm, graphql, UserPublicProfileFragment } from '@/graphql'
import { FloatingRouteContent, useFloatingRoute } from '@/next/components/sdk/floating_route'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { useMutation } from '@/next/components/sdk/graphql'
import { Button } from '@/next/components/ui/button'
import { DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { Form } from '@/next/components/ui/form'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useEffect, useMemo } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { Separator } from '@/next/components/ui/separator'
import { Trash2 } from 'lucide-react'

const createWorkflowQuery = graphql(`
  mutation WorkflowsIndex_Create($form: CreateWorkflowForm!) {
    createWorkflow(form: $form) {
      ...WorkflowAttributes
    }
  }
`)

export function CreateWorkflowPage({
  onSuccess,
  users,
}: {
  onSuccess: () => any
  users: UserPublicProfileFragment[]
}) {
  const [_, closeFloatingRoute] = useFloatingRoute()
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))

  const form = useForm<CreateWorkflowForm>({
    defaultValues: {
      name: '',
      roleId: '',
      steps: [],
    },
  })

  const stepsForm = useFieldArray({
    control: form.control,
    name: 'steps',
  })

  const [createWorkflow, createWorkflowState] = useMutation(createWorkflowQuery, {
    onSuccess: () => {
      onSuccess()
      closeFloatingRoute()
    },
  })

  const onSubmit = form.handleSubmit((formData) => {
    return createWorkflow({
      form: formData,
    })
  })

  return (
    <>
      <DialogHeader>
        <DialogTitle>Create Workflow</DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <FloatingRouteContent>
          <form className="flex flex-col gap-4" onSubmit={onSubmit}>
            <div className="flex flex-col gap-4 overflow-auto">
              <FormInputField
                control={form.control}
                name="name"
                label="Name"
                placeholder="Enter workflow name"
                required={true}
              />

              <FormSelectField
                control={form.control}
                name="roleId"
                label="Role"
                placeholder="Select Role"
                options={roleConfigMapCollection.map((role: any) => ({
                  value: role.id,
                  label: `${role.name} (${role.group})`,
                }))}
              />

              {stepsForm.fields.map((stepField, index) => (
                <div key={stepField.id} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormInputField
                      control={form.control}
                      name={`steps.${index}.name`}
                      label="Step Name"
                      placeholder="Enter step name"
                    />

                    <FormInputField
                      control={form.control}
                      name={`steps.${index}.action`}
                      label="Action"
                      placeholder="Enter action"
                    />

                    <FormInputField
                      control={form.control}
                      name={`steps.${index}.alternateAction`}
                      label="Alternate Action"
                      placeholder="Enter alternate action"
                    />

                    <FormSelectField
                      control={form.control}
                      name={`steps.${index}.assigneeId`}
                      label="PIC"
                      placeholder="Select PIC"
                      options={users.map((user) => ({
                        value: user.id,
                        label: user.fullName,
                      }))}
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <Separator className="flex-1" />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => stepsForm.remove(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  stepsForm.append({
                    name: '',
                    assigneeId: '',
                    action: '',
                    alternateAction: '',
                  })
                }
              >
                Add Step
              </Button>
            </div>

            <Button
              type="submit"
              disabled={createWorkflowState.loading || form.formState.isSubmitting}
            >
              Save
            </Button>
          </form>
        </FloatingRouteContent>
      </Form>
    </>
  )
}
