import { routes } from '@/components/location'
import { useServerProp } from '@/components/ssr'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormPasswordInputField } from '@/next/components/sdk/form/form_password_input_field'
import { PageConfig } from '@/next/components/sdk/page_config'
import { Button } from '@/next/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/next/components/ui/card'
import { Form } from '@/next/components/ui/form'
import { zodResolver } from '@hookform/resolvers/zod'
import { GalleryVerticalEnd } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const schema = z.object({
  email: z.string().email().trim(),
  password: z.string().trim(),
})

export default function Page() {
  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <PageConfig title="Login" breadcrumbs={[]} />

      <div className="flex w-full max-w-sm flex-col gap-6">
        <a href="#" className="flex items-center gap-2 self-center font-medium">
          <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
            <GalleryVerticalEnd className="size-4" />
          </div>
          Mirai Studio
        </a>
        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-xl">Welcome back</CardTitle>
              <CardDescription>Login with your Google account</CardDescription>
            </CardHeader>

            <CardContent>
              <LoginForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

function LoginForm() {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      password: '',
    },
  })
  const { control } = form

  const csrfToken = useServerProp((s) => s.csrfToken)

  return (
    <form method="post" action={routes.auth.sessions.store()}>
      <input type="hidden" name="_csrf" value={csrfToken} />

      <Form {...form}>
        <div className="grid gap-6">
          <div className="flex flex-col gap-4">
            <Button variant="outline" className="w-full" type="button" asChild>
              <a href={routes.auth.sessions.google()}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path
                    d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                    fill="currentColor"
                  />
                </svg>
                Login with Google
              </a>
            </Button>
          </div>
          <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
            <span className="relative z-10 bg-background px-2 text-muted-foreground">
              Or continue with
            </span>
          </div>
          <div className="grid gap-6">
            <div className="grid gap-2">
              <FormInputField
                control={control}
                name="email"
                label="Email"
                type="email"
                placeholder="Enter your email"
                required
                data-testid="login_email"
              />
            </div>
            <div className="grid gap-2">
              <FormPasswordInputField
                control={control}
                name="password"
                label="Password"
                placeholder="Enter your password"
                required
                data-testid="login_password"
              />
            </div>
            <Button type="submit" className="w-full" data-testid="login_submit">
              Login
            </Button>
          </div>
        </div>
      </Form>
    </form>
  )
}
