import { graphql } from '@/gql'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useQuery } from '@/next/components/sdk/graphql'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { PageConfig } from '@/next/components/sdk/page_config'
import { useMemo } from 'react'
import { UserListTable } from './index/table'
import { FloatingRoute } from '@/next/components/sdk/floating_route'
import { EditUserPage } from './index/edit'
import { parseAsInteger, useQueryState } from 'nuqs'
import { userAttributesFragment, UserAttributesFragment } from '@/graphql'
import { useApolloClient } from '@apollo/client'
import { NewUserPage } from './index/new'

const getUsersQuery = graphql(`
  query UserIndex_GetUsers($offset: Offset!) {
    users(where: {}, offset: $offset) {
      collection {
        ...UserAttributes
      }
      pageInfo {
        ...PageInfoAttributes
      }
    }
  }
`)

export default function Page() {
  const [, getConfigMaps] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))

  const [page] = useQueryState('page', parseAsInteger.withDefault(1))
  const [perPage] = useQueryState('perPage', parseAsInteger.withDefault(200))

  const getUsers = useQuery(getUsersQuery, { offset: { page, perPage } })

  const apolloClient = useApolloClient()
  const onDelete = (user: UserAttributesFragment) => {
    apolloClient.writeFragment({
      id: `User:${user.id}`,
      data: {
        ...user,
        isDeleted: true,
      },
      fragment: userAttributesFragment,
      fragmentName: 'UserAttributes',
    })
  }

  const onCreate = (user: UserAttributesFragment) => {
    apolloClient.cache.modify({
      fields: {
        users(existingUsers = {}) {
          const newUserRef = apolloClient.writeFragment({
            id: `User:${user.id}`,
            data: user,
            fragment: userAttributesFragment,
            fragmentName: 'UserAttributes',
          })

          return {
            ...existingUsers,
            collection: [newUserRef, ...existingUsers.collection],
          }
        },
      },
    })
  }

  return (
    <BaseLayout>
      <PageConfig title="All Users" breadcrumbs={['Users']} />

      <QueryState query={[getUsers, getConfigMaps]}>
        <UserListTable
          collection={getUsers.data?.users?.collection!}
          pageInfo={getUsers.data?.users?.pageInfo!}
          onDelete={onDelete}
        />

        <FloatingRoute id="edit" className="sm:max-w-[650px]">
          <EditUserPage collection={getUsers.data?.users?.collection!} />
        </FloatingRoute>
      </QueryState>

      <FloatingRoute id="new" className="sm:max-w-[650px]">
        <NewUserPage onCreate={onCreate} />
      </FloatingRoute>
    </BaseLayout>
  )
}
