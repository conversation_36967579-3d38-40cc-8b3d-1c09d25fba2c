import { UserAttributesFragment, UserKind, graphql } from '@/graphql'
import { FloatingRouteContent, useFloatingRoute } from '@/next/components/sdk/floating_route'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { useMutation } from '@/next/components/sdk/graphql'
import { useHashState } from '@/next/components/sdk/hash_link'
import { Button } from '@/next/components/ui/button'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { Form } from '@/next/components/ui/form'
import { zodResolver } from '@hookform/resolvers/zod'
import { parseAsString } from 'nuqs'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { FormCheckboxField } from '@/next/components/sdk/form/form_checkbox_field'
import { FormPasswordInputField } from '@/next/components/sdk/form/form_password_input_field'
import { parseToolPermissions, stringifyToolPermissions, toolPermissionOptions } from './utils'
import { FormTextAreaField } from '@/next/components/sdk/form/form_text_area_field'

export function EditUserPage({ collection }: { collection: UserAttributesFragment[] }) {
  const [id] = useHashState('id', parseAsString)
  const user = collection.find((u) => u.id === id)
  const [_, closeFloatingRoute] = useFloatingRoute()

  if (!user) {
    return null
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle>Edit User</DialogTitle>
        <DialogDescription>ID: {id}</DialogDescription>
      </DialogHeader>

      <EditForm
        record={user}
        onSuccess={() => {
          closeFloatingRoute()
        }}
      />
    </>
  )
}

const schema = z
  .object({
    email: z.string().email(),
    fullName: z.string().trim().min(1),
    hasPassword: z.boolean().default(false),
    password: z.string().min(6).optional(),
    passwordConfirmation: z.string().optional(),
    kind: z.enum([UserKind.Partner, UserKind.Inhouse]),
    toolPermissions: z.array(z.string()),
    note: z.string().trim().optional(),
  })
  .refine(
    (data) =>
      data.hasPassword && data.password ? data.password === data.passwordConfirmation : true,
    {
      message: 'Password and confirmation must match',
      path: ['passwordConfirmation'],
    }
  )

const updateUserMutation = graphql(`
  mutation UserIndex_UpdateUser($where: UpdateUserWhere!, $form: UpdateUserForm!) {
    updateUser(where: $where, form: $form) {
      ...UserAttributes
    }
  }
`)

function EditForm({ record, onSuccess }: { record: UserAttributesFragment; onSuccess: () => any }) {
  const form = useForm({
    defaultValues: {
      email: record.email,
      fullName: record.fullName,
      kind: record.kind,
      toolPermissions: stringifyToolPermissions(record.toolPermissions || []),
      hasPassword: Boolean(record.hasPassword),
      note: record.note || '',
    },
    resolver: zodResolver(schema),
  })

  const [updateUser, updateUserState] = useMutation(updateUserMutation, {
    onSuccess,
  })

  const onSubmit = form.handleSubmit((formData) => {
    return updateUser({
      where: { id: record.id },
      form: {
        fullName: formData.fullName,
        hasPassword: formData.hasPassword,
        password: formData.password,
        kind: formData.kind,
        toolPermissions: parseToolPermissions(formData.toolPermissions || []),
        note: formData.note,
      },
    })
  })

  return (
    <Form {...form}>
      <FloatingRouteContent>
        <form className="flex flex-col gap-4" onSubmit={onSubmit}>
          <div className="flex flex-col gap-4 overflow-auto">
            <FormInputField
              control={form.control}
              name="email"
              label="Email"
              type="email"
              disabled
            />

            <FormInputField control={form.control} name="fullName" label="Full Name" />

            <FormSelectField
              control={form.control}
              name="kind"
              label="Kind"
              options={[
                { value: UserKind.Partner, label: 'Partner' },
                { value: UserKind.Inhouse, label: 'Inhouse' },
              ]}
            />

            <FormCheckboxField
              control={form.control}
              name="hasPassword"
              label="Has Password"
              className="mb-2"
            />

            <FormPasswordInputField
              control={form.control}
              name="password"
              label="Password"
              placeholder="Leave empty to keep current password"
            />

            <FormPasswordInputField
              control={form.control}
              name="passwordConfirmation"
              label="Password Confirmation"
            />

            <FormMultiSelectField
              control={form.control}
              name="toolPermissions"
              label="Tool Permissions"
              options={toolPermissionOptions}
            />

            <FormTextAreaField
              control={form.control}
              name="note"
              label="Note"
              placeholder="Note about the user"
              rows={3}
            />
          </div>

          <Button type="submit" disabled={updateUserState.loading || form.formState.isSubmitting}>
            Save
          </Button>
        </form>
      </FloatingRouteContent>
    </Form>
  )
}
