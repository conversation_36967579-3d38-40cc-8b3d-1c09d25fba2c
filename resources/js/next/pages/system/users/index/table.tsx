import { graphql, PageInfoAttributesFragment, UserAttributesFragment } from '@/graphql'
import {
  DataTable,
  TableCellButton,
  TableCellDeleteButton,
  TableCellEditButton,
} from '@/next/components/sdk/data_table'
import { useDataTable } from '@/next/hooks/use-data-table'
import { createColumnHelper } from '@tanstack/react-table'
import { Badge } from '@/next/components/ui/badge'
import { CheckIcon, Drama, Plus, XIcon } from 'lucide-react'
import { useMemo } from 'react'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import { useMutation } from '@/next/components/sdk/graphql'
import { cn } from '@/next/lib/utils'
import { Button } from '@/next/components/ui/button'
import { Link } from '@inertiajs/react'
import { routes } from '@/components/location'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'

const columnHelper = createColumnHelper<UserAttributesFragment>()

const deleteUserMutation = graphql(`
  mutation UserIndex_DeleteUsers($where: DeleteUsersWhere!) {
    deleteUsers(where: $where)
  }
`)

export function UserListTable({
  collection,
  pageInfo,
  ...props
}: {
  collection: UserAttributesFragment[]
  pageInfo: PageInfoAttributesFragment
  onDelete: (user: UserAttributesFragment) => any
}) {
  const form = useDataTableFilterForm({
    email: {
      variant: 'text',
      defaultValue: '',
    },
  })

  const [navigate] = useFloatingRoute()
  const [deleteUser, deleteUserState] = useMutation(deleteUserMutation)

  const onDelete = async (user: UserAttributesFragment) => {
    await deleteUser({
      where: { ids: [user.id] },
    })

    props.onDelete(user)
  }

  const { email } = form.immediateValues

  const columns = useMemo(
    () => [
      columnHelper.accessor('email', {
        id: 'email',
        header: 'Email',
        enableColumnFilter: true,
        cell: ({ row, getValue }) => {
          return (
            <span className={cn({ 'line-through': row.original.isDeleted })}>{getValue()}</span>
          )
        },
        meta: {
          label: 'Email',
          variant: 'text',
        },
      }),
      columnHelper.accessor('fullName', {
        header: 'Full Name',
        cell: ({ row, getValue }) => {
          return (
            <span className={cn({ 'line-through': row.original.isDeleted })}>{getValue()}</span>
          )
        },
        meta: {
          label: 'Full Name',
        },
      }),
      columnHelper.display({
        id: 'password',
        header: () => <div className="text-center">Password Access</div>,
        cell: ({ row }) => {
          const hasPassword = row.original.hasPassword
          return (
            <div className="flex justify-center">
              {hasPassword ? (
                <CheckIcon className="w-4 h-4 text-green-500" />
              ) : (
                <XIcon className="w-4 h-4 text-red-500" />
              )}
            </div>
          )
        },
        meta: {
          label: 'Password Access',
        },
      }),
      columnHelper.accessor('toolPermissions', {
        header: () => <div className="text-center">Permissions</div>,
        cell: ({ getValue }) => {
          const permissions = getValue() || []
          return (
            <div className="flex flex-wrap gap-1 justify-center">
              {permissions.map((p: any) => (
                <Badge
                  key={`${p.tool}:${p.action}`}
                  variant={p.action === 'manage' ? 'default' : 'secondary'}
                >
                  {`${p.tool.toLowerCase()}:${p.action}`}
                </Badge>
              ))}
            </div>
          )
        },
        meta: {
          label: 'Permissions',
        },
      }),
      columnHelper.accessor('note', {
        id: 'note',
        header: 'Note',
        meta: {
          label: 'Note',
        },
      }),
      columnHelper.accessor(() => 'action', {
        id: 'action',
        header: () => <div className="text-center">Action</div>,
        cell: ({ row }) => {
          if (row.original.isDeleted) {
            return null
          }

          return (
            <div className="flex justify-center">
              <TableCellEditButton onClick={() => navigate('edit', { id: row.original.id })} />
              <TableCellDeleteButton
                onClick={() => onDelete(row.original)}
                disabled={deleteUserState.loading}
              />
              <TableCellButton variant="link" asChild>
                <Link href={routes.system.users.simulate(row.original.id)}>
                  <Drama />
                  Simulate
                </Link>
              </TableCellButton>
            </div>
          )
        },
        enableHiding: false,
      }),
    ],
    []
  )

  const rows = useMemo(() => {
    if (!email) {
      return collection
    }

    return collection.filter((user) => {
      return user.email.toLowerCase().includes(email.toLowerCase())
    })
  }, [email, collection])

  const { table } = useDataTable({
    columns,
    data: rows,
    pageCount: pageInfo.lastPage,
    initialState: {
      pagination: {
        pageIndex: pageInfo.currentPage - 1,
        pageSize: pageInfo.perPage,
      },
    },
    form,
  })

  return (
    <DataTable table={table} pageSizeOptions={[20, 50, 100, 200]} sticky>
      <DataTableToolbar table={table}>
        <Button size="sm" onClick={() => navigate('new')}>
          <Plus />
          Create User
        </Button>
      </DataTableToolbar>
    </DataTable>
  )
}
