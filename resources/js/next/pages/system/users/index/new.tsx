import { graphql, UserAttributesFragment, UserKind } from '@/graphql'
import { FloatingRouteContent, useFloatingRoute } from '@/next/components/sdk/floating_route'
import { FormCheckboxField } from '@/next/components/sdk/form/form_checkbox_field'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { FormPasswordInputField } from '@/next/components/sdk/form/form_password_input_field'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { useMutation } from '@/next/components/sdk/graphql'
import { DialogDescription, DialogHeader, DialogTitle } from '@/next/components/ui/dialog'
import { Form } from '@/next/components/ui/form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { parseToolPermissions, toolPermissionOptions } from './utils'
import { Button } from '@/next/components/ui/button'
import { Permission, Tool } from '#config/enums'
import { FormTextAreaField } from '@/next/components/sdk/form/form_text_area_field'

const schema = z
  .object({
    email: z.string().email(),
    fullName: z.string().min(1, 'Full Name is required'),
    kind: z.enum([UserKind.Partner, UserKind.Inhouse]),
    hasPassword: z.boolean().default(false),
    password: z.string().optional(),
    passwordConfirmation: z.string().optional(),
    toolPermissions: z.array(z.string()).default([`${Tool.Dashboard}:${Permission.View}`]),
    note: z.string().trim().optional(),
  })
  .refine(
    (data) => {
      if (data.hasPassword) {
        return (
          data.password && data.passwordConfirmation && data.password === data.passwordConfirmation
        )
      }
      return true
    },
    {
      message: 'Password and confirmation must match',
    }
  )

const createUserMutation = graphql(`
  mutation UserIndex_CreateUser($form: CreateUserForm!) {
    createUser(form: $form) {
      ...UserAttributes
    }
  }
`)

export function NewUserPage({ onCreate }: { onCreate: (user: UserAttributesFragment) => any }) {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      kind: UserKind.Inhouse,
    },
  })

  const [_, goBack] = useFloatingRoute()

  const [createUser, createUserState] = useMutation(createUserMutation, {
    onSuccess: (data) => {
      onCreate(data.createUser)
      goBack()
      form.reset()
    },
  })

  const onSubmit = form.handleSubmit(async (data) => {
    return createUser({
      form: {
        email: data.email,
        fullName: data.fullName,
        kind: data.kind,
        hasPassword: data.hasPassword,
        password: data.password,
        toolPermissions: parseToolPermissions(data.toolPermissions || []),
        note: data.note,
      },
    })
  })

  return (
    <>
      <DialogHeader>
        <DialogTitle>New User</DialogTitle>
        <DialogDescription>Create a new user account</DialogDescription>
      </DialogHeader>

      <Form {...form}>
        <FloatingRouteContent>
          <form className="flex flex-col gap-4" onSubmit={onSubmit}>
            <div className="flex flex-col gap-4 overflow-auto">
              <FormInputField control={form.control} name="email" label="Email" type="email" />

              <FormInputField control={form.control} name="fullName" label="Full Name" />

              <FormSelectField
                control={form.control}
                name="kind"
                label="Kind"
                options={[
                  { value: UserKind.Partner, label: 'Partner' },
                  { value: UserKind.Inhouse, label: 'Inhouse' },
                ]}
              />

              <FormCheckboxField
                control={form.control}
                name="hasPassword"
                label="Has Password"
                className="mb-2"
              />

              <FormPasswordInputField
                control={form.control}
                name="password"
                label="Password"
                placeholder="Leave empty to keep current password"
              />

              <FormPasswordInputField
                control={form.control}
                name="passwordConfirmation"
                label="Password Confirmation"
              />

              <FormMultiSelectField
                control={form.control}
                name="toolPermissions"
                label="Tool Permissions"
                options={toolPermissionOptions}
              />

              <FormTextAreaField
                control={form.control}
                name="note"
                label="Note"
                placeholder="Optional note for the user"
                rows={3}
              />
            </div>

            <Button type="submit" disabled={createUserState.loading || form.formState.isSubmitting}>
              Save
            </Button>
          </form>
        </FloatingRouteContent>
      </Form>
    </>
  )
}
