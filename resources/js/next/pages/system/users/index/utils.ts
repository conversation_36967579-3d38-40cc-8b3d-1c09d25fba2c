import fastCartesian from 'fast-cartesian'

import { Tool, Permission } from '#config/enums'
import { ToolPermissionAttributesFragment } from '@/graphql'

export const toolPermissionOptions = fastCartesian([
  Object.values(Tool),
  Object.values(Permission),
]).map(([tool, action]) => ({
  value: `${tool}:${action}`,
  label: `${tool}:${action}`,
}))

export const stringifyToolPermissions = (permissions: ToolPermissionAttributesFragment[]) => {
  return permissions.map((p) => `${p.tool}:${p.action}`)
}

export const parseToolPermissions = (permissions: string[]) => {
  return permissions.map((p) => {
    const [tool, action] = p.split(':')
    return { tool: tool as Tool, action: action as Permission }
  })
}
