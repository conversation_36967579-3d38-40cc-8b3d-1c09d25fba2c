import { useTranslation } from 'react-i18next'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { Loading } from '@/next/components/sdk/loading'
import { TeamListTable } from './index/table'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useMemo } from 'react'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { FloatingRoute } from '@/next/components/sdk/floating_route'
import { EditTeamPage } from './index/edit'
import { CreateTeamPage } from './index/create'
import { graphql } from '@/gql'
import { useQuery } from '@/next/components/sdk/graphql'

const getListTeamQuery = graphql(`
  query ListTeam {
    teams {
      id
      name
      roleId
      leader {
        id
        email
        fullName
      }
    }
  }
`)

export default function Page() {
  const { t } = useTranslation()
  const [, getConfigMaps] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))

  const getListTeam = useQuery(getListTeamQuery)

  const onSuccess = () => {
    getListTeam.refetch()
  }

  return (
    <BaseLayout>
      <PageConfig breadcrumbs={[t('team.listTitle')]} title={t('team.listTitle')} />

      <QueryState query={getConfigMaps}>
        <Loading loading={getListTeam.loading}>
          <TeamListTable collection={getListTeam.data?.teams || []} onDeleted={onSuccess} />
        </Loading>

        <FloatingRoute id="edit">
          <EditTeamPage onUpdated={onSuccess} />
        </FloatingRoute>
      </QueryState>

      <FloatingRoute id="create">
        <CreateTeamPage onCreated={onSuccess} />
      </FloatingRoute>
    </BaseLayout>
  )
}
