import Team from '#models/team'
import { graphql } from '@/gql'
import { DataTableToolbar } from '@/next/components/data-table/data-table-toolbar'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { ConfirmationPopover } from '@/next/components/sdk/confirmation_popover'
import {
  DataTable,
  TableCellButton,
  TableCellDeleteButton,
  TableCellEditButton,
  tableHeader,
} from '@/next/components/sdk/data_table'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import { useMutation } from '@/next/components/sdk/graphql'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/next/components/ui/tooltip'
import { useDataTable } from '@/next/hooks/use-data-table'
import { useDataTableFilterForm } from '@/next/hooks/use_data_table_filter_form'
import { createColumnHelper } from '@tanstack/react-table'
import { CirclePlus } from 'lucide-react'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

const columnHelper = createColumnHelper<Team>()

const deleteTeamMutation = graphql(`
  mutation DeleteTeam($where: DeleteTeamWhere!) {
    deleteTeam(where: $where)
  }
`)

export function TeamListTable({
  collection,
  onDeleted,
}: {
  collection: any
  onDeleted: () => void
}) {
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const [navigate] = useFloatingRoute()
  const { t } = useTranslation()

  const [deleteTeam, { loading }] = useMutation(deleteTeamMutation, {
    onSuccess: () => {
      onDeleted()
    },
  })

  const columns = useMemo(() => {
    return [
      columnHelper.accessor('name', {
        header: tableHeader('Name'),
        meta: {
          label: 'Name',
        },
      }),
      columnHelper.accessor('leader', {
        header: tableHeader('Leader'),
        cell: ({ row }) => {
          return (
            <Tooltip>
              <TooltipTrigger>{row.original.leader?.fullName}</TooltipTrigger>
              <TooltipContent>
                <p>{row.original.leader?.email}</p>
              </TooltipContent>
            </Tooltip>
          )
        },
        meta: {
          label: 'Leader',
        },
      }),
      columnHelper.accessor('roleId', {
        header: tableHeader('Role'),
        cell: ({ row }) => {
          const roleConfigMap = roleConfigMapCollection.find((c) => c.id === row.original.roleId)!
          return <>{t('role.name', { ...roleConfigMap })}</>
        },
        meta: {
          label: 'Role',
        },
      }),
      columnHelper.accessor(() => 'action', {
        id: 'action',
        header: tableHeader('Action'),
        cell: ({ row }) => {
          return (
            <div className="flex items-center">
              <TableCellEditButton
                onClick={() => navigate('edit', { id: row.original.id.toString() })}
              />
              <ConfirmationPopover
                trigger={<TableCellDeleteButton disabled={loading} />}
                onConfirm={() =>
                  deleteTeam({
                    where: {
                      id: row.original.id,
                    },
                  })
                }
                isSubmitting={loading}
                title="Delete selected items"
                confirmText="Are you sure you want to delete the selected items?"
              />
            </div>
          )
        },
        meta: {
          label: 'Action',
        },
      }),
    ]
  }, [roleConfigMapCollection])

  const { table } = useDataTable({
    columns,
    data: collection,
    pageCount: -1,
    form: useDataTableFilterForm({}),
  })

  return (
    <DataTable table={table}>
      <DataTableToolbar table={table}>
        <TableCellButton variant="outline" onClick={() => navigate('create')}>
          <CirclePlus />
          Create
        </TableCellButton>
      </DataTableToolbar>
    </DataTable>
  )
}
