import { graphql } from '@/gql'
import { TeamForm_MemberFragment, TeamForm_TeamFragment } from '@/graphql'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useQuery, useMutation } from '@/next/components/sdk/graphql'
import { useHashState } from '@/next/components/sdk/hash_link'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { isNil } from 'lodash-es'
import { parseAsInteger } from 'nuqs'
import { useMemo } from 'react'
import { schema, TeamFormFields, TeamFormValues } from './form'
import { useTranslation } from 'react-i18next'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import {
  FormFloatingRouteContent,
  FormFloatingRouteHeader,
} from '@/next/components/domain/form_floating_route'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { IconButton } from '@/next/components/sdk/button'
import { uniq } from 'lodash-es'

const getInitialDataQuery = graphql(`
  query TeamEdit_GetInitialData($teamId: Int!) {
    users(where: {}, offset: { perPage: 1000 }) {
      collection {
        ...TeamForm_Member
      }
    }

    team(id: $teamId) {
      ...TeamForm_Team
    }
  }
`)

const updateTeamMutation = graphql(`
  mutation UpdateTeam($where: UpdateTeamWhere!, $form: UpdateTeamForm!) {
    updateTeam(where: $where, form: $form) {
      ...TeamForm_Team
    }
  }
`)

export function EditTeamPage({ onUpdated }: { onUpdated: () => void }) {
  const [id] = useHashState('id', parseAsInteger)

  const getInitialData = useQuery(
    getInitialDataQuery,
    {
      teamId: Number(id),
    },
    {
      skip: isNil(id),
      // TODO: update client-side cache to prevent stale data
      fetchPolicy: 'no-cache',
    }
  )

  return (
    <>
      <FormFloatingRouteHeader title="Edit Team" description={`ID: ${id}`} />

      <QueryState query={getInitialData}>
        <EditForm
          onUpdated={onUpdated}
          users={getInitialData.data?.users.collection!}
          team={getInitialData.data?.team!}
        />
      </QueryState>
    </>
  )
}

function EditForm({
  users,
  team,
  onUpdated,
}: {
  users: TeamForm_MemberFragment[]
  team: TeamForm_TeamFragment
  onUpdated: () => void
}) {
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const { t } = useTranslation()
  const [, goBack] = useFloatingRoute()

  const roleConfigMap = roleConfigMapCollection.find((c) => c.id === team.roleId)!

  const [updateTeam, { loading }] = useMutation(updateTeamMutation, {
    onSuccess: () => {
      goBack()
      onUpdated()
    },
  })

  const form = useForm({
    defaultValues: {
      name: team.name,
      memberEmails: team.members.map((member) => member.email) || [],
      leaderEmail: team.leader?.email,
      roleId: team.roleId,
      roleName: t('role.name', { ...roleConfigMap }),
    },
    resolver: zodResolver(schema),
  })

  const onSubmit = async (data: TeamFormValues) => {
    const memberEmails = uniq(
      [data.leaderEmail, ...data.memberEmails].filter((e): e is string => Boolean(e))
    )

    return updateTeam({
      where: {
        id: Number(team.id),
      },
      form: {
        name: data.name,
        memberEmails,
        leaderEmail: data.leaderEmail || undefined,
      },
    })
  }

  return (
    <FormFloatingRouteContent
      form={form}
      onSubmit={onSubmit}
      slots={{
        after: (
          <IconButton type="submit" loading={loading} disabled={loading}>
            Update
          </IconButton>
        ),
      }}
      state={{
        fields: {
          roleName: {
            disabled: true,
          },
          roleId: {
            hidden: true,
          },
        },
      }}
    >
      <TeamFormFields users={users} form={form} />
    </FormFloatingRouteContent>
  )
}
