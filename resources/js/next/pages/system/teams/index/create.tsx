import { graphql } from '@/gql'
import { TeamForm_MemberFragment } from '@/graphql'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { useQuery, useMutation } from '@/next/components/sdk/graphql'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { useMemo } from 'react'
import { schema, TeamFormValues, TeamFormFields } from './form'
import { useFloatingRoute } from '@/next/components/sdk/floating_route'
import {
  FormFloatingRouteContent,
  FormFloatingRouteHeader,
} from '@/next/components/domain/form_floating_route'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { IconButton } from '@/next/components/sdk/button'
import { uniq } from 'lodash-es'

const getInitialDataQuery = graphql(`
  query TeamCreate_GetInitialData {
    users(where: {}, offset: { perPage: 1000 }) {
      collection {
        ...TeamForm_Member
      }
    }
  }
`)

const createTeamMutation = graphql(`
  mutation CreateTeam($form: CreateTeamForm!) {
    createTeam(form: $form) {
      ...TeamForm_Team
    }
  }
`)

export function CreateTeamPage({ onCreated }: { onCreated: () => void }) {
  const getInitialData = useQuery(getInitialDataQuery)

  return (
    <>
      <FormFloatingRouteHeader title="Create New Team" />

      <QueryState query={getInitialData}>
        <CreateForm users={getInitialData.data?.users.collection!} onCreated={onCreated} />
      </QueryState>
    </>
  )
}

function CreateForm({
  users,
  onCreated,
}: {
  users: TeamForm_MemberFragment[]
  onCreated: () => void
}) {
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const [, goBack] = useFloatingRoute()

  const [createTeam, { loading }] = useMutation(createTeamMutation, {
    onSuccess: () => {
      goBack()
      onCreated()
    },
  })

  const onSubmit = async (data: TeamFormValues) => {
    const memberEmails = uniq(
      [data.leaderEmail, ...data.memberEmails].filter((e): e is string => Boolean(e))
    )

    return createTeam({
      form: {
        name: data.name,
        memberEmails,
        leaderEmail: data.leaderEmail!,
        roleId: data.roleId,
      },
    })
  }

  const form = useForm({
    defaultValues: {
      name: '',
      memberEmails: [],
      leaderEmail: '',
      roleId: roleConfigMapCollection[0]?.id,
      roleName: '',
    },
    resolver: zodResolver(schema),
  })

  return (
    <FormFloatingRouteContent
      form={form}
      onSubmit={onSubmit}
      slots={{
        after: (
          <IconButton type="submit" loading={loading} disabled={loading}>
            Create
          </IconButton>
        ),
      }}
      state={{
        fields: {
          roleName: {
            hidden: true,
          },
        },
      }}
    >
      <TeamFormFields users={users} form={form} />
    </FormFloatingRouteContent>
  )
}
