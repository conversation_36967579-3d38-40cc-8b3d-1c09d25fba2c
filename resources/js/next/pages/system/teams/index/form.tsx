import { TeamForm_MemberFragment } from '@/graphql'
import { useConfigMap } from '@/next/components/sdk/config_map'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { useMemo } from 'react'
import { UseFormReturn } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

export type TeamFormValues = {
  name: string
  memberEmails: string[]
  leaderEmail?: string | null
  roleId: string
  roleName: string
}

export const schema = z
  .object({
    name: z.string().min(1),
    memberEmails: z.array(z.string()),
    leaderEmail: z.string().nullable().optional(),
    roleId: z.string(),
    roleName: z.string(),
  })
  .refine((data) => data.leaderEmail || data.memberEmails.length > 0, {
    message: 'At least one member is required',
    path: ['memberEmails'],
  })

export function TeamFormFields({
  users,
  form,
}: {
  users: TeamForm_MemberFragment[]
  form: UseFormReturn<TeamFormValues>
}) {
  const [[roleConfigMapCollection]] = useConfigMap(useMemo(() => ['cmap.dash.role'], []))
  const { t } = useTranslation()

  return (
    <>
      <FormInputField name="name" label="Team Name" control={form.control} />

      <FormInputField name="roleName" label="Role" control={form.control} />

      <FormSelectField
        name="roleId"
        label="Role"
        control={form.control}
        options={roleConfigMapCollection.map((role) => ({
          value: role.id,
          label: t('role.name', { ...role }),
        }))}
        placeholder="Select a role"
      />

      <FormSelectField
        name="leaderEmail"
        label="Leader"
        control={form.control}
        options={users.map((user) => ({
          value: user.email,
          label: `${user.fullName} (${user.email})`,
        }))}
        placeholder="Select a leader"
      />

      <FormMultiSelectField
        name="memberEmails"
        label="Members"
        control={form.control}
        options={users.map((user) => ({
          value: user.email,
          label: `${user.fullName} (${user.email})`,
        }))}
        placeholder="Select team members"
      />
    </>
  )
}
