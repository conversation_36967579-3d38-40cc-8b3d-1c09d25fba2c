import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import { PageConfig } from '@/next/components/sdk/page_config'
import { QueryState } from '@/next/components/sdk/loading/query_state'
import { Card, CardContent, CardHeader, CardTitle } from '@/next/components/ui/card'
import { Form } from '@/next/components/ui/form'
import { Input } from '@/next/components/ui/input'
import { Button } from '@/next/components/ui/button'
import { FieldLabel } from '@/next/components/ui/field-label'
import { FormPasswordInputField } from '@/next/components/sdk/form/form_password_input_field'
import { NoContent } from '@/next/components/sdk/loading/no_content'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

// Import the user profile hook and update profile hook
import { useUserProfile } from '@/next/api/use_user_profile'
import { useUpdateProfile } from '@/next/api/use_update_profile'

const passwordSchema = z
  .object({
    password: z.string().min(6, {
      message: 'Password must be at least 6 characters.',
    }),
    passwordConfirmation: z.string().min(6, {
      message: 'Password confirmation must be at least 6 characters.',
    }),
  })
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords don't match",
    path: ['passwordConfirmation'],
  })

type PasswordFormValues = z.infer<typeof passwordSchema>

export default function Page() {
  const [userData, getProfile] = useUserProfile()

  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: '',
      passwordConfirmation: '',
    },
  })

  const updatePassword = useUpdateProfile({
    onSuccess: () => {
      form.reset()
    },
  })

  const onSubmit = async (passwordData: PasswordFormValues) => {
    try {
      await updatePassword.mutateAsync(passwordData)
    } catch (error) {
      console.error('Error updating password:', error)
    }
  }

  return (
    <BaseLayout>
      <PageConfig title="My Profile" breadcrumbs={['My Profile']} />

      <QueryState query={getProfile}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <FieldLabel>Name</FieldLabel>
                <Input value={userData?.fullName ?? ''} disabled />
              </div>

              <div className="space-y-2">
                <FieldLabel>Email</FieldLabel>
                <Input value={userData?.email ?? ''} disabled />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>My Team</CardTitle>
            </CardHeader>
            <CardContent>
              <NoContent
                empty={!userData?.team}
                renderEmpty={() => (
                  <p className="text-muted-foreground">You have not joined a team</p>
                )}
              >
                <div className="space-y-4">
                  <div className="space-y-2">
                    <FieldLabel>Team</FieldLabel>
                    <Input value={userData?.team?.name} disabled />
                  </div>

                  <div className="space-y-2">
                    <FieldLabel>Role</FieldLabel>
                    <Input value={userData?.team?.roleId} disabled />
                  </div>
                </div>
              </NoContent>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormPasswordInputField
                    control={form.control}
                    name="password"
                    label="Password"
                    placeholder="New password"
                  />

                  <FormPasswordInputField
                    control={form.control}
                    name="passwordConfirmation"
                    label="Password Confirmation"
                    placeholder="Confirm password"
                  />

                  <Button
                    type="submit"
                    disabled={!form.formState.isDirty || updatePassword.isPending}
                  >
                    Save Password
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </QueryState>
    </BaseLayout>
  )
}
