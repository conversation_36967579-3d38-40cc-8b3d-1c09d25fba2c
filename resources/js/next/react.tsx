import en from '@/translations/en.json'
import i18next from 'i18next'
import { initReactI18next } from 'react-i18next'
import { ApolloProvider } from '@apollo/client'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import isLeapYear from 'dayjs/plugin/isLeapYear'
import minMax from 'dayjs/plugin/minMax'
import { NuqsAdapter } from 'nuqs/adapters/react'
import { Toaster } from '@/next/components/ui/sonner'

import { toolkitApolloClient } from './api/toolkit'
import { ConfigMapProvider } from '@/next/components/sdk/config_map'
import { HashLinkProvider } from '@/next/components/sdk/hash_link'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

dayjs.extend(quarterOfYear)
dayjs.extend(isLeapYear)
dayjs.extend(minMax)

i18next.use(initReactI18next).init({
  resources: {
    en: {
      translation: en,
    },
  },
  lng: 'en',
  fallbackLng: 'en',

  interpolation: {
    escapeValue: false, // react already safes from xss => https://www.i18next.com/translation-function/interpolation#unescape
  },
})

const queryClient = new QueryClient()

export function ReactRoot({ children }: { children: React.ReactNode }) {
  return (
    <NuqsAdapter>
      <QueryClientProvider client={queryClient}>
        <HashLinkProvider>
          <ApolloProvider client={toolkitApolloClient}>
            {children}
            <Toaster />
            <ConfigMapProvider />
          </ApolloProvider>
        </HashLinkProvider>
      </QueryClientProvider>
    </NuqsAdapter>
  )
}
