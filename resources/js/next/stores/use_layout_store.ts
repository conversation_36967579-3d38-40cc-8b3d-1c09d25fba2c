import { create } from 'zustand'

import { NavMainItem } from '@/next/components/nav-main'

export type BreadcrumbItem = {
  url?: string
  title: string
}

type SidebarItem = NavMainItem

interface LayoutStore {
  // Breadcrumbs
  breadcrumbs: BreadcrumbItem[]
  setBreadcrumbs: (items: BreadcrumbItem[]) => void
  clearBreadcrumbs: () => void

  // Sidebar Sub Items (optional overlay)
  sidebarSubItems: SidebarItem[]
  isSidebarSubItemsVisible: boolean
  setSidebarSubItems: (items: SidebarItem[]) => void
  showSidebarSubItems: () => void
  hideSidebarSubItems: () => void
  toggleSidebarSubItems: () => void

  // Helper to set all sidebar properties at once
  setSidebarState: (config: {
    mainItems?: SidebarItem[]
    subItems?: SidebarItem[]
    showSubItems?: boolean
  }) => void
}

export const useLayoutStore = create<LayoutStore>((set) => ({
  // Breadcrumbs
  breadcrumbs: [],
  setBreadcrumbs: (items) => set({ breadcrumbs: items }),
  clearBreadcrumbs: () => set({ breadcrumbs: [] }),

  // Sidebar Sub Items (optional overlay)
  sidebarSubItems: [],
  isSidebarSubItemsVisible: false,
  setSidebarSubItems: (items) => set({ sidebarSubItems: items }),
  showSidebarSubItems: () => set({ isSidebarSubItemsVisible: true }),
  hideSidebarSubItems: () => set({ isSidebarSubItemsVisible: false }),
  toggleSidebarSubItems: () =>
    set((state) => ({
      isSidebarSubItemsVisible: !state.isSidebarSubItemsVisible,
    })),

  // Helper to set all sidebar properties at once
  setSidebarState: (config) =>
    set((state) => ({
      sidebarSubItems: config.subItems ?? state.sidebarSubItems,
      isSidebarSubItemsVisible: config.showSubItems ?? state.isSidebarSubItemsVisible,
    })),
}))
