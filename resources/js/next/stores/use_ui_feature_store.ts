import { create } from 'zustand'
import Cookies from 'js-cookie'

interface UIFeatureStore {
  isExperimentalUIEnabled: boolean
  toggleExperimentalUI: () => void
}

export const useUIFeatureStore = create<UIFeatureStore>((set) => ({
  isExperimentalUIEnabled: Cookies.get('ui_next') === 'true',
  toggleExperimentalUI: () => {
    set((state) => {
      const newState = !state.isExperimentalUIEnabled
      Cookies.set('ui_next', String(newState), {
        expires: 365,
      })

      window.location.reload()

      return { isExperimentalUIEnabled: newState }
    })
  },
}))
