import { useQuery } from '@/next/components/sdk/graphql'
import { useEffect } from 'react'
import { graphql } from '@/gql'
import { useUserStore } from '../stores/use_user_store'
import { QueryResult } from '@apollo/client'
import { GetProfileQuery } from '@/graphql'

export const getProfileQuery = graphql(`
  query GetProfile {
    profile {
      ...UserPublicProfile
      team {
        ...TeamAttributes
      }
      ledTeam {
        ...TeamAttributes
      }
    }
  }
`)

export function useUserProfile() {
  const { user, setUser } = useUserStore()

  // Only fetch if we don't have the user data in store
  const query = useQuery(
    getProfileQuery,
    {},
    {
      skip: !!user,
      toastOnError: false, // Disable default error toast since this is a common query
    }
  )

  const { data } = query

  useEffect(() => {
    if (data?.profile) {
      setUser(data.profile as any)
    }
  }, [data, setUser])

  return [
    user,
    user
      ? ({ loading: false, called: true } as unknown as QueryResult<GetProfileQuery, {}>)
      : query,
  ] as const
}
