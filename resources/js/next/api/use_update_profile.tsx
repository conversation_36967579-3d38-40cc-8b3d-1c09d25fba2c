import { graphql } from '@/gql'
import { useMutation } from '@/next/components/sdk/graphql'

export const updateProfileMutation = graphql(`
  mutation UpdateProfile($form: UpdateProfileForm!) {
    updateProfile(form: $form) {
      ...UserPublicProfile
    }
  }
`)

export function useUpdateProfile(options = {}) {
  const [mutate, mutation] = useMutation(updateProfileMutation, {
    toastOnSuccess: true,
    successMessage: 'Password updated successfully',
    toastOnError: true,
    errorMessage: 'Failed to update password',
    ...options,
  })

  return {
    ...mutation,
    isPending: mutation.loading,
    mutateAsync: async (form: { password: string; passwordConfirmation: string }) => {
      return mutate({
        form: {
          password: form.password,
          passwordConfirmation: form.passwordConfirmation,
        },
      })
    },
  }
}
