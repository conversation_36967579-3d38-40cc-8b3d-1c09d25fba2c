import { graphql } from '@/gql'
import { useQuery } from '../components/sdk/graphql'
import { MetabaseChartWhere } from '@/graphql'

graphql(`
  fragment MetabaseChartAttributes on MetabaseChart {
    url
  }
`)

const query = graphql(`
  query GetMetabaseChart($where: MetabaseChartWhere!) {
    metabaseChart(where: $where) {
      ...MetabaseChartAttributes
    }
  }
`)

export function useGetMetabaseChart(where: MetabaseChartWhere) {
  return useQuery(query, { where })
}
