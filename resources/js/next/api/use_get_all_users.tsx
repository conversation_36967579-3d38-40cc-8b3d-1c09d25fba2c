import { graphql } from '@/gql'
import { useQuery } from '../components/sdk/graphql'

graphql(`
  fragment GetAllUsers_UserAttributes on User {
    id
    email
    fullName
    team {
      ...TeamAttributes
    }
  }
`)

const getAllUsersQuery = graphql(`
  query GetAllUsers {
    users(where: {}, offset: { page: 1, perPage: 1000 }) {
      collection {
        ...GetAllUsers_UserAttributes
      }
    }
  }
`)

export function useGetAllUsers() {
  return useQuery(getAllUsersQuery)
}
