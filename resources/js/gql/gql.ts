/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query ConfigMapCollections($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n": typeof types.ConfigMapCollectionsDocument,
    "\n  query AdminLayout_GetMenu {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n": typeof types.AdminLayout_GetMenuDocument,
    "\n  query Layout_getNotifications {\n    dashboardNotifications {\n      collection {\n        id\n        message\n        isPinned\n        isVisible\n      }\n    }\n  }\n": typeof types.Layout_GetNotificationsDocument,
    "\n  fragment UserPublicProfile on User {\n    id\n    fullName\n    email\n  }\n": typeof types.UserPublicProfileFragmentDoc,
    "\n  fragment TeamAttributes on Team {\n    id\n    name\n    roleId\n  }\n": typeof types.TeamAttributesFragmentDoc,
    "\n  fragment GameAttributes on Game {\n    id\n    name\n    packageName\n    platform\n    isInhouse\n    isActive\n  }\n": typeof types.GameAttributesFragmentDoc,
    "\n  fragment GameMetricAttributes on GameMetric {\n    id\n    date\n    gameId\n  }\n": typeof types.GameMetricAttributesFragmentDoc,
    "\n  fragment GameMetricInstall on GameMetric {\n    paidInstalls\n    organicInstalls\n    totalInstalls\n  }\n": typeof types.GameMetricInstallFragmentDoc,
    "\n  fragment GameMetricRetention on GameMetric {\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n  }\n": typeof types.GameMetricRetentionFragmentDoc,
    "\n  fragment GameMetricImpsDau on GameMetric {\n    bannerImpsDau\n    interImpsDau\n    rewardImpsDau\n    aoaImpsDau\n    mrecImpsDau\n    aoaAdmobImpsDau\n    collapseAdmobImpsDau\n  }\n": typeof types.GameMetricImpsDauFragmentDoc,
    "\n  fragment GameMetricPlaytime on GameMetric {\n    averageSession\n    playtime\n  }\n": typeof types.GameMetricPlaytimeFragmentDoc,
    "\n  fragment GameMetricMetadata on GameMetric {\n    metadata {\n      ...GameMetricMetadataAttributes\n    }\n  }\n": typeof types.GameMetricMetadataFragmentDoc,
    "\n  fragment GameMetricMetadataAttributes on GameMetricMetadata {\n    id\n    note\n    uaNote\n    monetNote\n    productNote\n    versionNote\n  }\n": typeof types.GameMetricMetadataAttributesFragmentDoc,
    "\n  fragment ConfigMapCollectionAttributes on ConfigMapCollection {\n    id\n    items\n  }\n": typeof types.ConfigMapCollectionAttributesFragmentDoc,
    "\n  fragment ConfigMapSoleAttributes on ConfigMap {\n    id\n    sole\n  }\n": typeof types.ConfigMapSoleAttributesFragmentDoc,
    "\n  fragment GameLevelDropAttributes on GameLevelDrop {\n    version\n    world\n    level\n    activeUserCount\n    userDropCount\n    userDropRate\n    completionRate\n    winRate\n    playtimeSecPerUser\n    overallUserDropRate\n    attemptCountPerUser\n  }\n": typeof types.GameLevelDropAttributesFragmentDoc,
    "\n  fragment AccessControlAttributes on AccessControl {\n    roles {\n      roleId\n      permits\n    }\n  }\n": typeof types.AccessControlAttributesFragmentDoc,
    "\n  fragment ModelAttribute on Attribute {\n    name\n    displayName\n    aggregate\n    permission\n  }\n": typeof types.ModelAttributeFragmentDoc,
    "\n  fragment PageInfoAttributes on PageInfo {\n    total\n    perPage\n    currentPage\n    lastPage\n    firstPage\n  }\n": typeof types.PageInfoAttributesFragmentDoc,
    "\n  fragment V2GameMetricAttributes on V2GameMetric {\n    id\n    paidInstalls\n    organicInstalls\n    organicPercentage\n    totalInstalls\n    cost\n    cpi\n    roas\n    revenue\n    profit\n    dailyActiveUsers\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n    uaNote\n    monetNote\n    productNote\n    versionNote\n    note\n  }\n": typeof types.V2GameMetricAttributesFragmentDoc,
    "\n  fragment AdPerformanceAttributes on AdPerformance {\n    ecpm\n    arpDau\n    impsDau\n    adType\n    revenue\n  }\n": typeof types.AdPerformanceAttributesFragmentDoc,
    "\n  fragment AclGameMetricMetadataAttributes on GameMetric {\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n": typeof types.AclGameMetricMetadataAttributesFragmentDoc,
    "\n  fragment AclGameMetricAttributes on GameMetric {\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    bannerImpsDau @include(if: $canViewBannerImpsDau)\n    interImpsDau @include(if: $canViewInterImpsDau)\n    rewardImpsDau @include(if: $canViewRewardImpsDau)\n    aoaImpsDau @include(if: $canViewAoaImpsDau)\n    mrecImpsDau @include(if: $canViewMrecImpsDau)\n    audioImpsDau @include(if: $canViewAudioImpsDau)\n    aoaAdmobImpsDau @include(if: $canViewAoaAdmobImpsDau)\n    collapseAdmobImpsDau @include(if: $canViewCollapseAdmobImpsDau)\n    nativeAdmobImpsDau @include(if: $canViewNativeAdmobImpsDau)\n    adaptiveAdmobImpsDau @include(if: $canViewAdaptiveAdmobImpsDau)\n    mrecAdmobImpsDau @include(if: $canViewMrecAdmobImpsDau)\n    averageSession @include(if: $canViewAverageSession)\n    playtime @include(if: $canViewPlaytime)\n  }\n": typeof types.AclGameMetricAttributesFragmentDoc,
    "\n  fragment AclAdPerformanceAttributes on AdPerformance {\n    adType\n    revenue @include(if: $canViewGameRevenueRevenue)\n    ecpm @include(if: $canViewGameRevenueEcpm)\n    arpDau @include(if: $canViewGameRevenueArpDau)\n    impsDau @include(if: $canViewGameRevenueImpsDau)\n  }\n": typeof types.AclAdPerformanceAttributesFragmentDoc,
    "\n  fragment AclGameMetricV2Attributes on V2GameMetric {\n    id\n\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    dailyActiveUsers @include(if: $canViewDailyActiveUsers)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    sessions @include(if: $canViewSessions)\n    playtime @include(if: $canViewPlaytime)\n\n    adPerformances {\n      ...AclAdPerformanceAttributes\n    }\n  }\n": typeof types.AclGameMetricV2AttributesFragmentDoc,
    "\n  fragment AdTypeAttributes on AdType {\n    id\n    name\n    order\n  }\n": typeof types.AdTypeAttributesFragmentDoc,
    "\n  fragment AdAgencyAttributes on AdAgency {\n    id\n    name\n  }\n": typeof types.AdAgencyAttributesFragmentDoc,
    "\n  fragment GameAgencyMetricAttributes on AgencyMetric {\n    cost\n    paidInstalls\n  }\n": typeof types.GameAgencyMetricAttributesFragmentDoc,
    "\n  fragment SideMenuAttributes on SideMenu {\n    name\n    path\n    group\n    icon\n  }\n": typeof types.SideMenuAttributesFragmentDoc,
    "\n  fragment GameStudioAttributes on GameStudio {\n    id\n    name\n  }\n": typeof types.GameStudioAttributesFragmentDoc,
    "\n  fragment GameStudioMetricAttributes on GameStudioMetric {\n    game {\n      ...GameAttributes\n    }\n\n    totalInstalls\n    mmpCostAmount\n    totalAgencyCost\n    revenue\n    profit\n  }\n": typeof types.GameStudioMetricAttributesFragmentDoc,
    "\n  fragment UserPublicInfoAttributes on UserPublicInfo {\n    id\n    fullName\n  }\n": typeof types.UserPublicInfoAttributesFragmentDoc,
    "\n  fragment WorkflowStepAttributes on WorkflowStep {\n    name\n    action\n    alternateAction\n    assignee {\n      ...UserPublicInfoAttributes\n    }\n  }\n": typeof types.WorkflowStepAttributesFragmentDoc,
    "\n  fragment BudgetRequestAttributes on BudgetRequest {\n    id\n    expirationDate\n    createdAt\n    stepId\n    lastAction\n    description\n    step {\n      ...WorkflowStepAttributes\n    }\n    game {\n      ...GameAttributes\n    }\n    workflow {\n      ...WorkflowAttributes\n    }\n    createdBy {\n      ...UserPublicInfoAttributes\n    }\n    amount\n  }\n": typeof types.BudgetRequestAttributesFragmentDoc,
    "\n  fragment NetworkAttributes on Network {\n    id\n    name\n  }\n": typeof types.NetworkAttributesFragmentDoc,
    "\n  fragment MediationAttributes on Mediation {\n    id\n    name\n  }\n": typeof types.MediationAttributesFragmentDoc,
    "\n  fragment FirebaseExperimentAttributes on FirebaseExperiment {\n    name\n  }\n": typeof types.FirebaseExperimentAttributesFragmentDoc,
    "\n  fragment FirebaseMetricAttributes on FirebaseMetric {\n    date\n    variantId\n    version\n    countryCode\n\n    sessionCount\n    sessionNthDayCounts\n    dailyActiveUserCount\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    retentionNthDayRates\n    installCount\n    sessionCountPerActiveUser\n    retentionRate\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n": typeof types.FirebaseMetricAttributesFragmentDoc,
    "\n  fragment FirebaseVersionVariantAttributes on FirebaseVersionVariant {\n    id\n    experiment\n    name\n  }\n": typeof types.FirebaseVersionVariantAttributesFragmentDoc,
    "\n  fragment FirebaseAdMetricAttributes on FirebaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n  }\n": typeof types.FirebaseAdMetricAttributesFragmentDoc,
    "\n  fragment UserAttributes on User {\n    id\n    fullName\n    email\n    kind\n    hasPassword\n    isDeleted\n    note\n    toolPermissions {\n      ...ToolPermissionAttributes\n    }\n  }\n": typeof types.UserAttributesFragmentDoc,
    "\n  fragment ToolPermissionAttributes on ToolPermission {\n    tool\n    action\n  }\n": typeof types.ToolPermissionAttributesFragmentDoc,
    "\n  fragment GameRoleMembershipAttributes on GameRoleMembership {\n    id\n    roleId\n    storeId\n    users {\n      email\n    }\n  }\n": typeof types.GameRoleMembershipAttributesFragmentDoc,
    "\n  fragment GameRolesAttributes on GameRoles {\n    id\n    roles {\n      id\n      users {\n        email\n      }\n    }\n  }\n": typeof types.GameRolesAttributesFragmentDoc,
    "\n  fragment ReleaseMetricAttributes on ReleaseMetric {\n    date\n    version\n    countryCode\n    sessionCount\n    sessionNthDayCounts\n    sessionCountPerActiveUser\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    installCount\n    retentionRate\n    retentionNthDayRates\n    dailyActiveUserCount\n    lifetimeNthDayValues\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n    adRevNthDayGrossAmounts\n    impressionNthDayCounts\n  }\n": typeof types.ReleaseMetricAttributesFragmentDoc,
    "\n  fragment ReleaseAdMetricAttributes on ReleaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n": typeof types.ReleaseAdMetricAttributesFragmentDoc,
    "\n  fragment GameMetricIndex_GameMetricAttributes on GameMetric {\n    id\n    date\n    ...AclGameMetricAttributes\n    ...AclGameMetricMetadataAttributes\n  }\n": typeof types.GameMetricIndex_GameMetricAttributesFragmentDoc,
    "\n  fragment GameCostAttributes on GameCost {\n    id\n    date\n    type\n    network {\n      id\n      name\n    }\n    preTaxAmount\n    totalAmount\n    mmpAmount\n    taxAmount\n  }\n": typeof types.GameCostAttributesFragmentDoc,
    "\n  fragment GetAllUsers_UserAttributes on User {\n    id\n    email\n    fullName\n    team {\n      ...TeamAttributes\n    }\n  }\n": typeof types.GetAllUsers_UserAttributesFragmentDoc,
    "\n  query GetAllUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GetAllUsers_UserAttributes\n      }\n    }\n  }\n": typeof types.GetAllUsersDocument,
    "\n  fragment MetabaseChartAttributes on MetabaseChart {\n    url\n  }\n": typeof types.MetabaseChartAttributesFragmentDoc,
    "\n  query GetMetabaseChart($where: MetabaseChartWhere!) {\n    metabaseChart(where: $where) {\n      ...MetabaseChartAttributes\n    }\n  }\n": typeof types.GetMetabaseChartDocument,
    "\n  mutation UpdateProfile($form: UpdateProfileForm!) {\n    updateProfile(form: $form) {\n      ...UserPublicProfile\n    }\n  }\n": typeof types.UpdateProfileDocument,
    "\n  query GetProfile {\n    profile {\n      ...UserPublicProfile\n      team {\n        ...TeamAttributes\n      }\n      ledTeam {\n        ...TeamAttributes\n      }\n    }\n  }\n": typeof types.GetProfileDocument,
    "\n  mutation ViewPresetEditor_CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n": typeof types.ViewPresetEditor_CreateViewPresetDocument,
    "\n  mutation ViewPresetSelection_DeleteViewPreset($where: DeleteViewPresetsWhere!) {\n    deleteViewPresets(where: $where)\n  }\n": typeof types.ViewPresetSelection_DeleteViewPresetDocument,
    "\n  query GetConfigMaps($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n": typeof types.GetConfigMapsDocument,
    "\n  query Layout_GetAnnouncements {\n    dashboardNotifications {\n      collection {\n        ...AnnouncementAttributes\n      }\n    }\n  }\n": typeof types.Layout_GetAnnouncementsDocument,
    "\n  fragment AnnouncementAttributes on DashboardNotification {\n    id\n    message\n    isPinned\n    isVisible\n  }\n": typeof types.AnnouncementAttributesFragmentDoc,
    "\n  mutation UpdateConfigMaps($where: UpdateConfigMapsWhere!) {\n    updateConfigMaps(where: $where)\n  }\n": typeof types.UpdateConfigMapsDocument,
    "\n  query Layout_GetGames($keyword: String!) {\n    games(where: { keyword: $keyword }, offset: { page: 1, perPage: 10 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n": typeof types.Layout_GetGamesDocument,
    "\n  query Sidebar_GetMainMenus {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n": typeof types.Sidebar_GetMainMenusDocument,
    "\n  query Acl_GameMetricAttributes(\n    $readSubject: String!\n    $writeSubject: String!\n    $modelName: String!\n  ) {\n    attributes(where: { gameId: null, modelName: $modelName }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    read: accessControl(where: { subject: $readSubject }) {\n      ...AccessControlAttributes\n    }\n\n    write: accessControl(where: { subject: $writeSubject }) {\n      ...AccessControlAttributes\n    }\n  }\n": typeof types.Acl_GameMetricAttributesDocument,
    "\n  mutation UpdateAccessControl($where: AccessControlWhere!, $form: AccessControlUpdateForm!) {\n    updateAccessControl(where: $where, form: $form) {\n      ...AccessControlAttributes\n    }\n  }\n": typeof types.UpdateAccessControlDocument,
    "\n  query GetAccessControl($where: AccessControlWhere!) {\n    accessControl(where: $where) {\n      ...AccessControlAttributes\n    }\n  }\n": typeof types.GetAccessControlDocument,
    "\n  fragment WorkflowStepActionAttributes on WorkflowStepAction {\n    action\n  }\n": typeof types.WorkflowStepActionAttributesFragmentDoc,
    "\n  query BudgetRequestIndex_GetInitialData {\n    games(where: {}, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    workflowStepActions {\n      collection {\n        ...WorkflowStepActionAttributes\n      }\n    }\n  }\n": typeof types.BudgetRequestIndex_GetInitialDataDocument,
    "\n  query BudgetRequestIndex_GetBudgetRequests($where: BudgetRequestsWhere!, $offset: Offset) {\n    budgetRequests(where: $where, offset: $offset) {\n      collection {\n        ...BudgetRequestAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": typeof types.BudgetRequestIndex_GetBudgetRequestsDocument,
    "\n  mutation BudgetRequestIndex_UpdateBudgetRequestsState($forms: [UpdateBudgetRequestStateForm!]!) {\n    updateBudgetRequestsState(forms: $forms) {\n      ...BudgetRequestAttributes\n    }\n  }\n": typeof types.BudgetRequestIndex_UpdateBudgetRequestsStateDocument,
    "\n  mutation BudgetRequestIndex_DeleteBudgetRequests($ids: [Int!]!) {\n    deleteBudgetRequests(where: { ids: $ids })\n  }\n": typeof types.BudgetRequestIndex_DeleteBudgetRequestsDocument,
    "\n  mutation BudgetRequestIndex_CreateBudgetRequest($form: CreateBudgetRequestForm!) {\n    createBudgetRequest(form: $form) {\n      ...BudgetRequestAttributes\n    }\n  }\n": typeof types.BudgetRequestIndex_CreateBudgetRequestDocument,
    "\n  mutation BudgetRequestIndex_UpdateBudgetRequests($form: UpdateBudgetRequestForm!) {\n    updateBudgetRequests(forms: [$form]) {\n      ...BudgetRequestAttributes\n    }\n  }\n": typeof types.BudgetRequestIndex_UpdateBudgetRequestsDocument,
    "\n  query GamesCampaignMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.GamesCampaignMetricsIndex_GetViewPresetsDocument,
    "\n  query GamesCampaignMetricsIndex_GetData(\n    $where: AdMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    adMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...AdMetricAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.GamesCampaignMetricsIndex_GetDataDocument,
    "\n  fragment FirebaseExperimentIndex_FirebaseMetric on FirebaseMetric {\n    ...FirebaseMetricAttributes\n\n    ads {\n      ...FirebaseAdMetricAttributes\n    }\n  }\n": typeof types.FirebaseExperimentIndex_FirebaseMetricFragmentDoc,
    "\n  query FirebaseExperimentIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.FirebaseExperimentIndex_GetViewPresetsDocument,
    "\n  query FirebaseExperimentIndex_GetInitialData($gameId: ID!) {\n    firebaseExperiments(where: { gameId: $gameId }) {\n      collection {\n        ...FirebaseExperimentAttributes\n      }\n    }\n\n    networks(where: { category: FIREBASE }) {\n      collection {\n        ...NetworkAttributes\n      }\n    }\n\n    mediations {\n      collection {\n        ...MediationAttributes\n      }\n    }\n  }\n": typeof types.FirebaseExperimentIndex_GetInitialDataDocument,
    "\n  query FirebaseExperimentIndex_GetVersionVariants($where: FirebaseVersionVariantsWhere!) {\n    firebaseVersionVariants(where: $where) {\n      collection {\n        ...FirebaseVersionVariantAttributes\n      }\n    }\n  }\n": typeof types.FirebaseExperimentIndex_GetVersionVariantsDocument,
    "\n  query FirebaseExperimentIndex_GetMetrics(\n    $where: FirebaseMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    firebaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...FirebaseExperimentIndex_FirebaseMetric\n      }\n\n      variants {\n        ...FirebaseVersionVariantAttributes\n      }\n\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.FirebaseExperimentIndex_GetMetricsDocument,
    "\n  fragment GameAgencyCostAttributes on GameAgencyCost {\n    totalCost\n    mediationCost\n    varianceRate\n    agencyId\n  }\n": typeof types.GameAgencyCostAttributesFragmentDoc,
    "\n  mutation GameCostEdit_UpdateGameCost($form: UpdateGameCostForm!, $where: UpdateGameCostWhere!) {\n    updateGameCost(form: $form, where: $where)\n  }\n": typeof types.GameCostEdit_UpdateGameCostDocument,
    "\n  query GameCostsOverview_GetAdNetworks {\n    adNetworks {\n      collection {\n        ...GameCostIndexOverview_AdNetworkAttributes\n      }\n    }\n  }\n": typeof types.GameCostsOverview_GetAdNetworksDocument,
    "\n  query GameSpendShow_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": typeof types.GameSpendShow_InitDocument,
    "\n  query GameCostsComparison_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": typeof types.GameCostsComparison_GetDataDocument,
    "\n  query GameCostsAggregation_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n": typeof types.GameCostsAggregation_GetDataDocument,
    "\n  fragment GameCostIndexOverview_GameCostAttributes on GameCost {\n    id\n    date\n    mmpAmount\n    preTaxAmount\n    totalAmount\n    taxAmount\n    network {\n      id\n      name\n    }\n  }\n": typeof types.GameCostIndexOverview_GameCostAttributesFragmentDoc,
    "\n  query GameCostsOverview_GetData($where: GameCostsWhere!, $offset: Offset!) {\n    gameCosts(where: $where, offset: $offset) {\n      collection {\n        ...GameCostIndexOverview_GameCostAttributes\n      }\n      meta {\n        aggregation {\n          mmpAmount\n          totalAmount\n          preTaxAmount\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n": typeof types.GameCostsOverview_GetDataDocument,
    "\n  fragment GameCostIndexOverview_AdNetworkAttributes on AdNetwork {\n    id\n    name\n    inputType\n  }\n": typeof types.GameCostIndexOverview_AdNetworkAttributesFragmentDoc,
    "\n  mutation GameMetricEdit_UpdateGameMetric(\n    $where: UpdateGameMetricWhere!\n    $form: UpdateGameMetricForm!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    updateGameMetric(where: $where, form: $form) {\n      ...GameMetricIndex_GameMetricAttributes\n    }\n  }\n": typeof types.GameMetricEdit_UpdateGameMetricDocument,
    "\n  query GamesMetricsShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": typeof types.GamesMetricsShow_GetInitDataDocument,
    "\n  query GamesMetricsShow_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        ...GameMetricIndex_GameMetricAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": typeof types.GamesMetricsShow_GetMetricsDocument,
    "\n  query GameMetricIndex_GetAggregation(\n    $gameId: ID!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n  ) {\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n": typeof types.GameMetricIndex_GetAggregationDocument,
    "\n  query GameMetricNextIndex_GetInitData($gameId: ID!) {\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": typeof types.GameMetricNextIndex_GetInitDataDocument,
    "\n  fragment GameMetricNextIndex_GameMetricAttributes on V2GameMetric {\n    date\n    gameId\n    ...AclGameMetricV2Attributes\n\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n": typeof types.GameMetricNextIndex_GameMetricAttributesFragmentDoc,
    "\n  query GameMetricNextIndex_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          ...GameMetricNextIndex_GameMetricAttributes\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n": typeof types.GameMetricNextIndex_GetMetricsDocument,
    "\n  query GameMetricNextIndex_GetAggregation(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n": typeof types.GameMetricNextIndex_GetAggregationDocument,
    "\n  query GamesIndex_GetInitialData($where: GamesWhere!) {\n    games(where: $where, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n": typeof types.GamesIndex_GetInitialDataDocument,
    "\n  query ListGameRoles($gameIds: [ID!]!) {\n    gameRoles(gameIds: $gameIds) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n": typeof types.ListGameRolesDocument,
    "\n  mutation UpdateGameRoles($forms: [UpdateGameRolesGame!]!) {\n    updateGameRoles(forms: $forms) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n": typeof types.UpdateGameRolesDocument,
    "\n  query GameRoles_GetData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": typeof types.GameRoles_GetDataDocument,
    "\n  fragment ReleaseMetricIndex_ReleaseMetricAttributes on ReleaseMetric {\n    ...ReleaseMetricAttributes\n\n    ads {\n      ...ReleaseAdMetricAttributes\n    }\n  }\n": typeof types.ReleaseMetricIndex_ReleaseMetricAttributesFragmentDoc,
    "\n  query ReleaseMetricIndex_GetData($where: ReleaseMetricsWhere!, $group: Group, $preset: Preset) {\n    releaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...ReleaseMetricIndex_ReleaseMetricAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.ReleaseMetricIndex_GetDataDocument,
    "\n  query ReleaseMetricIndex_GetVersion($where: ReleaseVersionsWhere!) {\n    releaseVersions(where: $where) {\n      collection {\n        version\n      }\n    }\n  }\n": typeof types.ReleaseMetricIndex_GetVersionDocument,
    "\n  query GamesReleaseMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.GamesReleaseMetricsIndex_GetViewPresetsDocument,
    "\n  query AdmobGamesQuery {\n    admobGames {\n      collection\n    }\n  }\n": typeof types.AdmobGamesQueryDocument,
    "\n  query AdmobMetrics(\n    $gameId: String!\n    $baselineDateFrom: Date!\n    $baselineDateTo: Date!\n    $targetDateFrom: Date!\n    $targetDateTo: Date!\n    $formats: [String!]!\n  ) {\n    baseline: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $baselineDateFrom\n        dateTo: $baselineDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n\n    target: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $targetDateFrom\n        dateTo: $targetDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n  }\n": typeof types.AdmobMetricsDocument,
    "\n  query ListTeam {\n    teams {\n      id\n      name\n      roleId\n      leader {\n        id\n        email\n        fullName\n      }\n    }\n  }\n": typeof types.ListTeamDocument,
    "\n  query TeamCreate_GetInitialData {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n  }\n": typeof types.TeamCreate_GetInitialDataDocument,
    "\n  mutation CreateTeam($form: CreateTeamForm!) {\n    createTeam(form: $form) {\n      ...TeamForm_Team\n    }\n  }\n": typeof types.CreateTeamDocument,
    "\n  query TeamEdit_GetInitialData($teamId: Int!) {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n\n    team(id: $teamId) {\n      ...TeamForm_Team\n    }\n  }\n": typeof types.TeamEdit_GetInitialDataDocument,
    "\n  mutation UpdateTeam($where: UpdateTeamWhere!, $form: UpdateTeamForm!) {\n    updateTeam(where: $where, form: $form) {\n      ...TeamForm_Team\n    }\n  }\n": typeof types.UpdateTeamDocument,
    "\n  mutation DeleteTeam($where: DeleteTeamWhere!) {\n    deleteTeam(where: $where)\n  }\n": typeof types.DeleteTeamDocument,
    "\n  fragment TeamForm_Member on User {\n    ...UserPublicProfile\n    inchargedGames {\n      storeId\n      roleId\n    }\n    team {\n      ...TeamAttributes\n    }\n  }\n": typeof types.TeamForm_MemberFragmentDoc,
    "\n  fragment TeamForm_Team on Team {\n    ...TeamAttributes\n    members {\n      ...UserPublicProfile\n    }\n    leader {\n      ...UserPublicProfile\n    }\n  }\n": typeof types.TeamForm_TeamFragmentDoc,
    "\n  query UserIndex_GetUsers($offset: Offset!) {\n    users(where: {}, offset: $offset) {\n      collection {\n        ...UserAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": typeof types.UserIndex_GetUsersDocument,
    "\n  mutation UserIndex_UpdateUser($where: UpdateUserWhere!, $form: UpdateUserForm!) {\n    updateUser(where: $where, form: $form) {\n      ...UserAttributes\n    }\n  }\n": typeof types.UserIndex_UpdateUserDocument,
    "\n  mutation UserIndex_CreateUser($form: CreateUserForm!) {\n    createUser(form: $form) {\n      ...UserAttributes\n    }\n  }\n": typeof types.UserIndex_CreateUserDocument,
    "\n  mutation UserIndex_DeleteUsers($where: DeleteUsersWhere!) {\n    deleteUsers(where: $where)\n  }\n": typeof types.UserIndex_DeleteUsersDocument,
    "\n  fragment WorkflowAttributes on Workflow {\n    id\n    name\n    roleId\n    steps {\n      ...WorkflowStepAttributes\n    }\n  }\n": typeof types.WorkflowAttributesFragmentDoc,
    "\n  query WorkflowsIndex_GetInitialData {\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    users(where: {}, offset: { perPage: 9999 }) {\n      collection {\n        ...UserPublicProfile\n      }\n    }\n  }\n": typeof types.WorkflowsIndex_GetInitialDataDocument,
    "\n  mutation WorkflowsIndex_Create($form: CreateWorkflowForm!) {\n    createWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n": typeof types.WorkflowsIndex_CreateDocument,
    "\n  mutation WorkflowsIndex_Update($form: UpdateWorkflowForm!) {\n    updateWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n": typeof types.WorkflowsIndex_UpdateDocument,
    "\n  fragment GameCreativeMetricAttributes on GameCreativeMetric {\n    id\n    date\n    campaign\n    adGroup\n    adGroupStartDate\n    adSet\n    editor\n    isPlayable\n    targetRoasRate\n    preTaxCostAmount\n    grossRevenueAmount\n    clickCount\n    impressionCount\n    installCount\n    roasRate\n    cpi\n    conversionRate\n    clickthroughRate\n\n    agency {\n      ...AdAgencyAttributes\n    }\n  }\n": typeof types.GameCreativeMetricAttributesFragmentDoc,
    "\n  query GameCreativeMetrics_Index(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $page: Int!\n    $perPage: Int!\n    $direction: OrderDirection!\n  ) {\n    gameCreativeMetrics(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $direction }\n    ) {\n      collection {\n        ...GameCreativeMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": typeof types.GameCreativeMetrics_IndexDocument,
    "\n  query GameLevelDropIndex_QueryData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    gameLevelDropVersions(where: { gameId: $gameId }) {\n      collection\n    }\n  }\n": typeof types.GameLevelDropIndex_QueryDataDocument,
    "\n  query GameLevelDropIndex_QueryVersions($gameId: ID!, $versions: [String!]!) {\n    gameLevelDrops(where: { gameId: $gameId, versions: $versions }) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n  }\n": typeof types.GameLevelDropIndex_QueryVersionsDocument,
    "\n  fragment GameNetworkRevenueAggregationAttributes on GameNetworkRevenue {\n    networkId\n    revenue\n    mediationRevenue\n    varianceRate\n  }\n": typeof types.GameNetworkRevenueAggregationAttributesFragmentDoc,
    "\n  fragment GameRevenueMetricAttributes on AgencyMetric {\n    revenue\n  }\n": typeof types.GameRevenueMetricAttributesFragmentDoc,
    "\n  fragment GameNetworkRevenueAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAggregationAttributes\n    date\n  }\n\n  fragment GameNetworkRevenueWithMetricAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAttributes\n    metric {\n      ...GameRevenueMetricAttributes\n    }\n  }\n": typeof types.GameNetworkRevenueAttributesFragmentDoc,
    "\n  query GameRevenueIndex_getNetworkRevenue(\n    $dateFrom: Date!\n    $dateTo: Date!\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n    $orderDirection: OrderDirection!\n  ) {\n    gameNetworkRevenues(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameNetworkRevenueWithMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameNetworkRevenue(where: { gameId: $gameId }) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n        network {\n          id\n          name\n        }\n      }\n    }\n\n    total: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n\n    weekly: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n  }\n": typeof types.GameRevenueIndex_GetNetworkRevenueDocument,
    "\n  query GameRevenueExplorerIndex_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": typeof types.GameRevenueExplorerIndex_InitDocument,
    "\n  query GameReviewIndex_Setting($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n    gamePerformanceSetting(gameId: $gameId) {\n      criterias {\n        conclusion\n        metrics\n      }\n    }\n  }\n": typeof types.GameReviewIndex_SettingDocument,
    "\n  query GameReviewIndex_Data($gameId: ID!, $from: Date!, $to: Date!) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $from, dateLte: $to }\n      offset: { perPage: 7, page: 1 }\n      order: { direction: ASC }\n    ) {\n      collection {\n        date\n        roas\n        playtime\n        retentionRateDay1\n        cpi\n        bannerImpsDau\n        interImpsDau\n        ...GameMetricMetadata\n      }\n    }\n\n    gameReview(gameId: $gameId, date: $from) {\n      productNote\n      marketingNote\n    }\n  }\n": typeof types.GameReviewIndex_DataDocument,
    "\n  query GameCostIndex_AgencyData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n": typeof types.GameCostIndex_AgencyDataDocument,
    "\n  query ProductMetricIndex_GetVersions($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {\n    gameRewardUsageVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameDailyLevelDropVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gamePlaytimeVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameRetentionRateVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n  }\n": typeof types.ProductMetricIndex_GetVersionsDocument,
    "\n  query ProductMetricIndex_GetGame($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": typeof types.ProductMetricIndex_GetGameDocument,
    "\n  query ProductMetricIndex_VersionsData(\n    $gameId: ID!\n    $versions: [String!]!\n    $groupByLevel: Boolean!\n    $groupByLocation: Boolean!\n    $group: [String!]!\n    $order: OrderDirection!\n    $activeDateFrom: Date!\n    $activeDateTo: Date!\n    $installTimeFrom: Date\n    $installTimeTo: Date\n  ) {\n    gameRewardUsages(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n      group: { fields: $group }\n      order: { direction: $order }\n    ) {\n      collection {\n        location @include(if: $groupByLocation)\n        level @include(if: $groupByLevel)\n        world @include(if: $groupByLevel)\n        useCount\n      }\n    }\n\n    gameLevelDrops(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n      }\n    ) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n\n    gameRetentionRates(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n    ) {\n      collection {\n        date\n        gameId\n        newUsers\n        day1\n        day2\n        day3\n        day4\n        day5\n        day6\n        day7\n      }\n    }\n\n    aggregatePlaytime(\n      where: {\n        gameId: $gameId\n        activeDateFrom: $activeDateFrom\n        activeDateTo: $activeDateTo\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n        versions: $versions\n      }\n    ) {\n      engagementSessionCount\n      playtimeSec\n    }\n  }\n": typeof types.ProductMetricIndex_VersionsDataDocument,
    "\n  fragment GameRevenueIndex_UserAttributes on User {\n    email\n    fullName\n  }\n": typeof types.GameRevenueIndex_UserAttributesFragmentDoc,
    "\n  query GameRevenuesIndex_GetUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GameRevenueIndex_UserAttributes\n      }\n    }\n  }\n": typeof types.GameRevenuesIndex_GetUsersDocument,
    "\n  query GameStudioMetricsIndex_GetInitialData {\n    gameStudios {\n      collection {\n        ...GameStudioAttributes\n      }\n    }\n  }\n": typeof types.GameStudioMetricsIndex_GetInitialDataDocument,
    "\n  query GameStudioMetricsIndex_GetMetrics($studioId: Int!, $dateFrom: Date!, $dateTo: Date!) {\n    metrics: gameStudioMetrics(\n      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameStudioMetricAttributes\n      }\n    }\n  }\n": typeof types.GameStudioMetricsIndex_GetMetricsDocument,
    "\n  fragment AdMetricAttributes on AdMetric {\n    date\n    adId\n    groupId\n    campaignId\n    countryCode\n    agencyId\n    adRevGrossAmount\n    adCostNonTaxAmount\n    impressionCount\n    roasNthDayRates\n    retentionNthDayRates\n    activeUserNthDayCounts\n    sessionNthDayCounts\n    sessionCount\n    dailyActiveUserCount\n    retentionRate\n    clickCount\n    installCount\n    adRevNthDayGrossAmounts\n    cpi\n    ctr\n    cvr\n    cpc\n    ipm\n    roas\n  }\n": typeof types.AdMetricAttributesFragmentDoc,
    "\n  fragment AdAttributes on Ad {\n    id\n    name\n    campaignId\n    groupId\n  }\n": typeof types.AdAttributesFragmentDoc,
    "\n  fragment AdGroupAttributes on AdGroup {\n    id\n    name\n  }\n": typeof types.AdGroupAttributesFragmentDoc,
    "\n  fragment CampaignAttributes on Campaign {\n    id\n    name\n  }\n": typeof types.CampaignAttributesFragmentDoc,
    "\n  fragment ViewPresetAttributes on ViewPreset {\n    id\n    name\n    attributes {\n      name\n      isCohort\n      cohortDays\n    }\n  }\n": typeof types.ViewPresetAttributesFragmentDoc,
    "\n  query AdMetricsQuery(\n    $gameId: ID!\n    $dateFrom: FilterValue!\n    $dateTo: FilterValue!\n    $groupByFields: [String!]!\n    $campaignId: Filter\n    $agencyId: Filter\n    $groupId: Filter\n    $adId: Filter\n    $countryCode: Filter\n    $cvr: Filter\n    $cpi: Filter\n    $ctr: Filter\n    $roas: Filter\n    $adRevGrossAmount: Filter\n    $adCostNonTaxAmount: Filter\n    $impressionCount: Filter\n    $clickCount: Filter\n    $installCount: Filter\n    $cpc: Filter\n    $ipm: Filter\n    $preset: Int\n  ) {\n    adMetrics(\n      where: {\n        gameId: $gameId\n        date: { operator: BETWEEN, values: [$dateFrom, $dateTo] }\n        campaignId: $campaignId\n        groupId: $groupId\n        adId: $adId\n        agencyId: $agencyId\n        countryCode: $countryCode\n        cvr: $cvr\n        cpi: $cpi\n        ctr: $ctr\n        roas: $roas\n        adRevGrossAmount: $adRevGrossAmount\n        adCostNonTaxAmount: $adCostNonTaxAmount\n        impressionCount: $impressionCount\n        clickCount: $clickCount\n        installCount: $installCount\n        cpc: $cpc\n        ipm: $ipm\n      }\n      group: { fields: $groupByFields }\n      preset: { viewPresetId: $preset }\n    ) {\n      collection {\n        ...AdMetricAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n    }\n  }\n": typeof types.AdMetricsQueryDocument,
    "\n  query GetViewPresets($pageId: String!) {\n    viewPresets(where: { pageId: $pageId }) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": typeof types.GetViewPresetsDocument,
    "\n  mutation CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n": typeof types.CreateViewPresetDocument,
    "\n  mutation DeleteViewPresets($ids: [Int!]!) {\n    deleteViewPresets(where: { ids: $ids })\n  }\n": typeof types.DeleteViewPresetsDocument,
    "\n  query GameMetricShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": typeof types.GameMetricShow_GetInitDataDocument,
    "\n  query GameMetricShow_GetData(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        id\n        date\n        ...AclGameMetricAttributes\n        ...AclGameMetricMetadataAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n": typeof types.GameMetricShow_GetDataDocument,
    "\n  query GameMetricIndexV2_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": typeof types.GameMetricIndexV2_GetInitDataDocument,
    "\n  query GameMetricIndexV2_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          date\n          gameId\n          ...AclGameMetricV2Attributes\n\n          uaNote @include(if: $canViewUaNote)\n          monetNote @include(if: $canViewMonetNote)\n          productNote @include(if: $canViewProductNote)\n          versionNote @include(if: $canViewVersionNote)\n          note @include(if: $canViewNote)\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n": typeof types.GameMetricIndexV2_GetMetricsDocument,
    "\n  fragment PartnerGameMetricAttributes on V2GameMetric {\n    date\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n\n    adPerformances {\n      adType\n      impressionCount\n      dailyActiveUserCount\n    }\n  }\n\n  fragment PartnerGameMetricAggregationAttributes on V2GameMetric {\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n  }\n": typeof types.PartnerGameMetricAttributesFragmentDoc,
    "\n  query PartnerGameMetricIndex_GetInitialData(\n    $gameId: ID!\n    $from: Date!\n    $to: Date!\n    $perPage: Int!\n    $page: Int!\n    $source: DataSource\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        offset: { perPage: $perPage, page: $page }\n        source: $source\n      ) {\n        collection {\n          ...PartnerGameMetricAttributes\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        source: SNAPSHOT\n      ) {\n        ...PartnerGameMetricAggregationAttributes\n      }\n    }\n  }\n": typeof types.PartnerGameMetricIndex_GetInitialDataDocument,
    "\n  query PartnerGameIndex_GetInitData {\n    games(where: { keyword: \"\" }, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n": typeof types.PartnerGameIndex_GetInitDataDocument,
};
const documents: Documents = {
    "\n  query ConfigMapCollections($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n": types.ConfigMapCollectionsDocument,
    "\n  query AdminLayout_GetMenu {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n": types.AdminLayout_GetMenuDocument,
    "\n  query Layout_getNotifications {\n    dashboardNotifications {\n      collection {\n        id\n        message\n        isPinned\n        isVisible\n      }\n    }\n  }\n": types.Layout_GetNotificationsDocument,
    "\n  fragment UserPublicProfile on User {\n    id\n    fullName\n    email\n  }\n": types.UserPublicProfileFragmentDoc,
    "\n  fragment TeamAttributes on Team {\n    id\n    name\n    roleId\n  }\n": types.TeamAttributesFragmentDoc,
    "\n  fragment GameAttributes on Game {\n    id\n    name\n    packageName\n    platform\n    isInhouse\n    isActive\n  }\n": types.GameAttributesFragmentDoc,
    "\n  fragment GameMetricAttributes on GameMetric {\n    id\n    date\n    gameId\n  }\n": types.GameMetricAttributesFragmentDoc,
    "\n  fragment GameMetricInstall on GameMetric {\n    paidInstalls\n    organicInstalls\n    totalInstalls\n  }\n": types.GameMetricInstallFragmentDoc,
    "\n  fragment GameMetricRetention on GameMetric {\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n  }\n": types.GameMetricRetentionFragmentDoc,
    "\n  fragment GameMetricImpsDau on GameMetric {\n    bannerImpsDau\n    interImpsDau\n    rewardImpsDau\n    aoaImpsDau\n    mrecImpsDau\n    aoaAdmobImpsDau\n    collapseAdmobImpsDau\n  }\n": types.GameMetricImpsDauFragmentDoc,
    "\n  fragment GameMetricPlaytime on GameMetric {\n    averageSession\n    playtime\n  }\n": types.GameMetricPlaytimeFragmentDoc,
    "\n  fragment GameMetricMetadata on GameMetric {\n    metadata {\n      ...GameMetricMetadataAttributes\n    }\n  }\n": types.GameMetricMetadataFragmentDoc,
    "\n  fragment GameMetricMetadataAttributes on GameMetricMetadata {\n    id\n    note\n    uaNote\n    monetNote\n    productNote\n    versionNote\n  }\n": types.GameMetricMetadataAttributesFragmentDoc,
    "\n  fragment ConfigMapCollectionAttributes on ConfigMapCollection {\n    id\n    items\n  }\n": types.ConfigMapCollectionAttributesFragmentDoc,
    "\n  fragment ConfigMapSoleAttributes on ConfigMap {\n    id\n    sole\n  }\n": types.ConfigMapSoleAttributesFragmentDoc,
    "\n  fragment GameLevelDropAttributes on GameLevelDrop {\n    version\n    world\n    level\n    activeUserCount\n    userDropCount\n    userDropRate\n    completionRate\n    winRate\n    playtimeSecPerUser\n    overallUserDropRate\n    attemptCountPerUser\n  }\n": types.GameLevelDropAttributesFragmentDoc,
    "\n  fragment AccessControlAttributes on AccessControl {\n    roles {\n      roleId\n      permits\n    }\n  }\n": types.AccessControlAttributesFragmentDoc,
    "\n  fragment ModelAttribute on Attribute {\n    name\n    displayName\n    aggregate\n    permission\n  }\n": types.ModelAttributeFragmentDoc,
    "\n  fragment PageInfoAttributes on PageInfo {\n    total\n    perPage\n    currentPage\n    lastPage\n    firstPage\n  }\n": types.PageInfoAttributesFragmentDoc,
    "\n  fragment V2GameMetricAttributes on V2GameMetric {\n    id\n    paidInstalls\n    organicInstalls\n    organicPercentage\n    totalInstalls\n    cost\n    cpi\n    roas\n    revenue\n    profit\n    dailyActiveUsers\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n    uaNote\n    monetNote\n    productNote\n    versionNote\n    note\n  }\n": types.V2GameMetricAttributesFragmentDoc,
    "\n  fragment AdPerformanceAttributes on AdPerformance {\n    ecpm\n    arpDau\n    impsDau\n    adType\n    revenue\n  }\n": types.AdPerformanceAttributesFragmentDoc,
    "\n  fragment AclGameMetricMetadataAttributes on GameMetric {\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n": types.AclGameMetricMetadataAttributesFragmentDoc,
    "\n  fragment AclGameMetricAttributes on GameMetric {\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    bannerImpsDau @include(if: $canViewBannerImpsDau)\n    interImpsDau @include(if: $canViewInterImpsDau)\n    rewardImpsDau @include(if: $canViewRewardImpsDau)\n    aoaImpsDau @include(if: $canViewAoaImpsDau)\n    mrecImpsDau @include(if: $canViewMrecImpsDau)\n    audioImpsDau @include(if: $canViewAudioImpsDau)\n    aoaAdmobImpsDau @include(if: $canViewAoaAdmobImpsDau)\n    collapseAdmobImpsDau @include(if: $canViewCollapseAdmobImpsDau)\n    nativeAdmobImpsDau @include(if: $canViewNativeAdmobImpsDau)\n    adaptiveAdmobImpsDau @include(if: $canViewAdaptiveAdmobImpsDau)\n    mrecAdmobImpsDau @include(if: $canViewMrecAdmobImpsDau)\n    averageSession @include(if: $canViewAverageSession)\n    playtime @include(if: $canViewPlaytime)\n  }\n": types.AclGameMetricAttributesFragmentDoc,
    "\n  fragment AclAdPerformanceAttributes on AdPerformance {\n    adType\n    revenue @include(if: $canViewGameRevenueRevenue)\n    ecpm @include(if: $canViewGameRevenueEcpm)\n    arpDau @include(if: $canViewGameRevenueArpDau)\n    impsDau @include(if: $canViewGameRevenueImpsDau)\n  }\n": types.AclAdPerformanceAttributesFragmentDoc,
    "\n  fragment AclGameMetricV2Attributes on V2GameMetric {\n    id\n\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    dailyActiveUsers @include(if: $canViewDailyActiveUsers)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    sessions @include(if: $canViewSessions)\n    playtime @include(if: $canViewPlaytime)\n\n    adPerformances {\n      ...AclAdPerformanceAttributes\n    }\n  }\n": types.AclGameMetricV2AttributesFragmentDoc,
    "\n  fragment AdTypeAttributes on AdType {\n    id\n    name\n    order\n  }\n": types.AdTypeAttributesFragmentDoc,
    "\n  fragment AdAgencyAttributes on AdAgency {\n    id\n    name\n  }\n": types.AdAgencyAttributesFragmentDoc,
    "\n  fragment GameAgencyMetricAttributes on AgencyMetric {\n    cost\n    paidInstalls\n  }\n": types.GameAgencyMetricAttributesFragmentDoc,
    "\n  fragment SideMenuAttributes on SideMenu {\n    name\n    path\n    group\n    icon\n  }\n": types.SideMenuAttributesFragmentDoc,
    "\n  fragment GameStudioAttributes on GameStudio {\n    id\n    name\n  }\n": types.GameStudioAttributesFragmentDoc,
    "\n  fragment GameStudioMetricAttributes on GameStudioMetric {\n    game {\n      ...GameAttributes\n    }\n\n    totalInstalls\n    mmpCostAmount\n    totalAgencyCost\n    revenue\n    profit\n  }\n": types.GameStudioMetricAttributesFragmentDoc,
    "\n  fragment UserPublicInfoAttributes on UserPublicInfo {\n    id\n    fullName\n  }\n": types.UserPublicInfoAttributesFragmentDoc,
    "\n  fragment WorkflowStepAttributes on WorkflowStep {\n    name\n    action\n    alternateAction\n    assignee {\n      ...UserPublicInfoAttributes\n    }\n  }\n": types.WorkflowStepAttributesFragmentDoc,
    "\n  fragment BudgetRequestAttributes on BudgetRequest {\n    id\n    expirationDate\n    createdAt\n    stepId\n    lastAction\n    description\n    step {\n      ...WorkflowStepAttributes\n    }\n    game {\n      ...GameAttributes\n    }\n    workflow {\n      ...WorkflowAttributes\n    }\n    createdBy {\n      ...UserPublicInfoAttributes\n    }\n    amount\n  }\n": types.BudgetRequestAttributesFragmentDoc,
    "\n  fragment NetworkAttributes on Network {\n    id\n    name\n  }\n": types.NetworkAttributesFragmentDoc,
    "\n  fragment MediationAttributes on Mediation {\n    id\n    name\n  }\n": types.MediationAttributesFragmentDoc,
    "\n  fragment FirebaseExperimentAttributes on FirebaseExperiment {\n    name\n  }\n": types.FirebaseExperimentAttributesFragmentDoc,
    "\n  fragment FirebaseMetricAttributes on FirebaseMetric {\n    date\n    variantId\n    version\n    countryCode\n\n    sessionCount\n    sessionNthDayCounts\n    dailyActiveUserCount\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    retentionNthDayRates\n    installCount\n    sessionCountPerActiveUser\n    retentionRate\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n": types.FirebaseMetricAttributesFragmentDoc,
    "\n  fragment FirebaseVersionVariantAttributes on FirebaseVersionVariant {\n    id\n    experiment\n    name\n  }\n": types.FirebaseVersionVariantAttributesFragmentDoc,
    "\n  fragment FirebaseAdMetricAttributes on FirebaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n  }\n": types.FirebaseAdMetricAttributesFragmentDoc,
    "\n  fragment UserAttributes on User {\n    id\n    fullName\n    email\n    kind\n    hasPassword\n    isDeleted\n    note\n    toolPermissions {\n      ...ToolPermissionAttributes\n    }\n  }\n": types.UserAttributesFragmentDoc,
    "\n  fragment ToolPermissionAttributes on ToolPermission {\n    tool\n    action\n  }\n": types.ToolPermissionAttributesFragmentDoc,
    "\n  fragment GameRoleMembershipAttributes on GameRoleMembership {\n    id\n    roleId\n    storeId\n    users {\n      email\n    }\n  }\n": types.GameRoleMembershipAttributesFragmentDoc,
    "\n  fragment GameRolesAttributes on GameRoles {\n    id\n    roles {\n      id\n      users {\n        email\n      }\n    }\n  }\n": types.GameRolesAttributesFragmentDoc,
    "\n  fragment ReleaseMetricAttributes on ReleaseMetric {\n    date\n    version\n    countryCode\n    sessionCount\n    sessionNthDayCounts\n    sessionCountPerActiveUser\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    installCount\n    retentionRate\n    retentionNthDayRates\n    dailyActiveUserCount\n    lifetimeNthDayValues\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n    adRevNthDayGrossAmounts\n    impressionNthDayCounts\n  }\n": types.ReleaseMetricAttributesFragmentDoc,
    "\n  fragment ReleaseAdMetricAttributes on ReleaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n": types.ReleaseAdMetricAttributesFragmentDoc,
    "\n  fragment GameMetricIndex_GameMetricAttributes on GameMetric {\n    id\n    date\n    ...AclGameMetricAttributes\n    ...AclGameMetricMetadataAttributes\n  }\n": types.GameMetricIndex_GameMetricAttributesFragmentDoc,
    "\n  fragment GameCostAttributes on GameCost {\n    id\n    date\n    type\n    network {\n      id\n      name\n    }\n    preTaxAmount\n    totalAmount\n    mmpAmount\n    taxAmount\n  }\n": types.GameCostAttributesFragmentDoc,
    "\n  fragment GetAllUsers_UserAttributes on User {\n    id\n    email\n    fullName\n    team {\n      ...TeamAttributes\n    }\n  }\n": types.GetAllUsers_UserAttributesFragmentDoc,
    "\n  query GetAllUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GetAllUsers_UserAttributes\n      }\n    }\n  }\n": types.GetAllUsersDocument,
    "\n  fragment MetabaseChartAttributes on MetabaseChart {\n    url\n  }\n": types.MetabaseChartAttributesFragmentDoc,
    "\n  query GetMetabaseChart($where: MetabaseChartWhere!) {\n    metabaseChart(where: $where) {\n      ...MetabaseChartAttributes\n    }\n  }\n": types.GetMetabaseChartDocument,
    "\n  mutation UpdateProfile($form: UpdateProfileForm!) {\n    updateProfile(form: $form) {\n      ...UserPublicProfile\n    }\n  }\n": types.UpdateProfileDocument,
    "\n  query GetProfile {\n    profile {\n      ...UserPublicProfile\n      team {\n        ...TeamAttributes\n      }\n      ledTeam {\n        ...TeamAttributes\n      }\n    }\n  }\n": types.GetProfileDocument,
    "\n  mutation ViewPresetEditor_CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n": types.ViewPresetEditor_CreateViewPresetDocument,
    "\n  mutation ViewPresetSelection_DeleteViewPreset($where: DeleteViewPresetsWhere!) {\n    deleteViewPresets(where: $where)\n  }\n": types.ViewPresetSelection_DeleteViewPresetDocument,
    "\n  query GetConfigMaps($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n": types.GetConfigMapsDocument,
    "\n  query Layout_GetAnnouncements {\n    dashboardNotifications {\n      collection {\n        ...AnnouncementAttributes\n      }\n    }\n  }\n": types.Layout_GetAnnouncementsDocument,
    "\n  fragment AnnouncementAttributes on DashboardNotification {\n    id\n    message\n    isPinned\n    isVisible\n  }\n": types.AnnouncementAttributesFragmentDoc,
    "\n  mutation UpdateConfigMaps($where: UpdateConfigMapsWhere!) {\n    updateConfigMaps(where: $where)\n  }\n": types.UpdateConfigMapsDocument,
    "\n  query Layout_GetGames($keyword: String!) {\n    games(where: { keyword: $keyword }, offset: { page: 1, perPage: 10 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n": types.Layout_GetGamesDocument,
    "\n  query Sidebar_GetMainMenus {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n": types.Sidebar_GetMainMenusDocument,
    "\n  query Acl_GameMetricAttributes(\n    $readSubject: String!\n    $writeSubject: String!\n    $modelName: String!\n  ) {\n    attributes(where: { gameId: null, modelName: $modelName }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    read: accessControl(where: { subject: $readSubject }) {\n      ...AccessControlAttributes\n    }\n\n    write: accessControl(where: { subject: $writeSubject }) {\n      ...AccessControlAttributes\n    }\n  }\n": types.Acl_GameMetricAttributesDocument,
    "\n  mutation UpdateAccessControl($where: AccessControlWhere!, $form: AccessControlUpdateForm!) {\n    updateAccessControl(where: $where, form: $form) {\n      ...AccessControlAttributes\n    }\n  }\n": types.UpdateAccessControlDocument,
    "\n  query GetAccessControl($where: AccessControlWhere!) {\n    accessControl(where: $where) {\n      ...AccessControlAttributes\n    }\n  }\n": types.GetAccessControlDocument,
    "\n  fragment WorkflowStepActionAttributes on WorkflowStepAction {\n    action\n  }\n": types.WorkflowStepActionAttributesFragmentDoc,
    "\n  query BudgetRequestIndex_GetInitialData {\n    games(where: {}, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    workflowStepActions {\n      collection {\n        ...WorkflowStepActionAttributes\n      }\n    }\n  }\n": types.BudgetRequestIndex_GetInitialDataDocument,
    "\n  query BudgetRequestIndex_GetBudgetRequests($where: BudgetRequestsWhere!, $offset: Offset) {\n    budgetRequests(where: $where, offset: $offset) {\n      collection {\n        ...BudgetRequestAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": types.BudgetRequestIndex_GetBudgetRequestsDocument,
    "\n  mutation BudgetRequestIndex_UpdateBudgetRequestsState($forms: [UpdateBudgetRequestStateForm!]!) {\n    updateBudgetRequestsState(forms: $forms) {\n      ...BudgetRequestAttributes\n    }\n  }\n": types.BudgetRequestIndex_UpdateBudgetRequestsStateDocument,
    "\n  mutation BudgetRequestIndex_DeleteBudgetRequests($ids: [Int!]!) {\n    deleteBudgetRequests(where: { ids: $ids })\n  }\n": types.BudgetRequestIndex_DeleteBudgetRequestsDocument,
    "\n  mutation BudgetRequestIndex_CreateBudgetRequest($form: CreateBudgetRequestForm!) {\n    createBudgetRequest(form: $form) {\n      ...BudgetRequestAttributes\n    }\n  }\n": types.BudgetRequestIndex_CreateBudgetRequestDocument,
    "\n  mutation BudgetRequestIndex_UpdateBudgetRequests($form: UpdateBudgetRequestForm!) {\n    updateBudgetRequests(forms: [$form]) {\n      ...BudgetRequestAttributes\n    }\n  }\n": types.BudgetRequestIndex_UpdateBudgetRequestsDocument,
    "\n  query GamesCampaignMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.GamesCampaignMetricsIndex_GetViewPresetsDocument,
    "\n  query GamesCampaignMetricsIndex_GetData(\n    $where: AdMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    adMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...AdMetricAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.GamesCampaignMetricsIndex_GetDataDocument,
    "\n  fragment FirebaseExperimentIndex_FirebaseMetric on FirebaseMetric {\n    ...FirebaseMetricAttributes\n\n    ads {\n      ...FirebaseAdMetricAttributes\n    }\n  }\n": types.FirebaseExperimentIndex_FirebaseMetricFragmentDoc,
    "\n  query FirebaseExperimentIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.FirebaseExperimentIndex_GetViewPresetsDocument,
    "\n  query FirebaseExperimentIndex_GetInitialData($gameId: ID!) {\n    firebaseExperiments(where: { gameId: $gameId }) {\n      collection {\n        ...FirebaseExperimentAttributes\n      }\n    }\n\n    networks(where: { category: FIREBASE }) {\n      collection {\n        ...NetworkAttributes\n      }\n    }\n\n    mediations {\n      collection {\n        ...MediationAttributes\n      }\n    }\n  }\n": types.FirebaseExperimentIndex_GetInitialDataDocument,
    "\n  query FirebaseExperimentIndex_GetVersionVariants($where: FirebaseVersionVariantsWhere!) {\n    firebaseVersionVariants(where: $where) {\n      collection {\n        ...FirebaseVersionVariantAttributes\n      }\n    }\n  }\n": types.FirebaseExperimentIndex_GetVersionVariantsDocument,
    "\n  query FirebaseExperimentIndex_GetMetrics(\n    $where: FirebaseMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    firebaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...FirebaseExperimentIndex_FirebaseMetric\n      }\n\n      variants {\n        ...FirebaseVersionVariantAttributes\n      }\n\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.FirebaseExperimentIndex_GetMetricsDocument,
    "\n  fragment GameAgencyCostAttributes on GameAgencyCost {\n    totalCost\n    mediationCost\n    varianceRate\n    agencyId\n  }\n": types.GameAgencyCostAttributesFragmentDoc,
    "\n  mutation GameCostEdit_UpdateGameCost($form: UpdateGameCostForm!, $where: UpdateGameCostWhere!) {\n    updateGameCost(form: $form, where: $where)\n  }\n": types.GameCostEdit_UpdateGameCostDocument,
    "\n  query GameCostsOverview_GetAdNetworks {\n    adNetworks {\n      collection {\n        ...GameCostIndexOverview_AdNetworkAttributes\n      }\n    }\n  }\n": types.GameCostsOverview_GetAdNetworksDocument,
    "\n  query GameSpendShow_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": types.GameSpendShow_InitDocument,
    "\n  query GameCostsComparison_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": types.GameCostsComparison_GetDataDocument,
    "\n  query GameCostsAggregation_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n": types.GameCostsAggregation_GetDataDocument,
    "\n  fragment GameCostIndexOverview_GameCostAttributes on GameCost {\n    id\n    date\n    mmpAmount\n    preTaxAmount\n    totalAmount\n    taxAmount\n    network {\n      id\n      name\n    }\n  }\n": types.GameCostIndexOverview_GameCostAttributesFragmentDoc,
    "\n  query GameCostsOverview_GetData($where: GameCostsWhere!, $offset: Offset!) {\n    gameCosts(where: $where, offset: $offset) {\n      collection {\n        ...GameCostIndexOverview_GameCostAttributes\n      }\n      meta {\n        aggregation {\n          mmpAmount\n          totalAmount\n          preTaxAmount\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n": types.GameCostsOverview_GetDataDocument,
    "\n  fragment GameCostIndexOverview_AdNetworkAttributes on AdNetwork {\n    id\n    name\n    inputType\n  }\n": types.GameCostIndexOverview_AdNetworkAttributesFragmentDoc,
    "\n  mutation GameMetricEdit_UpdateGameMetric(\n    $where: UpdateGameMetricWhere!\n    $form: UpdateGameMetricForm!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    updateGameMetric(where: $where, form: $form) {\n      ...GameMetricIndex_GameMetricAttributes\n    }\n  }\n": types.GameMetricEdit_UpdateGameMetricDocument,
    "\n  query GamesMetricsShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": types.GamesMetricsShow_GetInitDataDocument,
    "\n  query GamesMetricsShow_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        ...GameMetricIndex_GameMetricAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": types.GamesMetricsShow_GetMetricsDocument,
    "\n  query GameMetricIndex_GetAggregation(\n    $gameId: ID!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n  ) {\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n": types.GameMetricIndex_GetAggregationDocument,
    "\n  query GameMetricNextIndex_GetInitData($gameId: ID!) {\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": types.GameMetricNextIndex_GetInitDataDocument,
    "\n  fragment GameMetricNextIndex_GameMetricAttributes on V2GameMetric {\n    date\n    gameId\n    ...AclGameMetricV2Attributes\n\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n": types.GameMetricNextIndex_GameMetricAttributesFragmentDoc,
    "\n  query GameMetricNextIndex_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          ...GameMetricNextIndex_GameMetricAttributes\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n": types.GameMetricNextIndex_GetMetricsDocument,
    "\n  query GameMetricNextIndex_GetAggregation(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n": types.GameMetricNextIndex_GetAggregationDocument,
    "\n  query GamesIndex_GetInitialData($where: GamesWhere!) {\n    games(where: $where, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n": types.GamesIndex_GetInitialDataDocument,
    "\n  query ListGameRoles($gameIds: [ID!]!) {\n    gameRoles(gameIds: $gameIds) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n": types.ListGameRolesDocument,
    "\n  mutation UpdateGameRoles($forms: [UpdateGameRolesGame!]!) {\n    updateGameRoles(forms: $forms) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n": types.UpdateGameRolesDocument,
    "\n  query GameRoles_GetData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": types.GameRoles_GetDataDocument,
    "\n  fragment ReleaseMetricIndex_ReleaseMetricAttributes on ReleaseMetric {\n    ...ReleaseMetricAttributes\n\n    ads {\n      ...ReleaseAdMetricAttributes\n    }\n  }\n": types.ReleaseMetricIndex_ReleaseMetricAttributesFragmentDoc,
    "\n  query ReleaseMetricIndex_GetData($where: ReleaseMetricsWhere!, $group: Group, $preset: Preset) {\n    releaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...ReleaseMetricIndex_ReleaseMetricAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.ReleaseMetricIndex_GetDataDocument,
    "\n  query ReleaseMetricIndex_GetVersion($where: ReleaseVersionsWhere!) {\n    releaseVersions(where: $where) {\n      collection {\n        version\n      }\n    }\n  }\n": types.ReleaseMetricIndex_GetVersionDocument,
    "\n  query GamesReleaseMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.GamesReleaseMetricsIndex_GetViewPresetsDocument,
    "\n  query AdmobGamesQuery {\n    admobGames {\n      collection\n    }\n  }\n": types.AdmobGamesQueryDocument,
    "\n  query AdmobMetrics(\n    $gameId: String!\n    $baselineDateFrom: Date!\n    $baselineDateTo: Date!\n    $targetDateFrom: Date!\n    $targetDateTo: Date!\n    $formats: [String!]!\n  ) {\n    baseline: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $baselineDateFrom\n        dateTo: $baselineDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n\n    target: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $targetDateFrom\n        dateTo: $targetDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n  }\n": types.AdmobMetricsDocument,
    "\n  query ListTeam {\n    teams {\n      id\n      name\n      roleId\n      leader {\n        id\n        email\n        fullName\n      }\n    }\n  }\n": types.ListTeamDocument,
    "\n  query TeamCreate_GetInitialData {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n  }\n": types.TeamCreate_GetInitialDataDocument,
    "\n  mutation CreateTeam($form: CreateTeamForm!) {\n    createTeam(form: $form) {\n      ...TeamForm_Team\n    }\n  }\n": types.CreateTeamDocument,
    "\n  query TeamEdit_GetInitialData($teamId: Int!) {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n\n    team(id: $teamId) {\n      ...TeamForm_Team\n    }\n  }\n": types.TeamEdit_GetInitialDataDocument,
    "\n  mutation UpdateTeam($where: UpdateTeamWhere!, $form: UpdateTeamForm!) {\n    updateTeam(where: $where, form: $form) {\n      ...TeamForm_Team\n    }\n  }\n": types.UpdateTeamDocument,
    "\n  mutation DeleteTeam($where: DeleteTeamWhere!) {\n    deleteTeam(where: $where)\n  }\n": types.DeleteTeamDocument,
    "\n  fragment TeamForm_Member on User {\n    ...UserPublicProfile\n    inchargedGames {\n      storeId\n      roleId\n    }\n    team {\n      ...TeamAttributes\n    }\n  }\n": types.TeamForm_MemberFragmentDoc,
    "\n  fragment TeamForm_Team on Team {\n    ...TeamAttributes\n    members {\n      ...UserPublicProfile\n    }\n    leader {\n      ...UserPublicProfile\n    }\n  }\n": types.TeamForm_TeamFragmentDoc,
    "\n  query UserIndex_GetUsers($offset: Offset!) {\n    users(where: {}, offset: $offset) {\n      collection {\n        ...UserAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": types.UserIndex_GetUsersDocument,
    "\n  mutation UserIndex_UpdateUser($where: UpdateUserWhere!, $form: UpdateUserForm!) {\n    updateUser(where: $where, form: $form) {\n      ...UserAttributes\n    }\n  }\n": types.UserIndex_UpdateUserDocument,
    "\n  mutation UserIndex_CreateUser($form: CreateUserForm!) {\n    createUser(form: $form) {\n      ...UserAttributes\n    }\n  }\n": types.UserIndex_CreateUserDocument,
    "\n  mutation UserIndex_DeleteUsers($where: DeleteUsersWhere!) {\n    deleteUsers(where: $where)\n  }\n": types.UserIndex_DeleteUsersDocument,
    "\n  fragment WorkflowAttributes on Workflow {\n    id\n    name\n    roleId\n    steps {\n      ...WorkflowStepAttributes\n    }\n  }\n": types.WorkflowAttributesFragmentDoc,
    "\n  query WorkflowsIndex_GetInitialData {\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    users(where: {}, offset: { perPage: 9999 }) {\n      collection {\n        ...UserPublicProfile\n      }\n    }\n  }\n": types.WorkflowsIndex_GetInitialDataDocument,
    "\n  mutation WorkflowsIndex_Create($form: CreateWorkflowForm!) {\n    createWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n": types.WorkflowsIndex_CreateDocument,
    "\n  mutation WorkflowsIndex_Update($form: UpdateWorkflowForm!) {\n    updateWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n": types.WorkflowsIndex_UpdateDocument,
    "\n  fragment GameCreativeMetricAttributes on GameCreativeMetric {\n    id\n    date\n    campaign\n    adGroup\n    adGroupStartDate\n    adSet\n    editor\n    isPlayable\n    targetRoasRate\n    preTaxCostAmount\n    grossRevenueAmount\n    clickCount\n    impressionCount\n    installCount\n    roasRate\n    cpi\n    conversionRate\n    clickthroughRate\n\n    agency {\n      ...AdAgencyAttributes\n    }\n  }\n": types.GameCreativeMetricAttributesFragmentDoc,
    "\n  query GameCreativeMetrics_Index(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $page: Int!\n    $perPage: Int!\n    $direction: OrderDirection!\n  ) {\n    gameCreativeMetrics(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $direction }\n    ) {\n      collection {\n        ...GameCreativeMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n": types.GameCreativeMetrics_IndexDocument,
    "\n  query GameLevelDropIndex_QueryData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    gameLevelDropVersions(where: { gameId: $gameId }) {\n      collection\n    }\n  }\n": types.GameLevelDropIndex_QueryDataDocument,
    "\n  query GameLevelDropIndex_QueryVersions($gameId: ID!, $versions: [String!]!) {\n    gameLevelDrops(where: { gameId: $gameId, versions: $versions }) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n  }\n": types.GameLevelDropIndex_QueryVersionsDocument,
    "\n  fragment GameNetworkRevenueAggregationAttributes on GameNetworkRevenue {\n    networkId\n    revenue\n    mediationRevenue\n    varianceRate\n  }\n": types.GameNetworkRevenueAggregationAttributesFragmentDoc,
    "\n  fragment GameRevenueMetricAttributes on AgencyMetric {\n    revenue\n  }\n": types.GameRevenueMetricAttributesFragmentDoc,
    "\n  fragment GameNetworkRevenueAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAggregationAttributes\n    date\n  }\n\n  fragment GameNetworkRevenueWithMetricAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAttributes\n    metric {\n      ...GameRevenueMetricAttributes\n    }\n  }\n": types.GameNetworkRevenueAttributesFragmentDoc,
    "\n  query GameRevenueIndex_getNetworkRevenue(\n    $dateFrom: Date!\n    $dateTo: Date!\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n    $orderDirection: OrderDirection!\n  ) {\n    gameNetworkRevenues(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameNetworkRevenueWithMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameNetworkRevenue(where: { gameId: $gameId }) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n        network {\n          id\n          name\n        }\n      }\n    }\n\n    total: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n\n    weekly: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n  }\n": types.GameRevenueIndex_GetNetworkRevenueDocument,
    "\n  query GameRevenueExplorerIndex_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": types.GameRevenueExplorerIndex_InitDocument,
    "\n  query GameReviewIndex_Setting($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n    gamePerformanceSetting(gameId: $gameId) {\n      criterias {\n        conclusion\n        metrics\n      }\n    }\n  }\n": types.GameReviewIndex_SettingDocument,
    "\n  query GameReviewIndex_Data($gameId: ID!, $from: Date!, $to: Date!) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $from, dateLte: $to }\n      offset: { perPage: 7, page: 1 }\n      order: { direction: ASC }\n    ) {\n      collection {\n        date\n        roas\n        playtime\n        retentionRateDay1\n        cpi\n        bannerImpsDau\n        interImpsDau\n        ...GameMetricMetadata\n      }\n    }\n\n    gameReview(gameId: $gameId, date: $from) {\n      productNote\n      marketingNote\n    }\n  }\n": types.GameReviewIndex_DataDocument,
    "\n  query GameCostIndex_AgencyData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n": types.GameCostIndex_AgencyDataDocument,
    "\n  query ProductMetricIndex_GetVersions($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {\n    gameRewardUsageVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameDailyLevelDropVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gamePlaytimeVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameRetentionRateVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n  }\n": types.ProductMetricIndex_GetVersionsDocument,
    "\n  query ProductMetricIndex_GetGame($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n": types.ProductMetricIndex_GetGameDocument,
    "\n  query ProductMetricIndex_VersionsData(\n    $gameId: ID!\n    $versions: [String!]!\n    $groupByLevel: Boolean!\n    $groupByLocation: Boolean!\n    $group: [String!]!\n    $order: OrderDirection!\n    $activeDateFrom: Date!\n    $activeDateTo: Date!\n    $installTimeFrom: Date\n    $installTimeTo: Date\n  ) {\n    gameRewardUsages(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n      group: { fields: $group }\n      order: { direction: $order }\n    ) {\n      collection {\n        location @include(if: $groupByLocation)\n        level @include(if: $groupByLevel)\n        world @include(if: $groupByLevel)\n        useCount\n      }\n    }\n\n    gameLevelDrops(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n      }\n    ) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n\n    gameRetentionRates(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n    ) {\n      collection {\n        date\n        gameId\n        newUsers\n        day1\n        day2\n        day3\n        day4\n        day5\n        day6\n        day7\n      }\n    }\n\n    aggregatePlaytime(\n      where: {\n        gameId: $gameId\n        activeDateFrom: $activeDateFrom\n        activeDateTo: $activeDateTo\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n        versions: $versions\n      }\n    ) {\n      engagementSessionCount\n      playtimeSec\n    }\n  }\n": types.ProductMetricIndex_VersionsDataDocument,
    "\n  fragment GameRevenueIndex_UserAttributes on User {\n    email\n    fullName\n  }\n": types.GameRevenueIndex_UserAttributesFragmentDoc,
    "\n  query GameRevenuesIndex_GetUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GameRevenueIndex_UserAttributes\n      }\n    }\n  }\n": types.GameRevenuesIndex_GetUsersDocument,
    "\n  query GameStudioMetricsIndex_GetInitialData {\n    gameStudios {\n      collection {\n        ...GameStudioAttributes\n      }\n    }\n  }\n": types.GameStudioMetricsIndex_GetInitialDataDocument,
    "\n  query GameStudioMetricsIndex_GetMetrics($studioId: Int!, $dateFrom: Date!, $dateTo: Date!) {\n    metrics: gameStudioMetrics(\n      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameStudioMetricAttributes\n      }\n    }\n  }\n": types.GameStudioMetricsIndex_GetMetricsDocument,
    "\n  fragment AdMetricAttributes on AdMetric {\n    date\n    adId\n    groupId\n    campaignId\n    countryCode\n    agencyId\n    adRevGrossAmount\n    adCostNonTaxAmount\n    impressionCount\n    roasNthDayRates\n    retentionNthDayRates\n    activeUserNthDayCounts\n    sessionNthDayCounts\n    sessionCount\n    dailyActiveUserCount\n    retentionRate\n    clickCount\n    installCount\n    adRevNthDayGrossAmounts\n    cpi\n    ctr\n    cvr\n    cpc\n    ipm\n    roas\n  }\n": types.AdMetricAttributesFragmentDoc,
    "\n  fragment AdAttributes on Ad {\n    id\n    name\n    campaignId\n    groupId\n  }\n": types.AdAttributesFragmentDoc,
    "\n  fragment AdGroupAttributes on AdGroup {\n    id\n    name\n  }\n": types.AdGroupAttributesFragmentDoc,
    "\n  fragment CampaignAttributes on Campaign {\n    id\n    name\n  }\n": types.CampaignAttributesFragmentDoc,
    "\n  fragment ViewPresetAttributes on ViewPreset {\n    id\n    name\n    attributes {\n      name\n      isCohort\n      cohortDays\n    }\n  }\n": types.ViewPresetAttributesFragmentDoc,
    "\n  query AdMetricsQuery(\n    $gameId: ID!\n    $dateFrom: FilterValue!\n    $dateTo: FilterValue!\n    $groupByFields: [String!]!\n    $campaignId: Filter\n    $agencyId: Filter\n    $groupId: Filter\n    $adId: Filter\n    $countryCode: Filter\n    $cvr: Filter\n    $cpi: Filter\n    $ctr: Filter\n    $roas: Filter\n    $adRevGrossAmount: Filter\n    $adCostNonTaxAmount: Filter\n    $impressionCount: Filter\n    $clickCount: Filter\n    $installCount: Filter\n    $cpc: Filter\n    $ipm: Filter\n    $preset: Int\n  ) {\n    adMetrics(\n      where: {\n        gameId: $gameId\n        date: { operator: BETWEEN, values: [$dateFrom, $dateTo] }\n        campaignId: $campaignId\n        groupId: $groupId\n        adId: $adId\n        agencyId: $agencyId\n        countryCode: $countryCode\n        cvr: $cvr\n        cpi: $cpi\n        ctr: $ctr\n        roas: $roas\n        adRevGrossAmount: $adRevGrossAmount\n        adCostNonTaxAmount: $adCostNonTaxAmount\n        impressionCount: $impressionCount\n        clickCount: $clickCount\n        installCount: $installCount\n        cpc: $cpc\n        ipm: $ipm\n      }\n      group: { fields: $groupByFields }\n      preset: { viewPresetId: $preset }\n    ) {\n      collection {\n        ...AdMetricAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n    }\n  }\n": types.AdMetricsQueryDocument,
    "\n  query GetViewPresets($pageId: String!) {\n    viewPresets(where: { pageId: $pageId }) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n": types.GetViewPresetsDocument,
    "\n  mutation CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n": types.CreateViewPresetDocument,
    "\n  mutation DeleteViewPresets($ids: [Int!]!) {\n    deleteViewPresets(where: { ids: $ids })\n  }\n": types.DeleteViewPresetsDocument,
    "\n  query GameMetricShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": types.GameMetricShow_GetInitDataDocument,
    "\n  query GameMetricShow_GetData(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        id\n        date\n        ...AclGameMetricAttributes\n        ...AclGameMetricMetadataAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n": types.GameMetricShow_GetDataDocument,
    "\n  query GameMetricIndexV2_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n": types.GameMetricIndexV2_GetInitDataDocument,
    "\n  query GameMetricIndexV2_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          date\n          gameId\n          ...AclGameMetricV2Attributes\n\n          uaNote @include(if: $canViewUaNote)\n          monetNote @include(if: $canViewMonetNote)\n          productNote @include(if: $canViewProductNote)\n          versionNote @include(if: $canViewVersionNote)\n          note @include(if: $canViewNote)\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n": types.GameMetricIndexV2_GetMetricsDocument,
    "\n  fragment PartnerGameMetricAttributes on V2GameMetric {\n    date\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n\n    adPerformances {\n      adType\n      impressionCount\n      dailyActiveUserCount\n    }\n  }\n\n  fragment PartnerGameMetricAggregationAttributes on V2GameMetric {\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n  }\n": types.PartnerGameMetricAttributesFragmentDoc,
    "\n  query PartnerGameMetricIndex_GetInitialData(\n    $gameId: ID!\n    $from: Date!\n    $to: Date!\n    $perPage: Int!\n    $page: Int!\n    $source: DataSource\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        offset: { perPage: $perPage, page: $page }\n        source: $source\n      ) {\n        collection {\n          ...PartnerGameMetricAttributes\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        source: SNAPSHOT\n      ) {\n        ...PartnerGameMetricAggregationAttributes\n      }\n    }\n  }\n": types.PartnerGameMetricIndex_GetInitialDataDocument,
    "\n  query PartnerGameIndex_GetInitData {\n    games(where: { keyword: \"\" }, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n": types.PartnerGameIndex_GetInitDataDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ConfigMapCollections($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n"): (typeof documents)["\n  query ConfigMapCollections($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query AdminLayout_GetMenu {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query AdminLayout_GetMenu {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Layout_getNotifications {\n    dashboardNotifications {\n      collection {\n        id\n        message\n        isPinned\n        isVisible\n      }\n    }\n  }\n"): (typeof documents)["\n  query Layout_getNotifications {\n    dashboardNotifications {\n      collection {\n        id\n        message\n        isPinned\n        isVisible\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment UserPublicProfile on User {\n    id\n    fullName\n    email\n  }\n"): (typeof documents)["\n  fragment UserPublicProfile on User {\n    id\n    fullName\n    email\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment TeamAttributes on Team {\n    id\n    name\n    roleId\n  }\n"): (typeof documents)["\n  fragment TeamAttributes on Team {\n    id\n    name\n    roleId\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameAttributes on Game {\n    id\n    name\n    packageName\n    platform\n    isInhouse\n    isActive\n  }\n"): (typeof documents)["\n  fragment GameAttributes on Game {\n    id\n    name\n    packageName\n    platform\n    isInhouse\n    isActive\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricAttributes on GameMetric {\n    id\n    date\n    gameId\n  }\n"): (typeof documents)["\n  fragment GameMetricAttributes on GameMetric {\n    id\n    date\n    gameId\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricInstall on GameMetric {\n    paidInstalls\n    organicInstalls\n    totalInstalls\n  }\n"): (typeof documents)["\n  fragment GameMetricInstall on GameMetric {\n    paidInstalls\n    organicInstalls\n    totalInstalls\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricRetention on GameMetric {\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n  }\n"): (typeof documents)["\n  fragment GameMetricRetention on GameMetric {\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricImpsDau on GameMetric {\n    bannerImpsDau\n    interImpsDau\n    rewardImpsDau\n    aoaImpsDau\n    mrecImpsDau\n    aoaAdmobImpsDau\n    collapseAdmobImpsDau\n  }\n"): (typeof documents)["\n  fragment GameMetricImpsDau on GameMetric {\n    bannerImpsDau\n    interImpsDau\n    rewardImpsDau\n    aoaImpsDau\n    mrecImpsDau\n    aoaAdmobImpsDau\n    collapseAdmobImpsDau\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricPlaytime on GameMetric {\n    averageSession\n    playtime\n  }\n"): (typeof documents)["\n  fragment GameMetricPlaytime on GameMetric {\n    averageSession\n    playtime\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricMetadata on GameMetric {\n    metadata {\n      ...GameMetricMetadataAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment GameMetricMetadata on GameMetric {\n    metadata {\n      ...GameMetricMetadataAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricMetadataAttributes on GameMetricMetadata {\n    id\n    note\n    uaNote\n    monetNote\n    productNote\n    versionNote\n  }\n"): (typeof documents)["\n  fragment GameMetricMetadataAttributes on GameMetricMetadata {\n    id\n    note\n    uaNote\n    monetNote\n    productNote\n    versionNote\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ConfigMapCollectionAttributes on ConfigMapCollection {\n    id\n    items\n  }\n"): (typeof documents)["\n  fragment ConfigMapCollectionAttributes on ConfigMapCollection {\n    id\n    items\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ConfigMapSoleAttributes on ConfigMap {\n    id\n    sole\n  }\n"): (typeof documents)["\n  fragment ConfigMapSoleAttributes on ConfigMap {\n    id\n    sole\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameLevelDropAttributes on GameLevelDrop {\n    version\n    world\n    level\n    activeUserCount\n    userDropCount\n    userDropRate\n    completionRate\n    winRate\n    playtimeSecPerUser\n    overallUserDropRate\n    attemptCountPerUser\n  }\n"): (typeof documents)["\n  fragment GameLevelDropAttributes on GameLevelDrop {\n    version\n    world\n    level\n    activeUserCount\n    userDropCount\n    userDropRate\n    completionRate\n    winRate\n    playtimeSecPerUser\n    overallUserDropRate\n    attemptCountPerUser\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AccessControlAttributes on AccessControl {\n    roles {\n      roleId\n      permits\n    }\n  }\n"): (typeof documents)["\n  fragment AccessControlAttributes on AccessControl {\n    roles {\n      roleId\n      permits\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ModelAttribute on Attribute {\n    name\n    displayName\n    aggregate\n    permission\n  }\n"): (typeof documents)["\n  fragment ModelAttribute on Attribute {\n    name\n    displayName\n    aggregate\n    permission\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment PageInfoAttributes on PageInfo {\n    total\n    perPage\n    currentPage\n    lastPage\n    firstPage\n  }\n"): (typeof documents)["\n  fragment PageInfoAttributes on PageInfo {\n    total\n    perPage\n    currentPage\n    lastPage\n    firstPage\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment V2GameMetricAttributes on V2GameMetric {\n    id\n    paidInstalls\n    organicInstalls\n    organicPercentage\n    totalInstalls\n    cost\n    cpi\n    roas\n    revenue\n    profit\n    dailyActiveUsers\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n    uaNote\n    monetNote\n    productNote\n    versionNote\n    note\n  }\n"): (typeof documents)["\n  fragment V2GameMetricAttributes on V2GameMetric {\n    id\n    paidInstalls\n    organicInstalls\n    organicPercentage\n    totalInstalls\n    cost\n    cpi\n    roas\n    revenue\n    profit\n    dailyActiveUsers\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n    uaNote\n    monetNote\n    productNote\n    versionNote\n    note\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AdPerformanceAttributes on AdPerformance {\n    ecpm\n    arpDau\n    impsDau\n    adType\n    revenue\n  }\n"): (typeof documents)["\n  fragment AdPerformanceAttributes on AdPerformance {\n    ecpm\n    arpDau\n    impsDau\n    adType\n    revenue\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AclGameMetricMetadataAttributes on GameMetric {\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n"): (typeof documents)["\n  fragment AclGameMetricMetadataAttributes on GameMetric {\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AclGameMetricAttributes on GameMetric {\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    bannerImpsDau @include(if: $canViewBannerImpsDau)\n    interImpsDau @include(if: $canViewInterImpsDau)\n    rewardImpsDau @include(if: $canViewRewardImpsDau)\n    aoaImpsDau @include(if: $canViewAoaImpsDau)\n    mrecImpsDau @include(if: $canViewMrecImpsDau)\n    audioImpsDau @include(if: $canViewAudioImpsDau)\n    aoaAdmobImpsDau @include(if: $canViewAoaAdmobImpsDau)\n    collapseAdmobImpsDau @include(if: $canViewCollapseAdmobImpsDau)\n    nativeAdmobImpsDau @include(if: $canViewNativeAdmobImpsDau)\n    adaptiveAdmobImpsDau @include(if: $canViewAdaptiveAdmobImpsDau)\n    mrecAdmobImpsDau @include(if: $canViewMrecAdmobImpsDau)\n    averageSession @include(if: $canViewAverageSession)\n    playtime @include(if: $canViewPlaytime)\n  }\n"): (typeof documents)["\n  fragment AclGameMetricAttributes on GameMetric {\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    bannerImpsDau @include(if: $canViewBannerImpsDau)\n    interImpsDau @include(if: $canViewInterImpsDau)\n    rewardImpsDau @include(if: $canViewRewardImpsDau)\n    aoaImpsDau @include(if: $canViewAoaImpsDau)\n    mrecImpsDau @include(if: $canViewMrecImpsDau)\n    audioImpsDau @include(if: $canViewAudioImpsDau)\n    aoaAdmobImpsDau @include(if: $canViewAoaAdmobImpsDau)\n    collapseAdmobImpsDau @include(if: $canViewCollapseAdmobImpsDau)\n    nativeAdmobImpsDau @include(if: $canViewNativeAdmobImpsDau)\n    adaptiveAdmobImpsDau @include(if: $canViewAdaptiveAdmobImpsDau)\n    mrecAdmobImpsDau @include(if: $canViewMrecAdmobImpsDau)\n    averageSession @include(if: $canViewAverageSession)\n    playtime @include(if: $canViewPlaytime)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AclAdPerformanceAttributes on AdPerformance {\n    adType\n    revenue @include(if: $canViewGameRevenueRevenue)\n    ecpm @include(if: $canViewGameRevenueEcpm)\n    arpDau @include(if: $canViewGameRevenueArpDau)\n    impsDau @include(if: $canViewGameRevenueImpsDau)\n  }\n"): (typeof documents)["\n  fragment AclAdPerformanceAttributes on AdPerformance {\n    adType\n    revenue @include(if: $canViewGameRevenueRevenue)\n    ecpm @include(if: $canViewGameRevenueEcpm)\n    arpDau @include(if: $canViewGameRevenueArpDau)\n    impsDau @include(if: $canViewGameRevenueImpsDau)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AclGameMetricV2Attributes on V2GameMetric {\n    id\n\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    dailyActiveUsers @include(if: $canViewDailyActiveUsers)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    sessions @include(if: $canViewSessions)\n    playtime @include(if: $canViewPlaytime)\n\n    adPerformances {\n      ...AclAdPerformanceAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment AclGameMetricV2Attributes on V2GameMetric {\n    id\n\n    paidInstalls @include(if: $canViewPaidInstalls)\n    organicInstalls @include(if: $canViewOrganicInstalls)\n    organicPercentage @include(if: $canViewOrganicPercentage)\n    totalInstalls @include(if: $canViewTotalInstalls)\n    cost @include(if: $canViewCost)\n    cpi @include(if: $canViewCpi)\n    roas @include(if: $canViewRoas)\n    revenue @include(if: $canViewRevenue)\n    profit @include(if: $canViewProfit)\n    dailyActiveUsers @include(if: $canViewDailyActiveUsers)\n    retentionRateDay1 @include(if: $canViewRetentionRateDay1)\n    retentionRateDay3 @include(if: $canViewRetentionRateDay3)\n    retentionRateDay7 @include(if: $canViewRetentionRateDay7)\n    sessions @include(if: $canViewSessions)\n    playtime @include(if: $canViewPlaytime)\n\n    adPerformances {\n      ...AclAdPerformanceAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AdTypeAttributes on AdType {\n    id\n    name\n    order\n  }\n"): (typeof documents)["\n  fragment AdTypeAttributes on AdType {\n    id\n    name\n    order\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AdAgencyAttributes on AdAgency {\n    id\n    name\n  }\n"): (typeof documents)["\n  fragment AdAgencyAttributes on AdAgency {\n    id\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameAgencyMetricAttributes on AgencyMetric {\n    cost\n    paidInstalls\n  }\n"): (typeof documents)["\n  fragment GameAgencyMetricAttributes on AgencyMetric {\n    cost\n    paidInstalls\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment SideMenuAttributes on SideMenu {\n    name\n    path\n    group\n    icon\n  }\n"): (typeof documents)["\n  fragment SideMenuAttributes on SideMenu {\n    name\n    path\n    group\n    icon\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameStudioAttributes on GameStudio {\n    id\n    name\n  }\n"): (typeof documents)["\n  fragment GameStudioAttributes on GameStudio {\n    id\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameStudioMetricAttributes on GameStudioMetric {\n    game {\n      ...GameAttributes\n    }\n\n    totalInstalls\n    mmpCostAmount\n    totalAgencyCost\n    revenue\n    profit\n  }\n"): (typeof documents)["\n  fragment GameStudioMetricAttributes on GameStudioMetric {\n    game {\n      ...GameAttributes\n    }\n\n    totalInstalls\n    mmpCostAmount\n    totalAgencyCost\n    revenue\n    profit\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment UserPublicInfoAttributes on UserPublicInfo {\n    id\n    fullName\n  }\n"): (typeof documents)["\n  fragment UserPublicInfoAttributes on UserPublicInfo {\n    id\n    fullName\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment WorkflowStepAttributes on WorkflowStep {\n    name\n    action\n    alternateAction\n    assignee {\n      ...UserPublicInfoAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment WorkflowStepAttributes on WorkflowStep {\n    name\n    action\n    alternateAction\n    assignee {\n      ...UserPublicInfoAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment BudgetRequestAttributes on BudgetRequest {\n    id\n    expirationDate\n    createdAt\n    stepId\n    lastAction\n    description\n    step {\n      ...WorkflowStepAttributes\n    }\n    game {\n      ...GameAttributes\n    }\n    workflow {\n      ...WorkflowAttributes\n    }\n    createdBy {\n      ...UserPublicInfoAttributes\n    }\n    amount\n  }\n"): (typeof documents)["\n  fragment BudgetRequestAttributes on BudgetRequest {\n    id\n    expirationDate\n    createdAt\n    stepId\n    lastAction\n    description\n    step {\n      ...WorkflowStepAttributes\n    }\n    game {\n      ...GameAttributes\n    }\n    workflow {\n      ...WorkflowAttributes\n    }\n    createdBy {\n      ...UserPublicInfoAttributes\n    }\n    amount\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment NetworkAttributes on Network {\n    id\n    name\n  }\n"): (typeof documents)["\n  fragment NetworkAttributes on Network {\n    id\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment MediationAttributes on Mediation {\n    id\n    name\n  }\n"): (typeof documents)["\n  fragment MediationAttributes on Mediation {\n    id\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment FirebaseExperimentAttributes on FirebaseExperiment {\n    name\n  }\n"): (typeof documents)["\n  fragment FirebaseExperimentAttributes on FirebaseExperiment {\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment FirebaseMetricAttributes on FirebaseMetric {\n    date\n    variantId\n    version\n    countryCode\n\n    sessionCount\n    sessionNthDayCounts\n    dailyActiveUserCount\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    retentionNthDayRates\n    installCount\n    sessionCountPerActiveUser\n    retentionRate\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n"): (typeof documents)["\n  fragment FirebaseMetricAttributes on FirebaseMetric {\n    date\n    variantId\n    version\n    countryCode\n\n    sessionCount\n    sessionNthDayCounts\n    dailyActiveUserCount\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    retentionNthDayRates\n    installCount\n    sessionCountPerActiveUser\n    retentionRate\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment FirebaseVersionVariantAttributes on FirebaseVersionVariant {\n    id\n    experiment\n    name\n  }\n"): (typeof documents)["\n  fragment FirebaseVersionVariantAttributes on FirebaseVersionVariant {\n    id\n    experiment\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment FirebaseAdMetricAttributes on FirebaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n  }\n"): (typeof documents)["\n  fragment FirebaseAdMetricAttributes on FirebaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment UserAttributes on User {\n    id\n    fullName\n    email\n    kind\n    hasPassword\n    isDeleted\n    note\n    toolPermissions {\n      ...ToolPermissionAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment UserAttributes on User {\n    id\n    fullName\n    email\n    kind\n    hasPassword\n    isDeleted\n    note\n    toolPermissions {\n      ...ToolPermissionAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ToolPermissionAttributes on ToolPermission {\n    tool\n    action\n  }\n"): (typeof documents)["\n  fragment ToolPermissionAttributes on ToolPermission {\n    tool\n    action\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameRoleMembershipAttributes on GameRoleMembership {\n    id\n    roleId\n    storeId\n    users {\n      email\n    }\n  }\n"): (typeof documents)["\n  fragment GameRoleMembershipAttributes on GameRoleMembership {\n    id\n    roleId\n    storeId\n    users {\n      email\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameRolesAttributes on GameRoles {\n    id\n    roles {\n      id\n      users {\n        email\n      }\n    }\n  }\n"): (typeof documents)["\n  fragment GameRolesAttributes on GameRoles {\n    id\n    roles {\n      id\n      users {\n        email\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ReleaseMetricAttributes on ReleaseMetric {\n    date\n    version\n    countryCode\n    sessionCount\n    sessionNthDayCounts\n    sessionCountPerActiveUser\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    installCount\n    retentionRate\n    retentionNthDayRates\n    dailyActiveUserCount\n    lifetimeNthDayValues\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n    adRevNthDayGrossAmounts\n    impressionNthDayCounts\n  }\n"): (typeof documents)["\n  fragment ReleaseMetricAttributes on ReleaseMetric {\n    date\n    version\n    countryCode\n    sessionCount\n    sessionNthDayCounts\n    sessionCountPerActiveUser\n    activeUserNthDayCounts\n    playtimeMsec\n    playtimeNthDayMsecs\n    installCount\n    retentionRate\n    retentionNthDayRates\n    dailyActiveUserCount\n    lifetimeNthDayValues\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n    adRevNthDayGrossAmounts\n    impressionNthDayCounts\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ReleaseAdMetricAttributes on ReleaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n"): (typeof documents)["\n  fragment ReleaseAdMetricAttributes on ReleaseAdMetric {\n    adTypeCategory\n\n    adRevGrossAmount\n    impressionCount\n    impressionCountPerActiveUser\n    adRevGrossAmountPerActiveUser\n\n    impressionNthDayCounts\n    adRevNthDayGrossAmounts\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricIndex_GameMetricAttributes on GameMetric {\n    id\n    date\n    ...AclGameMetricAttributes\n    ...AclGameMetricMetadataAttributes\n  }\n"): (typeof documents)["\n  fragment GameMetricIndex_GameMetricAttributes on GameMetric {\n    id\n    date\n    ...AclGameMetricAttributes\n    ...AclGameMetricMetadataAttributes\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameCostAttributes on GameCost {\n    id\n    date\n    type\n    network {\n      id\n      name\n    }\n    preTaxAmount\n    totalAmount\n    mmpAmount\n    taxAmount\n  }\n"): (typeof documents)["\n  fragment GameCostAttributes on GameCost {\n    id\n    date\n    type\n    network {\n      id\n      name\n    }\n    preTaxAmount\n    totalAmount\n    mmpAmount\n    taxAmount\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GetAllUsers_UserAttributes on User {\n    id\n    email\n    fullName\n    team {\n      ...TeamAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment GetAllUsers_UserAttributes on User {\n    id\n    email\n    fullName\n    team {\n      ...TeamAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAllUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GetAllUsers_UserAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetAllUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GetAllUsers_UserAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment MetabaseChartAttributes on MetabaseChart {\n    url\n  }\n"): (typeof documents)["\n  fragment MetabaseChartAttributes on MetabaseChart {\n    url\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetMetabaseChart($where: MetabaseChartWhere!) {\n    metabaseChart(where: $where) {\n      ...MetabaseChartAttributes\n    }\n  }\n"): (typeof documents)["\n  query GetMetabaseChart($where: MetabaseChartWhere!) {\n    metabaseChart(where: $where) {\n      ...MetabaseChartAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateProfile($form: UpdateProfileForm!) {\n    updateProfile(form: $form) {\n      ...UserPublicProfile\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateProfile($form: UpdateProfileForm!) {\n    updateProfile(form: $form) {\n      ...UserPublicProfile\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetProfile {\n    profile {\n      ...UserPublicProfile\n      team {\n        ...TeamAttributes\n      }\n      ledTeam {\n        ...TeamAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetProfile {\n    profile {\n      ...UserPublicProfile\n      team {\n        ...TeamAttributes\n      }\n      ledTeam {\n        ...TeamAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ViewPresetEditor_CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation ViewPresetEditor_CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ViewPresetSelection_DeleteViewPreset($where: DeleteViewPresetsWhere!) {\n    deleteViewPresets(where: $where)\n  }\n"): (typeof documents)["\n  mutation ViewPresetSelection_DeleteViewPreset($where: DeleteViewPresetsWhere!) {\n    deleteViewPresets(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetConfigMaps($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n"): (typeof documents)["\n  query GetConfigMaps($ids: [ID!]!) {\n    configMapCollections(ids: $ids) {\n      ...ConfigMapCollectionAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Layout_GetAnnouncements {\n    dashboardNotifications {\n      collection {\n        ...AnnouncementAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query Layout_GetAnnouncements {\n    dashboardNotifications {\n      collection {\n        ...AnnouncementAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AnnouncementAttributes on DashboardNotification {\n    id\n    message\n    isPinned\n    isVisible\n  }\n"): (typeof documents)["\n  fragment AnnouncementAttributes on DashboardNotification {\n    id\n    message\n    isPinned\n    isVisible\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateConfigMaps($where: UpdateConfigMapsWhere!) {\n    updateConfigMaps(where: $where)\n  }\n"): (typeof documents)["\n  mutation UpdateConfigMaps($where: UpdateConfigMapsWhere!) {\n    updateConfigMaps(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Layout_GetGames($keyword: String!) {\n    games(where: { keyword: $keyword }, offset: { page: 1, perPage: 10 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query Layout_GetGames($keyword: String!) {\n    games(where: { keyword: $keyword }, offset: { page: 1, perPage: 10 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Sidebar_GetMainMenus {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query Sidebar_GetMainMenus {\n    sideMenus {\n      collection {\n        ...SideMenuAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Acl_GameMetricAttributes(\n    $readSubject: String!\n    $writeSubject: String!\n    $modelName: String!\n  ) {\n    attributes(where: { gameId: null, modelName: $modelName }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    read: accessControl(where: { subject: $readSubject }) {\n      ...AccessControlAttributes\n    }\n\n    write: accessControl(where: { subject: $writeSubject }) {\n      ...AccessControlAttributes\n    }\n  }\n"): (typeof documents)["\n  query Acl_GameMetricAttributes(\n    $readSubject: String!\n    $writeSubject: String!\n    $modelName: String!\n  ) {\n    attributes(where: { gameId: null, modelName: $modelName }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    read: accessControl(where: { subject: $readSubject }) {\n      ...AccessControlAttributes\n    }\n\n    write: accessControl(where: { subject: $writeSubject }) {\n      ...AccessControlAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateAccessControl($where: AccessControlWhere!, $form: AccessControlUpdateForm!) {\n    updateAccessControl(where: $where, form: $form) {\n      ...AccessControlAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateAccessControl($where: AccessControlWhere!, $form: AccessControlUpdateForm!) {\n    updateAccessControl(where: $where, form: $form) {\n      ...AccessControlAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAccessControl($where: AccessControlWhere!) {\n    accessControl(where: $where) {\n      ...AccessControlAttributes\n    }\n  }\n"): (typeof documents)["\n  query GetAccessControl($where: AccessControlWhere!) {\n    accessControl(where: $where) {\n      ...AccessControlAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment WorkflowStepActionAttributes on WorkflowStepAction {\n    action\n  }\n"): (typeof documents)["\n  fragment WorkflowStepActionAttributes on WorkflowStepAction {\n    action\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query BudgetRequestIndex_GetInitialData {\n    games(where: {}, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    workflowStepActions {\n      collection {\n        ...WorkflowStepActionAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query BudgetRequestIndex_GetInitialData {\n    games(where: {}, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    workflowStepActions {\n      collection {\n        ...WorkflowStepActionAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query BudgetRequestIndex_GetBudgetRequests($where: BudgetRequestsWhere!, $offset: Offset) {\n    budgetRequests(where: $where, offset: $offset) {\n      collection {\n        ...BudgetRequestAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query BudgetRequestIndex_GetBudgetRequests($where: BudgetRequestsWhere!, $offset: Offset) {\n    budgetRequests(where: $where, offset: $offset) {\n      collection {\n        ...BudgetRequestAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation BudgetRequestIndex_UpdateBudgetRequestsState($forms: [UpdateBudgetRequestStateForm!]!) {\n    updateBudgetRequestsState(forms: $forms) {\n      ...BudgetRequestAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation BudgetRequestIndex_UpdateBudgetRequestsState($forms: [UpdateBudgetRequestStateForm!]!) {\n    updateBudgetRequestsState(forms: $forms) {\n      ...BudgetRequestAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation BudgetRequestIndex_DeleteBudgetRequests($ids: [Int!]!) {\n    deleteBudgetRequests(where: { ids: $ids })\n  }\n"): (typeof documents)["\n  mutation BudgetRequestIndex_DeleteBudgetRequests($ids: [Int!]!) {\n    deleteBudgetRequests(where: { ids: $ids })\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation BudgetRequestIndex_CreateBudgetRequest($form: CreateBudgetRequestForm!) {\n    createBudgetRequest(form: $form) {\n      ...BudgetRequestAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation BudgetRequestIndex_CreateBudgetRequest($form: CreateBudgetRequestForm!) {\n    createBudgetRequest(form: $form) {\n      ...BudgetRequestAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation BudgetRequestIndex_UpdateBudgetRequests($form: UpdateBudgetRequestForm!) {\n    updateBudgetRequests(forms: [$form]) {\n      ...BudgetRequestAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation BudgetRequestIndex_UpdateBudgetRequests($form: UpdateBudgetRequestForm!) {\n    updateBudgetRequests(forms: [$form]) {\n      ...BudgetRequestAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GamesCampaignMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GamesCampaignMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GamesCampaignMetricsIndex_GetData(\n    $where: AdMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    adMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...AdMetricAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GamesCampaignMetricsIndex_GetData(\n    $where: AdMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    adMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...AdMetricAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment FirebaseExperimentIndex_FirebaseMetric on FirebaseMetric {\n    ...FirebaseMetricAttributes\n\n    ads {\n      ...FirebaseAdMetricAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment FirebaseExperimentIndex_FirebaseMetric on FirebaseMetric {\n    ...FirebaseMetricAttributes\n\n    ads {\n      ...FirebaseAdMetricAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query FirebaseExperimentIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query FirebaseExperimentIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query FirebaseExperimentIndex_GetInitialData($gameId: ID!) {\n    firebaseExperiments(where: { gameId: $gameId }) {\n      collection {\n        ...FirebaseExperimentAttributes\n      }\n    }\n\n    networks(where: { category: FIREBASE }) {\n      collection {\n        ...NetworkAttributes\n      }\n    }\n\n    mediations {\n      collection {\n        ...MediationAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query FirebaseExperimentIndex_GetInitialData($gameId: ID!) {\n    firebaseExperiments(where: { gameId: $gameId }) {\n      collection {\n        ...FirebaseExperimentAttributes\n      }\n    }\n\n    networks(where: { category: FIREBASE }) {\n      collection {\n        ...NetworkAttributes\n      }\n    }\n\n    mediations {\n      collection {\n        ...MediationAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query FirebaseExperimentIndex_GetVersionVariants($where: FirebaseVersionVariantsWhere!) {\n    firebaseVersionVariants(where: $where) {\n      collection {\n        ...FirebaseVersionVariantAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query FirebaseExperimentIndex_GetVersionVariants($where: FirebaseVersionVariantsWhere!) {\n    firebaseVersionVariants(where: $where) {\n      collection {\n        ...FirebaseVersionVariantAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query FirebaseExperimentIndex_GetMetrics(\n    $where: FirebaseMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    firebaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...FirebaseExperimentIndex_FirebaseMetric\n      }\n\n      variants {\n        ...FirebaseVersionVariantAttributes\n      }\n\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query FirebaseExperimentIndex_GetMetrics(\n    $where: FirebaseMetricsWhere!\n    $group: Group!\n    $preset: Preset\n  ) {\n    firebaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...FirebaseExperimentIndex_FirebaseMetric\n      }\n\n      variants {\n        ...FirebaseVersionVariantAttributes\n      }\n\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameAgencyCostAttributes on GameAgencyCost {\n    totalCost\n    mediationCost\n    varianceRate\n    agencyId\n  }\n"): (typeof documents)["\n  fragment GameAgencyCostAttributes on GameAgencyCost {\n    totalCost\n    mediationCost\n    varianceRate\n    agencyId\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GameCostEdit_UpdateGameCost($form: UpdateGameCostForm!, $where: UpdateGameCostWhere!) {\n    updateGameCost(form: $form, where: $where)\n  }\n"): (typeof documents)["\n  mutation GameCostEdit_UpdateGameCost($form: UpdateGameCostForm!, $where: UpdateGameCostWhere!) {\n    updateGameCost(form: $form, where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameCostsOverview_GetAdNetworks {\n    adNetworks {\n      collection {\n        ...GameCostIndexOverview_AdNetworkAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameCostsOverview_GetAdNetworks {\n    adNetworks {\n      collection {\n        ...GameCostIndexOverview_AdNetworkAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameSpendShow_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"): (typeof documents)["\n  query GameSpendShow_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameCostsComparison_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameCostsComparison_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameCostsAggregation_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameCostsAggregation_GetData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameCostIndexOverview_GameCostAttributes on GameCost {\n    id\n    date\n    mmpAmount\n    preTaxAmount\n    totalAmount\n    taxAmount\n    network {\n      id\n      name\n    }\n  }\n"): (typeof documents)["\n  fragment GameCostIndexOverview_GameCostAttributes on GameCost {\n    id\n    date\n    mmpAmount\n    preTaxAmount\n    totalAmount\n    taxAmount\n    network {\n      id\n      name\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameCostsOverview_GetData($where: GameCostsWhere!, $offset: Offset!) {\n    gameCosts(where: $where, offset: $offset) {\n      collection {\n        ...GameCostIndexOverview_GameCostAttributes\n      }\n      meta {\n        aggregation {\n          mmpAmount\n          totalAmount\n          preTaxAmount\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameCostsOverview_GetData($where: GameCostsWhere!, $offset: Offset!) {\n    gameCosts(where: $where, offset: $offset) {\n      collection {\n        ...GameCostIndexOverview_GameCostAttributes\n      }\n      meta {\n        aggregation {\n          mmpAmount\n          totalAmount\n          preTaxAmount\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameCostIndexOverview_AdNetworkAttributes on AdNetwork {\n    id\n    name\n    inputType\n  }\n"): (typeof documents)["\n  fragment GameCostIndexOverview_AdNetworkAttributes on AdNetwork {\n    id\n    name\n    inputType\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GameMetricEdit_UpdateGameMetric(\n    $where: UpdateGameMetricWhere!\n    $form: UpdateGameMetricForm!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    updateGameMetric(where: $where, form: $form) {\n      ...GameMetricIndex_GameMetricAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation GameMetricEdit_UpdateGameMetric(\n    $where: UpdateGameMetricWhere!\n    $form: UpdateGameMetricForm!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    updateGameMetric(where: $where, form: $form) {\n      ...GameMetricIndex_GameMetricAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GamesMetricsShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"): (typeof documents)["\n  query GamesMetricsShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GamesMetricsShow_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        ...GameMetricIndex_GameMetricAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GamesMetricsShow_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        ...GameMetricIndex_GameMetricAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricIndex_GetAggregation(\n    $gameId: ID!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n  ) {\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n"): (typeof documents)["\n  query GameMetricIndex_GetAggregation(\n    $gameId: ID!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n  ) {\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricNextIndex_GetInitData($gameId: ID!) {\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameMetricNextIndex_GetInitData($gameId: ID!) {\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameMetricNextIndex_GameMetricAttributes on V2GameMetric {\n    date\n    gameId\n    ...AclGameMetricV2Attributes\n\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n"): (typeof documents)["\n  fragment GameMetricNextIndex_GameMetricAttributes on V2GameMetric {\n    date\n    gameId\n    ...AclGameMetricV2Attributes\n\n    uaNote @include(if: $canViewUaNote)\n    monetNote @include(if: $canViewMonetNote)\n    productNote @include(if: $canViewProductNote)\n    versionNote @include(if: $canViewVersionNote)\n    note @include(if: $canViewNote)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricNextIndex_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          ...GameMetricNextIndex_GameMetricAttributes\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameMetricNextIndex_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          ...GameMetricNextIndex_GameMetricAttributes\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricNextIndex_GetAggregation(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameMetricNextIndex_GetAggregation(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GamesIndex_GetInitialData($where: GamesWhere!) {\n    games(where: $where, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GamesIndex_GetInitialData($where: GamesWhere!) {\n    games(where: $where, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListGameRoles($gameIds: [ID!]!) {\n    gameRoles(gameIds: $gameIds) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListGameRoles($gameIds: [ID!]!) {\n    gameRoles(gameIds: $gameIds) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateGameRoles($forms: [UpdateGameRolesGame!]!) {\n    updateGameRoles(forms: $forms) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateGameRoles($forms: [UpdateGameRolesGame!]!) {\n    updateGameRoles(forms: $forms) {\n      collection {\n        ...GameRolesAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameRoles_GetData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"): (typeof documents)["\n  query GameRoles_GetData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ReleaseMetricIndex_ReleaseMetricAttributes on ReleaseMetric {\n    ...ReleaseMetricAttributes\n\n    ads {\n      ...ReleaseAdMetricAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment ReleaseMetricIndex_ReleaseMetricAttributes on ReleaseMetric {\n    ...ReleaseMetricAttributes\n\n    ads {\n      ...ReleaseAdMetricAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ReleaseMetricIndex_GetData($where: ReleaseMetricsWhere!, $group: Group, $preset: Preset) {\n    releaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...ReleaseMetricIndex_ReleaseMetricAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query ReleaseMetricIndex_GetData($where: ReleaseMetricsWhere!, $group: Group, $preset: Preset) {\n    releaseMetrics(where: $where, group: $group, preset: $preset) {\n      collection {\n        ...ReleaseMetricIndex_ReleaseMetricAttributes\n      }\n      viewPreset {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ReleaseMetricIndex_GetVersion($where: ReleaseVersionsWhere!) {\n    releaseVersions(where: $where) {\n      collection {\n        version\n      }\n    }\n  }\n"): (typeof documents)["\n  query ReleaseMetricIndex_GetVersion($where: ReleaseVersionsWhere!) {\n    releaseVersions(where: $where) {\n      collection {\n        version\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GamesReleaseMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GamesReleaseMetricsIndex_GetViewPresets($where: ViewPresetsWhere!) {\n    viewPresets(where: $where) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query AdmobGamesQuery {\n    admobGames {\n      collection\n    }\n  }\n"): (typeof documents)["\n  query AdmobGamesQuery {\n    admobGames {\n      collection\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query AdmobMetrics(\n    $gameId: String!\n    $baselineDateFrom: Date!\n    $baselineDateTo: Date!\n    $targetDateFrom: Date!\n    $targetDateTo: Date!\n    $formats: [String!]!\n  ) {\n    baseline: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $baselineDateFrom\n        dateTo: $baselineDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n\n    target: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $targetDateFrom\n        dateTo: $targetDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n  }\n"): (typeof documents)["\n  query AdmobMetrics(\n    $gameId: String!\n    $baselineDateFrom: Date!\n    $baselineDateTo: Date!\n    $targetDateFrom: Date!\n    $targetDateTo: Date!\n    $formats: [String!]!\n  ) {\n    baseline: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $baselineDateFrom\n        dateTo: $baselineDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n\n    target: admobMetrics(\n      where: {\n        gameId: $gameId\n        dateFrom: $targetDateFrom\n        dateTo: $targetDateTo\n        formats: $formats\n      }\n    ) {\n      mediation\n      network\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListTeam {\n    teams {\n      id\n      name\n      roleId\n      leader {\n        id\n        email\n        fullName\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListTeam {\n    teams {\n      id\n      name\n      roleId\n      leader {\n        id\n        email\n        fullName\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TeamCreate_GetInitialData {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n  }\n"): (typeof documents)["\n  query TeamCreate_GetInitialData {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateTeam($form: CreateTeamForm!) {\n    createTeam(form: $form) {\n      ...TeamForm_Team\n    }\n  }\n"): (typeof documents)["\n  mutation CreateTeam($form: CreateTeamForm!) {\n    createTeam(form: $form) {\n      ...TeamForm_Team\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TeamEdit_GetInitialData($teamId: Int!) {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n\n    team(id: $teamId) {\n      ...TeamForm_Team\n    }\n  }\n"): (typeof documents)["\n  query TeamEdit_GetInitialData($teamId: Int!) {\n    users(where: {}, offset: { perPage: 1000 }) {\n      collection {\n        ...TeamForm_Member\n      }\n    }\n\n    team(id: $teamId) {\n      ...TeamForm_Team\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateTeam($where: UpdateTeamWhere!, $form: UpdateTeamForm!) {\n    updateTeam(where: $where, form: $form) {\n      ...TeamForm_Team\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateTeam($where: UpdateTeamWhere!, $form: UpdateTeamForm!) {\n    updateTeam(where: $where, form: $form) {\n      ...TeamForm_Team\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteTeam($where: DeleteTeamWhere!) {\n    deleteTeam(where: $where)\n  }\n"): (typeof documents)["\n  mutation DeleteTeam($where: DeleteTeamWhere!) {\n    deleteTeam(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment TeamForm_Member on User {\n    ...UserPublicProfile\n    inchargedGames {\n      storeId\n      roleId\n    }\n    team {\n      ...TeamAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment TeamForm_Member on User {\n    ...UserPublicProfile\n    inchargedGames {\n      storeId\n      roleId\n    }\n    team {\n      ...TeamAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment TeamForm_Team on Team {\n    ...TeamAttributes\n    members {\n      ...UserPublicProfile\n    }\n    leader {\n      ...UserPublicProfile\n    }\n  }\n"): (typeof documents)["\n  fragment TeamForm_Team on Team {\n    ...TeamAttributes\n    members {\n      ...UserPublicProfile\n    }\n    leader {\n      ...UserPublicProfile\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query UserIndex_GetUsers($offset: Offset!) {\n    users(where: {}, offset: $offset) {\n      collection {\n        ...UserAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query UserIndex_GetUsers($offset: Offset!) {\n    users(where: {}, offset: $offset) {\n      collection {\n        ...UserAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UserIndex_UpdateUser($where: UpdateUserWhere!, $form: UpdateUserForm!) {\n    updateUser(where: $where, form: $form) {\n      ...UserAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation UserIndex_UpdateUser($where: UpdateUserWhere!, $form: UpdateUserForm!) {\n    updateUser(where: $where, form: $form) {\n      ...UserAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UserIndex_CreateUser($form: CreateUserForm!) {\n    createUser(form: $form) {\n      ...UserAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation UserIndex_CreateUser($form: CreateUserForm!) {\n    createUser(form: $form) {\n      ...UserAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UserIndex_DeleteUsers($where: DeleteUsersWhere!) {\n    deleteUsers(where: $where)\n  }\n"): (typeof documents)["\n  mutation UserIndex_DeleteUsers($where: DeleteUsersWhere!) {\n    deleteUsers(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment WorkflowAttributes on Workflow {\n    id\n    name\n    roleId\n    steps {\n      ...WorkflowStepAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment WorkflowAttributes on Workflow {\n    id\n    name\n    roleId\n    steps {\n      ...WorkflowStepAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query WorkflowsIndex_GetInitialData {\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    users(where: {}, offset: { perPage: 9999 }) {\n      collection {\n        ...UserPublicProfile\n      }\n    }\n  }\n"): (typeof documents)["\n  query WorkflowsIndex_GetInitialData {\n    workflows {\n      collection {\n        ...WorkflowAttributes\n      }\n    }\n\n    users(where: {}, offset: { perPage: 9999 }) {\n      collection {\n        ...UserPublicProfile\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation WorkflowsIndex_Create($form: CreateWorkflowForm!) {\n    createWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation WorkflowsIndex_Create($form: CreateWorkflowForm!) {\n    createWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation WorkflowsIndex_Update($form: UpdateWorkflowForm!) {\n    updateWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation WorkflowsIndex_Update($form: UpdateWorkflowForm!) {\n    updateWorkflow(form: $form) {\n      ...WorkflowAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameCreativeMetricAttributes on GameCreativeMetric {\n    id\n    date\n    campaign\n    adGroup\n    adGroupStartDate\n    adSet\n    editor\n    isPlayable\n    targetRoasRate\n    preTaxCostAmount\n    grossRevenueAmount\n    clickCount\n    impressionCount\n    installCount\n    roasRate\n    cpi\n    conversionRate\n    clickthroughRate\n\n    agency {\n      ...AdAgencyAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment GameCreativeMetricAttributes on GameCreativeMetric {\n    id\n    date\n    campaign\n    adGroup\n    adGroupStartDate\n    adSet\n    editor\n    isPlayable\n    targetRoasRate\n    preTaxCostAmount\n    grossRevenueAmount\n    clickCount\n    impressionCount\n    installCount\n    roasRate\n    cpi\n    conversionRate\n    clickthroughRate\n\n    agency {\n      ...AdAgencyAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameCreativeMetrics_Index(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $page: Int!\n    $perPage: Int!\n    $direction: OrderDirection!\n  ) {\n    gameCreativeMetrics(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $direction }\n    ) {\n      collection {\n        ...GameCreativeMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameCreativeMetrics_Index(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $page: Int!\n    $perPage: Int!\n    $direction: OrderDirection!\n  ) {\n    gameCreativeMetrics(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $direction }\n    ) {\n      collection {\n        ...GameCreativeMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameLevelDropIndex_QueryData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    gameLevelDropVersions(where: { gameId: $gameId }) {\n      collection\n    }\n  }\n"): (typeof documents)["\n  query GameLevelDropIndex_QueryData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    gameLevelDropVersions(where: { gameId: $gameId }) {\n      collection\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameLevelDropIndex_QueryVersions($gameId: ID!, $versions: [String!]!) {\n    gameLevelDrops(where: { gameId: $gameId, versions: $versions }) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameLevelDropIndex_QueryVersions($gameId: ID!, $versions: [String!]!) {\n    gameLevelDrops(where: { gameId: $gameId, versions: $versions }) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameNetworkRevenueAggregationAttributes on GameNetworkRevenue {\n    networkId\n    revenue\n    mediationRevenue\n    varianceRate\n  }\n"): (typeof documents)["\n  fragment GameNetworkRevenueAggregationAttributes on GameNetworkRevenue {\n    networkId\n    revenue\n    mediationRevenue\n    varianceRate\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameRevenueMetricAttributes on AgencyMetric {\n    revenue\n  }\n"): (typeof documents)["\n  fragment GameRevenueMetricAttributes on AgencyMetric {\n    revenue\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameNetworkRevenueAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAggregationAttributes\n    date\n  }\n\n  fragment GameNetworkRevenueWithMetricAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAttributes\n    metric {\n      ...GameRevenueMetricAttributes\n    }\n  }\n"): (typeof documents)["\n  fragment GameNetworkRevenueAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAggregationAttributes\n    date\n  }\n\n  fragment GameNetworkRevenueWithMetricAttributes on GameNetworkRevenue {\n    ...GameNetworkRevenueAttributes\n    metric {\n      ...GameRevenueMetricAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameRevenueIndex_getNetworkRevenue(\n    $dateFrom: Date!\n    $dateTo: Date!\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n    $orderDirection: OrderDirection!\n  ) {\n    gameNetworkRevenues(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameNetworkRevenueWithMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameNetworkRevenue(where: { gameId: $gameId }) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n        network {\n          id\n          name\n        }\n      }\n    }\n\n    total: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n\n    weekly: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameRevenueIndex_getNetworkRevenue(\n    $dateFrom: Date!\n    $dateTo: Date!\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n    $orderDirection: OrderDirection!\n  ) {\n    gameNetworkRevenues(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameNetworkRevenueWithMetricAttributes\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameNetworkRevenue(where: { gameId: $gameId }) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n        network {\n          id\n          name\n        }\n      }\n    }\n\n    total: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n\n    weekly: aggregateGameNetworkRevenue(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameNetworkRevenueAggregationAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameRevenueExplorerIndex_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"): (typeof documents)["\n  query GameRevenueExplorerIndex_Init($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameReviewIndex_Setting($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n    gamePerformanceSetting(gameId: $gameId) {\n      criterias {\n        conclusion\n        metrics\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameReviewIndex_Setting($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n    gamePerformanceSetting(gameId: $gameId) {\n      criterias {\n        conclusion\n        metrics\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameReviewIndex_Data($gameId: ID!, $from: Date!, $to: Date!) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $from, dateLte: $to }\n      offset: { perPage: 7, page: 1 }\n      order: { direction: ASC }\n    ) {\n      collection {\n        date\n        roas\n        playtime\n        retentionRateDay1\n        cpi\n        bannerImpsDau\n        interImpsDau\n        ...GameMetricMetadata\n      }\n    }\n\n    gameReview(gameId: $gameId, date: $from) {\n      productNote\n      marketingNote\n    }\n  }\n"): (typeof documents)["\n  query GameReviewIndex_Data($gameId: ID!, $from: Date!, $to: Date!) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $from, dateLte: $to }\n      offset: { perPage: 7, page: 1 }\n      order: { direction: ASC }\n    ) {\n      collection {\n        date\n        roas\n        playtime\n        retentionRateDay1\n        cpi\n        bannerImpsDau\n        interImpsDau\n        ...GameMetricMetadata\n      }\n    }\n\n    gameReview(gameId: $gameId, date: $from) {\n      productNote\n      marketingNote\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameCostIndex_AgencyData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameCostIndex_AgencyData(\n    $gameId: ID!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $orderDirection: OrderDirection!\n    $page: Int!\n    $perPage: Int!\n    $weeklyDateFrom: Date!\n    $weeklyDateTo: Date!\n  ) {\n    gameAgencyCosts(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n      offset: { page: $page, perPage: $perPage }\n      order: { direction: $orderDirection }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n        date\n        metric {\n          ...GameAgencyMetricAttributes\n        }\n      }\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameAgencyCost(where: { gameId: $gameId }) {\n      collection {\n        ...GameAgencyCostAttributes\n        agency {\n          ...AdAgencyAttributes\n        }\n      }\n    }\n\n    total: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n\n    weekly: aggregateGameAgencyCost(\n      where: { gameId: $gameId, dateFrom: $weeklyDateFrom, dateTo: $weeklyDateTo }\n    ) {\n      collection {\n        ...GameAgencyCostAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ProductMetricIndex_GetVersions($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {\n    gameRewardUsageVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameDailyLevelDropVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gamePlaytimeVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameRetentionRateVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n  }\n"): (typeof documents)["\n  query ProductMetricIndex_GetVersions($dateFrom: Date!, $dateTo: Date!, $gameId: ID!) {\n    gameRewardUsageVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameDailyLevelDropVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gamePlaytimeVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n\n    gameRetentionRateVersions(where: { dateFrom: $dateFrom, dateTo: $dateTo, gameId: $gameId }) {\n      collection\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ProductMetricIndex_GetGame($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"): (typeof documents)["\n  query ProductMetricIndex_GetGame($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ProductMetricIndex_VersionsData(\n    $gameId: ID!\n    $versions: [String!]!\n    $groupByLevel: Boolean!\n    $groupByLocation: Boolean!\n    $group: [String!]!\n    $order: OrderDirection!\n    $activeDateFrom: Date!\n    $activeDateTo: Date!\n    $installTimeFrom: Date\n    $installTimeTo: Date\n  ) {\n    gameRewardUsages(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n      group: { fields: $group }\n      order: { direction: $order }\n    ) {\n      collection {\n        location @include(if: $groupByLocation)\n        level @include(if: $groupByLevel)\n        world @include(if: $groupByLevel)\n        useCount\n      }\n    }\n\n    gameLevelDrops(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n      }\n    ) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n\n    gameRetentionRates(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n    ) {\n      collection {\n        date\n        gameId\n        newUsers\n        day1\n        day2\n        day3\n        day4\n        day5\n        day6\n        day7\n      }\n    }\n\n    aggregatePlaytime(\n      where: {\n        gameId: $gameId\n        activeDateFrom: $activeDateFrom\n        activeDateTo: $activeDateTo\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n        versions: $versions\n      }\n    ) {\n      engagementSessionCount\n      playtimeSec\n    }\n  }\n"): (typeof documents)["\n  query ProductMetricIndex_VersionsData(\n    $gameId: ID!\n    $versions: [String!]!\n    $groupByLevel: Boolean!\n    $groupByLocation: Boolean!\n    $group: [String!]!\n    $order: OrderDirection!\n    $activeDateFrom: Date!\n    $activeDateTo: Date!\n    $installTimeFrom: Date\n    $installTimeTo: Date\n  ) {\n    gameRewardUsages(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n      group: { fields: $group }\n      order: { direction: $order }\n    ) {\n      collection {\n        location @include(if: $groupByLocation)\n        level @include(if: $groupByLevel)\n        world @include(if: $groupByLevel)\n        useCount\n      }\n    }\n\n    gameLevelDrops(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n      }\n    ) {\n      collection {\n        ...GameLevelDropAttributes\n      }\n    }\n\n    gameRetentionRates(\n      where: {\n        dateFrom: $activeDateFrom\n        dateTo: $activeDateTo\n        gameId: $gameId\n        versions: $versions\n      }\n    ) {\n      collection {\n        date\n        gameId\n        newUsers\n        day1\n        day2\n        day3\n        day4\n        day5\n        day6\n        day7\n      }\n    }\n\n    aggregatePlaytime(\n      where: {\n        gameId: $gameId\n        activeDateFrom: $activeDateFrom\n        activeDateTo: $activeDateTo\n        installDateFrom: $installTimeFrom\n        installDateTo: $installTimeTo\n        versions: $versions\n      }\n    ) {\n      engagementSessionCount\n      playtimeSec\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment GameRevenueIndex_UserAttributes on User {\n    email\n    fullName\n  }\n"): (typeof documents)["\n  fragment GameRevenueIndex_UserAttributes on User {\n    email\n    fullName\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameRevenuesIndex_GetUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GameRevenueIndex_UserAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameRevenuesIndex_GetUsers {\n    users(where: {}, offset: { page: 1, perPage: 1000 }) {\n      collection {\n        ...GameRevenueIndex_UserAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameStudioMetricsIndex_GetInitialData {\n    gameStudios {\n      collection {\n        ...GameStudioAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameStudioMetricsIndex_GetInitialData {\n    gameStudios {\n      collection {\n        ...GameStudioAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameStudioMetricsIndex_GetMetrics($studioId: Int!, $dateFrom: Date!, $dateTo: Date!) {\n    metrics: gameStudioMetrics(\n      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameStudioMetricAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameStudioMetricsIndex_GetMetrics($studioId: Int!, $dateFrom: Date!, $dateTo: Date!) {\n    metrics: gameStudioMetrics(\n      where: { studioId: $studioId, dateFrom: $dateFrom, dateTo: $dateTo }\n    ) {\n      collection {\n        ...GameStudioMetricAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AdMetricAttributes on AdMetric {\n    date\n    adId\n    groupId\n    campaignId\n    countryCode\n    agencyId\n    adRevGrossAmount\n    adCostNonTaxAmount\n    impressionCount\n    roasNthDayRates\n    retentionNthDayRates\n    activeUserNthDayCounts\n    sessionNthDayCounts\n    sessionCount\n    dailyActiveUserCount\n    retentionRate\n    clickCount\n    installCount\n    adRevNthDayGrossAmounts\n    cpi\n    ctr\n    cvr\n    cpc\n    ipm\n    roas\n  }\n"): (typeof documents)["\n  fragment AdMetricAttributes on AdMetric {\n    date\n    adId\n    groupId\n    campaignId\n    countryCode\n    agencyId\n    adRevGrossAmount\n    adCostNonTaxAmount\n    impressionCount\n    roasNthDayRates\n    retentionNthDayRates\n    activeUserNthDayCounts\n    sessionNthDayCounts\n    sessionCount\n    dailyActiveUserCount\n    retentionRate\n    clickCount\n    installCount\n    adRevNthDayGrossAmounts\n    cpi\n    ctr\n    cvr\n    cpc\n    ipm\n    roas\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AdAttributes on Ad {\n    id\n    name\n    campaignId\n    groupId\n  }\n"): (typeof documents)["\n  fragment AdAttributes on Ad {\n    id\n    name\n    campaignId\n    groupId\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment AdGroupAttributes on AdGroup {\n    id\n    name\n  }\n"): (typeof documents)["\n  fragment AdGroupAttributes on AdGroup {\n    id\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment CampaignAttributes on Campaign {\n    id\n    name\n  }\n"): (typeof documents)["\n  fragment CampaignAttributes on Campaign {\n    id\n    name\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ViewPresetAttributes on ViewPreset {\n    id\n    name\n    attributes {\n      name\n      isCohort\n      cohortDays\n    }\n  }\n"): (typeof documents)["\n  fragment ViewPresetAttributes on ViewPreset {\n    id\n    name\n    attributes {\n      name\n      isCohort\n      cohortDays\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query AdMetricsQuery(\n    $gameId: ID!\n    $dateFrom: FilterValue!\n    $dateTo: FilterValue!\n    $groupByFields: [String!]!\n    $campaignId: Filter\n    $agencyId: Filter\n    $groupId: Filter\n    $adId: Filter\n    $countryCode: Filter\n    $cvr: Filter\n    $cpi: Filter\n    $ctr: Filter\n    $roas: Filter\n    $adRevGrossAmount: Filter\n    $adCostNonTaxAmount: Filter\n    $impressionCount: Filter\n    $clickCount: Filter\n    $installCount: Filter\n    $cpc: Filter\n    $ipm: Filter\n    $preset: Int\n  ) {\n    adMetrics(\n      where: {\n        gameId: $gameId\n        date: { operator: BETWEEN, values: [$dateFrom, $dateTo] }\n        campaignId: $campaignId\n        groupId: $groupId\n        adId: $adId\n        agencyId: $agencyId\n        countryCode: $countryCode\n        cvr: $cvr\n        cpi: $cpi\n        ctr: $ctr\n        roas: $roas\n        adRevGrossAmount: $adRevGrossAmount\n        adCostNonTaxAmount: $adCostNonTaxAmount\n        impressionCount: $impressionCount\n        clickCount: $clickCount\n        installCount: $installCount\n        cpc: $cpc\n        ipm: $ipm\n      }\n      group: { fields: $groupByFields }\n      preset: { viewPresetId: $preset }\n    ) {\n      collection {\n        ...AdMetricAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query AdMetricsQuery(\n    $gameId: ID!\n    $dateFrom: FilterValue!\n    $dateTo: FilterValue!\n    $groupByFields: [String!]!\n    $campaignId: Filter\n    $agencyId: Filter\n    $groupId: Filter\n    $adId: Filter\n    $countryCode: Filter\n    $cvr: Filter\n    $cpi: Filter\n    $ctr: Filter\n    $roas: Filter\n    $adRevGrossAmount: Filter\n    $adCostNonTaxAmount: Filter\n    $impressionCount: Filter\n    $clickCount: Filter\n    $installCount: Filter\n    $cpc: Filter\n    $ipm: Filter\n    $preset: Int\n  ) {\n    adMetrics(\n      where: {\n        gameId: $gameId\n        date: { operator: BETWEEN, values: [$dateFrom, $dateTo] }\n        campaignId: $campaignId\n        groupId: $groupId\n        adId: $adId\n        agencyId: $agencyId\n        countryCode: $countryCode\n        cvr: $cvr\n        cpi: $cpi\n        ctr: $ctr\n        roas: $roas\n        adRevGrossAmount: $adRevGrossAmount\n        adCostNonTaxAmount: $adCostNonTaxAmount\n        impressionCount: $impressionCount\n        clickCount: $clickCount\n        installCount: $installCount\n        cpc: $cpc\n        ipm: $ipm\n      }\n      group: { fields: $groupByFields }\n      preset: { viewPresetId: $preset }\n    ) {\n      collection {\n        ...AdMetricAttributes\n      }\n      ads {\n        ...AdAttributes\n      }\n      adGroups {\n        ...AdGroupAttributes\n      }\n      campaigns {\n        ...CampaignAttributes\n      }\n      agencies {\n        ...AdAgencyAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetViewPresets($pageId: String!) {\n    viewPresets(where: { pageId: $pageId }) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetViewPresets($pageId: String!) {\n    viewPresets(where: { pageId: $pageId }) {\n      collection {\n        ...ViewPresetAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n"): (typeof documents)["\n  mutation CreateViewPreset($form: CreateViewPresetForm!) {\n    createViewPreset(form: $form) {\n      ...ViewPresetAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteViewPresets($ids: [Int!]!) {\n    deleteViewPresets(where: { ids: $ids })\n  }\n"): (typeof documents)["\n  mutation DeleteViewPresets($ids: [Int!]!) {\n    deleteViewPresets(where: { ids: $ids })\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameMetricShow_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetric\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricShow_GetData(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        id\n        date\n        ...AclGameMetricAttributes\n        ...AclGameMetricMetadataAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n"): (typeof documents)["\n  query GameMetricShow_GetData(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateGte: Date\n    $dateLte: Date\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewBannerImpsDau: Boolean = false\n    $canViewInterImpsDau: Boolean = false\n    $canViewRewardImpsDau: Boolean = false\n    $canViewAoaImpsDau: Boolean = false\n    $canViewMrecImpsDau: Boolean = false\n    $canViewAudioImpsDau: Boolean = false\n    $canViewAoaAdmobImpsDau: Boolean = false\n    $canViewCollapseAdmobImpsDau: Boolean = false\n    $canViewNativeAdmobImpsDau: Boolean = false\n    $canViewAdaptiveAdmobImpsDau: Boolean = false\n    $canViewMrecAdmobImpsDau: Boolean = false\n    $canViewAverageSession: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n  ) {\n    gameMetrics(\n      where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }\n      offset: { page: $page, perPage: $perPage }\n    ) {\n      collection {\n        id\n        date\n        ...AclGameMetricAttributes\n        ...AclGameMetricMetadataAttributes\n      }\n\n      pageInfo {\n        ...PageInfoAttributes\n      }\n    }\n\n    summary: aggregateGameMetric(where: { gameId: $gameId }) {\n      ...AclGameMetricAttributes\n    }\n\n    total: aggregateGameMetric(where: { gameId: $gameId, dateGte: $dateGte, dateLte: $dateLte }) {\n      ...AclGameMetricAttributes\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricIndexV2_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameMetricIndexV2_GetInitData($gameId: ID!) {\n    game(id: $gameId) {\n      ...GameAttributes\n    }\n\n    adTypes {\n      collection {\n        ...AdTypeAttributes\n      }\n    }\n\n    attributes(where: { gameId: $gameId, modelName: \"GameMetricV2\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n\n    gameRevenueAttributes: attributes(where: { gameId: $gameId, modelName: \"GameRevenue\" }) {\n      collection {\n        ...ModelAttribute\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GameMetricIndexV2_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          date\n          gameId\n          ...AclGameMetricV2Attributes\n\n          uaNote @include(if: $canViewUaNote)\n          monetNote @include(if: $canViewMonetNote)\n          productNote @include(if: $canViewProductNote)\n          versionNote @include(if: $canViewVersionNote)\n          note @include(if: $canViewNote)\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query GameMetricIndexV2_GetMetrics(\n    $gameId: ID!\n    $page: Int!\n    $perPage: Int!\n    $dateFrom: Date!\n    $dateTo: Date!\n    $canViewPaidInstalls: Boolean = false\n    $canViewOrganicInstalls: Boolean = false\n    $canViewOrganicPercentage: Boolean = false\n    $canViewTotalInstalls: Boolean = false\n    $canViewCost: Boolean = false\n    $canViewCpi: Boolean = false\n    $canViewRoas: Boolean = false\n    $canViewRevenue: Boolean = false\n    $canViewProfit: Boolean = false\n    $canViewDailyActiveUsers: Boolean = false\n    $canViewRetentionRateDay1: Boolean = false\n    $canViewRetentionRateDay3: Boolean = false\n    $canViewRetentionRateDay7: Boolean = false\n    $canViewSessions: Boolean = false\n    $canViewPlaytime: Boolean = false\n    $canViewUaNote: Boolean = false\n    $canViewMonetNote: Boolean = false\n    $canViewProductNote: Boolean = false\n    $canViewVersionNote: Boolean = false\n    $canViewNote: Boolean = false\n    $canViewGameRevenueRevenue: Boolean = false\n    $canViewGameRevenueArpDau: Boolean = false\n    $canViewGameRevenueEcpm: Boolean = false\n    $canViewGameRevenueImpsDau: Boolean = false\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n        offset: { page: $page, perPage: $perPage }\n      ) {\n        collection {\n          date\n          gameId\n          ...AclGameMetricV2Attributes\n\n          uaNote @include(if: $canViewUaNote)\n          monetNote @include(if: $canViewMonetNote)\n          productNote @include(if: $canViewProductNote)\n          versionNote @include(if: $canViewVersionNote)\n          note @include(if: $canViewNote)\n        }\n\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      summary: aggregateGameMetrics(id: \"summary\", where: { gameId: $gameId }) {\n        ...AclGameMetricV2Attributes\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateTo: $dateTo, dateFrom: $dateFrom }\n      ) {\n        ...AclGameMetricV2Attributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment PartnerGameMetricAttributes on V2GameMetric {\n    date\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n\n    adPerformances {\n      adType\n      impressionCount\n      dailyActiveUserCount\n    }\n  }\n\n  fragment PartnerGameMetricAggregationAttributes on V2GameMetric {\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n  }\n"): (typeof documents)["\n  fragment PartnerGameMetricAttributes on V2GameMetric {\n    date\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n    retentionRateDay1\n    retentionRateDay3\n    retentionRateDay7\n    sessions\n    playtime\n\n    adPerformances {\n      adType\n      impressionCount\n      dailyActiveUserCount\n    }\n  }\n\n  fragment PartnerGameMetricAggregationAttributes on V2GameMetric {\n    totalInstalls\n    cost\n    roas\n    revenue\n    profit\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query PartnerGameMetricIndex_GetInitialData(\n    $gameId: ID!\n    $from: Date!\n    $to: Date!\n    $perPage: Int!\n    $page: Int!\n    $source: DataSource\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        offset: { perPage: $perPage, page: $page }\n        source: $source\n      ) {\n        collection {\n          ...PartnerGameMetricAttributes\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        source: SNAPSHOT\n      ) {\n        ...PartnerGameMetricAggregationAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query PartnerGameMetricIndex_GetInitialData(\n    $gameId: ID!\n    $from: Date!\n    $to: Date!\n    $perPage: Int!\n    $page: Int!\n    $source: DataSource\n  ) {\n    v2 {\n      gameMetrics(\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        offset: { perPage: $perPage, page: $page }\n        source: $source\n      ) {\n        collection {\n          ...PartnerGameMetricAttributes\n        }\n        pageInfo {\n          ...PageInfoAttributes\n        }\n      }\n\n      total: aggregateGameMetrics(\n        id: \"total\"\n        where: { gameId: $gameId, dateFrom: $from, dateTo: $to }\n        source: SNAPSHOT\n      ) {\n        ...PartnerGameMetricAggregationAttributes\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query PartnerGameIndex_GetInitData {\n    games(where: { keyword: \"\" }, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n"): (typeof documents)["\n  query PartnerGameIndex_GetInitData {\n    games(where: { keyword: \"\" }, offset: { page: 1, perPage: 9999 }) {\n      collection {\n        ...GameAttributes\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;