import { FormControl, FormControlProps, FormHelperText, InputLabel } from '@mui/material'
import { ChangeEvent, ReactNode, useState } from 'react'
import {
  Control,
  FieldErrors,
  FieldPath,
  FieldValues,
  useController,
  UseControllerReturn,
} from 'react-hook-form'
import { v4 as uuid } from 'uuid'

import { ExtendProps } from '@/types'

export type RenderableElement<TProps = unknown> =
  | ((props: TProps) => JSX.Element | null)
  | JSX.Element
  | null
  | ReactNode

export function YupFormGroup<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  children,
  fullWidth = false,
  ...props
}: ExtendProps<Omit<FormControlProps, 'children'>, 'fullWidth' | 'error'> & {
  control: Control<TFieldValues>
  name: TName
  children?: RenderableElement<{
    id: string
    error: boolean
    field: UseControllerReturn<TFieldValues, TName>['field']
  }>
  label?: RenderableElement<{ id: string; error: boolean }>
  fullWidth?: boolean
}) {
  const [id] = useState(uuid())
  const controller = useController({ name, control })
  const { errors } = controller.formState

  const isError = Boolean(errors[name])
  const errorMessage = errors[name]?.message as string

  return (
    <FormControl error={isError} fullWidth={fullWidth} {...props}>
      {label && typeof label === 'function' ? (
        label({ id, error: isError })
      ) : (
        <InputLabel htmlFor={id}>{label}</InputLabel>
      )}
      {children && typeof children === 'function'
        ? children({ id, error: isError, field: controller.field })
        : children}
      <FormHelperText>{errorMessage || ''}</FormHelperText>
    </FormControl>
  )
}

export function YupCheckbox<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  render,
  control,
  value,
  name,
}: {
  control: Control<TFieldValues>
  name: TName
  value: string
  render: (props: {
    checked: boolean
    value: string
    onChange: (event: ChangeEvent<HTMLInputElement>) => void
  }) => JSX.Element
}) {
  const { field } = useController({ name: name, control: control })
  const values = field.value as string[]

  const onChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      field.onChange([...values, event.target.value])
    }

    if (!event.target.checked) {
      field.onChange(values.filter((v) => v !== event.target.value))
    }
  }

  return render({ onChange, checked: values.includes(value), value })
}

export function errorsToMessage<T extends FieldValues>(errors: FieldErrors<T>, seperator = ', ') {
  const messages = Object.values(errors)
    .map((e) => e?.message)
    .filter(Boolean)
    .flat()

  if (messages.length === 0) {
    return ''
  }

  return messages.join(seperator)
}
