import {
  DatePickerProps,
  DateRangePicker,
  DateRangePickerProps,
  DatePicker as MuiDatePicker,
  PickersShortcutsItem,
} from '@mui/x-date-pickers-pro'
import dayjs, { Dayjs } from 'dayjs'
import { DateRange as DateRangeType } from '@mui/x-date-pickers-pro/models'

const shortcutsItems: PickersShortcutsItem<DateRangeType<Dayjs>>[] = [
  {
    label: 'This Week',
    getValue: () => {
      const today = dayjs()
      return [today.startOf('week'), today.endOf('week')]
    },
  },
  {
    label: 'Last Week',
    getValue: () => {
      const today = dayjs()
      const prevWeek = today.subtract(7, 'day')
      return [prevWeek.startOf('week'), prevWeek.endOf('week')]
    },
  },
  {
    label: 'Last 7 Days',
    getValue: () => {
      const today = dayjs()
      return [today.subtract(7, 'day'), today]
    },
  },
  {
    label: 'Current Month',
    getValue: () => {
      const today = dayjs()
      return [today.startOf('month'), today.endOf('month')]
    },
  },
  {
    label: 'Last Month',
    getValue: () => {
      const today = dayjs()
      const startOfLastMonth = today.subtract(1, 'month').startOf('month')
      const endOfLastMonth = today.subtract(1, 'month').endOf('month')
      return [startOfLastMonth, endOfLastMonth]
    },
  },
  { label: 'Reset', getValue: () => [null, null] },
]

const shortcutsDatePickerItems: PickersShortcutsItem<Dayjs | null>[] = [
  {
    label: 'Today',
    getValue: () => {
      return dayjs(new Date())
    },
  },
  {
    label: 'Yesterday',
    getValue: () => {
      const date = new Date()
      date.setDate(date.getDate() - 1)
      return dayjs(date)
    },
  },
  {
    label: 'Last Week',
    getValue: () => {
      const date = new Date()
      date.setDate(date.getDate() - 7)
      return dayjs(date)
    },
  },
  {
    label: 'Last 3 day',
    getValue: () => {
      const date = new Date()
      date.setDate(date.getDate() - 3)
      return dayjs(date)
    },
  },
]

export function DateRange<TEnableAccessibleFieldDOMStructure extends boolean = true>({
  slotProps = {},
  ...props
}: DateRangePickerProps<TEnableAccessibleFieldDOMStructure>) {
  return (
    <DateRangePicker
      slotProps={{
        textField: {
          size: 'small',
          sx: {
            '& .MuiInputBase-input': {
              padding: '4px 6px',
            },
          },
        },
        shortcuts: {
          items: shortcutsItems,
        },
        ...slotProps,
      }}
      format="DD-MM-YYYY"
      {...props}
    />
  )
}

export function DatePicker<TEnableAccessibleFieldDOMStructure extends boolean = true>({
  slotProps = {},
  ...props
}: DatePickerProps<TEnableAccessibleFieldDOMStructure>) {
  return (
    <MuiDatePicker
      slotProps={{
        textField: {
          size: 'small',
          sx: {
            '& .MuiInputBase-input': {
              padding: '4px 6px !important',
            },
            '& .MuiIconButton-root': {
              padding: '4px 6px !important',
            },
          },
        },
        shortcuts: { items: shortcutsDatePickerItems },
        ...slotProps,
      }}
      format="DD-MM-YYYY"
      {...props}
    />
  )
}
