import { styled } from '@mui/material/styles'
import { TextField as MuiTextField } from '@mui/material'
import { ChangeEvent, useEffect, useState } from 'react'

const CustomTextField = styled(MuiTextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    '&.Mui-focused fieldset': {
      fontSize: 12,
      borderWidth: '2px',
    },
  },
  '& .MuiInputLabel-root': {
    color: theme.palette.grey[600],
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: theme.palette.primary.main,
  },
  '& .MuiInputBase-input': {
    padding: '4px 6px',
  },
}))

function TextField({
  name,
  value,
  onChange,
  placeholder,
  type = 'number',
}: {
  name: string
  value: string
  onChange: (name: string, value: string) => void
  placeholder: string
  type?: string
}) {
  const [localValue, setLocalValue] = useState(value)

  useEffect(() => {
    setLocalValue(value)
  }, [value])

  return (
    <CustomTextField
      placeholder={placeholder}
      name={name}
      size="small"
      type={type}
      sx={{ width: 100 }}
      value={localValue}
      onChange={(e: ChangeEvent<HTMLInputElement>) => {
        setLocalValue(e.target.value)
      }}
      onBlur={() => {
        onChange(name, localValue)
      }}
    />
  )
}

export default TextField
