import { Autocomplete, AutocompleteProps, ChipTypeMap, TextField } from '@mui/material'

export function StaticAutocomplete<
  Value,
  Multiple extends boolean | undefined,
  DisableClearable extends boolean | undefined,
  FreeSolo extends boolean | undefined,
  ChipComponent extends React.ElementType = ChipTypeMap['defaultComponent'],
>({
  label,
  ...props
}: AutocompleteProps<Value, Multiple, DisableClearable, FreeSolo, ChipComponent> & {
  label: string
}) {
  return (
    <Autocomplete
      {...props}
      size="small"
      disablePortal
      renderInput={(params) => <TextField {...params} />}
    />
  )
}
