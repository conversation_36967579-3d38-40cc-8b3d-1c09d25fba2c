import { FieldValues, UseFormReturn } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

const directions = ['asc', 'desc']

export function useSortOptions() {
  const { t } = useTranslation()

  return directions.map((direction) => ({ label: t(`common.sort.${direction}`), value: direction }))
}

export function useDefaultValues<T extends FieldValues>(form: UseFormReturn<T>) {
  return form.formState.defaultValues as T
}
