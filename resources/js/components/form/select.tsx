import { MenuItem, Select as MuiSelect, SelectProps, styled } from '@mui/material'
import { forwardRef } from 'react'

const CustomSelect = styled(MuiSelect)(() => ({
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderWidth: '2px',
  },
  '& .MuiSelect-select': {
    padding: '4px 6px',
  },
}))

export const Select = forwardRef(function SelectEl(
  {
    fullWidth = true,
    size = 'small',
    options = [],
    allowEmpty = false,
    hint,
    ...props
  }: SelectProps & {
    options: Array<{ value: string; label: string }>
    hint?: string
    allowEmpty?: boolean
  },
  ref
) {
  return (
    <CustomSelect size={size} fullWidth={fullWidth} {...props} ref={ref}>
      {hint && (
        <MenuItem value="" disabled={!allowEmpty}>
          {hint}
        </MenuItem>
      )}
      {options.map((option) => (
        <MenuItem key={option.value} value={option.value}>
          {option.label}
        </MenuItem>
      ))}
    </CustomSelect>
  )
})
