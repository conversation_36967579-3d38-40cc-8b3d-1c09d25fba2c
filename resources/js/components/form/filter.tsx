import { Button, ButtonGroup, ButtonProps, Stack, StackProps } from '@mui/material'
import dayjs from 'dayjs'
import { useForm, UseFormProps } from 'react-hook-form'

import { useParams } from '../location'
import { forwardRef } from 'react'

export function useFilterForm<
  T extends {
    dateRange: [dayjs.Dayjs | null, dayjs.Dayjs | null]
    orderDirection: 'asc' | 'desc'
  },
  TContext = any,
>({ defaultValues, ...formProps }: UseFormProps<T, TContext> = {}) {
  const { from, to, direction } = useParams<{
    from?: string
    to?: string
    direction?: 'asc' | 'desc'
  }>()

  const defaultDateFrom = from ? dayjs(from) : null
  const defaultDateTo = to ? dayjs(to) : null
  const defaultDirection = direction || 'desc'

  const filterForm = useForm<T>({
    ...formProps,
    defaultValues: {
      // @ts-expect-error
      dateRange: [defaultDateFrom, defaultDateTo],
      orderDirection: defaultDirection,
      ...(defaultValues || {}),
    },
  })

  return [
    filterForm,
    { from: defaultDateFrom, to: defaultDateTo, direction: defaultDirection },
  ] as const
}

export const FilterContainer = forwardRef(
  ({ direction = 'row', alignItems = 'flex-end', spacing = 2, ...props }: StackProps, ref) => {
    return (
      <Stack
        direction={direction}
        alignItems={alignItems}
        spacing={spacing}
        {...props}
        ref={ref as any}
      />
    )
  }
)

export function FilterGroup({
  error,
  label,
  alignItems = 'flex-start',
  children,
  ...props
}: StackProps & { label?: string; error?: string }) {
  return (
    <Stack alignItems={alignItems} {...props}>
      {!!label && (
        <Button disabled size="small" sx={{ justifyContent: 'flex-start' }}>
          {label}
        </Button>
      )}
      {children}
      {!!error && <span style={{ fontSize: 11, color: 'red' }}>{error}</span>}
    </Stack>
  )
}

export function FilterActions({
  onSubmit,
  onReset,
  slotProps: { submit: submitProps = {}, reset: resetProps = {} } = {},
}: {
  onSubmit?: () => void
  onReset?: () => void
  slotProps?: {
    submit?: ButtonProps
    reset?: ButtonProps
  }
}) {
  return (
    <ButtonGroup>
      <Button
        sx={{ paddingY: '4px', maxHeight: 31 }}
        variant="contained"
        type="submit"
        onClick={onSubmit}
        {...submitProps}
      >
        Apply
      </Button>

      <Button
        sx={{ paddingY: '4px', maxHeight: 31 }}
        variant="contained"
        type="button"
        color="warning"
        onClick={onReset}
        {...resetProps}
      >
        Reset
      </Button>
    </ButtonGroup>
  )
}
