import { IconButton, IconButtonProps } from '@mui/material'
import TableViewIcon from '@mui/icons-material/TableView'
import BarChartIcon from '@mui/icons-material/BarChart'

import { ExtendProps } from '@/types'

export function ChartViewButton({
  active,
  ...props
}: ExtendProps<IconButtonProps, 'color'> & { active: boolean }) {
  return (
    <IconButton color={active ? 'primary' : 'default'} {...props}>
      <BarChartIcon />
    </IconButton>
  )
}

export function TableViewButton({
  active,
  ...props
}: ExtendProps<IconButtonProps, 'color'> & { active: boolean }) {
  return (
    <IconButton color={active ? 'primary' : 'default'} {...props}>
      <TableViewIcon />
    </IconButton>
  )
}
