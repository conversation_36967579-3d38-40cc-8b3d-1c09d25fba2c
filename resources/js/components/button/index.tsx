import { ReactNode } from 'react'
import clsx from 'clsx'
import { But<PERSON> } from '@mui/material'

const BaseButton = ({
  children,
  loading,
  className,
  variant,
  disabled = false,
  color,
  onClick,
}: {
  children: ReactNode
  loading?: boolean
  variant: 'contained' | 'outlined'
  className?: string
  disabled?: boolean
  color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
  onClick?: () => void
}) => {
  return (
    <Button
      onClick={onClick}
      variant={variant}
      color={color}
      loading={loading}
      disabled={disabled}
      className={clsx(className, '')}
    >
      {children}
    </Button>
  )
}

export default BaseButton
