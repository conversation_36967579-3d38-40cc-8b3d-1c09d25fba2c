import { useCallback, useMemo } from 'react'
import { useServerProp } from '../ssr'

export function useCsrf() {
  const csrfToken = useServerProp((s) => s.csrfToken)
  const token = useMemo(
    () => csrfToken || document.querySelector('input[name="_csrf"]')?.getAttribute('value'),
    [csrfToken]
  )
  const getField = useCallback(() => ({ _csrf: token }), [token])
  return { token, getField }
}

export function Csrf() {
  const { token } = useCsrf()

  return <input type="hidden" name="_csrf" value={token!} />
}
