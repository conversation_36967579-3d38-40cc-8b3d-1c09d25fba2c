import { useState } from 'react'
import {
  DataGridPro,
  DataGridProProps,
  GridActionsColDef,
  GridColDef,
  GridRowId,
  GridRowModes,
  GridRowModesModel,
  GridToolbar,
  GridValidRowModel,
  GridCsvExportOptions,
  GridRowParams,
} from '@mui/x-data-grid-pro'
import { colors } from '@mui/material'

import { PaginationState } from '@/components/pagination/index'
import { useNavigate } from '@/components/ssr'

import { GridCancelAction, GridEditAction, GridSaveAction } from './action'

export function useGridRowModesModel({ fieldToFocus }: { fieldToFocus?: string } = {}) {
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})

  const onEditClick = (id: GridRowId) => () => {
    setRowModesModel({
      ...rowModesModel,
      [id]: { mode: GridRowModes.Edit, fieldToFocus: fieldToFocus },
    })
  }

  const onSaveClick = (id: GridRowId) => () => {
    setRowModesModel({ ...rowModesModel, [id]: { mode: GridRowModes.View } })
  }

  const onCancelClick = (id: GridRowId) => () => {
    setRowModesModel({
      ...rowModesModel,
      [id]: { mode: GridRowModes.View, ignoreModifications: true },
    })
  }

  const onRowModesModelChange = (newRowModesModel: GridRowModesModel) => {
    setRowModesModel(newRowModesModel)
  }

  return { rowModesModel, onEditClick, onSaveClick, onCancelClick, onRowModesModelChange }
}

type PaginationProps = {
  pagination?: PaginationState
  onPageChange?: (page: number) => void
}

export type DataGridProps<T extends GridValidRowModel> = Omit<DataGridProProps<T>, 'pagination'> &
  PaginationProps & {
    editable: boolean
    fieldToFocus?: string
    actionColumn:
      | boolean
      | (Partial<Omit<GridActionsColDef<T>, 'getActions'>> & {
          getActions?: (
            params: GridRowParams<T>,
            handlers: { onSave: any; onCancel: any; onEdit: any }
          ) => any
        })
  }

export function DataGrid<T extends GridValidRowModel>({
  pagination,
  onPageChange,
  slots = {},
  sx = {},
  slotProps: { toolbar: toolbarProps = {}, ...slotProps } = {},
  editable,
  fieldToFocus,
  columns,
  actionColumn = {},
  disableColumnSorting = true,
  disableColumnMenu = true,
  initialState = {},
  showCellVerticalBorder = true,
  ...props
}: DataGridProps<T>) {
  const { rowModesModel, onRowModesModelChange, onCancelClick, onEditClick, onSaveClick } =
    useGridRowModesModel({ fieldToFocus })
  const { navigate } = useNavigate()

  const paginationProps: Partial<DataGridProProps<T>> = pagination
    ? {
        pagination: true,
        paginationModel: {
          page: pagination.page - 1,
          pageSize: pagination.perPage,
        },
        onPaginationModelChange: (paginationModel) => {
          if (onPageChange) {
            return onPageChange(paginationModel.page + 1)
          }

          navigate('', { page: paginationModel.page + 1, perPage: paginationModel.pageSize }, true)
        },
        paginationMode: 'server',
        pageSizeOptions: [pagination.perPage],
        rowCount: pagination.total,
        paginationMeta: {
          hasNextPage: !pagination.isLastPage,
        },
      }
    : {}

  // @ts-ignore
  const tableColumns: GridColDef<T>[] =
    typeof actionColumn === 'boolean'
      ? columns
      : [
          ...columns,
          {
            width: 100,
            headerName: 'Actions',
            field: 'actions',
            type: 'actions',
            ...actionColumn,
            getActions: (row) => {
              const getActions = actionColumn.getActions
              const handlers = {
                onSave: onSaveClick(row.id),
                onEdit: onEditClick(row.id),
                onCancel: onCancelClick(row.id),
              }
              if (getActions) {
                const actions = getActions(row, handlers)

                if (actions) {
                  return actions
                }
              }

              const isInEditMode = rowModesModel[row.id]?.mode === GridRowModes.Edit

              if (isInEditMode) {
                return [
                  <GridSaveAction onClick={handlers.onSave} />,
                  <GridCancelAction onClick={handlers.onCancel} />,
                ]
              }

              return [<GridEditAction onClick={handlers.onEdit} />]
            },
          },
        ]

  return (
    <DataGridPro<T>
      columns={tableColumns}
      // Pagination props
      {...paginationProps}
      //
      // Editable props
      editMode="row"
      rowModesModel={rowModesModel}
      onRowModesModelChange={onRowModesModelChange}
      //
      // Styling props
      showCellVerticalBorder={showCellVerticalBorder}
      disableColumnSorting={disableColumnSorting}
      disableColumnMenu={disableColumnMenu}
      slots={{
        toolbar: GridToolbar,
        ...slots,
      }}
      slotProps={{
        toolbar: {
          csvOptions: {
            escapeFormulas: false,
          } as GridCsvExportOptions,
          ...toolbarProps,
        },
        ...slotProps,
      }}
      initialState={{
        density: 'compact',
        ...initialState,
      }}
      sx={[
        {
          '& .MuiDataGrid-columnHeader': {
            height: 'auto !important',
          },
          '& .MuiDataGrid-columnHeaderTitle': {
            whiteSpace: 'break-spaces',
            textAlign: 'center',
            flex: 1,
            fontWeight: 'bold',
          },
          '& .MuiDataGrid-columnHeaderTitleContainer': {
            flex: 1,
          },
          '& .MuiDataGrid-columnHeaderTitleContainerContent': {
            flex: 1,
          },
          '& .MuiDataGrid-cell': {
            textAlign: 'right',
          },
        },
        // @ts-expect-error lib
        sx,
      ]}
      {...props}
    />
  )
}

export const TREE_GROUPING_COL_DEF = {
  resizable: false,
  width: 1,
  headerName: '',
  valueGetter: () => null,
  hideDescendantCount: true,
}

export const CURRENCY_COLORS = {
  '& .green': {
    bgcolor: colors.green['A400'],
  },
  '& .pink': {
    bgcolor: colors.red['100'],
  },
  '& .orange': {
    bgcolor: colors.orange['A400'],
  },
  '& .cyan': {
    bgcolor: colors.cyan['A400'],
  },
  '& .red': {
    bgcolor: colors.red['A200'],
  },
  '& .yellow': {
    bgcolor: colors.yellow['300'],
  },
}
