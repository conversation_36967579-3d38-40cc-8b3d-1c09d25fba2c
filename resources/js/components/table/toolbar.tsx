import { Box, Button } from '@mui/material'
import {
  GridRowsProp,
  GridToolbar,
  GridToolbarProps,
  GridValidRowModel,
} from '@mui/x-data-grid-pro'
import AddIcon from '@mui/icons-material/Add'
import { useGridApiRef } from '@mui/x-data-grid-pro'

export function makeGridToolbar<T extends GridValidRowModel>({
  makeRow,
  getRowId,
  fieldToFocus,
}: {
  makeRow: () => T
  getRowId: (row: T) => any
  fieldToFocus?: string
}) {
  return ({
    setRows,
    apiRef,
    ...props
  }: GridToolbarProps & {
    setRows: (newRows: (oldRows: GridRowsProp) => GridRowsProp) => void
    apiRef: ReturnType<typeof useGridApiRef>
  }) => {
    const onAddClick = () => {
      const row = makeRow()
      const id = getRowId(row)
      setRows((oldRows) => [row, ...oldRows])
      apiRef.current.startRowEditMode({
        id,
        fieldToFocus,
      })
    }

    return (
      <Box>
        <GridToolbar {...props} />
        <Button startIcon={<AddIcon />} onClick={onAddClick}>
          Add record
        </Button>
      </Box>
    )
  }
}
