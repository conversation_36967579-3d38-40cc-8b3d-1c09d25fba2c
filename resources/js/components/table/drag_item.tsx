import { groupItem } from '@/pages/dashboard/games/campaign_metrics/index/_type'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import { Checkbox, FormControlLabel } from '@mui/material'

const SortableItem = ({
  id,
  checked,
  setChecked,
}: {
  id: string
  checked: boolean
  setChecked: (value: groupItem) => void
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: '2px 8px',
    cursor: 'move',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    marginBottom: '4px',
    backgroundColor: 'white',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  }

  // Xử lý khi checkbox được click
  const handleChange = (event: React.SyntheticEvent, checked: boolean) => {
    // Ngăn sự kiện lan truyền để tránh xung đột với drag
    event.stopPropagation()

    // Cập nhật trạng thái
    setChecked({
      title: id,
      isShow: checked,
      id,
    })
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <div
          {...listeners}
          style={{ cursor: 'grab', marginRight: '4px', display: 'flex', alignItems: 'center' }}
        >
          <DragIndicatorIcon
            style={{
              fontSize: '20px',
              color: '#666',
            }}
          />
        </div>

        <FormControlLabel
          control={<Checkbox checked={checked} onChange={handleChange} size="small" />}
          label={id}
          style={{
            margin: 0,
            flexGrow: 1,
          }}
        />
      </div>
    </div>
  )
}

export default SortableItem
