import { <PERSON><PERSON>, Popover } from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import DataTreeControl from './data_tree_control'
import { groupItem } from '@/pages/dashboard/games/campaign_metrics/index/_type'
import { useApolloClient } from '@apollo/client'
import {
  addAgencyNameToMetrics,
  adMetricsQuery,
  formatUSNumber,
  getOperator,
  useParam,
  useParams,
} from '@/pages/dashboard/games/campaign_metrics/index/_util'
import {
  DataGridPro,
  GridColDef,
  GridDataSource,
  useGridApiRef,
  GridSortModel,
  GRID_TREE_DATA_GROUPING_FIELD,
} from '@mui/x-data-grid-pro'
import FileDownloadIcon from '@mui/icons-material/FileDownload'
import SettingsIcon from '@mui/icons-material/Settings'
import { useTranslation } from 'react-i18next'
import ReactCountryFlag from 'react-country-flag'

const GroupDataTree = ({ tableColumns }: { tableColumns: string[] }) => {
  const apolloClient = useApolloClient()
  const apiRef = useGridApiRef()
  const { gameId } = useParams()
  const dateFrom = useParam((p) => p.from)
  const dateTo = useParam((p) => p.to)
  const filter = useParam((p) => p.filter)
  const preset = useParam((p) => p.query_id)
  const { t } = useTranslation()

  const filterList = useMemo(() => {
    return filter?.map((item) => JSON.parse(item ?? ''))
  }, [filter])

  const groups: groupItem[] = [
    { title: 'Agency', isShow: true, id: 'agencyId' },
    { title: 'Campaign', isShow: true, id: 'campaignId' },
    { title: 'AdGroup', isShow: true, id: 'groupId' },
    { title: 'Ad', isShow: true, id: 'adId' },
    { title: 'Country Code', isShow: true, id: 'countryCode' },
    { title: 'Date', isShow: true, id: 'date' },
  ]

  const [items, setItems] = useState<groupItem[]>(groups)
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
  const [loadingData, setLoadingData] = useState(true)
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>({})
  const [sortModel, setSortModel] = useState<GridSortModel>([])

  const open = Boolean(anchorEl)
  const id = open ? 'simple-popover' : undefined

  const currencyFormatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 3,
  })

  const percentageFormatter = new Intl.NumberFormat('en-US', {
    style: 'percent',
  })

  const cols: GridColDef[] = tableColumns.map((col) => {
    let baseFieldName = col
    let cohortDay = ''
    if (
      col.match(
        /^(activeUserNthDayCounts|adRevNthDayGrossAmounts|retentionNthDayRates|roasNthDayRates|sessionNthDayCounts|activeUserNthDayCountsD|adRevNthDayGrossAmountsD|retentionNthDayRatesD|roasNthDayRatesD|sessionNthDayCountsD)\d+$/
      )
    ) {
      const match = col.match(/^(.+?)(D\d+)$/)
      if (match) {
        baseFieldName = match[1]
        cohortDay = match[2]
      }
    }

    // Get translation for the field name
    let translatedName = t(`campaignMetrics.${baseFieldName}`, baseFieldName)

    // Add cohort day to the header if present
    let headerName = translatedName
    if (cohortDay) {
      // Extract the day number from the cohortDay (e.g., 'D7' -> '7')
      const dayNumber = cohortDay.replace('D', '')
      // Replace <n> with the actual day number
      headerName = translatedName.replace('<n>', dayNumber)
    }

    return {
      field: col,
      headerName: headerName,
      type: 'number',
      minWidth: 60,
      resizable: true,
      valueFormatter: (value: number) => {
        // Currency format for Amount fields
        if (col?.includes('Amount')) return currencyFormatter.format(value ?? 0)

        // Percentage format for ROAS, RR (retentionRate), CVR, CTR
        if (
          col === 'roas' ||
          col === 'retentionRate' ||
          col === 'ctr' ||
          col === 'cvr' ||
          col?.includes('roasNthDayRates') ||
          col?.includes('retentionNthDayRates') ||
          col?.includes('roasNthDayRatesD') ||
          col?.includes('retentionNthDayRatesD')
        ) {
          return percentageFormatter.format(value ?? 0)
        }

        // No units for IPM
        if (col === 'ipm') {
          return formatUSNumber(value ?? 0)
        }
        if (col?.includes('Count')) return formatUSNumber(value ?? 0)

        return currencyFormatter.format(value ?? 0)
      },
    }
  })

  const columns: GridColDef[] = [...cols]

  // Initialize column visibility after columns are defined
  useEffect(() => {
    // Set all metric columns to visible by default
    const initialVisibility: Record<string, boolean> = {}

    // Dimension columns are always hidden
    initialVisibility.agency = false
    initialVisibility.campaign = false
    initialVisibility.adGroup = false
    initialVisibility.ad = false

    // All other columns are visible by default
    columns.forEach((column) => {
      if (!['agency', 'campaign', 'adGroup', 'ad'].includes(column.field)) {
        initialVisibility[column.field] = true
      }
    })

    setVisibleColumns(initialVisibility)
  }, [])

  useEffect(() => {
    apiRef.current.unstable_dataSource.cache.clear()
  }, [apiRef])

  const dataSource = useMemo<GridDataSource>(
    () => ({
      getRows: async (params) => {
        const { groupKeys } = params

        const visibleItems = items.filter((item) => item.isShow)
        const dimensions = visibleItems.map((item) => item.id)

        const groupByFields = dimensions.slice(0, groupKeys!.length + 1)

        const dimensionMap: Record<string, number> = {}

        visibleItems.forEach((item, index) => {
          dimensionMap[item.id] = index
        })

        const metricFilter = filterList?.reduce((acc, item) => {
          acc[item.metric] = !!item.metric
            ? { operator: item.filterOperator, values: [item.min, item.max] }
            : undefined
          return acc
        }, {})

        const queryVariables: any = {
          gameId,
          dateFrom: dateFrom?.format('YYYY-MM-DD'),
          dateTo: dateTo?.format('YYYY-MM-DD'),
          groupByFields,
          preset: !!preset ? preset : undefined,
          ...metricFilter,
          sortField: sortModel[0]?.field ?? undefined,
          sortDirection: sortModel[0]?.sort?.toUpperCase() ?? undefined,
        }

        // Build query filters based on expanded items
        if (groupKeys && groupKeys.length > 0) {
          const expandedFilters: Record<string, any> = {}

          // Get the cached data for the parent level
          const parentKeys = [...groupKeys]
          parentKeys.pop() // Remove the last key to get parent level
          const parentCache = apiRef.current.unstable_dataSource.cache.get({
            ...params,
            groupKeys: parentKeys,
          })?.rows

          // Find the expanding item from parent cache
          const expandingItem = parentCache?.find((row) => {
            for (let i = 0; i < groupKeys.length; i++) {
              const dimId = visibleItems[i].id
              if (dimId === 'countryCode') {
                // Handle special case for N/A (countryCode: "__")
                if (groupKeys[i] === 'N/A') {
                  if (row.countryCode !== '__') return false
                } else {
                  // Normal country code case
                  const countryCodeValue = groupKeys[i].replace('country-', '')
                  if (row.countryCode !== countryCodeValue) return false
                }
              } else {
                // Compare by name in the tree display
                if (row.itemName[dimId] !== groupKeys[i]) return false
              }
            }
            return true
          })

          // Build filters for each expanded level
          for (let i = 0; i < groupKeys.length; i++) {
            const dimension = visibleItems[i]
            if (dimension.id === 'countryCode') {
              // Handle special case for N/A (countryCode: "__")
              if (groupKeys[i] === 'N/A') {
                expandedFilters[dimension.id] = getOperator('__')
              } else {
                // Normal country code case
                const countryCodeFilter = groupKeys[i].replace('country-', '')
                expandedFilters[dimension.id] = getOperator(countryCodeFilter)
              }
            } else if (dimension.id === 'date') {
              expandedFilters[dimension.id] = getOperator(expandingItem?.date)
            } else {
              // Use the actual ID instead of the name for the query
              expandedFilters[dimension.id] = getOperator(expandingItem?.[dimension.id])
            }
          }

          // Apply all filters to query variables
          Object.assign(queryVariables, expandedFilters)
        }

        const response = await apolloClient.query({
          query: adMetricsQuery,
          variables: queryVariables,
        })

        console.log(response)

        setLoadingData(response.loading)

        const rows = addAgencyNameToMetrics(
          response.data.adMetrics.agencies,
          response.data.adMetrics.campaigns,
          response.data.adMetrics.adGroups,
          response.data.adMetrics.ads,
          response.data.adMetrics.collection
        )

        // Cache the data with the exact groupKeys used
        apiRef.current.unstable_dataSource.cache.set(
          { ...params },
          {
            rows,
            rowCount: response.data.adMetrics.collection?.length,
          }
        )

        if (sortModel.length > 0 && sortModel[0]) {
          const { field, sort } = sortModel[0]
          rows.sort((a, b) => {
            const aValue = a[field as keyof typeof a]
            const bValue = b[field as keyof typeof b]
            const modifier = sort === 'asc' ? 1 : -1

            if (typeof aValue === 'number' && typeof bValue === 'number') {
              return (aValue - bValue) * modifier
            }
            return String(aValue ?? '').localeCompare(String(bValue ?? '')) * modifier
          })
        }

        return {
          rows,
          rowCount: response.data.adMetrics.collection?.length,
        }
      },

      getGroupKey: (row) => {
        // Dynamically handle hierarchy based on dimension order in items array
        const visibleItems = items.filter((item) => item.isShow)

        // Find the first dimension that doesn't have a value in the row
        for (let i = 0; i < visibleItems?.length - 1; i++) {
          const currentDim = visibleItems[i].id
          const nextDim = visibleItems[i + 1].id

          if (!row[nextDim]) {
            switch (currentDim) {
              case 'agencyId':
                return row.itemName.agencyId
              case 'campaignId':
                return row.itemName.campaignId
              case 'groupId':
                return row.itemName.groupId
              case 'adId':
                return row.itemName.adId
              case 'countryCode':
                return row.countryCode === '__' ? 'N/A' : `country-${row.countryCode}`
              case 'date':
                return row.date
              default:
                return row[currentDim]
            }
          }
        }

        // For the last dimension in the hierarchy
        const lastDim = visibleItems[visibleItems?.length - 1].id
        switch (lastDim) {
          case 'agencyId':
            return row.itemName.agencyId
          case 'campaignId':
            return row.itemName.campaignId
          case 'groupId':
            return row.itemName.groupId
          case 'adId':
            return row.itemName.adId
          case 'countryCode':
            return row.countryCode === '__' ? 'N/A' : `country-${row.countryCode}`
          case 'date':
            return row.date
          default:
            return row[lastDim]
        }
      },

      getChildrenCount: (row) => {
        if (row.date && !row.hour) {
          return 0
        }
        return 1
      },
    }),
    [apolloClient, dateFrom, dateTo, items, filterList, preset, sortModel]
  )

  const handleExportCsv = () => {
    apiRef.current.exportDataAsCsv()
  }

  return (
    <>
      <div style={{ position: 'relative', marginBottom: '8px' }}>
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <Button onClick={() => handleExportCsv()} sx={{ padding: '4px 6px', minWidth: 'auto' }}>
            <FileDownloadIcon />
          </Button>
          <Button
            sx={{ padding: '4px 6px', minWidth: 'auto' }}
            style={{ cursor: 'pointer', border: 'none', background: 'none' }}
            onClick={(event) => setAnchorEl(event.currentTarget)}
          >
            <SettingsIcon style={{ fontSize: 20 }} />
          </Button>
        </div>
        <Popover
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={() => setAnchorEl(null)}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <div style={{ padding: '6px' }}>
            <DataTreeControl
              items={items}
              setItems={setItems}
              columns={columns}
              visibleColumns={visibleColumns}
              setVisibleColumns={setVisibleColumns}
            />
          </div>
        </Popover>
      </div>
      <DataGridPro
        loading={loadingData}
        apiRef={apiRef}
        columns={columns.map((col) => ({
          ...col,
          sortable: true,
          resizable: true,
          disableReorder: true,
        }))}
        unstable_dataSource={dataSource}
        treeData
        disableColumnMenu
        disableColumnResize={false}
        disableVirtualization={true}
        getRowId={(r) =>
          [r.agencyId, r.campaignId, r.groupId, r.adId, r.date, r.countryCode].join('')
        }
        sortingMode="server"
        onSortModelChange={(model: GridSortModel) => {
          setSortModel(model)
          apiRef.current.unstable_dataSource.cache.clear()
        }}
        initialState={{
          columns: {
            columnVisibilityModel: {
              campaign: false,
              ad: false,
              adGroup: false,
              agency: false,
            },
          },
          sorting: {
            sortModel: [{ field: columns[0]?.field || '', sort: 'desc' }],
          },
        }}
        columnVisibilityModel={{
          campaign: false,
          ad: false,
          adGroup: false,
          agency: false,
          ...visibleColumns,
        }}
        onColumnVisibilityModelChange={(newModel) => {
          // Maintain the state of dimension columns
          const updatedModel = { ...visibleColumns }

          // Only update metric columns
          Object.keys(newModel).forEach((field) => {
            if (!['agency', 'campaign', 'adGroup', 'ad'].includes(field)) {
              updatedModel[field] = newModel[field]
            }
          })

          // Ensure dimension columns are always hidden
          updatedModel.agency = false
          updatedModel.campaign = false
          updatedModel.adGroup = false
          updatedModel.ad = false

          setVisibleColumns(updatedModel)
        }}
        pinnedColumns={{
          left: [GRID_TREE_DATA_GROUPING_FIELD],
        }}
        groupingColDef={{
          // flex: 1,
          minWidth: 300,
          headerAlign: 'center',
          hideDescendantCount: true,
          description: items
            .filter((item) => item.isShow)
            .map((item) => item.title)
            .join(', '),
          valueFormatter: (value: string) => {
            const isCountry = value?.includes('country')
            const flag = value.split('-')?.[1]
            if (isCountry) {
              return (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <ReactCountryFlag
                    countryCode={flag}
                    svg
                    style={{
                      width: '24px',
                      height: '24px',
                    }}
                  />
                  <span>{`${t(`campaignMetrics.countryCode.${flag}`)} (${flag})`}</span>
                </div>
              )
            }
            return value
          },
        }}
        sx={{
          '& .MuiDataGrid-cell': {
            borderRight: '1px solid #e0e0e0',
            borderBottom: '1px solid #e0e0e0',
            borderCollapse: 'collapse',
            padding: '0px 6px',
            maxHeight: '35px',
            lineHeight: '35px',
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: '#f5f5f5',
            fontSize: '14px',
            maxHeight: '40px',
          },
          '& .MuiDataGrid-columnHeader': {
            borderRight: '1px solid #e0e0e0',
            padding: '0px 6px',
            maxHeight: '40px',
          },
          '& .MuiDataGrid-row': { minHeight: '35px !important', height: '35px' },
          '& .MuiDataGrid-row--firstVisible': { minHeight: '35px !important', height: '35px' },
        }}
      />
    </>
  )
}

export default GroupDataTree
