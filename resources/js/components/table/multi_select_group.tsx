import React, { useState, useEffect } from 'react'
import { Box, Button, Typography } from '@mui/material'
import { useTranslation } from 'react-i18next'

export interface MultiSelectButtonGroupProps {
  title?: string
  options: string[]
  defaultSelected?: string[]
  onChange?: (selected: string[]) => void
}

const MultiSelectButtonGroup: React.FC<MultiSelectButtonGroupProps> = ({
  title,
  options,
  defaultSelected = [],
  onChange,
}) => {
  const { t } = useTranslation()
  const [selected, setSelected] = useState<string[]>(defaultSelected)

  useEffect(() => {
    onChange?.(selected)
  }, [selected, onChange])

  const toggle = (value: string) => {
    setSelected((prev) =>
      prev.includes(value) ? prev.filter((v) => v !== value) : [...prev, value]
    )
  }

  return (
    <Box>
      {title && (
        <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
          {title}
        </Typography>
      )}
      <Box display="flex" flexWrap="wrap" gap={1}>
        {options.map((option) => {
          const isActive = selected.includes(option)
          return (
            <Button
              key={option}
              variant={isActive ? 'contained' : 'outlined'}
              onClick={() => toggle(option)}
              sx={{
                fontWeight: 'normal',
                textTransform: 'none',
                minWidth: 100,
                height: 30,
                borderRadius: 2,
                backgroundColor: isActive ? '#2979ff' : 'transparent',
                color: isActive ? '#fff' : 'black',
                borderColor: isActive ? '#2979ff' : '#ccc',
              }}
            >
              {t(`campaignMetrics.${option}`, option)}
            </Button>
          )
        })}
      </Box>
    </Box>
  )
}

export default MultiSelectButtonGroup
