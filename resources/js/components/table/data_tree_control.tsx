import { DndContext, closestCenter } from '@dnd-kit/core'
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import SortableItem from './drag_item'
import { useCallback, useMemo } from 'react'
import { groupItem } from '@/pages/dashboard/games/campaign_metrics/index/_type'
import { Box, Divider, Paper, Typography } from '@mui/material'
import ColumnVisibilityControl from './column_visibility_control'
import { GridColDef } from '@mui/x-data-grid-pro'

const FIXED_GROUP_IDS = ['campaignId', 'groupId', 'adId']

const DataTreeControl = ({
  items,
  setItems,
  columns,
  visibleColumns,
  setVisibleColumns,
}: {
  items: any[]
  setItems: (values: groupItem[]) => void
  columns: GridColDef[]
  visibleColumns: Record<string, boolean>
  setVisibleColumns: (columns: Record<string, boolean>) => void
}) => {
  const controlItems = useMemo(() => {
    return items.map((item) => item.title)
  }, [items])

  const titleToIdMap = useMemo(() => {
    const map: Record<string, string> = {}
    items.forEach((item) => {
      map[item.title] = item.id
    })
    return map
  }, [items])

  /**
   * Get indices of visible fixed group items (isShow = true)
   */
  const getVisibleFixedGroupIndices = () => {
    const indices: number[] = []
    items.forEach((item, index) => {
      if (FIXED_GROUP_IDS.includes(item.id) && item.isShow) {
        indices.push(index)
      }
    })
    return indices.sort((a, b) => a - b)
  }

  /**
   * Get indices of all fixed group items regardless of isShow status
   */
  const getAllFixedGroupIndices = () => {
    const indices: number[] = []
    items.forEach((item, index) => {
      if (FIXED_GROUP_IDS.includes(item.id)) {
        indices.push(index)
      }
    })
    return indices.sort((a, b) => a - b)
  }

  /**
   * Check if an item belongs to the fixed group
   */
  const isItemInFixedGroup = (itemId: string): boolean => {
    return FIXED_GROUP_IDS.includes(itemId)
  }

  /**
   * Handle moving the entire fixed group
   */
  const handleMoveFixedGroup = (
    oldIndex: number,
    newIndex: number,
    visibleFixedIndices: number[]
  ) => {
    // Create a new array without fixed group items
    let newItems = [...items]
    const fixedItems: groupItem[] = []

    // Extract fixed items and remove them from the array
    ;[...visibleFixedIndices].reverse().forEach((index) => {
      fixedItems.unshift(newItems[index])
      newItems.splice(index, 1)
    })

    // Calculate insertion position
    const moveDistance = newIndex - oldIndex
    let targetIndex

    if (moveDistance > 0) {
      // Moving down
      targetIndex = Math.min(
        newItems.length,
        Math.max(0, newIndex - visibleFixedIndices.filter((idx) => idx < oldIndex).length)
      )
    } else {
      // Moving up
      targetIndex = Math.max(0, Math.min(newItems.length, newIndex))
    }

    // Insert fixed items at the new position in their original order
    fixedItems.forEach((item, i) => {
      newItems.splice(targetIndex + i, 0, item)
    })

    return newItems
  }

  /**
   * Handle moving an item around the fixed group
   */
  const handleMoveAroundFixedGroup = (
    oldIndex: number,
    firstFixedIndex: number,
    lastFixedIndex: number
  ) => {
    let newItemArray = [...items]
    const movedItem = newItemArray.splice(oldIndex, 1)[0]

    // Determine where to place the item
    let insertPosition
    if (oldIndex < firstFixedIndex) {
      // Moving from above - place before the group
      insertPosition = firstFixedIndex - 1
    } else {
      // Moving from below - place after the group
      insertPosition = lastFixedIndex + 1
    }

    // Ensure valid insertion position
    insertPosition = Math.max(0, Math.min(insertPosition, newItemArray.length))
    newItemArray.splice(insertPosition, 0, movedItem)

    return newItemArray
  }

  /**
   * Handle drag end event
   */
  const handleDragEnd = (event: any) => {
    const { active, over } = event

    // Only process when dragging to a different position
    if (active.id === over.id) return

    const oldIndex = controlItems.indexOf(active.id)
    const newIndex = controlItems.indexOf(over.id)

    // Get information about dragged item and target
    const activeItemId = titleToIdMap[active.id]
    const overItemId = titleToIdMap[over.id]

    // Check if dragged item and target belong to fixed group
    const isFixedGroupItem = isItemInFixedGroup(activeItemId)
    const isTargetFixedGroupItem = isItemInFixedGroup(overItemId)

    // Get indices of all fixed group items
    const allFixedIndices = getAllFixedGroupIndices()

    // Get indices of visible fixed group items
    const visibleFixedIndices = getVisibleFixedGroupIndices()

    // Handle normal movement if no fixed group items exist
    if (allFixedIndices.length === 0) {
      setItems(arrayMove(items, oldIndex, newIndex))
      return
    }

    // Determine fixed group boundaries
    const firstFixedIndex = allFixedIndices[0]
    const lastFixedIndex = allFixedIndices[allFixedIndices.length - 1]

    // Handle different drag scenarios
    if (isFixedGroupItem) {
      // Case 1: Dragging a fixed group item - move the entire group
      const newItems = handleMoveFixedGroup(oldIndex, newIndex, visibleFixedIndices)
      setItems(newItems)
    } else if (newIndex >= firstFixedIndex && newIndex <= lastFixedIndex) {
      // Case 2: Dragging a non-fixed item into the fixed group
      // Place it before or after the group
      const newItems = handleMoveAroundFixedGroup(oldIndex, firstFixedIndex, lastFixedIndex)
      setItems(newItems)
    } else if (!isFixedGroupItem && isTargetFixedGroupItem) {
      // Case 3: Dragging a non-fixed item directly before a fixed group item
      // Place it before the entire fixed group
      let newItemArray = [...items]
      const movedItem = newItemArray.splice(oldIndex, 1)[0]
      newItemArray.splice(firstFixedIndex, 0, movedItem)
      setItems(newItemArray)
    } else {
      // Case 4: Other safe drag operations
      setItems(arrayMove(items, oldIndex, newIndex))
    }
  }

  /**
   * Handle item check/uncheck event
   */
  const handlecheckItem = (checkingItem: groupItem) => {
    // Find item in the list
    const checkingitemIndex = items.findIndex(
      (item: groupItem) => item.title === checkingItem.title
    )

    if (checkingitemIndex === -1) {
      console.warn('Item not found with title:', checkingItem.title)
      return
    }

    const newItems = [...items]
    const isFixedGroupItem = FIXED_GROUP_IDS.includes(checkingItem.id)

    // Update current item state
    newItems[checkingitemIndex] = {
      ...newItems[checkingitemIndex],
      isShow: checkingItem.isShow,
    }

    // If item is in fixed group and being turned off, update other items in group
    if (isFixedGroupItem && !checkingItem.isShow) {
      // Turn off all other items in fixed group
      newItems.forEach((item, index) => {
        if (FIXED_GROUP_IDS.includes(item.id) && index !== checkingitemIndex) {
          newItems[index] = { ...item, isShow: false }
        }
      })
    }

    setItems(newItems)
  }

  const isItemChecked = useCallback(
    (name: string) => {
      const item = items.find((item: groupItem) => item.title === name)
      return item?.isShow || false
    },
    [items]
  )

  return (
    <Paper
      elevation={0}
      sx={{ p: 2, maxHeight: 500, overflowY: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}
    >
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500 }}>
        Dimensions
      </Typography>
      <Box>
        <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={controlItems} strategy={verticalListSortingStrategy}>
            {controlItems.map((item) => (
              <SortableItem
                setChecked={(value) => handlecheckItem(value)}
                checked={isItemChecked(item)}
                key={item}
                id={item}
              />
            ))}
          </SortableContext>
        </DndContext>
      </Box>

      <Divider sx={{ my: 2 }} />

      <ColumnVisibilityControl
        columns={columns}
        visibleColumns={visibleColumns}
        setVisibleColumns={setVisibleColumns}
      />
    </Paper>
  )
}

export default DataTreeControl
