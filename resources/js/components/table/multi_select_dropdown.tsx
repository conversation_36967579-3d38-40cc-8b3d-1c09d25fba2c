import React from 'react'
import {
  Box,
  FormControl,
  MenuItem,
  Select,
  OutlinedInput,
  Checkbox,
  ListItemText,
  SelectChangeEvent,
  styled,
} from '@mui/material'
import { useTranslation } from 'react-i18next'

interface MultiSelectDropdownProps {
  label: string
  placeholder?: string
  options: string[]
  value: string[]
  onChange: (selected: string[]) => void
  labelWidth?: number | string
  selectWidth?: number | string
}

// Styled components for consistent layout
const FlexContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  flexWrap: 'wrap',
  gap: 2,
  padding: '2px 0',
}))

const LabelContainer = styled(Box)(({ theme }) => ({
  minWidth: 120,
  fontSize: '0.85rem',
}))

const SelectContainer = styled(FormControl)(({ theme }) => ({
  minWidth: 140,
}))

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  label,
  placeholder = 'Select Days',
  options,
  value,
  onChange,
  labelWidth = 120,
  selectWidth = 140,
}) => {
  const { t } = useTranslation()
  const handleChange = (event: SelectChangeEvent<string[]>) => {
    const {
      target: { value },
    } = event
    onChange(typeof value === 'string' ? value.split(',') : value)
  }

  return (
    <FlexContainer>
      <LabelContainer style={{ minWidth: labelWidth }}>
        {t(`campaignMetrics.${label}`, label)}:
      </LabelContainer>

      <SelectContainer style={{ minWidth: selectWidth }}>
        <Select
          multiple
          displayEmpty
          value={value}
          onChange={handleChange}
          input={<OutlinedInput />}
          size="small"
          renderValue={(selected) => {
            if (selected.length === 0) {
              return <span style={{ color: '#aaa', fontSize: '0.85rem' }}>{placeholder}</span>
            }
            return <span style={{ fontSize: '0.85rem' }}>{selected.join(', ')}</span>
          }}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 224,
              },
            },
          }}
          sx={{
            '.MuiOutlinedInput-notchedOutline': {
              borderColor: '#d9d9d9',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#4096ff',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#1677ff',
            },
            '.MuiSelect-select': {
              padding: '4px 6px',
              fontSize: '0.85rem',
            },
            'height': '32px',
          }}
        >
          {options.map((option) => (
            <MenuItem key={option} value={option}>
              <Checkbox
                checked={value.indexOf(option) > -1}
                size="small"
                sx={{ padding: 0.5, marginRight: 1 }}
              />
              <ListItemText
                primary={option}
                sx={{ '.MuiTypography-root': { fontSize: '0.85rem' } }}
              />
            </MenuItem>
          ))}
        </Select>
      </SelectContainer>
    </FlexContainer>
  )
}

export default MultiSelectDropdown
