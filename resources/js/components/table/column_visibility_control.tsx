import { Box, Checkbox, FormControlLabel } from '@mui/material'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { useCallback, useMemo } from 'react'
import { GridColDef } from '@mui/x-data-grid-pro'
import { useTranslation } from 'react-i18next'

const DIMENSION_FIELDS = ['agency', 'campaign', 'adGroup', 'ad']

interface ColumnVisibilityControlProps {
  columns: GridColDef[]
  visibleColumns: Record<string, boolean>
  setVisibleColumns: (columns: Record<string, boolean>) => void
}

const ColumnVisibilityControl = ({
  columns,
  visibleColumns,
  setVisibleColumns,
}: ColumnVisibilityControlProps) => {
  const { t } = useTranslation()
  const metricColumns = useMemo(() => {
    return columns
      .filter((column) => !DIMENSION_FIELDS.includes(column.field))
      .sort((a, b) => {
        if (a.headerName && b.headerName) {
          return a.headerName.localeCompare(b.headerName)
        }
        return 0
      })
  }, [columns])

  const handleColumnVisibilityChange = useCallback(
    (field: string, isVisible: boolean) => {
      const updatedColumns = { ...visibleColumns }
      updatedColumns[field] = isVisible

      updatedColumns.agency = false
      updatedColumns.campaign = false
      updatedColumns.adGroup = false
      updatedColumns.ad = false

      setVisibleColumns(updatedColumns)
    },
    [visibleColumns, setVisibleColumns]
  )

  return (
    <Box>
      <Box sx={{ mb: 1 }}>
        <span style={{ fontWeight: 500, fontSize: '0.875rem' }}>
          {t('campaignMetrics.metricColumns', 'Metric Columns')}
        </span>
      </Box>
      {metricColumns.map((column) => (
        <div
          key={column.field}
          style={{
            padding: '2px 8px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            marginBottom: '4px',
            backgroundColor: 'white',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={visibleColumns[column.field] !== false}
                  onChange={(_, checked) => handleColumnVisibilityChange(column.field, checked)}
                  size="small"
                />
              }
              label={column.headerName}
              style={{
                margin: 0,
                flexGrow: 1,
              }}
            />
          </div>
        </div>
      ))}
    </Box>
  )
}

export default ColumnVisibilityControl
