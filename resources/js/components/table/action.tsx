import { GridActionsCellItem, GridActionsCellItemProps } from '@mui/x-data-grid-pro'
import SaveIcon from '@mui/icons-material/Save'
import CancelIcon from '@mui/icons-material/Cancel'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'

import { ExtendProps } from '@/types'

export type GridActionProps = ExtendProps<GridActionsCellItemProps, 'color'>

export function GridAction({ color = 'inherit', ...props }: GridActionProps) {
  return (
    // @ts-ignore
    <GridActionsCellItem color={color} {...props} />
  )
}

export function GridSaveAction({
  label = 'Save',
  icon = <SaveIcon />,
  ...props
}: ExtendProps<GridActionProps, 'label' | 'icon'>) {
  return <GridAction icon={icon} label={label} {...props} />
}

export function GridCancelAction({
  label = 'Cancel',
  icon = <CancelIcon />,
  ...props
}: ExtendProps<GridActionProps, 'label' | 'icon'>) {
  return <GridAction icon={icon} label={label} {...props} />
}

export function GridEditAction({
  icon = <EditIcon />,
  label = 'Edit',
  ...props
}: ExtendProps<GridActionProps, 'label' | 'icon'>) {
  return <GridAction icon={icon} label={label} {...props} />
}

export function GridDeleteAction({
  icon = <DeleteIcon />,
  label = 'Delete',
  ...props
}: ExtendProps<GridActionProps, 'label' | 'icon'>) {
  return <GridAction icon={icon} label={label} {...props} />
}
