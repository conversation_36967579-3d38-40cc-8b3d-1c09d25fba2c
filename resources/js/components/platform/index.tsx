import AndroidIcon from '@mui/icons-material/Android'
import { colors } from '@mui/material'
import AppleIcon from '@mui/icons-material/Apple'

import { GamePlatform } from '#config/enums'

export function Android() {
  return <AndroidIcon sx={{ color: colors.green[500] }} />
}

export function Ios() {
  return <AppleIcon sx={{ color: colors.grey[500] }} />
}

export function Platform({ value }: { value: GamePlatform | string }) {
  switch (value) {
    case GamePlatform.Android:
      return <Android />
    case GamePlatform.iOS:
      return <Ios />
  }
}
