import dayjs from 'dayjs'
import { addMethod, array, DayjsDateSchemaOptions, mixed } from 'yup'

addMethod(
  mixed<dayjs.Dayjs>,
  'paramDate',
  function paramDate({ defaultValue, format = 'YYYY-MM-DD' }: DayjsDateSchemaOptions = {}): any {
    const schema = this.transform((value: string | null | undefined) => {
      if (!value) {
        return defaultValue?.()
      }

      const date = dayjs(value, format)
      return date.isValid() ? date : null
    })

    return defaultValue ? schema.default(defaultValue) : schema
  }
)

addMethod(array, 'paramArray', function paramArray(): any {
  return this.transform((v: any) => (v ? Array(v).flat() : [])).default([])
})

addMethod(mixed<string>, 'stringifyDate', function paramDate(format: string = 'YYYY-MM-DD'): any {
  return this.transform((v: dayjs.Dayjs) => {
    if (!v) {
      return v
    }

    if (!v.isValid || !v?.isValid()) {
      return null
    }

    return v.format(format)
  })
})
