import dayjs from 'dayjs'
import 'yup'

declare module 'yup' {
  interface DayjsDateSchemaOptions {
    format?: string
    defaultValue?: () => dayjs.Dayjs
  }

  interface MixedSchema<TType extends dayjs.Dayjs, TContext, TDefault, TFlags> {
    paramDate(options: DayjsDateSchemaOptions = {}): this
    stringifyDate(format?: string): this
  }

  interface ArraySchema<TIn, TContext, TDefault, TFlags> {
    paramArray(): this
  }
}
