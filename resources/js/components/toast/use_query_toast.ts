import { UseQueryResult } from '@tanstack/react-query'
import { useEffect } from 'react'
import { ToastPosition } from 'react-toastify'

import { useSingleToast } from './use_single_toast.js'

export type UseQueryToastOptions = {
  errorPosition?: ToastPosition
}

export function useQueryToast<T, U extends Error>(
  query: UseQueryResult<T, U>,
  { errorPosition }: UseQueryToastOptions = {}
) {
  const [toast] = useSingleToast()

  useEffect(() => {
    if (query.error) {
      toast(query.error.message, {
        position: errorPosition,
        type: 'error',
      })
    }
  }, [query.error])
}
