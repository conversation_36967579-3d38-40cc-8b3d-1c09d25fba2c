import { useCallback, useRef } from 'react'
import { Id, toast, ToastOptions } from 'react-toastify'
import { v4 as uuid } from 'uuid'

export type Toast = typeof toast

export function useSingleToast(id?: string | null) {
  const toastRef = useRef<Id>(id || uuid())

  const toastFn = useCallback(
    (message: string, options: ToastOptions) => {
      if (toastRef.current) {
        if (toast.isActive(toastRef.current)) {
          toast.update(toastRef.current, {
            ...options,
            render: message,
          })
        } else {
          toast(message, {
            ...options,
            toastId: toastRef.current,
          })
        }
      } else {
        // @ts-ignore
        toastRef.current = toast(message, options)
      }
    },
    [toastRef]
  )

  return [toastFn] as [Toast]
}
