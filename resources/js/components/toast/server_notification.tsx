import { useEffect, useState } from 'react'
import { transmit } from '../toolkit-api/sse'
import { toast } from 'react-toastify'

export function ServerNotification({ userId }: { userId?: string }) {
  const [subscription, setSubscription] = useState<ReturnType<typeof transmit.subscription> | null>(
    null
  )

  useEffect(() => {
    if (subscription) {
      subscription.create()
    }
  })

  useEffect(() => {
    if (!userId) {
      return
    }

    const sub = transmit.subscription(`global/notifications`)

    const stopListening = sub.onMessage(
      (notifications: Array<{ message: string; type: string; code: string }>) => {
        notifications.forEach((notification) => {
          switch (notification.type) {
            case 'success':
              toast.success(notification.message, {
                autoClose: false,
              })
              break

            case 'error':
              toast.error(notification.message, {
                autoClose: false,
              })
              break

            case 'warn':
              toast.warn(notification.message, {
                autoClose: false,
              })
              break

            case 'info':
            default:
              toast.info(notification.message, {
                autoClose: false,
              })
              break
          }
        })
      }
    )

    setSubscription(sub)

    return () => {
      stopListening()
    }
  }, [userId])

  return null
}
