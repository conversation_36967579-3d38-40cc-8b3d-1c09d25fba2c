import { UseMutationResult } from '@tanstack/react-query'
import { useEffect } from 'react'
import { ToastPosition } from 'react-toastify'
import { AxiosError } from 'axios'

import { useSingleToast } from './use_single_toast'

export type UseMutationToastOptions = {
  loadingMessage?: string
  successMessage?: string
  loadingPosition?: ToastPosition
  successPosition?: ToastPosition
  errorPosition?: ToastPosition
}

export function useMutationToast<T, U extends Error, V>(
  mutation: UseMutationResult<T, U, V>,
  {
    loadingMessage = 'Saving...',
    successMessage = 'Saved',
    loadingPosition,
    successPosition,
    errorPosition,
  }: UseMutationToastOptions = {}
) {
  const [toast] = useSingleToast()

  useEffect(() => {
    if (mutation.isPending && loadingMessage) {
      toast(loadingMessage, {
        position: loadingPosition,
        type: 'info',
      })
    }
  }, [mutation.isPending, loadingMessage, loadingPosition])

  useEffect(() => {
    if (mutation.isSuccess && successMessage) {
      toast(successMessage, {
        position: successPosition,
        type: 'success',
      })
    }
  }, [mutation.isSuccess, successMessage, successPosition])

  useEffect(() => {
    if (mutation.error) {
      const error = mutation.error as U | AxiosError

      if (error instanceof AxiosError) {
        const getMessage = () => {
          if (error.response!.status === 422) {
            return (error.response!.data as { errors: { message: string }[] })
              .errors!.map((err) => err.message)
              .join('\n')
          } else if (error.response!.status === 404) {
            return 'Route does not exist. Please report to administrators.'
          } else if ((error.response!.data as any)?.errors) {
            return (error.response!.data as { errors: { message: string }[] })
              .errors!.map((err) => err.message)
              .join('\n')
          }
        }

        toast(getMessage(), {
          position: errorPosition,
          type: 'error',
        })
      } else {
        toast(mutation.error.message, {
          position: errorPosition,
          type: 'error',
        })
      }
    }
  }, [mutation.error, errorPosition])
}
