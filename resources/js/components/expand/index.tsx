import { styled } from '@mui/material/styles'
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp'
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion'
import MuiAccordionSummary, {
  AccordionSummaryProps,
  accordionSummaryClasses,
} from '@mui/material/AccordionSummary'
import Typography from '@mui/material/Typography'
import AccordionDetails from '@mui/material/AccordionDetails'
import { useState } from 'react'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(() => ({
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&::before': {
    display: 'none',
  },
}))

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
    {...props}
  />
))(({ theme }) => ({
  backgroundColor: theme.palette.grey[100],
  marginTop: '4px',
  borderRadius: '4px',
  flexDirection: 'row-reverse',
  [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]: {
    transform: 'rotate(90deg)',
  },
  [`& .${accordionSummaryClasses.content}`]: {
    marginLeft: theme.spacing(1),
  },
}))

export default function CustomizedExpand({
  expandTitle,
  children,
  isLoading = false, // Add isLoading prop with default false
  ...props
}: AccordionProps & {
  children: React.ReactNode
  expandTitle: string
  isLoading?: boolean // Make isLoading optional
}) {
  const [expanded, setExpanded] = useState<boolean>(true)

  const handleChange = (isExpand: boolean) => {
    setExpanded(isExpand)
  }

  return (
    <div>
      <Accordion
        {...props}
        expanded={expanded}
        onChange={() => handleChange(!expanded)}
        // Disable the accordion when loading to prevent user interaction
        disabled={isLoading}
      >
        <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          <Typography component="span">{expandTitle}</Typography>
          {/* Show loading indicator next to title when loading */}
          {isLoading && <CircularProgress size={16} sx={{ ml: 2 }} />}
        </AccordionSummary>
        <AccordionDetails>
          {/* Show loading state or children */}
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
              <CircularProgress />
            </Box>
          ) : (
            children
          )}
        </AccordionDetails>
      </Accordion>
    </div>
  )
}
