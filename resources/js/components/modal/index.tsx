import {
  ElementType,
  HTMLAttributes,
  MouseEvent<PERSON><PERSON>ler,
  use<PERSON><PERSON>back,
  useMemo,
  useState,
} from 'react'
import {
  Box,
  BoxProps,
  Button,
  Divider,
  Modal as MuiModal,
  Typography,
  TypographyProps,
} from '@mui/material'

export type UseModalOptions = {
  opened?: boolean
}

export type ModalController = {
  opened: boolean
  onClose: MouseEventHandler<HTMLButtonElement>
  onOpen: MouseEventHandler<HTMLButtonElement>
}

export function useModal(options: UseModalOptions): ModalController {
  const [opened, setOpened] = useState(options.opened ?? false)

  const onClose = useCallback(() => setOpened(false), [setOpened])

  const onOpen = useCallback(() => setOpened(true), [setOpened])

  const modal = useMemo(
    () => ({
      opened,
      onClose,
      onOpen,
    }),
    [opened, onClose, onOpen]
  )

  return modal
}

export type ModalProps<RootComponent extends ElementType = 'div', AdditionalProps = {}> = BoxProps<
  RootComponent,
  AdditionalProps
> & {
  control: ModalController
  slots?: {
    buttonsLeft?: React.ReactNode
    buttonsRight?: React.ReactNode
  }
}

export function ModalHeader(props: TypographyProps) {
  return <Typography variant="h6" component="h2" p={3} {...props} />
}

export function ModalBody(props: BoxProps) {
  return (
    <>
      <Divider />
      <Box p={3} {...props} />
    </>
  )
}

export function Modal<RootComponent extends ElementType = 'div', AdditionalProps = {}>({
  control,
  children,
  slots: { buttonsLeft, buttonsRight } = {},
  sx,
  ...props
}: ModalProps<RootComponent, AdditionalProps> & HTMLAttributes<HTMLDivElement>) {
  return (
    <MuiModal open={Boolean(control.opened)} onClose={control.onClose}>
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 600,
          bgcolor: 'background.paper',
          boxShadow: 24,
          borderRadius: 2,
          ...sx,
        }}
        {...props}
      >
        {children}

        <Divider />

        <Box
          sx={{
            px: 3,
            py: 2,
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            boxSizing: 'border-box',
          }}
        >
          {buttonsLeft && <Box>{buttonsLeft}</Box>}

          <Box sx={{ display: 'flex', gap: 2, ml: 'auto' }}>
            <Button onClick={control.onClose} variant="outlined">
              Close
            </Button>
            {buttonsRight}
          </Box>
        </Box>
      </Box>
    </MuiModal>
  )
}
