import { HTMLAttributes, useCallback, useState } from 'react'
import classNames from 'classnames'

import { PaginationMeta } from '@/components/toolkit-api/index.js'
import { useParams } from '../location'
import queryString from 'query-string'

export type PaginationState = {
  page: number
  perPage: number
  isLastPage: boolean
  totalPage: number
  isFirstPage: boolean
  toNextPage: () => void
  toPreviousPage: () => void
  toPage: (value: number) => void
  setFromMeta: (meta: PaginationMeta) => void
  total: number
}

type PaginationOverride = {
  perPage?: number
}

/**
 * @deprecated
 */
export function usePagination(overrides: Partial<PaginationOverride> = {}): PaginationState {
  const params = useParams<{ page?: string; perPage?: string }>()

  const page = params.page ? Number(params.page) : 1
  const perPage = Number(params.perPage ?? overrides.perPage ?? 200)

  const [state, setState] = useState({
    page: page,
    totalPage: page,
    perPage,
    total: (page - 1) * perPage + 1,
  })

  const setFromMeta = useCallback(
    (meta: PaginationMeta) => {
      setState({
        page: meta.currentPage,
        totalPage: meta.lastPage,
        perPage: meta.perPage,
        total: meta.total,
      })
    },
    [setState]
  )

  return {
    total: state.total,
    page: state.page,
    isLastPage: state.page === state.totalPage,
    totalPage: state.totalPage,
    perPage: state.perPage,
    isFirstPage: state.page === 1,
    toNextPage: () => {
      window.location.search = queryString.stringify({ ...params, page: state.page + 1 })
    },
    toPreviousPage: () => {
      window.location.search = queryString.stringify({ ...params, page: state.page - 1 })
    },
    toPage: (value: number) => {
      window.location.search = queryString.stringify({ ...params, page: value })
    },
    setFromMeta,
  }
}

export function Pagination({
  control,
  className,
}: { control: PaginationState } & HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={classNames(className, 'join')}>
      <button
        className={classNames('join-item btn', { 'btn-disabled': control.isFirstPage })}
        onClick={() => control.toPreviousPage()}
        disabled={control.isFirstPage}
      >
        «
      </button>
      <button className="join-item btn">Page {control.page}</button>
      <button
        className={classNames('join-item btn', { 'btn-disabled': control.isLastPage })}
        onClick={() => control.toNextPage()}
        disabled={control.isLastPage}
      >
        »
      </button>
    </div>
  )
}
