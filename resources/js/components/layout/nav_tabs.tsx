import Box, { BoxProps } from '@mui/material/Box'
import Tab from '@mui/material/Tab'
import TabContext from '@mui/lab/TabContext'
import TabList from '@mui/lab/TabList'

import { useNavigate } from '@/components/ssr'
import { forwardRef } from 'react'

type NavTabItem = {
  label: string
  href: string
}

export const NavTabs = forwardRef(
  ({ items, sx, ...props }: { items: NavTabItem[] } & BoxProps, ref) => {
    const { navigate } = useNavigate()

    return (
      <TabContext value={items.findIndex((t) => t.href === window.location.pathname).toString()}>
        {/* @ts-ignore sx props */}
        <Box sx={[{ borderBottom: 1, borderColor: 'divider' }, sx]} {...props} ref={ref}>
          <TabList variant="scrollable" orientation="horizontal" onChange={() => {}}>
            {items.map((t, index) => (
              <Tab
                label={t.label}
                href={t.href}
                value={index.toString()}
                key={t.href}
                onClick={() => navigate(t.href)}
                component="a"
              />
            ))}
          </TabList>
        </Box>
      </TabContext>
    )
  }
)
