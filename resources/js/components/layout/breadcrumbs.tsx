import { useEffect } from 'react'
import MuiBreadcrumbs from '@mui/material/Breadcrumbs'
import Typography from '@mui/material/Typography'
import Link from '@mui/material/Link'
import Stack from '@mui/material/Stack'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'

import { useNavigate } from '@/components/ssr'

import { BreadcrumbItem, useLayoutStore } from './store'
import { useLayoutComponentRegistration } from '@/components/layout/size.js'

declare module '@/components/layout/size.js' {
  export interface LayoutComponents {
    breadcrumbs: Size
  }
}

export function useBreadcrumbs(items: BreadcrumbItem[], home: 'default' | 'dashboard' = 'default') {
  const setBreadcrumbs = useLayoutStore((state) => state.setBreadcrumbs)
  const setDashboardHome = useLayoutStore((state) => state.setDashboardHomeBreadcrumb)
  const setDefaultHome = useLayoutStore((state) => state.setDefaultHomeBreadcrumb)

  useEffect(() => {
    if (home === 'dashboard') {
      setDashboardHome()
    } else {
      setDefaultHome()
    }

    return () => {
      setBreadcrumbs([])
      setDefaultHome()
    }
  }, [home, setDashboardHome, setDefaultHome])

  useEffect(() => {
    setBreadcrumbs(items)

    return () => {
      setBreadcrumbs([])
    }
  }, [setBreadcrumbs, items])
}

export function Breadcrumbs() {
  const breadcrumbs = useLayoutStore((state) => state.breadcrumbs)
  const homeBreadcrumb = useLayoutStore((state) => state.homeBreadcrumb)
  const [breadcrumbsRef, setBreadcrumbsSize] = useLayoutComponentRegistration('breadcrumbs')

  const { navigate } = useNavigate()

  useEffect(() => {
    setBreadcrumbsSize({
      height: breadcrumbsRef.current?.clientHeight || 0,
    })
  }, [breadcrumbsRef, setBreadcrumbsSize, breadcrumbs, homeBreadcrumb])

  return (
    <>
      <Stack spacing={2} sx={{ py: 2 }} ref={breadcrumbsRef as any}>
        <MuiBreadcrumbs separator={<NavigateNextIcon fontSize="small" />} aria-label="breadcrumb">
          {[homeBreadcrumb, ...breadcrumbs].map((b) => {
            if (typeof b === 'string') {
              return (
                <Typography key="3" color="text.primary">
                  {b}
                </Typography>
              )
            }

            return (
              <Link
                underline="hover"
                key={b.url}
                color="inherit"
                href="#"
                onClick={() => navigate(b.url)}
              >
                {b.label}
              </Link>
            )
          })}
        </MuiBreadcrumbs>
      </Stack>
    </>
  )
}
