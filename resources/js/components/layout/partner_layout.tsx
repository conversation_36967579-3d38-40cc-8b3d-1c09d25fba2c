import { BoxProps } from '@mui/material'
import QueryStatsIcon from '@mui/icons-material/QueryStats'
import { BaseLayout } from './base_layout'
import { routes, useNavigate } from '../location'

export function PartnerLayout({ children }: BoxProps) {
  const { navigate } = useNavigate()

  return (
    <BaseLayout
      menuGroups={[
        [
          {
            icon: <QueryStatsIcon />,
            label: 'Game Metrics',
            onClick: () => navigate(routes.partner.games.index(), { query: {} }),
            permitted: true,
          },
        ],
      ]}
    >
      {children}
    </BaseLayout>
  )
}
