import React, { <PERSON>Event, useEffect, useState } from 'react'
import { useTheme } from '@mui/material/styles'
import Box, { BoxProps } from '@mui/material/Box'
import Toolbar, { ToolbarProps } from '@mui/material/Toolbar'
import List from '@mui/material/List'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import MenuIcon from '@mui/icons-material/Menu'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { LinearProgress, Menu, MenuItem, Stack, Tooltip, Typography } from '@mui/material'
import AccountCircle from '@mui/icons-material/AccountCircle'
import queryString from 'query-string'
import LogoutIcon from '@mui/icons-material/Logout'
import PersonIcon from '@mui/icons-material/Person'
import ScienceIcon from '@mui/icons-material/Science'

import { useGetProfile } from '@/components/toolkit-api'
import { Csrf } from '@/components/csrf'
import { routes } from '../location'

import { DashboardNotification } from './dashboard_notification'
import { Breadcrumbs } from './breadcrumbs'
import { ServerNotification } from '../toast/server_notification'
import { useLayoutComponentRegistration } from '@/components/layout/size.js'
import { AppBar, Drawer, DrawerHeader, drawerWidth } from './drawer'
import { useUIFeatureStore } from '@/next/stores/use_ui_feature_store'

export type AdminLayoutProps = {
  children: React.ReactNode
  toolbar?: React.ReactNode
  slotProps?: {
    toolbar?: ToolbarProps
    content?: BoxProps
    appBar?: BoxProps
  }
  menuGroups: DrawerItem[][]
}

export type DrawerItem = {
  label: string
  permitted: boolean | null | undefined
  icon: React.ReactNode
  onClick: () => any
}

export function BaseLayout({ children, toolbar, slotProps = {}, menuGroups }: AdminLayoutProps) {
  const theme = useTheme()
  const [open, setOpen] = React.useState(localStorage.getItem('drawer:open') === '1')

  useEffect(() => {
    localStorage.setItem('drawer:open', open ? '1' : '0')
  }, [open])

  const handleDrawerOpen = () => {
    setOpen(true)
  }

  const handleDrawerClose = () => {
    setOpen(false)
  }

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const handleMenu = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }
  const {
    toolbar: toolbarProps = {},
    content: { sx: contentSx = {}, ...contentProps } = {},
    appBar: { children: appBarChildren } = {},
  } = slotProps

  const getProfileQuery = useGetProfile()

  const [appBarRef, setNavbarSize] = useLayoutComponentRegistration('navbar')

  const { toggleExperimentalUI, isExperimentalUIEnabled } = useUIFeatureStore()

  useEffect(() => {
    setNavbarSize({ height: appBarRef.current!.clientHeight })
  }, [appBarRef, setNavbarSize])

  return (
    <Box sx={{ display: 'flex', overflowX: 'hidden' }}>
      <ServerNotification userId={getProfileQuery.data?.profile?.id} />
      <AppBar position="fixed" open={open} ref={appBarRef}>
        <Toolbar {...toolbarProps}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={{
              marginRight: { xs: 1, sm: 5 },
              ...(open && { display: 'none' }),
            }}
          >
            <MenuIcon />
          </IconButton>

          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flex={1}
            spacing={{ xs: 1, sm: 2 }}
          >
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                display: { xs: 'none', sm: 'block' },
              }}
            >
              Mirai Studio Toolkit
            </Typography>
            <Typography
              sx={{
                display: { xs: 'block', sm: 'none' },
                flexGrow: 1,
                flexShrink: 0,
              }}
              variant="h6"
              noWrap
              component="div"
            >
              MST
            </Typography>

            <Stack direction="row" alignItems="center">
              {toolbar}

              <Box>
                <IconButton
                  size="large"
                  aria-label="Menu"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleMenu}
                  color="inherit"
                >
                  <AccountCircle />
                </IconButton>

                <Menu
                  id="menu-appbar"
                  anchorEl={anchorEl}
                  keepMounted
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                  sx={{ width: 300 }}
                >
                  <Box
                    component="form"
                    method="post"
                    action={queryString.stringifyUrl({
                      url: routes.auth.sessions.destroy(),
                      query: {
                        _method: 'delete',
                      },
                    })}
                  >
                    <MenuItem sx={{ width: '100%' }} component="a" href={routes.me.profile.index()}>
                      <ListItemIcon>
                        <PersonIcon />
                      </ListItemIcon>
                      {getProfileQuery.isLoading ? (
                        <LinearProgress />
                      ) : (
                        getProfileQuery.data?.profile?.fullName
                      )}
                    </MenuItem>
                    <MenuItem sx={{ width: '100%' }} onClick={() => toggleExperimentalUI()}>
                      <ListItemIcon>
                        <ScienceIcon />
                      </ListItemIcon>
                      {isExperimentalUIEnabled ? 'Disable' : 'Enable'} Experimental UI
                    </MenuItem>
                    <MenuItem
                      onClick={handleClose}
                      component="button"
                      type="submit"
                      sx={{ width: '100%' }}
                    >
                      <ListItemIcon>
                        <LogoutIcon />
                      </ListItemIcon>
                      Logout
                      <Csrf />
                    </MenuItem>
                  </Box>
                </Menu>
              </Box>
            </Stack>
          </Stack>
        </Toolbar>
        {appBarChildren}
      </AppBar>

      <Drawer variant="permanent" open={open}>
        <DrawerHeader>
          <IconButton onClick={handleDrawerClose}>
            {theme.direction === 'rtl' ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <List>
          {menuGroups
            .filter((menus) => menus.filter((menu) => menu.permitted).length > 0)
            .map((menus, index) => {
              return (
                <List key={index.toString()}>
                  {menus.map((menu) => (
                    <SideMenuItem
                      key={menu.label}
                      hidden={!menu.permitted}
                      open={open}
                      icon={menu.icon}
                      text={menu.label}
                      onClick={menu.onClick}
                    />
                  ))}
                </List>
              )
            })
            .reduce((list, item) => {
              if (list.length === 0) {
                return [item] as any
              }

              return [list, <Divider key={`divider-${list.length}`} />, item] as any
            }, [])}
        </List>
      </Drawer>

      <Box
        component="main"
        sx={Object.assign(
          {
            flexGrow: 1,
            px: 3,
            backgroundColor: 'white',
            minHeight: '100vh',
          },
          open
            ? {
                width: `calc(100% - ${drawerWidth}px)`,
              }
            : { width: `calc(100% - 1px - ${theme.spacing(7)})` },
          contentSx
        )}
        {...contentProps}
      >
        <DrawerHeader />

        <DashboardNotification />

        <Breadcrumbs />

        {children}
      </Box>
    </Box>
  )
}

function SideMenuItem({
  open,
  hidden = false,
  icon,
  text,
  onClick,
}: {
  open: boolean
  hidden?: boolean
  icon: React.ReactNode
  text: string
  onClick: () => void
}) {
  if (hidden) {
    return null
  }

  return (
    <Tooltip title={text} placement="right">
      <ListItem disablePadding sx={{ display: 'block' }} onClick={onClick}>
        <ListItemButton
          sx={{
            minHeight: 48,
            justifyContent: open ? 'initial' : 'center',
            px: 2.5,
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              mr: open ? 3 : 'auto',
              justifyContent: 'center',
            }}
          >
            {icon}
          </ListItemIcon>
          <ListItemText
            primary={text}
            sx={{
              opacity: open ? 1 : 0,
              ...(open ? { whiteSpace: 'normal', wordBreak: 'break-word' } : {}),
            }}
          />
        </ListItemButton>
      </ListItem>
    </Tooltip>
  )
}

declare module '@/components/layout/size.js' {
  export interface LayoutComponents {
    navbar: Size
  }
}
