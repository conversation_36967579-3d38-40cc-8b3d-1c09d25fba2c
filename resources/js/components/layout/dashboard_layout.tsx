import { HTMLAttributes, useCallback, useEffect, useMemo, useState } from 'react'
import { MenuItem, Select, colors, SxProps, Stack } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { sumBy } from 'lodash-es'

import { toolkitApi } from '../toolkit-api/client'
import { formatter } from '#utils/formatter'
import { ReportPresenter } from '#controllers/me/report_presenter'
import { Loading } from '../loading'
import { safeDivide } from '#utils/math'

import { AdminLayout } from './admin_layout'

export function DashboardLayout({ children }: HTMLAttributes<HTMLDivElement>) {
  // const gameFilterForm = useForm<{ keyword: string }>({
  //   defaultValues: {
  //     keyword: '',
  //   },
  // })

  // const gameFilterKeyword = useDebounce(gameFilterForm.watch('keyword'), 500)

  // const listGamesQuery = useQuery({
  //   queryKey: ['dash', 'games', 'list', gameFilterKeyword],
  //   queryFn: async () => {
  //     if (!gameFilterKeyword) {
  //       return []
  //     }

  //     const response = await toolkitApi.listGame({
  //       page: 1,
  //       perPage: 10,
  //       keyword: gameFilterKeyword,
  //     })

  //     return response.data
  //   },
  // })
  // useQueryToast(listGamesQuery)
  // const games = listGamesQuery.data ?? []

  const getReportQuery = useQuery({
    queryKey: ['dash', 'reports'],
    queryFn: () =>
      toolkitApi.me
        .getReport({
          to: DateTime.now().startOf('day').minus({ days: 1 }).toISODate(),
          from: DateTime.now().startOf('day').minus({ days: 30 }).toISODate(),
        })
        .then((r) => r.data),
  })

  const [toolbarStyle, setToolbarStyle] = useState<SxProps>({})

  return (
    <AdminLayout
      slotProps={{
        toolbar: {
          sx: toolbarStyle,
        },
      }}
      toolbar={
        <Stack direction="row" spacing={2}>
          {getReportQuery.isLoading ? (
            <Loading visible />
          ) : (
            <ProfitReport
              report={getReportQuery.data!}
              onProfitChange={(profit) => {
                if (profit >= 0) {
                  setToolbarStyle({})
                } else {
                  setToolbarStyle({ bgcolor: colors.red[400] })
                }
              }}
            />
          )}
          {/* <Box
            sx={{
              width: '100%',
              flex: 1,
              position: 'relative',
              maxWidth: 400,
              minWidth: { xs: 0, sm: 200, md: 300, lg: 400 },
            }}
          >
            <TextField
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              hiddenLabel
              id="filled-hidden-label-small"
              defaultValue="Small"
              variant="filled"
              size="small"
              sx={{ bgcolor: 'background.paper' }}
              fullWidth
              {...gameFilterForm.register('keyword')}
            />
            {games.length > 0 && (
              <List
                sx={{
                  position: 'absolute',
                  top: '100%',
                  left: { xs: 'unset', sm: 0 },
                  right: { xs: '-32px', sm: 'unset' },
                  width: { xs: '80vw', sm: '100%' },
                  zIndex: 1,
                  bgcolor: 'background.paper',
                  borderColor: 'divider',
                  borderWidth: 1,
                  borderStyle: 'solid',
                  color: 'text.primary',
                }}
              >
                {games.map((game) => (
                  <ListItemButton
                    component="a"
                    href={routes.dash.gameMetrics.index(game.storeId)}
                    key={game.storeId}
                  >
                    <ListItemIcon>
                      {game.platform === GamePlatform.Android ? <Android /> : <Ios />}
                    </ListItemIcon>
                    <ListItemText primary={game.name} />
                  </ListItemButton>
                ))}
              </List>
            )}
          </Box> */}
        </Stack>
      }
    >
      {children}
    </AdminLayout>
  )
}

function ProfitReport({
  report,
  onProfitChange,
}: {
  report: ReportPresenter
  onProfitChange: (profit: number) => void
}) {
  const [today] = useState(DateTime.now().startOf('day'))
  const dayNthMetric = useCallback(
    (nth: number) =>
      report.profits.filter((p) => DateTime.fromISO(p.date as any) >= today.minus({ days: nth })),
    [report.profits]
  )
  const profits = useMemo(() => {
    const days = [1, 3, 7, 30]
    return days.map((day) => ({
      nth: day,
      value: safeDivide(
        sumBy(dayNthMetric(day), (m) => m.profit),
        day
      ),
    }))
  }, [dayNthMetric])

  const [nth, setNth] = useState(1)

  useEffect(() => {
    onProfitChange(profits.find((p) => p.nth === nth)?.value || 0)
  }, [nth, profits])

  return (
    <Select
      defaultValue={nth.toString()}
      sx={{
        width: '100%',
        flex: 1,
        maxWidth: 200,
        background: 'white',
        minWidth: { xs: 0, sm: 200 },
      }}
      size="small"
      onChange={(e) => {
        setNth(Number(e.target.value))
      }}
    >
      {profits.map((profit) => (
        <MenuItem key={profit.nth} value={profit.nth.toString()}>
          D{profit.nth}: ${formatter.round(profit.value)}
        </MenuItem>
      ))}
    </Select>
  )
}
