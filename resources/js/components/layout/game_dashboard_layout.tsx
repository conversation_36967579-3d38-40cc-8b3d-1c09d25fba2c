import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { BoxProps } from '@mui/material/Box'
import { ToolbarProps } from '@mui/material/Toolbar'
import MoneyOffIcon from '@mui/icons-material/MoneyOff'
import AttachMoneyIcon from '@mui/icons-material/AttachMoney'
import TrendingDownIcon from '@mui/icons-material/TrendingDown'
import RateReviewIcon from '@mui/icons-material/RateReview'
import CampaignIcon from '@mui/icons-material/Campaign'
import { MenuItem, Select, colors, SxProps, Stack } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { sumBy } from 'lodash-es'
import { toolkitApi } from '../toolkit-api/client'
import { formatter } from '#utils/formatter'
import { ReportPresenter } from '#controllers/me/report_presenter'
import { Loading } from '../loading'
import { safeDivide } from '#utils/math'

import HomeIcon from '@mui/icons-material/Home'
import { routes } from '../location'
import { useNavigate } from '../location'
import { BaseLayout, DrawerItem } from './base_layout'
import TableChartIcon from '@mui/icons-material/TableChart'
import SportsEsportsIcon from '@mui/icons-material/SportsEsports'
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary'
export type GameDashboardLayoutProps = {
  children: React.ReactNode
  gameId: string
  toolbar?: React.ReactNode
  slotProps?: {
    toolbar?: ToolbarProps
    content?: BoxProps
    appBar?: BoxProps
  }
}

function ProfitReport({
  report,
  onProfitChange,
}: {
  report: ReportPresenter
  onProfitChange: (profit: number) => void
}) {
  const [today] = useState(DateTime.now().startOf('day'))
  const dayNthMetric = useCallback(
    (nth: number) =>
      report.profits.filter((p) => DateTime.fromISO(p.date as any) >= today.minus({ days: nth })),
    [report.profits]
  )
  const profits = useMemo(() => {
    const days = [1, 3, 7, 30]
    return days.map((day) => ({
      nth: day,
      value: safeDivide(
        sumBy(dayNthMetric(day), (m) => m.profit),
        day
      ),
    }))
  }, [dayNthMetric])

  const [nth, setNth] = useState(1)

  useEffect(() => {
    onProfitChange(profits.find((p) => p.nth === nth)?.value || 0)
  }, [nth, profits, onProfitChange])

  return (
    <Select
      defaultValue={nth.toString()}
      sx={{
        width: '100%',
        flex: 1,
        maxWidth: 200,
        background: 'white',
        minWidth: { xs: 0, sm: 200 },
      }}
      size="small"
      onChange={(e) => {
        setNth(Number(e.target.value))
      }}
    >
      {profits.map((profit) => (
        <MenuItem key={profit.nth} value={profit.nth.toString()}>
          D{profit.nth}: ${formatter.round(profit.value)}
        </MenuItem>
      ))}
    </Select>
  )
}

export function GameDashboardLayout({
  children,
  gameId,
  toolbar: _toolbar, // Rename to _toolbar to indicate it's not used
  slotProps = {},
}: GameDashboardLayoutProps) {
  const { navigate } = useNavigate()

  // const gameFilterForm = useForm<{ keyword: string }>({
  //   defaultValues: {
  //     keyword: '',
  //   },
  // })

  // const gameFilterKeyword = useDebounce(gameFilterForm.watch('keyword'), 500)

  // const listGamesQuery = useQuery({
  //   queryKey: ['dash', 'games', 'list', gameFilterKeyword],
  //   queryFn: async () => {
  //     if (!gameFilterKeyword) {
  //       return []
  //     }

  //     const response = await toolkitApi.listGame({
  //       page: 1,
  //       perPage: 10,
  //       keyword: gameFilterKeyword,
  //     })

  //     return response.data
  //   },
  // })
  // useQueryToast(listGamesQuery)
  // const games = listGamesQuery.data ?? []

  const getReportQuery = useQuery({
    queryKey: ['dash', 'reports'],
    queryFn: () =>
      toolkitApi.me
        .getReport({
          to: DateTime.now().startOf('day').minus({ days: 1 }).toISODate(),
          from: DateTime.now().startOf('day').minus({ days: 30 }).toISODate(),
        })
        .then((r) => r.data),
  })

  const [profit, setProfit] = useState<number>(0)
  const toolbarStyle = useMemo<SxProps>(() => {
    return profit >= 0 ? {} : { bgcolor: colors.red[400] }
  }, [profit])

  const handleProfitChange = useCallback((newProfit: number) => {
    setProfit(newProfit)
  }, [])

  // Game-specific navigation items for the sidebar
  const gameNavItems: DrawerItem[] = [
    {
      label: 'All Games',
      icon: <HomeIcon />,
      onClick: () => navigate(routes.dash.games.index()),
      permitted: true,
    },
    {
      label: 'Game Metrics',
      icon: <TableChartIcon />,
      onClick: () => navigate(routes.dash.gameMetrics.index(gameId)),
      permitted: true,
    },
    {
      label: 'New Game Metrics',
      icon: <TableChartIcon />,
      onClick: () => navigate(routes.dash.gameMetricsNext.index(gameId)),
      permitted: true,
    },
    {
      label: 'Cost Explorer',
      icon: <MoneyOffIcon />,
      onClick: () => navigate(routes.dash.games.costs(gameId)),
      permitted: true,
    },
    {
      label: 'Revenue Explorer',
      icon: <AttachMoneyIcon />,
      onClick: () => navigate(routes.dash.games.revenues(gameId)),
      permitted: true,
    },
    {
      label: 'Campaign Performance',
      icon: <CampaignIcon />,
      onClick: () => navigate(routes.dash.games.campaignMetrics(gameId)),
      permitted: true,
    },
    {
      label: 'Level Drop Explorer',
      icon: <TrendingDownIcon />,
      onClick: () => navigate(routes.dash.games.levelDrops(gameId)),
      permitted: true,
    },
    {
      label: 'Game Review',
      icon: <RateReviewIcon />,
      onClick: () => navigate(routes.dash.gameReviews.index(gameId)),
      permitted: true,
    },
    {
      label: 'Product Metrics',
      icon: <SportsEsportsIcon />,
      onClick: () => navigate(routes.dash.games.productMetrics(gameId)),
      permitted: true,
    },
    {
      label: 'Creative Performance',
      icon: <PhotoLibraryIcon />,
      onClick: () => navigate(routes.dash.games.creativeMetrics(gameId)),
      permitted: true,
    },
  ]

  // Use custom toolbar if provided, otherwise use default toolbar
  const defaultToolbar = (
    <Stack direction="row" spacing={2}>
      {getReportQuery.isLoading ? (
        <Loading visible />
      ) : (
        <ProfitReport report={getReportQuery.data!} onProfitChange={handleProfitChange} />
      )}
      {/* <Box
        sx={{
          width: '100%',
          flex: 1,
          position: 'relative',
          maxWidth: 400,
          minWidth: { xs: 0, sm: 200, md: 300, lg: 400 },
        }}
      >
        <TextField
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          hiddenLabel
          id="filled-hidden-label-small"
          defaultValue="Small"
          variant="filled"
          size="small"
          sx={{ bgcolor: 'background.paper' }}
          fullWidth
          {...gameFilterForm.register('keyword')}
        />
        {games.length > 0 && (
          <List
            sx={{
              position: 'absolute',
              top: '100%',
              left: { xs: 'unset', sm: 0 },
              right: { xs: '-32px', sm: 'unset' },
              width: { xs: '80vw', sm: '100%' },
              zIndex: 1,
              bgcolor: 'background.paper',
              borderColor: 'divider',
              borderWidth: 1,
              borderStyle: 'solid',
              color: 'text.primary',
            }}
          >
            {games.map((game) => (
              <ListItemButton
                component="a"
                href={routes.dash.gameMetrics.index(game.storeId)}
                key={game.storeId}
              >
                <ListItemIcon>
                  {game.platform === GamePlatform.Android ? <Android /> : <Ios />}
                </ListItemIcon>
                <ListItemText primary={game.name} />
              </ListItemButton>
            ))}
          </List>
        )}
      </Box> */}
    </Stack>
  )

  return (
    <BaseLayout
      toolbar={defaultToolbar}
      slotProps={{
        ...slotProps,
        toolbar: {
          ...slotProps.toolbar,
          sx: toolbarStyle as any,
        },
      }}
      menuGroups={[[...gameNavItems]]}
    >
      {children}
    </BaseLayout>
  )
}
