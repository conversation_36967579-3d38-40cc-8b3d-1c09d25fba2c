import { sumBy } from 'lodash-es'
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef } from 'react'
import { create } from 'zustand'
import { useShallow } from 'zustand/react/shallow'

export type Size = {
  height: number
  width: number
}

export interface LayoutComponents {
  filter: Size
}

export type LayoutComponentName = keyof LayoutComponents

const useLayoutSizeStore = create<{
  components: Record<LayoutComponentName, Size>
  setSize: (component: LayoutComponentName | string, size: Partial<Size>) => void
  getSize: (component: LayoutComponentName | string) => Size
}>((set, get) => ({
  components: {} as any,
  setSize: (component, size) => {
    set((s) => ({
      components: {
        ...s.components,
        [component]: {
          ...(s.components as any)[component],
          ...size,
        },
      },
    }))
  },
  getSize: (component) => {
    return (get().components as any)[component] || { height: 0, width: 0 }
  },
}))

export const useLayoutSize = (name: LayoutComponentName) => {
  const setSize = useLayoutSizeStore((s) => s.setSize)
  const setComponentSize = useCallback(
    (size: Partial<Size>) => setSize(name, size),
    [name, setSize]
  )
  const size = useLayoutSizeStore(useShallow((s) => s.components[name] || { height: 0, width: 0 }))

  useEffect(() => {
    console.log(name, size)
  }, [size])

  return [setComponentSize, size] as const
}

export const useLayoutComponentRegistration = (name: LayoutComponentName) => {
  const ref = useRef<HTMLElement>(null)
  const [setComponentSize, size] = useLayoutSize(name)

  useLayoutEffect(() => {
    setComponentSize({
      height: ref.current?.clientHeight || 0,
      width: ref.current?.clientWidth || 0,
    })
  }, [setComponentSize, name, ref])

  return [ref, setComponentSize, size] as const
}

export const useLayoutHeight = (...sizes: Size[]) => {
  const height = useMemo(() => sumBy(sizes, 'height'), [...sizes])
  return height
}
