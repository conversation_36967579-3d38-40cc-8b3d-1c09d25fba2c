import React from 'react'
import { BoxProps } from '@mui/material/Box'
import { ToolbarProps } from '@mui/material/Toolbar'
import QueryStatsIcon from '@mui/icons-material/QueryStats'
import ManageSearchIcon from '@mui/icons-material/ManageSearch'
import NewReleasesIcon from '@mui/icons-material/NewReleases'
import RuleIcon from '@mui/icons-material/Rule'
import ShieldIcon from '@mui/icons-material/Shield'
import GitHubIcon from '@mui/icons-material/GitHub'
import DashboardIcon from '@mui/icons-material/Dashboard'
import AttachMoneyIcon from '@mui/icons-material/AttachMoney'
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts'
import ArticleIcon from '@mui/icons-material/Article'
import GroupIcon from '@mui/icons-material/Group'
import SettingsIcon from '@mui/icons-material/Settings'
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome'
import RequestPageIcon from '@mui/icons-material/RequestPage'
import QuestionMarkIcon from '@mui/icons-material/QuestionMark'
import CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange'
import AccountTreeIcon from '@mui/icons-material/AccountTree'
import WalletIcon from '@mui/icons-material/Wallet'
import RequestQuoteIcon from '@mui/icons-material/RequestQuote'
import { useGraphql } from '@/components/toolkit-api'
import { useNavigate as useNavigateV2 } from '@/components/location'
import AssessmentIcon from '@mui/icons-material/Assessment'
import DescriptionIcon from '@mui/icons-material/Description'
import { BaseLayout, DrawerItem } from './base_layout'
import { graphql } from '@/gql'
import { groupBy, orderBy, thru, toPairs } from 'lodash-es'

const icons: Record<string, any> = {
  QueryStatsIcon,
  NewReleasesIcon,
  RuleIcon,
  ShieldIcon,
  GitHubIcon,
  DashboardIcon,
  AttachMoneyIcon,
  ManageAccountsIcon,
  ArticleIcon,
  GroupIcon,
  SettingsIcon,
  AutoAwesomeIcon,
  RequestPageIcon,
  QuestionMarkIcon,
  AccountTreeIcon,
  WalletIcon,
  CurrencyExchangeIcon,
  ManageSearchIcon,
  RequestQuoteIcon,
  AssessmentIcon,
  DescriptionIcon,
}

export type AdminLayoutProps = {
  children: React.ReactNode
  toolbar?: React.ReactNode
  slotProps?: {
    toolbar?: ToolbarProps
    content?: BoxProps
    appBar?: BoxProps
  }
  additionalMenuGroups?: DrawerItem[][]
}

const getMenuQuery = graphql(`
  query AdminLayout_GetMenu {
    sideMenus {
      collection {
        ...SideMenuAttributes
      }
    }
  }
`)

export function AdminLayout({
  children,
  toolbar,
  slotProps = {},
  additionalMenuGroups = [],
}: AdminLayoutProps) {
  const getMenu = useGraphql(getMenuQuery)

  const { navigate } = useNavigateV2()

  const menuGroups: DrawerItem[][] = [
    ...(additionalMenuGroups || []),
    ...thru(getMenu.data?.sideMenus?.collection, (menus) => {
      if (!menus) {
        return []
      }

      return orderBy(toPairs(groupBy(menus, (m) => m.group)), (pair) => pair[0]).map(
        ([_group, ms]) =>
          ms.map((m) => {
            const Icon = icons[m.icon] || icons.QuestionMarkIcon
            return {
              label: m.name,
              icon: <Icon />,
              onClick: () => navigate(m.path),
              permitted: true,
            }
          })
      )
    }),
  ]

  return (
    <BaseLayout slotProps={slotProps} toolbar={toolbar} menuGroups={menuGroups}>
      {children}
    </BaseLayout>
  )
}
