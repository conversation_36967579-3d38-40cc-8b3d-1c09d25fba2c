import { create } from 'zustand'

import { routes } from '../location'

export type BreadcrumbItem =
  | {
      label: string
      url: string
    }
  | string

export interface LayoutStore {
  breadcrumbs: BreadcrumbItem[]
  homeBreadcrumb: BreadcrumbItem
  setBreadcrumbs: (items: BreadcrumbItem[]) => any
  setDashboardHomeBreadcrumb: () => any
  setDefaultHomeBreadcrumb: () => any
}

const homeBreadcrumb: BreadcrumbItem = { label: 'Home', url: '/' }
const dashboardHomeBreadcrumb: BreadcrumbItem = { label: 'Home', url: routes.dash.games.index() }

export const useLayoutStore = create<LayoutStore>((set) => ({
  breadcrumbs: [],
  homeBreadcrumb,
  setBreadcrumbs: (items: BreadcrumbItem[]) => set({ breadcrumbs: items }),
  setDashboardHomeBreadcrumb: () => set({ homeBreadcrumb: dashboardHomeBreadcrumb }),
  setDefaultHomeBreadcrumb: () => set({ homeBreadcrumb }),
}))
