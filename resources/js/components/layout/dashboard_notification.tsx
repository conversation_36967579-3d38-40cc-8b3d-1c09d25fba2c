import { graphql } from '@/gql'
import {
  useCreateDashboardNotification,
  useGraphql,
  useUpdateDashboardNotification,
} from '../toolkit-api'
import { isLoading } from '../loading'
import {
  Box,
  Button,
  colors,
  Divider,
  IconButton,
  ListItemButton,
  ListItemText,
  TextField,
  TextFieldProps,
  Typography,
} from '@mui/material'
import NewReleasesIcon from '@mui/icons-material/NewReleases'
import { Fragment, useEffect, useRef, useState, useMemo } from 'react'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import PushPinIcon from '@mui/icons-material/PushPin'
import SaveIcon from '@mui/icons-material/Save'
import VisibilityIcon from '@mui/icons-material/Visibility'
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff'
import AddIcon from '@mui/icons-material/Add'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import CloseIcon from '@mui/icons-material/Close'

import { useApolloClient } from '@apollo/client'
import { UnpinIcon } from '../icons/unpin'
import { useAuthorization } from '../authorization'
import { FieldArrayWithId, useFieldArray, useForm } from 'react-hook-form'
import { DashboardNotification as DashboardNotificationRecord } from '#graphql/main'
import { Dropdown, useDropdownControl } from '../dropdown'
import { useLayoutComponentRegistration } from '@/components/layout/size.js'

declare module '@/components/layout/size.js' {
  interface LayoutComponents {
    notification: Size
  }
}

const query = graphql(`
  query Layout_getNotifications {
    dashboardNotifications {
      collection {
        id
        message
        isPinned
        isVisible
      }
    }
  }
`)

export function DashboardNotification() {
  const [visible, setVisible] = useState(true)
  const [authorize] = useAuthorization([
    {
      action: 'update',
      name: 'dash.notification',
      args: [],
    },
    {
      action: 'store',
      name: 'dash.notification',
      args: [],
    },
    {
      action: 'index',
      name: 'dash.notification',
      args: [],
    },
  ])

  const canUpdate = authorize('dash.notification', 'update')
  const canCreate = authorize('dash.notification', 'store')
  const canView = authorize('dash.notification', 'index')

  const getNotifications = useGraphql(query, { skip: !canView })

  const notifications = useMemo(() => {
    return getNotifications.data?.dashboardNotifications?.collection || []
  }, [getNotifications.data])

  const pinnedNotifications = notifications.filter((n) => n.isPinned)

  const [pinnedNotificationIndex, setPinnedNotificationIndex] = useState(0)
  const rotateRef = useRef<NodeJS.Timeout | null>(null)
  useEffect(() => {
    if (!pinnedNotifications.length) {
      return
    }

    rotateRef.current = setInterval(() => {
      setPinnedNotificationIndex(
        pinnedNotifications[pinnedNotificationIndex + 1] ? pinnedNotificationIndex + 1 : 0
      )
    }, 5_000)

    return () => {
      if (rotateRef.current) {
        clearInterval(rotateRef.current)
      }
    }
  }, [pinnedNotifications])

  const pinnedNotification = pinnedNotifications[pinnedNotificationIndex]

  const dropdownControl = useDropdownControl()

  const updateNotification = useUpdateDashboardNotification()
  const apolloClient = useApolloClient()
  useEffect(() => {
    if (updateNotification.isSuccess) {
      const notification = updateNotification.data!.data
      apolloClient.cache.updateQuery({ query }, (data) => {
        if (!data) {
          return
        }

        return {
          ...data,
          dashboardNotifications: {
            ...data.dashboardNotifications!,
            collection: data.dashboardNotifications!.collection.map((e) =>
              e.id === notification.id ? { ...e, ...notification } : e
            ),
          },
        }
      })
    }
  }, [updateNotification.isSuccess])

  const createNotification = useCreateDashboardNotification()
  useEffect(() => {
    if (createNotification.isSuccess) {
      const notification = createNotification.data!.data
      apolloClient.cache.updateQuery({ query }, (data) => {
        if (!data) {
          return
        }

        return {
          ...data,
          dashboardNotifications: {
            ...data.dashboardNotifications!,
            collection: [notification, ...data.dashboardNotifications!.collection],
          },
        }
      })
    }
  }, [createNotification.isSuccess])

  const form = useForm({
    defaultValues: {
      notifications: [] as Array<{ value: DashboardNotificationRecord }>,
    },
  })

  useEffect(() => {
    if (notifications) {
      form.setValue(
        'notifications',
        notifications.map((n) => ({ value: n }))
      )
    }
  }, [notifications])

  const { fields, prepend } = useFieldArray({ control: form.control, name: 'notifications' })

  const onSave = (notification: DashboardNotificationRecord, index: number) => {
    const formValues = form.getValues()
    const currentMessage = formValues.notifications[index]?.value.message || notification.message
    if (notification.id < 0) {
      return createNotification.mutateAsync({
        message: currentMessage,
      })
    }

    return updateNotification.mutateAsync({
      id: notification.id,
      message: currentMessage,
      isPinned: notification.isPinned,
      isVisible: notification.isVisible,
    })
  }

  const shouldRender =
    !isLoading([getNotifications.error, getNotifications.loading]) && canView && visible

  const [ref, setNotificationSize] = useLayoutComponentRegistration('notification')

  useEffect(() => {
    setNotificationSize({ height: ref.current?.clientHeight || 0 })
  }, [shouldRender, ref, pinnedNotification, setNotificationSize])

  if (!shouldRender) {
    return null
  }

  return (
    <Box sx={{ bgcolor: colors.orange[100], mx: -3, py: 1, position: 'relative' }} ref={ref}>
      <Box
        sx={{ width: '70%', margin: 'auto' }}
        onClick={(e) => dropdownControl.onOpen(e as any)}
        id={dropdownControl.id}
      >
        {pinnedNotification ? (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <NewReleasesIcon color="warning" />
            <Typography color="textPrimary" sx={{ pl: 1, textAlign: 'center' }}>
              {pinnedNotification.message}
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Typography color="textPrimary">See news</Typography>
          </Box>
        )}
      </Box>

      <Dropdown
        control={dropdownControl}
        sx={{ width: '100%', maxHeight: '80vh' }}
        fullWidth
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <List>
          {canCreate && (
            <>
              <ListItem>
                <Button
                  variant="outlined"
                  onClick={() =>
                    prepend({
                      value: {
                        id: 0 - fields.length,
                        message: '',
                        isPinned: false,
                        isVisible: false,
                      },
                    })
                  }
                  fullWidth
                >
                  <AddIcon />
                </Button>
              </ListItem>

              <Divider />
            </>
          )}
          {fields.length === 0 && <ListItem>No notifications</ListItem>}
          {fields.map((field, index) => {
            return (
              <Fragment key={field.id}>
                <NotificationListItem
                  field={field}
                  slotProps={{ textField: form.register(`notifications.${index}.value.message`) }}
                  onSave={(notification) => onSave(notification, index)}
                  canUpdate={canUpdate}
                  updateNotification={updateNotification}
                  createNotification={createNotification}
                />

                {index < fields.length - 1 && <Divider />}
              </Fragment>
            )
          })}
        </List>
      </Dropdown>

      <Box sx={{ position: 'absolute', right: 3, top: '50%', transform: 'translate(0, -50%)' }}>
        <IconButton onClick={() => setVisible(false)}>
          <CloseIcon />
        </IconButton>
      </Box>
    </Box>
  )
}

function NotificationListItem({
  field,
  onSave,
  canUpdate,
  updateNotification,
  createNotification,
  slotProps: { textField: textFieldProps = {} },
}: {
  slotProps: { textField?: Partial<TextFieldProps> }
  canUpdate: boolean
  updateNotification: ReturnType<typeof useUpdateDashboardNotification>
  createNotification: ReturnType<typeof useCreateDashboardNotification>
  onSave: (notification: DashboardNotificationRecord) => any
  field: FieldArrayWithId<
    {
      notifications: Array<{
        value: DashboardNotificationRecord
      }>
    },
    'notifications',
    'id'
  >
}) {
  const n = field.value
  const dropdownControl = useDropdownControl()
  return (
    <Fragment key={field.id}>
      <ListItem sx={{ flexDirection: 'column', alignItems: 'flex-start' }}>
        {canUpdate ? (
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Box sx={{ flex: 1 }}>
              <TextField {...textFieldProps} fullWidth multiline maxRows={3} />
            </Box>

            <Box>
              <IconButton onClick={dropdownControl.onOpen}>
                <MoreVertIcon />
              </IconButton>
            </Box>
            <Dropdown
              control={dropdownControl}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            >
              <ListItemButton
                onClick={() =>
                  updateNotification.mutateAsync({
                    id: n.id,
                    isPinned: !n.isPinned,
                  })
                }
                disabled={isLoading(updateNotification.isPending) || n.id < 0}
              >
                {n.isPinned ? (
                  <>
                    <UnpinIcon />
                    Upin
                  </>
                ) : (
                  <>
                    <PushPinIcon />
                    Pin
                  </>
                )}
              </ListItemButton>
              <ListItemButton
                onClick={() =>
                  updateNotification.mutateAsync({
                    id: n.id,
                    isVisible: !n.isVisible,
                  })
                }
                disabled={isLoading(updateNotification.isPending) || n.id < 0}
              >
                {n.isVisible ? (
                  <>
                    <VisibilityOffIcon />
                    Hide
                  </>
                ) : (
                  <>
                    <VisibilityIcon />
                    Show
                  </>
                )}
              </ListItemButton>
              <ListItemButton
                onClick={() => onSave(n)}
                disabled={isLoading([createNotification.isPending, updateNotification.isPending])}
              >
                <SaveIcon />
                Save
              </ListItemButton>
            </Dropdown>
          </Box>
        ) : (
          <ListItemButton sx={{ width: '100%' }}>
            <ListItemText primary={n.message} />
          </ListItemButton>
        )}
      </ListItem>
    </Fragment>
  )
}
