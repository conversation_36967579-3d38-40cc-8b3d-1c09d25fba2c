import { isNil } from 'lodash-es'

import { formatter } from '#utils/formatter'

export const formatCurrency = (value: any, decimals: any = 2) => {
  return isNil(value)
    ? undefined
    : `$${formatter.round(value, Number.isNaN(Number(decimals)) ? 2 : decimals)}`
}

export const formatNumber = (value: any, decimals: any = 2) =>
  formatter.round(value, Number.isNaN(Number(decimals)) ? 2 : decimals)

export const formatDuration = (value: any) => {
  const formatted = formatter.time('s', 'm')(value)
  return `${formatter.round(formatted['m'])}m${formatter.round(formatted['s'])}s`
}

export const formatPercentage = (value: any) => {
  return `${formatter.round(value * 100, 2)}%`
}

export const formatNote = (note: any, maxLength: number = 100): string => {
  return formatter.note(note, maxLength)
}
