import { CKEditor } from '@ckeditor/ckeditor5-react'
import {
  Alignment,
  Autoformat,
  Bold,
  Italic,
  Strikethrough,
  Subscript,
  Superscript,
  Underline,
  BlockQuote,
  Base64UploadAdapter,
  CKFinder,
  CKFinderUploadAdapter,
  CloudServices,
  Essentials,
  FindAndReplace,
  FontBackgroundColor,
  FontColor,
  FontFamily,
  FontSize,
  Heading,
  HorizontalLine,
  Image,
  ImageCaption,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
  PictureEditing,
  Indent,
  IndentBlock,
  Link,
  List,
  ListProperties,
  MediaEmbed,
  Mention,
  PageBreak,
  Paragraph,
  PasteFromOffice,
  RemoveFormat,
  SpecialCharacters,
  SpecialCharactersEssentials,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  TableProperties,
  TableToolbar,
  TextTransformation,
  ClassicEditor,
  Editor,
  EditorConfig,
} from 'ckeditor5'
import 'ckeditor5/ckeditor5.css'
import { useCallback, useState } from 'react'

export function useEditorControl() {
  const [editor, setEditor] = useState<Editor | null>(null)
  const getContent = useCallback(() => {
    return editor ? editor.getData() : ''
  }, [editor])
  return { editor, setEditor, getContent }
}

export function WYSIWYG({
  control,
  config = {},
}: {
  config?: EditorConfig
  control: ReturnType<typeof useEditorControl>
}) {
  return (
    <CKEditor
      editor={ClassicEditor}
      config={{
        licenseKey: 'GPL',
        plugins: [
          Alignment,
          Autoformat,
          BlockQuote,
          Bold,
          CKFinder,
          CKFinderUploadAdapter,
          CloudServices,
          Essentials,
          FindAndReplace,
          FontBackgroundColor,
          FontColor,
          FontFamily,
          FontSize,
          Heading,
          HorizontalLine,
          Image,
          ImageCaption,
          ImageResize,
          ImageStyle,
          ImageToolbar,
          ImageUpload,
          Base64UploadAdapter,
          Indent,
          IndentBlock,
          Italic,
          Link,
          List,
          ListProperties,
          MediaEmbed,
          Mention,
          PageBreak,
          Paragraph,
          PasteFromOffice,
          PictureEditing,
          RemoveFormat,
          SpecialCharacters,
          SpecialCharactersEssentials,
          Strikethrough,
          Subscript,
          Superscript,
          Table,
          TableCaption,
          TableCellProperties,
          TableColumnResize,
          TableProperties,
          TableToolbar,
          TextTransformation,
          Underline,
        ],
        toolbar: {
          shouldNotGroupWhenFull: true,
          items: [
            // --- Document-wide tools ----------------------------------------------------------------------
            'undo',
            'redo',
            '|',
            'findAndReplace',
            'selectAll',
            '|',

            // --- "Insertables" ----------------------------------------------------------------------------

            'link',
            'insertImage',
            'insertTable',
            'blockQuote',
            'mediaEmbed',
            'pageBreak',
            'horizontalLine',
            'specialCharacters',
            '-',

            // --- Block-level formatting -------------------------------------------------------------------
            'heading',
            '|',

            // --- Font formatting -------------------------------------------------------------------
            'fontSize',
            'fontFamily',
            'fontColor',
            'fontBackgroundColor',
            '|',

            // --- Basic styles and inline formatting -------------------------------------------------------
            'bold',
            'italic',
            'underline',
            {
              label: 'Basic styles',
              icon: 'text',
              items: ['strikethrough', 'superscript', 'subscript'],
            },
            'removeFormat',
            '|',

            // --- Text alignment ---------------------------------------------------------------------------
            'alignment',
            '|',

            // --- Lists and indentation --------------------------------------------------------------------
            'bulletedList',
            'numberedList',
            '|',
            'outdent',
            'indent',
          ],
        },
        heading: {
          options: [
            {
              model: 'paragraph',
              title: 'Paragraph',
              class: 'ck-heading_paragraph',
            },
            {
              model: 'heading1',
              view: 'h1',
              title: 'Heading 1',
              class: 'ck-heading_heading1',
            },
            {
              model: 'heading2',
              view: 'h2',
              title: 'Heading 2',
              class: 'ck-heading_heading2',
            },
            {
              model: 'heading3',
              view: 'h3',
              title: 'Heading 3',
              class: 'ck-heading_heading3',
            },
            {
              model: 'heading4',
              view: 'h4',
              title: 'Heading 4',
              class: 'ck-heading_heading4',
            },
            {
              model: 'heading5',
              view: 'h5',
              title: 'Heading 5',
              class: 'ck-heading_heading5',
            },
            {
              model: 'heading6',
              view: 'h6',
              title: 'Heading 6',
              class: 'ck-heading_heading6',
            },
          ],
        },
        fontFamily: {
          supportAllValues: true,
        },
        fontSize: {
          options: [10, 12, 14, 'default', 18, 20, 22],
          supportAllValues: true,
        },
        fontColor: {
          columns: 12,
        },
        fontBackgroundColor: {
          columns: 12,
        },
        image: {
          toolbar: [
            'imageTextAlternative',
            'toggleImageCaption',
            '|',
            'imageStyle:inline',
            'imageStyle:wrapText',
            'imageStyle:breakText',
          ],
        },
        link: {
          addTargetToExternalLinks: true,
          defaultProtocol: 'https://',
        },
        list: {
          properties: {
            styles: true,
            startIndex: true,
            reversed: true,
          },
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells',
            'tableProperties',
            'tableCellProperties',
            'toggleTableCaption',
          ],
        },
        menuBar: {
          isVisible: true,
        },
        ...config,
      }}
      onReady={(e) => {
        control.setEditor(e)
      }}
    />
  )
}
