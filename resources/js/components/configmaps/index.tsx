import { useEffect, useMemo, useRef, useState } from 'react'
import { UseQueryResult, useQuery } from '@tanstack/react-query'
import { ConfigMapBindings, ConfigMapCollection, ConfigMapRecord } from '@munkit/main'

import { toolkitApi, useLazyGraphql } from '@/components/toolkit-api/index.js'
import { useQueryToast } from '../toast'
import { graphql } from '@/graphql'
import { create } from 'zustand'

class Dummy extends ConfigMapRecord {}

const idToConfigMapCollection = new Map<keyof ConfigMapBindings, ConfigMapCollection<any>>()

export function useConfigMaps<T extends keyof ConfigMapBindings>(
  id: T
): UseQueryResult<ConfigMapBindings[T]> {
  const defaultValues = useMemo(() => new ConfigMapCollection(Dummy) as any, [])

  const q = useQuery({
    queryKey: ['configmaps', id],
    queryFn: async () => {
      if (idToConfigMapCollection.has(id)) {
        return idToConfigMapCollection.get(id)!
      }

      const configMapCollection = await toolkitApi.getConfigMap(id).then((r) => r.data)
      idToConfigMapCollection.set(id, configMapCollection)
      return configMapCollection
    },
  })

  const { data, ...query } = q

  useQueryToast(q)

  return {
    ...query,
    data: data || defaultValues,
  }
}

const useConfigMapStore = create<{
  collections: Record<keyof ConfigMapBindings, ConfigMapCollection<any>>
  loading: boolean
  error?: Error | null
  // @ts-ignore
  getConfigMap: <T extends keyof ConfigMapBindings>(
    id: T,
    sole?: boolean
    // @ts-ignore
  ) => ConfigMapCollection<ConfigMapBindings[T]> | null
  enableLoading: () => void
  saveCollections: (
    collections: Record<keyof ConfigMapBindings, ConfigMapCollection<any>>,
    error?: null | Error
  ) => void
}>((set, get) => ({
  collections: {} as any,
  loading: false,
  error: null,
  getConfigMap: (id, sole = false) => {
    const collections = get().collections
    const collection = collections[id]
    if (!collection) {
      return null
    }

    return sole ? collection.find((e) => e.id === 'sole') : collection
  },
  enableLoading: () => {
    set({ loading: true, error: null })
  },
  saveCollections: (collections, error) => {
    set((state) => ({
      collections: { ...state.collections, ...collections },
      loading: false,
      error,
    }))
  },
}))

const ids = new Set<string>()
const fetch = (configMapIds: (keyof ConfigMapBindings)[]) => {
  configMapIds.forEach((id) => ids.add(id))
}
const remove = (configMapIds: (keyof ConfigMapBindings)[]) => {
  configMapIds.forEach((id) => ids.delete(id))
}

const query = graphql(`
  query ConfigMapCollections($ids: [ID!]!) {
    configMapCollections(ids: $ids) {
      ...ConfigMapCollectionAttributes
    }
  }
`)

export function useConfigMapCollections<
  TIds extends Array<keyof ConfigMapBindings | { id: keyof ConfigMapBindings; sole: true }> | [],
>(ids: TIds) {
  const loading = useConfigMapStore((s) => s.loading)
  const error = useConfigMapStore((s) => s.error)
  const getConfigMap = useConfigMapStore((s) => s.getConfigMap)

  useEffect(() => {
    fetch(ids.map((i) => (typeof i === 'object' ? i.id : i)))
  }, [ids])

  const collections = ids.map((id) =>
    typeof id === 'object' ? getConfigMap(id.id, id.sole) : getConfigMap(id)
  )

  const allLoaded = collections.every((c) => c !== null)

  return {
    error,
    loading: loading || !allLoaded,
    data: collections as {
      [P in keyof TIds]: TIds[P] extends { id: infer U; sole: true }
        ? U extends keyof ConfigMapBindings
          ? ConfigMapBindings[U][0]
          : unknown
        : TIds[P] extends keyof ConfigMapBindings
          ? ConfigMapBindings[TIds[P]]
          : unknown
    },
  }
}

export function useConfigMapCollectionsBatchProcessor() {
  const [tick, setTick] = useState(0)
  const enableLoading = useConfigMapStore((s) => s.enableLoading)
  const saveCollections = useConfigMapStore((s) => s.saveCollections)
  const loading = useConfigMapStore((s) => s.loading)
  const loadingRef = useRef(loading)

  const tickIncrementInterval = useRef<NodeJS.Timeout | null>(null)

  const [queryConfigMaps, queryConfigMapsState] = useLazyGraphql(query, {
    onCompleted: (data) => {
      remove(data.configMapCollections.map((c) => c.id as any))
    },
  })

  useEffect(() => {
    if (!queryConfigMapsState.data && !queryConfigMapsState.error) {
      return
    }

    if (queryConfigMapsState.error) {
      saveCollections({} as any, queryConfigMapsState.error)
    } else {
      queryConfigMapsState.data!.configMapCollections.forEach((c) => {
        ids.delete(c.id)
      })

      saveCollections(
        Object.fromEntries(
          queryConfigMapsState.data!.configMapCollections.map(({ id, items }) => [id, items])
        ) as any,
        null
      )
    }
  }, [queryConfigMapsState.data, queryConfigMapsState.error, saveCollections])

  useEffect(() => {
    if (queryConfigMapsState.loading) {
      enableLoading()
    }
  }, [queryConfigMapsState.loading, enableLoading])

  useEffect(() => {
    loadingRef.current = loading
  }, [loading])

  useEffect(() => {
    const skip = tick <= 0 || loadingRef.current || ids.size === 0

    if (skip) {
      return
    }

    console.log(
      `ConfigMapBatch fetch tick=${tick} ids=${Array.from(ids.values())} size=${ids.size} loading=${loadingRef.current}`
    )

    queryConfigMaps({ variables: { ids: Array.from(ids.values()) } })
  }, [tick, queryConfigMaps])

  useEffect(() => {
    tickIncrementInterval.current = setInterval(() => {
      setTick((t) => t + 1)
    }, 200)

    return () => {
      if (tickIncrementInterval.current) {
        clearInterval(tickIncrementInterval.current)
      }
    }
  }, [setTick])
}
