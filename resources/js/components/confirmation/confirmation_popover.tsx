import LoadingButton, { LoadingButtonProps } from '@mui/lab/LoadingButton'
import { Box, Button, Popover, PopoverProps, Stack, Typography } from '@mui/material'
import { ReactNode, useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { ExtendProps } from '@/types'

export type PopoverControl<TData = {}> = Required<Pick<PopoverProps, 'anchorEl' | 'open'>> & {
  onClick: (data: TData) => (event: React.MouseEvent<HTMLButtonElement>) => void
  onClose: () => void
  data?: TData
}

export function usePopoverControl<TData = {}>(): PopoverControl<TData> {
  const [data, setData] = useState<TData>()
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
  const open = Boolean(anchorEl)

  const onClick = useCallback(
    (d: TData) => (event: React.MouseEvent<HTMLButtonElement>) => {
      setData(d)
      setAnchorEl(event.currentTarget)
    },
    [setAnchorEl]
  )

  const onClose = useCallback(() => {
    setAnchorEl(null)
  }, [setAnchorEl])

  return { open, onClose, onClick, anchorEl, data }
}

export function ConfirmationPopover<TData = {}>({
  control,
  title,
  slotProps: { button: buttonProps, ...slotProps },
  ...props
}: ExtendProps<Omit<PopoverProps, 'slotProps'>, 'open' | 'anchorEl' | 'onClose'> & {
  control: PopoverControl<TData>
  title: ReactNode
  slotProps: PopoverProps['slotProps'] & { button?: Partial<LoadingButtonProps> }
}) {
  const { t } = useTranslation()

  return (
    <Popover
      onClose={control.onClose}
      open={control.open}
      anchorEl={control.anchorEl}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      slotProps={slotProps}
      {...props}
    >
      <Box sx={{ px: 2, py: 1, maxWidth: 400 }}>
        <Typography>{title}</Typography>

        <Stack spacing={1} direction="row" justifyContent="flex-end" sx={{ mt: 1 }}>
          <LoadingButton
            variant="contained"
            loading={false}
            disabled={false}
            size="small"
            {...buttonProps}
          >
            {t('common.btn.delete')}
          </LoadingButton>

          <Button variant="outlined" size="small" color="error" onClick={control.onClose as any}>
            {t('common.btn.cancel')}
          </Button>
        </Stack>
      </Box>
    </Popover>
  )
}
