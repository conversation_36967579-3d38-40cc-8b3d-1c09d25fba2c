import { IconButton, IconButtonProps, Tooltip } from '@mui/material'
import CompareArrowsIcon from '@mui/icons-material/CompareArrows'

export function SwitchButton({ tooltip, ...props }: IconButtonProps & { tooltip?: string }) {
  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        <IconButton {...props}>
          <CompareArrowsIcon />
        </IconButton>
      </Tooltip>
    )
  }

  return (
    <IconButton {...props}>
      <CompareArrowsIcon />
    </IconButton>
  )
}
