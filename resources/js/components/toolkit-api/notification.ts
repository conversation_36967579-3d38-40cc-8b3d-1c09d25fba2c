import { UseMutationResult } from '@tanstack/react-query'
import { useEffect } from 'react'
import { toast } from 'react-toastify'

export function useMutationNotification<T, U, V, Z>(mutation: UseMutationResult<T, U, V, Z>) {
  useEffect(() => {
    if (mutation.isSuccess) {
      toast.success('Success!')
    }
  }, [mutation.isSuccess])

  useEffect(() => {
    if (mutation.error) {
      toast.error(`Error: ${(mutation.error as any).message}`)
    }
  }, [mutation.error])
}
