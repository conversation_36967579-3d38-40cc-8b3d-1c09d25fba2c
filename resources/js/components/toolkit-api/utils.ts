import dayjs from 'dayjs'

import { autoFill } from '#utils/pure'

export function autoFillDate<T extends { date: any; autogen?: boolean }, U>(
  collection: T[],
  makeRecord: (attrs: { date: dayjs.Dayjs; autogen: true }, index: number) => T,
  orderDirection: string,
  iteratee: (item: T) => U = (item) => item.date
) {
  return autoFill(collection, iteratee, (i) => {
    if (!collection[0]) {
      return null
    }

    const dates = collection.map((item) => iteratee(item)).sort()
    const dateFirst = dayjs(dates[0]! as any)
    const dateLast = dayjs(dates[dates.length - 1]! as any)

    if (orderDirection.toLowerCase() === 'asc') {
      if (dateFirst.add(i, 'day').isAfter(dateLast)) {
        return null
      }

      return makeRecord(
        {
          date: dateFirst.add(i, 'day'),
          autogen: true,
        },
        i
      )
    }

    if (dateLast.subtract(i, 'day').isBefore(dateFirst)) {
      return null
    }

    return makeRecord(
      {
        date: dateLast.subtract(i, 'day'),
        autogen: true,
      },
      i
    )
  })
}
