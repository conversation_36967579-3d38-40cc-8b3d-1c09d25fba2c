import axios from 'axios'
import { ConfigMapBindings } from '@munkit/main'
import type { CherryPick } from '@adonisjs/lucid/types/model'
import { ApolloClient, InMemoryCache } from '@apollo/client'

import DashboardNotification from '#models/dashboard_notification'
import type GameReleaseProposal from '#models/game_release_proposal'
import GameMetric, { SubjectMetric } from '#models/game_metric'
import Game from '#models/game'
import GameMetricMetadatum from '#models/game_metric_metadatum'
import DashboardRoleMembership from '#models/dashboard_role_membership'
import { Authorization } from '#controllers/web/policies_controller'
import type GameSpend from '#models/game_spend'
import { GameLevelDropPresenter } from '#controllers/dashboard/game/game_level_drop_presenter'
import { ModelAttributePresenter } from '#controllers/dashboard/game/model_attribute_presenter'
import User from '#models/user'
import Document from '#models/document'
import AdNetwork from '#models/ad_agency'
import Team from '#models/team'
import { routes } from '@/components/location'
import { ReportPresenter } from '#controllers/me/report_presenter'
import { MePresenter } from '#controllers/me_presenter'
import {
  MediationAggregationPresenter,
  MediationDailyRevenueGroup,
  MediationPresenter,
} from '#controllers/dashboard/game/mediation_presenter'
import { SubjectPresenter } from '#controllers/dashboard/team_revenue_presenter'
import GameReleaseApproval from '#models/game_release_approval'
import GamePerformanceSetting from '#models/game_performance_setting'
import GameReview from '#models/game_review'
import { ACLPresenter } from '#controllers/dashboard/access_controls_controller'

export interface PaginationMeta {
  total: number
  perPage: number
  currentPage: number
  lastPage: number
  firstPage: number
  firstPageUrl: string
  lastPageUrl: string
  nextPageUrl: string | null
  previousPageUrl: string | null
}

export interface ToolkitResponse<T, Meta = {}> {
  data: T
  meta: Meta
  feedbacks: Array<{ type: 'info' | 'error' | 'success'; message: string }>
}

export interface ToolkitPaginationResponse<T> extends ToolkitResponse<T[], PaginationMeta> {}

export interface Publisher {
  id: string
  name: string
}

export const apolloClient = new ApolloClient({
  uri: `${window.location.origin}/graphql`,
  cache: new InMemoryCache(),
})

class ToolkitApi {
  #client = axios.create({
    withCredentials: true,
  })

  async approveProposal(form: any) {
    const response = await this.#client.post<
      ToolkitResponse<GameReleaseProposal, { proposal: GameReleaseProposal }>
    >('/game/release_approvals', form, {
      params: {
        _format: 'json',
      },
    })

    return response.data
  }

  async listGameMetric(params: any) {
    const response = await this.#client.get<
      ToolkitResponse<
        GameMetric[],
        PaginationMeta & {
          aggregation: GameMetric
          cherryPick: CherryPick
          scopedAggregation: GameMetric
        }
      >
    >(routes.dash.gameMetrics.index(params.storeId), {
      params: {
        ...params,
        _format: 'json',
      },
    })

    return response.data
  }

  async updateGameMetric(gameId: string, date: any, form: any) {
    const response = await this.#client.put<ToolkitResponse<GameMetricMetadatum>>(
      routes.dash.gameMetrics.update(gameId, date),
      form,
      {
        params: {
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async listGame(params: any) {
    const response = await this.#client.get<ToolkitResponse<Game[], PaginationMeta>>(
      routes.dash.games.index(),
      {
        params: {
          ...params,
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async getConfigMap<T extends keyof ConfigMapBindings>(id: T) {
    const response = await this.#client.get<ToolkitResponse<ConfigMapBindings[T]>>(
      `/configmaps/${id}`,
      {
        params: {
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async updateRole(storeId: string, form: any) {
    const response = await this.#client.put<ToolkitResponse<DashboardRoleMembership[]>>(
      `/dash/roles/${storeId}`,
      form,
      {
        params: {
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async listRole(storeIds: string[]) {
    const response = await this.#client.get<ToolkitPaginationResponse<DashboardRoleMembership>>(
      `/dash/roles`,
      {
        params: {
          _format: 'json',
          storeIds: storeIds,
        },
      }
    )

    return response.data
  }

  async listGameMetricAcl(subject: string) {
    const response = await this.#client.get<ToolkitResponse<ACLPresenter[]>>(
      routes.dash.acls.show(subject),
      {
        params: {
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async updateGameMetricAcl(subject: string, form: any) {
    const response = await this.#client.request<ToolkitResponse<ACLPresenter[]>>({
      url: routes.dash.acls.show(subject),
      data: form,
      params: {
        _format: 'json',
      },
      method: 'PATCH',
    })

    return response.data
  }

  async authorize(form: any) {
    const response = await this.#client.post<ToolkitResponse<Authorization[]>>('/policies', form, {
      params: {
        _format: 'json',
      },
    })

    return response.data
  }

  async listGameSpend(storeId: string, query: any = {}) {
    const response = await this.#client.get<
      ToolkitResponse<GameSpend[], PaginationMeta & { aggregation: GameSpend }>
    >(`/dash/games/${storeId}/costs`, {
      params: {
        _format: 'json',
        ...query,
      },
    })

    return response.data
  }

  async updateGameSpend(storeId: string, form: any) {
    const response = await this.#client.put<
      ToolkitResponse<
        GameSpend[],
        {
          aggregation: GameSpend
        }
      >
    >(`/dash/games/${storeId}/costs/default`, form, { params: { _format: 'json' } })
    return response.data
  }

  async listGameLevelDrop(form: { storeId: string; versions: string[] }) {
    const response = await this.#client.get<
      ToolkitResponse<GameLevelDropPresenter[], { versions: string[] }>
    >(`/dash/game_level_drops/${form.storeId}`, {
      params: {
        _format: 'json',
        versions: form.versions,
      },
    })

    return response.data
  }

  async listGameMetricAttributes() {
    const response = await this.#client.get<ToolkitResponse<ModelAttributePresenter[]>>(
      `/dash/game_metric_attributes`,
      {
        params: {
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async listGameRevenue(form: any) {
    const response = await this.#client.get<
      ToolkitResponse<
        GameMetric[],
        PaginationMeta & {
          breakdowns: GameMetric[]
          aggregation: {
            data: GameMetric
            breakdown: GameMetric[]
          }
        }
      >
    >(`/dash/game_revenues`, {
      params: {
        _format: 'json',
        ...form,
      },
    })

    return response.data
  }

  async listRevenueReport(form: any) {
    const response = await this.#client.get<ToolkitResponse<GameMetric[]>>(
      `/dash/revenue_reports`,
      {
        params: {
          _format: 'json',
          ...form,
        },
      }
    )

    return response.data
  }

  async updateUser(id: string, form: any) {
    const response = await this.#client.put<ToolkitResponse<User>>(`/system/users/${id}`, form, {
      params: {
        _format: 'json',
      },
    })

    return response.data
  }

  async getDocument(id: string) {
    const response = await this.#client.get<ToolkitResponse<Document>>(`/documents/${id}/edit`, {
      params: {
        _format: 'json',
      },
    })

    return response.data
  }

  async updateDocument(form: any) {
    const response = await this.#client.put<ToolkitResponse<Document>>(
      `/documents/${form.id}`,
      form,
      {
        params: {
          _format: 'json',
        },
      }
    )

    return response.data
  }

  async listAdNetwork() {
    const response = await this.#client.get<ToolkitResponse<AdNetwork[]>>('/ad_networks', {
      params: {
        _format: 'json',
      },
    })

    return response.data
  }

  game = {
    listRevenue: async (form: any) => {
      const response = await this.#client.get<
        ToolkitResponse<
          MediationDailyRevenueGroup[],
          PaginationMeta & {
            mediations: MediationPresenter[]
            aggregation: MediationDailyRevenueGroup
            aggregationAttributes: MediationAggregationPresenter[]
          }
        >
      >(`/dash/games/${form.storeId}/revenues`, {
        params: {
          _format: 'json',
          ...form,
        },
      })

      return response.data
    },

    updatePerformanceSetting: async (gameId: Game['id'], form: any) => {
      const response = await this.#client.put<ToolkitResponse<GamePerformanceSetting>>(
        routes.dash.gamePerformanceSettings.update(gameId),
        form,
        {
          params: { _format: 'json' },
        }
      )

      return response.data
    },

    updateReview: async (gameId: Game['id'], date: string, form: any) => {
      const response = await this.#client.put<ToolkitResponse<GameReview>>(
        routes.dash.gameReviews.update(gameId, date),
        form,
        {
          params: { _format: 'json' },
        }
      )

      return response.data
    },

    updateGameMetric: async (gameId: Game['id'], date: any, form: any) => {
      const response = await this.#client.put<ToolkitResponse<GameMetric>>(
        routes.dash.gameMetrics.update(gameId, date),
        form,
        {
          params: { _format: 'json' },
        }
      )

      return response.data
    },
  }

  dashboard = {
    listTeamRevenue: async (form: any) => {
      const response = await this.#client.get<
        ToolkitResponse<
          SubjectMetric[],
          PaginationMeta & {
            breakdowns: SubjectMetric[]
            subjects: SubjectPresenter[]
            aggregation: {
              data: SubjectMetric
              breakdown: SubjectMetric[]
            }
          }
        >
      >(routes.dash.teamRevenues.index(), {
        params: {
          _format: 'json',
          ...form,
        },
      })

      return response.data
    },

    createNotification: async (form: any) => {
      const response = await this.#client.post<ToolkitResponse<DashboardNotification>>(
        `/dash/notifications`,
        form,
        {
          params: {
            _format: 'json',
          },
        }
      )

      return response.data
    },

    updateNotification: async (id: DashboardNotification['id'], form: any) => {
      const response = await this.#client.put<ToolkitResponse<DashboardNotification>>(
        `/dash/notifications/${id}`,
        form,
        {
          params: {
            _format: 'json',
          },
        }
      )

      return response.data
    },
  }

  system = {
    listTeam: async (form: any = {}) => {
      const response = await this.#client.get<ToolkitPaginationResponse<Team>>(
        routes.system.teams.index(),
        {
          params: {
            _format: 'json',
            ...form,
          },
        }
      )

      return response.data
    },

    getTeam: async (id: number) => {
      const response = await this.#client.get<ToolkitResponse<Team>>(routes.system.teams.show(id), {
        params: {
          _format: 'json',
        },
      })

      return response.data
    },

    updateTeam: async (id: number, form: any) => {
      const response = await this.#client.put<ToolkitResponse<Team>>(
        routes.system.teams.update(id),
        form,
        {
          params: {
            _format: 'json',
          },
        }
      )

      return response.data
    },

    createTeam: async (form: any) => {
      const response = await this.#client.post<ToolkitResponse<Team>>(
        routes.system.teams.store(),
        form,
        {
          params: {
            _format: 'json',
          },
        }
      )

      return response.data
    },

    deleteTeam: async (id: number, form: any) => {
      const response = await this.#client.delete<ToolkitResponse<Team>>(
        routes.system.teams.destroy(id),
        {
          params: {
            _format: 'json',
          },
          data: form,
        }
      )

      return response.data
    },

    updateConfigMaps: async (form: any) => {
      const response = await this.#client.patch<ToolkitResponse<null>>(
        routes.system.configmaps.update(),
        form,
        {
          params: { _format: 'json' },
        }
      )

      return response.data
    },

    listUser: async (form: any) => {
      const response = await this.#client.get<ToolkitPaginationResponse<User>>(`/system/users`, {
        params: {
          _format: 'json',
          ...form,
        },
      })

      return response.data
    },

    deleteUser: async (id: string, form: any) => {
      await this.#client.delete(`/system/users/${id}`, {
        data: form,
        params: {
          _format: 'json',
        },
      })

      return { id }
    },

    createUser: async (form: any) => {
      const response = await this.#client.post<ToolkitResponse<User>>(`/system/users`, form, {
        params: {
          _format: 'json',
        },
      })

      return response.data
    },
  }

  publish = {
    listGameReleaseProposal: async (form: any) => {
      const response = await this.#client.get<
        ToolkitResponse<
          GameReleaseProposal[],
          PaginationMeta & {
            publishers: Publisher[]
          }
        >
      >(routes.publising.proposals.index(), {
        params: {
          ...form,
          _format: 'json',
        },
      })

      return response.data
    },

    listGameReleaseApproval: async (proposalId: number, form: any) => {
      const response = await this.#client.get<
        ToolkitResponse<
          GameReleaseApproval[],
          PaginationMeta & {
            proposal: GameReleaseProposal
          }
        >
      >(routes.publising.approvals.index(proposalId), {
        params: {
          ...form,
          _format: 'json',
        },
      })

      return response.data
    },
  }

  me = {
    getReport: async (form: any) => {
      const response = await this.#client.get<ToolkitResponse<ReportPresenter>>(`/me/report`, {
        params: {
          _format: 'json',
          ...form,
        },
      })

      return response.data
    },

    getProfile: async () => {
      const response = await this.#client.get<ToolkitResponse<MePresenter>>(`/me`, {
        params: {
          _format: 'json',
        },
      })

      return response.data
    },

    updateProfile: async (form: any) => {
      const response = await this.#client.put<ToolkitResponse<User>>(
        routes.me.profile.update(),
        form,
        {
          params: {
            _format: 'json',
          },
        }
      )

      return response.data
    },
  }

  github = {
    updateAcls: async (data: any) => {
      await this.#client.request({
        url: routes.github.accessControls.update(),
        method: 'put',
        data,
        params: {
          _format: 'json',
        },
      })
    },

    updateTeams: async (data: any) => {
      await this.#client.request({
        url: routes.github.teams.update(),
        method: 'put',
        data,
        params: {
          _format: 'json',
        },
      })
    },

    updateProtectedBranchs: async (data: any) => {
      await this.#client.request({
        url: routes.github.protectedBranches.update(),
        method: 'put',
        data,
        params: {
          _format: 'json',
        },
      })
    },
  }
}

export const toolkitApi = new ToolkitApi()
