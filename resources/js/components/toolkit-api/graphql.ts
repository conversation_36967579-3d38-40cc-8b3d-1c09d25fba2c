import { TypedDocumentNode } from '@graphql-typed-document-node/core'
import { GraphQLClient } from 'graphql-request'
import {
  useQuery,
  QueryHookOptions,
  NoInfer,
  OperationVariables,
  useLazyQuery,
  MutationHookOptions,
  useMutation,
} from '@apollo/client'
import { useEffect } from 'react'

import { useSingleToast } from '../toast/use_single_toast'

export const graphQLClient = new GraphQLClient(window.location.origin + '/graphql', {
  credentials: 'same-origin',
})

export function useGraphql<TData, TVariables extends OperationVariables>(
  documentQuery: TypedDocumentNode<TData, TVariables>,
  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> = {}
) {
  const query = useQuery(documentQuery, options)

  const [toast] = useSingleToast()

  useEffect(() => {
    if (query.error) {
      toast(query.error.message, {
        position: 'top-right',
        type: 'error',
      })
    }
  }, [query.error])

  return query
}

export function useGraphqlMutation<TData, TVariables extends OperationVariables>(
  documentMutation: TypedDocumentNode<TData, TVariables>,
  options: MutationHookOptions<NoInfer<TData>, NoInfer<TVariables>> = {}
) {
  const [toast] = useSingleToast()

  const [mutate, mutation] = useMutation(documentMutation, {
    ...options,
    onCompleted: (...args) => {
      if (options.onCompleted) {
        options.onCompleted(...args)
      }

      toast('Success', {
        position: 'top-right',
        type: 'success',
      })
    },
  })

  useEffect(() => {
    if (mutation.error) {
      toast(mutation.error.message, {
        position: 'top-right',
        type: 'error',
      })
    }
  }, [mutation.error])

  return [mutate, mutation] as const
}

export function useLazyGraphql<TData, TVariables extends OperationVariables>(
  documentQuery: TypedDocumentNode<TData, TVariables>,
  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> = {}
) {
  const query = useLazyQuery(documentQuery, options)

  const [toast] = useSingleToast()

  useEffect(() => {
    if (query[1].error) {
      toast(query[1].error.message, {
        position: 'top-right',
        type: 'error',
      })
    }
  }, [query[1].error])

  return query
}
