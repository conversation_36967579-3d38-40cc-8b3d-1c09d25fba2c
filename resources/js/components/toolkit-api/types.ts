// @ts-expect-error
import type * as x from '#controllers/dashboard/game/mediation_presenter'
// @ts-expect-error
import type * as y from '#models/game_metric'
// @ts-expect-error
import type * as z from '#controllers/dashboard/game_daily_metric_presenter'
import GameMetric, { SubjectMetric } from '#models/game_metric'

declare module '#models/game_metric' {
  export default interface GameMetric {
    autogen?: boolean
    hierarchy?: string[]
  }
}

declare module '#controllers/dashboard/game/mediation_presenter' {
  export interface MediationDailyRevenueGroup {
    'autogen'?: boolean
    'hierarchy'?: string[]
    'override.tuning': number
    'total': {
      grossRevenue: number
      netRevenue: number
      bannerRevenueRate: number
      bannerRevenue: number
      interRevenueRate: number
      interRevenue: number
      aoaRevenueRate: number
      aoaRevenue: number
      rewardRevenueRate: number
      rewardRevenue: number
      mrecRevenueRate: number
      mrecRevenue: number
      audioRevenue: number
      audioRevenueRate: number
    }
  }
}

export interface DailyMetricGroup {
  date: string
  metrics: GameMetric[]
  autogen?: boolean
  hierarchy?: string[]
}

export interface DailySubjectMetricGroup {
  date: string
  metrics: SubjectMetric[]
  autogen?: boolean
  hierarchy?: string[]
}
