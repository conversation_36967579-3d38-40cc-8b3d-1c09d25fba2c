import {
  DefaultError,
  useInfiniteQuery,
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query'
import { groupBy, last, orderBy, toPairs, uniq } from 'lodash-es'
import { ModelAttributes } from '@adonisjs/lucid/types/model'
import { useEffect } from 'react'

import { useMutationToast, UseMutationToastOptions, useQueryToast } from '../toast'
import Team from '#models/team'
import { ToolPermission } from '#models/user'
import { useCsrf } from '../csrf'
import { ConfigMapScope, GameReleaseProposalStatus } from '#config/enums'
import GameReleaseProposal from '#models/game_release_proposal'
import { TeamCreateRequestData, TeamUpdateRequestData } from '#controllers/system/teams_controller'
import GameMetric, { SubjectMetric } from '#models/game_metric'
import Game from '#models/game'
import { GamePerformanceCriteria } from '#models/game_performance_setting'
import { isLoading } from '../loading'
import GameMetricMetadatum from '#models/game_metric_metadatum'
import GameMetricOverride from '#models/game_metric_override'

import { toolkitApi } from './client'
import { autoFillDate } from './utils'
import { DailyMetricGroup, DailySubjectMetricGroup } from './types'

type QueryApiOptions = Omit<UseQueryOptions, 'queryFn'>

type QueryOptions = Omit<UseQueryOptions, 'queryFn' | 'queryKey'>

type MutationApiOptions = Omit<
  UseMutationOptions,
  'mutationFn' | 'onSuccess' | 'onMutate' | 'onSettled' | 'onError'
>

type MutationOptions = Omit<
  UseMutationOptions,
  'queryKey' | 'onSuccess' | 'mutationFn' | 'onMutate' | 'onSettled' | 'onError'
>

// @ts-ignore - this is a hack to get the return type of a function
export type QueryData<T> = NonNullable<ReturnType<T>['data']>

export function useQueryApi<T>(options: QueryApiOptions & { queryFn: () => Promise<T> }) {
  const query = useQuery(options)

  useQueryToast(query)

  return query as UseQueryResult<T>
}

export function useMutationApi<T extends {}, R, TError = DefaultError, TContext = unknown>(
  options: MutationApiOptions & {
    mutationFn: (data: T) => Promise<R>
    onSuccess?: (res: R, form: T, context: any) => void
    onMutate?: (variables: T) => Promise<TContext | undefined> | TContext | undefined
    onError?: (
      error: TError,
      variables: T,
      context: TContext | undefined
    ) => Promise<unknown> | unknown
    onSettled?: (
      data: R | undefined,
      error: TError | null,
      variables: T,
      context: TContext | undefined
    ) => Promise<unknown> | unknown
  },
  toastOptions: UseMutationToastOptions = {}
) {
  // @ts-ignore
  const mutation = useMutation(options)

  // @ts-ignore
  useMutationToast(mutation, toastOptions)

  return mutation
}

export function useGetProfile(options: QueryOptions = {}) {
  return useQueryApi({
    ...options,
    queryKey: ['profile'],
    queryFn: () => toolkitApi.me.getProfile().then((res) => res.data),
  })
}

export function useUpdateProfile(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi({
    ...options,
    mutationKey: ['me', 'profile'],
    mutationFn: (form: { password: string; passwordConfirmation: string }) =>
      toolkitApi.me.updateProfile({ ...form, ...csrf.getField() }).then((res) => res.data),
  })
}

export function useListTeam(options: QueryOptions = {}) {
  return useQueryApi({
    ...options,
    queryKey: ['dashboard', 'team', 'list'],
    queryFn: async () => {
      return toolkitApi.system.listTeam({ perPage: 999_999 }).then((r) => r.data)
    },
  })
}

export function useGetTeam(id: number, options: QueryOptions = {}) {
  return useQueryApi({
    ...options,
    queryKey: ['dashboard', 'team', 'get', id],
    queryFn: () => toolkitApi.system.getTeam(id).then((r) => r.data),
  })
}

export function useUpdateTeam(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi({
    mutationFn: async (data: TeamUpdateRequestData & { id: number }) => {
      return await toolkitApi.system.updateTeam(data.id, {
        ...data,
        memberEmails: uniq(
          [data.leaderEmail, ...data.memberEmails].filter((e): e is string => Boolean(e))
        ),
        ...csrf.getField(),
      })
    },
    mutationKey: ['team', 'update'],
    ...options,
  })
}

export function useCreateTeam(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi({
    mutationFn: async (data: TeamCreateRequestData) => {
      return await toolkitApi.system.createTeam({
        ...data,
        memberEmails: uniq(
          [data.leaderEmail, ...data.memberEmails].filter((e): e is string => Boolean(e))
        ),
        ...csrf.getField(),
      })
    },
    mutationKey: ['team', 'create'],
    ...options,
  })
}

export function useDeleteTeam(options: MutationOptions = {}) {
  const queryClient = useQueryClient()

  return useMutationApi(
    {
      mutationKey: ['dashboard', 'team', 'delete'],
      mutationFn: ({ id, ...form }: { id: number }) => {
        return toolkitApi.system.deleteTeam(id, form)
      },
      onSuccess: (_, { id }) => {
        queryClient.setQueryData(
          ['dashboard', 'team', 'list'],
          (ts: Awaited<ReturnType<typeof toolkitApi.system.listTeam>>['data']) => {
            return ts.filter((team: Team) => team.id !== id)
          }
        )
      },
      ...options,
    },
    {
      successMessage: 'Team deleted',
    }
  )
}

export function useListUser(
  form: Partial<{ page: number; perPage: number }>,
  options: QueryOptions = {}
) {
  return useQueryApi({
    ...options,
    queryKey: ['system', 'user', 'list'],
    queryFn: async () => {
      const response = await toolkitApi.system.listUser(form)

      return response.data
    },
  })
}

export function useDeleteUser() {
  const queryClient = useQueryClient()
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['system', 'user', 'delete'],
    mutationFn: (id: string) => toolkitApi.system.deleteUser(id, csrf.getField()),
    onSuccess: (_, id) => {
      queryClient.setQueryData(
        ['system', 'user', 'list'],
        (users: Awaited<ReturnType<typeof toolkitApi.system.listUser>>['data']) => {
          return users.filter((user) => user.id !== (id as unknown as string))
        }
      )
    },
  })
}

export function useCreateUser(options: MutationOptions = {}) {
  const queryClient = useQueryClient()
  const csrf = useCsrf()

  return useMutationApi({
    ...options,
    mutationKey: ['system', 'user', 'create'],
    mutationFn: (form: {
      email: string
      fullName: string
      password?: string
      toolPermissions: ToolPermission[]
    }) =>
      toolkitApi.system
        .createUser({
          ...form,
          ...csrf.getField(),
        })
        .then((r) => r.data),
    onSuccess: (res) => {
      const data = res as Awaited<ReturnType<typeof toolkitApi.system.createUser>>['data']
      queryClient.setQueryData(
        ['system', 'user', 'list'],
        (users: Awaited<ReturnType<typeof toolkitApi.system.listUser>>['data']) => {
          return [data, ...users]
        }
      )
    },
  })
}

export function useUpdateUser(options: MutationOptions = {}) {
  const queryClient = useQueryClient()
  const csrf = useCsrf()

  return useMutationApi({
    ...options,
    mutationKey: ['system', 'user', 'update'],
    mutationFn: ({
      id,
      ...form
    }: {
      id: string
      password?: string
      toolPermissions: ToolPermission[]
    }) => toolkitApi.updateUser(id, { ...form, ...csrf.getField() }).then((r) => r.data),
    onSuccess: (res) => {
      const data = res as Awaited<ReturnType<typeof toolkitApi.updateUser>>['data']
      queryClient.setQueryData(
        ['system', 'user', 'list'],
        (users: Awaited<ReturnType<typeof toolkitApi.system.listUser>>['data']) => {
          return users.map((user) => {
            if (user.id === data.id) {
              return data
            }

            return user
          })
        }
      )
    },
  })
}

export function useUpdateConfigMaps(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi(
    {
      ...options,
      mutationKey: ['configmaps', 'update'],
      mutationFn: (form: { scopes: ConfigMapScope[] }) => {
        return toolkitApi.system.updateConfigMaps({ ...form, ...csrf.getField() })
      },
    },
    {
      successMessage: 'You will get notified when new config map is available',
    }
  )
}

export function useListGameRevenue(
  form: Partial<{
    page: number
    from: string
    to: string
    direction: 'asc' | 'desc'
    teamId: string
    memberEmail: string
    sort: string
    date: string
  }>,
  options: QueryOptions = {}
) {
  return useQueryApi({
    ...options,
    queryKey: ['dashboard', 'game_revenues', 'list', JSON.stringify(form)],
    queryFn: async () => {
      const {
        meta: {
          breakdowns,
          aggregation: { breakdown, ...aggregation },
          ...meta
        },
        ...response
      } = await toolkitApi.listGameRevenue({
        perPage: 30,
        ...form,
      })

      const orderDirection = form.direction || 'desc'

      const dailies = orderBy(
        toPairs(groupBy(breakdowns, 'date')).map(([date, metrics]): DailyMetricGroup => {
          return {
            date,
            metrics,
          }
        }),
        (g) => g.date,
        orderDirection
      )

      return {
        ...response,
        meta: {
          ...meta,
          dailies: autoFillDate(
            dailies,
            (attrs) =>
              ({
                ...attrs,
                date: attrs.date.format('YYYY-MM-DD') as any,
                metrics: [],
              }) as DailyMetricGroup,
            orderDirection
          ),
          aggregation: {
            ...aggregation,
            dailies: autoFillDate(
              orderBy(breakdown, 'date', orderDirection),
              (attrs) =>
                ({
                  ...attrs,
                  date: attrs.date.format('YYYY-MM-DD') as any,
                  cost: 0,
                  revenue: 0,
                  profit: 0,
                  externalProfit: 0,
                  internalProfit: 0,
                }) as GameMetric,
              orderDirection
            ),
          },
        },
      }
    },
  })
}

export function useListTeamRevenue(
  form: Partial<{
    page: number
    perPage: number
    orderDirection: 'asc' | 'desc'
    from: string
    to: string
    teamId: number
    roleId: string
    groupBy: string
  }>,
  options: QueryOptions = {}
) {
  return useQueryApi({
    ...options,
    queryKey: ['dashboard', 'team_revenues', 'list'],
    queryFn: async () => {
      const {
        meta: {
          breakdowns,
          aggregation: { breakdown, ...aggregation },
          ...meta
        },
        ...response
      } = await toolkitApi.dashboard.listTeamRevenue({
        perPage: 30,
        ...form,
      })

      const dailies = orderBy(
        toPairs(groupBy(breakdowns, 'date')).map(
          ([date, ms]): DailySubjectMetricGroup => ({
            date,
            metrics: ms,
          })
        ),
        'date',
        form.orderDirection || 'desc'
      )

      return {
        ...response,
        meta: {
          ...meta,
          dailies: autoFillDate(
            dailies,
            (attrs) => ({
              ...attrs,
              date: attrs.date.format('YYYY-MM-DD') as any,
              metrics: [],
            }),
            form.orderDirection || 'desc'
          ),
          aggregation: {
            ...aggregation,
            dailies: autoFillDate(
              breakdown,
              (attrs) =>
                ({
                  ...attrs,
                  date: attrs.date.format('YYYY-MM-DD') as any,
                  externalProfit: 0,
                  internalProfit: 0,
                  cost: 0,
                  profit: 0,
                  revenue: 0,
                }) as SubjectMetric,
              form.orderDirection || 'desc'
            ),
          },
        },
      }
    },
  })
}

export function useListReleaseProposal(
  form: Partial<{ page: number; perPage: number; status: GameReleaseProposalStatus[] }>,
  options: QueryOptions = {}
) {
  return useQueryApi({
    ...options,
    queryKey: ['publish', 'release_proposals', 'list'],
    queryFn: async () => toolkitApi.publish.listGameReleaseProposal(form),
  })
}

export function useApproveReleaseProposal() {
  const csrf = useCsrf()
  const queryClient = useQueryClient()

  return useMutationApi({
    mutationKey: ['publish', 'release_approvals'],
    mutationFn: (form: {
      proposalId: number
      publisherId: string
      status: GameReleaseProposalStatus
    }) =>
      toolkitApi.approveProposal({
        ...form,
        ...csrf.getField(),
      }),
    onSuccess: (res) => {
      const data = res as Awaited<ReturnType<typeof toolkitApi.approveProposal>>

      queryClient.setQueryData(
        ['publish', 'release_proposals', 'list'],
        (cache: Awaited<ReturnType<typeof toolkitApi.publish.listGameReleaseProposal>>) => {
          return {
            ...cache,
            data: cache.data.map((proposal) => {
              console.log(proposal, data)
              if (proposal.id === data.meta.proposal.id) {
                return data.meta.proposal
              }

              return proposal
            }),
          }
        }
      )
    },
  })
}

export function useListReleaseApproval(
  proposalId: GameReleaseProposal['id'],
  form: Partial<{ perPage: number; page: number }>
) {
  return useQueryApi({
    queryKey: ['publish', 'release_approvals', 'list', proposalId],
    queryFn: async () => toolkitApi.publish.listGameReleaseApproval(proposalId, form),
  })
}

export function useUpdateGamePerformanceSetting<TError = DefaultError, TContext = unknown>(
  options: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof toolkitApi.game.updatePerformanceSetting>>,
      {
        gameId: Game['storeId']
        criterias: ModelAttributes<GamePerformanceCriteria>[]
      },
      TError,
      TContext
    >,
    'mutationKey' | 'mutationFn'
  >
) {
  const csrf = useCsrf()

  return useMutationApi({
    ...(options as any),
    mutationKey: ['dash', 'game', 'performance_setting', 'update'],
    mutationFn: (form: {
      gameId: Game['storeId']
      criterias: ModelAttributes<GamePerformanceCriteria>[]
    }) => toolkitApi.game.updatePerformanceSetting(form.gameId, { ...form, ...csrf.getField() }),
  })
}

export function useUpdateGameReview(options: MutationOptions = {}) {
  const csrf = useCsrf()
  return useMutationApi({
    ...options,
    mutationKey: ['dash', 'game_review', 'update'],
    mutationFn: ({
      gameId,
      date,
      ...form
    }: {
      gameId: string
      date: string
      productNote?: string
      marketingNote?: string
    }) => {
      return toolkitApi.game.updateReview(gameId, date, {
        ...csrf.getField(),
        ...form,
      })
    },
  })
}

export function useListRole(storeIds: Array<Game['id']>, options: { enabled?: boolean } = {}) {
  const perPage = 25
  const getPagedStoreIds = (page: number) => storeIds.slice((page - 1) * perPage, page * perPage)
  const lastPage = Math.ceil(storeIds.length / perPage)
  // @ts-ignore
  const query = useInfiniteQuery({
    queryKey: ['dash', 'game_metrics', 'roles'],
    queryFn: async ({ pageParam }) => {
      console.log('fetch page', pageParam)
      return toolkitApi.listRole(getPagedStoreIds(pageParam)).then((r) => r.data)
    },
    initialPageParam: 1,
    getNextPageParam: (_, _2, lastPageParam) => {
      return lastPageParam + 1
    },
    getPreviousPageParam: (_, _2, lastPageParam) => lastPageParam - 1,
    maxPages: lastPage,
    ...options,
  })

  useEffect(() => {
    const loading = isLoading([
      query.isFetching,
      query.isLoading,
      query.isFetchingNextPage,
      query.isRefetching,
      query.isPending,
    ])

    if (loading) {
      return
    }

    console.log(query.data?.pageParams, query.hasNextPage)

    const page = last(query.data?.pageParams) as number

    if (page < lastPage) {
      query.fetchNextPage()
    }
  }, [
    query.data?.pageParams,
    lastPage,
    query.isFetching,
    query.isLoading,
    query.isFetchingNextPage,
  ])

  return query
}

export function useUpdateGitHubAcl() {
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['github', 'acl'],
    mutationFn: (ids: string[]) =>
      toolkitApi.github.updateAcls({
        ids,
        ...csrf.getField(),
      }),
  })
}

export function useUpdateGitHubTeam() {
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['github', 'team'],
    mutationFn: (names: string[]) =>
      toolkitApi.github.updateTeams({
        names,
        ...csrf.getField(),
      }),
  })
}

export function useUpdateGitHubProtectedBranch() {
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['github', 'protected_branch'],
    mutationFn: (repositories: string[]) =>
      toolkitApi.github.updateProtectedBranchs({
        repos: repositories,
        ...csrf.getField(),
      }),
  })
}

export function useUpdateGameMetric(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['dashboard', 'game_metrics', 'update'],
    mutationFn: async (data: {
      date: any
      gameId: string
      metadata: Partial<ModelAttributes<GameMetricMetadatum>>
      override: Partial<ModelAttributes<GameMetricOverride>>
    }) => {
      return toolkitApi.game
        .updateGameMetric(data.gameId, data.date, {
          ...data,
          ...csrf.getField(),
        })
        .then((r) => r.data)
    },
    ...options,
  })
}

export function useUpdateDashboardNotification(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['dashboard', 'notification', 'update'],
    mutationFn: (data: {
      id: number
      isVisible?: boolean
      isPinned?: boolean
      message?: string
    }) => {
      return toolkitApi.dashboard.updateNotification(data.id, { ...data, ...csrf.getField() })
    },
    ...options,
  })
}

export function useCreateDashboardNotification(options: MutationOptions = {}) {
  const csrf = useCsrf()

  return useMutationApi({
    mutationKey: ['dashboard', 'notification', 'create'],
    mutationFn: (data: { message: string }) => {
      return toolkitApi.dashboard.createNotification({
        ...data,
        ...csrf.getField(),
      })
    },
    ...options,
  })
}
