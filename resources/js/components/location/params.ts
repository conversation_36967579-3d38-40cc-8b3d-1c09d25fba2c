import queryString from 'query-string'
import { useCallback, useMemo } from 'react'
import { AnySchema, mixed } from 'yup'
import { create } from 'zustand'

import '@/components/yup/params.js'
import { PaginationState } from '../pagination/index.jsx'
import { PageInfoAttributesFragment } from '@/graphql.js'

const defaultSchema = mixed()

export function stringifyUrl(url: string, query: Record<string, any>) {
  return queryString.stringifyUrl({
    url,
    query: stringifyQuery(query),
  })
}

export function stringifyQuery(query: Record<string, any>) {
  return Object.keys(query).reduce((acc, key) => {
    if (typeof query[key] === 'object') {
      try {
        acc[key] = JSON.stringify(query[key] as any)
      } catch (err) {
        console.debug(`Failed to parse query param ${key}: ${err}`)
        acc[key] = query[key]
      }
    } else {
      acc[key] = query[key]
    }

    return acc
  }, {} as any)
}

export function parseQuery(query: Record<string, any>) {
  return Object.keys(query).reduce((acc, key) => {
    try {
      acc[key] = JSON.parse(decodeURIComponent(query[key]))
    } catch (err) {
      console.debug(`Failed to parse query param ${key}: ${err}`)
      acc[key] = query[key]
    }
    return acc
  }, {} as any)
}

export function createParamsStore<T extends Record<string, any> = {}>(
  template: string = '',
  schema: RouteParamsSchema<T>
) {
  const parseSchema = schema.parse || defaultSchema
  const stringifySchema = schema.stringify || defaultSchema

  const parseParamsFromUrl = () => {
    const qs = parseQuery(queryString.parse(window.location.search) || {})
    if (!template) {
      return parseSchema.cast(qs)
    }

    const pathVariables = template
      .split('/')
      .map((e, i) => ({
        isVariable: e.startsWith(':'),
        index: i,
        name: e.replace(':', ''),
      }))
      .filter((e) => e.isVariable)

    const pathnameValues = window.location.pathname.split('/')

    const pathParams = Object.fromEntries(
      pathVariables.map((e) => [e.name, pathnameValues[e.index]])
    )

    return parseSchema.cast({ ...pathParams, ...qs })
  }

  const params = parseParamsFromUrl() as T

  const useParamsStore = create<{
    params: T
    setParams: (params: Partial<T>, options?: { replace?: boolean }) => void
  }>((set) => ({
    params,
    setParams: (p: Partial<T>) => {
      set((state) => {
        const newParams = { ...state.params, ...p }
        const stringified = stringifySchema.cast(newParams)

        window.history.pushState(
          {},
          document.title,
          stringifyUrl(window.location.pathname, stringified)
        )

        return { params: newParams }
      })
    },
  }))

  return {
    useSetParams: () => useParamsStore((s) => s.setParams),
    useParam: <U>(selector: (params: T) => U) => useParamsStore((s) => selector(s.params)),
    useParams: () => useParamsStore((s) => s.params),
    usePagination: (meta?: PageInfoAttributesFragment): PaginationState => {
      const page = useParamsStore((s) => Number(s.params['page'] ?? 0))
      const perPage = useParamsStore((s) => Number(s.params['perPage'] ?? 0))
      const totalPage = meta?.lastPage ?? 1
      const total = meta?.total ?? totalPage * perPage

      const setParams = useParamsStore((s) => s.setParams)

      const toPage = useCallback(
        (value: number) => {
          setParams({ page: value } as any)
        },
        [setParams]
      )
      const toNextPage = useCallback(() => toPage(page + 1), [page, toPage])
      const toPreviousPage = useCallback(() => toPage(page - 1), [page, toPage])

      return {
        page,
        perPage,
        isFirstPage: page === 1,
        isLastPage: page === totalPage,
        total,
        totalPage,
        toNextPage,
        toPreviousPage,
        toPage,
        setFromMeta: () => {},
      }
    },
  }
}

export interface RouteParamsSchema<T extends Record<string, any>> {
  parse?: AnySchema<T>
  stringify?: AnySchema
}

/**
 * @deprecated
 */
export function useParams<T>(template: string = ''): T {
  const qs = useMemo(() => queryString.parse(window.location.search), [])

  const params = useMemo(() => {
    if (!template) {
      return qs as T
    }

    const pathVariables = template
      .split('/')
      .map((e, i) => ({
        isVariable: e.startsWith(':'),
        index: i,
        name: e.replace(':', ''),
      }))
      .filter((e) => e.isVariable)

    const pathnameValues = window.location.pathname.split('/')

    return Object.assign(
      Object.fromEntries(pathVariables.map((e) => [e.name, pathnameValues[e.index]])),
      qs
    )
  }, [template, qs])

  return params as T
}
