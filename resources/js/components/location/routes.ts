import { formatString } from '#utils/route'

const createRoutes = <T extends Record<string, string>>(
  routes: T
): Record<keyof T, (...args: any[]) => string> => {
  return new Proxy(routes, {
    get: function (target, prop: string) {
      return function (...args: any[]) {
        return formatString(target[prop], ...args)
      }
    },
  }) as any
}

export const routes = {
  dash: {
    root: '/dash/',
    gameStudioMetrics: createRoutes({
      index: '/dash/game_studio_metrics',
    }),
    revenueReports: createRoutes({
      index: '/dash/revenue_reports',
    }),
    gameMetrics: createRoutes({
      index: `/dash/games/{0}/metrics`,
      update: `/dash/games/{0}/metrics/{1}`,
    }),
    gameMetricsNext: createRoutes({
      index: '/dash/games/{0}/metrics-next',
    }),
    games: createRoutes({
      overview: '/dash/games/{0}/overview/{1}',
      show: '/dash/games/{0}',
      index: '/dash/games',
      revenues: '/dash/games/{0}/revenues',
      updateRevenue: '/dash/games/{0}/revenues/{1}',
      productMetrics: '/dash/games/{0}/product_metrics',
      manageRoles: '/dash/games/{0}/roles',
      costs: '/dash/games/{0}/costs',
      levelDrops: '/dash/games/{0}/level_drops',
      creativeMetrics: '/dash/games/{0}/creative_metrics',
      campaignMetrics: '/dash/games/{0}/campaign_metrics',
      firebaseExperiments: '/dash/games/{0}/firebase-experiments',
      releaseMetrics: '/dash/games/{0}/release-metrics',
    }),
    acls: createRoutes({
      show: '/dash/acls/{0}',
      update: '/dash/acls/{0}',
    }),
    gameRevenues: createRoutes({
      index: '/dash/game_revenues',
    }),
    teamRevenues: createRoutes({
      index: '/dash/team_revenues',
    }),
    gameReviews: createRoutes({
      index: '/dash/games/{0}/review',
      update: '/dash/games/{0}/review/{1}',
    }),
    gamePerformanceSettings: createRoutes({
      update: '/dash/games/{0}/performance_settings/default',
    }),
  },
  home: '/',
  system: {
    teams: createRoutes({
      index: '/system/teams',
      edit: '/system/teams/{0}/edit',
      update: '/system/teams/{0}',
      create: '/system/teams/create',
      store: '/system/teams',
      show: '/system/teams/{0}',
      destroy: '/system/teams/{0}',
    }),
    configmaps: createRoutes({
      update: '/configmaps',
      get: '/configmaps/{0}',
    }),
    users: createRoutes({
      index: '/system/users',
      update: '/system/users/{0}',
      simulate: '/system/users/{0}/simulate',
    }),
  },
  auth: {
    sessions: createRoutes({
      destroy: '/sessions',
      google: '/google/redirect',
      store: '/sessions',
    }),
  },
  publising: {
    proposals: createRoutes({
      store: '/game/release_proposals',
      create: '/game/release_proposals/create',
      index: '/game/release_proposals',
    }),
    approvals: createRoutes({
      index: '/game/release_proposals/{0}/approvals',
    }),
    uploads: createRoutes({
      // TODO: Fix
      show: 'https://toolkit-queue.miraistudio.games/queue/dashboard/queue/default/{0}',
    }),
  },
  me: {
    profile: createRoutes({
      update: '/me/profile/default',
      index: '/me/profile',
    }),
  },
  github: {
    accessControls: createRoutes({
      index: '/github/access_controls',
      update: '/github/access_controls/default',
    }),
    teams: createRoutes({
      update: '/github/teams/default',
    }),
    protectedBranches: createRoutes({
      index: '/github/protected_branches',
      update: '/github/protected_branches/default',
    }),
  },
  documents: createRoutes({
    changelog: '/changelog',
  }),
  partner: {
    games: createRoutes({
      metrics: '/partner/games/{0}/metrics',
      index: '/partner/games',
    }),
  },
}
