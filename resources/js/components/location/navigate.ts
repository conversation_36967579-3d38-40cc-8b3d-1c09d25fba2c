import queryString from 'query-string'
import { object } from 'yup'

import '@/components/yup/params.js'

import { RouteParamsSchema, parseQuery, stringifyUrl } from './params'

export function useNavigate() {
  return { navigate, refresh }
}

export interface NavigateParams<T extends Record<string, any>> {
  query?: T
  schema?: RouteParamsSchema<T>
}

const defaultSchema = object()

export const makeUrl = <T extends Record<string, any>>(
  url: string,
  { query = {} as any, ...params }: NavigateParams<T>
) => {
  const schema = params.schema?.stringify || defaultSchema
  return stringifyUrl(url, schema.cast(query))
}

function navigate<T extends Record<string, any>>(
  url: string,
  { query = {} as any, ...params }: NavigateParams<T> = {}
) {
  window.location.href = makeUrl(url, { query, ...params })
}

function refresh<T extends Record<string, any>>(
  { query = {} as any, ...params }: NavigateParams<T> = {} as any
) {
  navigate(window.location.pathname, {
    ...params,
    query:
      Object.keys(query).length === 0
        ? {}
        : Object.assign(parseQuery(queryString.parse(window.location.search)), query),
  })
}
