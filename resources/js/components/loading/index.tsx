import { Box, CircularProgress } from '@mui/material'
import { ReactNode } from 'react'

export function isLoading<T>(loading: T | Array<any>) {
  return Array.isArray(loading) ? loading.some((v) => Boolean(v)) : <PERSON>olean(loading)
}

export function Loading({
  visible,
  align = 'center',
  children,
}: {
  visible: boolean | Array<any>
  align?: 'center' | 'left'
  children?: ReactNode
}) {
  const isVisible = isLoading(visible)

  return isVisible ? (
    <Box sx={{ textAlign: align }}>
      <CircularProgress />
    </Box>
  ) : (
    children
  )
}
