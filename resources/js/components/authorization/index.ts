import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'

import type { Policies } from '#policies/main'
import { toolkitApi } from '@/components/toolkit-api/index.js'
import { useCsrf } from '@/components/csrf/index.js'

export type AuthorizationPolicy<T extends keyof Policies> = {
  name: T
  action: PolicyAction<T>
  args: any[]
}

export type PolicyAction<T extends keyof Policies> = keyof InstanceType<
  Awaited<ReturnType<Policies[T]>>['default']
>

export function useAuthorization(policies: AuthorizationPolicy<any>[]) {
  const csrf = useCsrf()

  const { data: authorizations } = useQuery({
    queryKey: ['authorization', policies],
    queryFn: () => toolkitApi.authorize({ policies, ...csrf.getField() }).then((r) => r.data),
  })

  const authorizationMap = useMemo(() => {
    if (!authorizations) return new Map()

    return new Map(
      authorizations.map((authorization) => [authorization.policy, authorization.allowed])
    )
  }, [authorizations])

  const isAllowed = useCallback(
    <TPolicy extends keyof Policies>(policy: TPolicy, action: PolicyAction<TPolicy>) => {
      const key = `${policy}.${action as string}` as TPolicy
      return authorizationMap.has(key) ? authorizationMap.get(key) : false
    },
    [authorizationMap]
  )

  return [isAllowed] as const
}
