import { colors, SxProps } from '@mui/material'
import fastCartesian from 'fast-cartesian'

export const COLORS: Record<string, SxProps> = {
  ...Object.fromEntries(
    fastCartesian([
      [50, ...new Array(9).fill(0).map((_, i) => (i + 1) * 100)],
      ['red', 'green', 'blue', 'pink', 'purple', 'cyan', 'orange', 'yellow'],
    ]).flatMap(([shade, color]) => {
      return [
        [`& .bg-${color}-${shade}`, { bgcolor: (colors as any)[color][shade] }],
        [`& .text-${color}-${shade}`, { color: (colors as any)[color][shade] }],
      ]
    })
  ),
  ...Object.fromEntries(
    ['white'].flatMap((color) => {
      return [
        [`& .bg-${color}`, { bgcolor: color }],
        [`& .text-${color}`, { color }],
      ]
    })
  ),
}
