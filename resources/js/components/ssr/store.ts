import { SharedProps } from '@adonisjs/inertia/types'
import { createStore } from 'zustand'

export interface PageProps extends Record<string, any> {}

export type ServerProps<T extends PageProps = {}> = SharedProps & T

export const createServerPropsStore = <T extends PageProps>(props: ServerProps<T>) => {
  return createStore<ServerProps<T>>()((_set, _get) => {
    return {
      ...props,
    }
  })
}

export type ServerPropsStore<T extends PageProps> = ReturnType<typeof createServerPropsStore<T>>
