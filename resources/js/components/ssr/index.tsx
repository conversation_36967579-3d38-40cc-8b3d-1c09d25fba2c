// import queryString from 'query-string'
import queryString from 'query-string'
import { createContext, PropsWithChildren, useContext, useMemo, useRef } from 'react'
import { bool } from 'yup'
import { createServerPropsStore, PageProps, ServerProps, ServerPropsStore } from './store'
import { useStore } from 'zustand'

const schemas = {
  boolean: bool().default(false),
}

const ssrDataKeyToVal = new Map<string, any>()

export function useSSRData<T = string>(
  name: string,
  options: { defaultValue?: T; formName?: string } = {}
): T | undefined | null {
  options.formName ||= 'data'

  return useMemo(() => {
    const cacheKey = `${options.formName}__${name}`
    if (ssrDataKeyToVal.has(cacheKey)) {
      return ssrDataKeyToVal.get(cacheKey)
    }

    const element = document.querySelector(`form[name="${options.formName}"] input[name="${name}"]`)
    if (!element) {
      return options.defaultValue
    }

    const strValue = element.getAttribute('value') as string
    const schema = schemas[element.getAttribute('schema') as keyof typeof schemas]
    const value = schema.cast(strValue) as T

    ssrDataKeyToVal.set(`${options.formName}__${name}`, value)

    return value
  }, [name, options.formName, options.defaultValue])
}

export function useLayoutSSRData() {
  const canViewGithub = useSSRData('can.ToolPolicy.view:github', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canManageGithub = useSSRData('can.ToolPolicy.manage:github', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canManageDashboard = useSSRData('can.ToolPolicy.manage:dashboard', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canViewGameRevenue = useSSRData('can.DashboardGameRevenuePolicy.index', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canViewRevenueReport = useSSRData('can.DashboardRevenueReportPolicy.index', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canViewUser = useSSRData('can.UserPolicy.index', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canEditLandingPageDocument = useSSRData('can.LandingPageDocumentPolicy.edit', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canViewTeam = useSSRData('can.TeamPolicy.index', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canViewGame = useSSRData('can.GamePolicy.index', {
    defaultValue: false,
    formName: 'layout-data',
  })

  const canViewChangelog = useSSRData('can.ChangelogPolicy.index', {
    defaultValue: false,
    formName: 'layout-data',
  })

  return {
    canViewGithub,
    canManageGithub,
    canManageDashboard,
    canViewGameRevenue,
    canViewRevenueReport,
    canViewUser,
    canEditLandingPageDocument,
    canViewTeam,
    canViewGame,
    canViewChangelog,
  }
}

/**
 *
 * @deprecated prefer useNavigate from '@/components/location'
 */
export function useNavigate() {
  return { navigate }
}

/**
 *
 * @deprecated prefer useNavigate from '@/components/location'
 */
export function navigate(url: string, query = {}, keepQuery = false) {
  window.location.href = queryString.stringifyUrl({
    url: url || window.location.pathname,
    query: keepQuery ? Object.assign(queryString.parse(window.location.search), query) : query,
  })
}

/**
 *
 * @deprecated prefer useNavigate from '@/components/location'
 */
export function useClientNavigate() {
  return { clientNavigate }
}

/**
 *
 * @deprecated prefer useNavigate from '@/components/location'
 */
export function clientNavigate(url: string = '', query = {} as any, data = {}, keepQuery = true) {
  if (keepQuery) {
    query = Object.assign({}, queryString.parse(window.location.search), query)
  }

  Object.keys(query).forEach((k) => {
    try {
      query[k] = JSON.stringify(query[k] as any)
    } catch (err) {
      console.debug(`Failed to parse query param ${k}: ${err}`)
    }
  })

  window.history.pushState(
    data,
    document.title,
    queryString.stringifyUrl({
      url: url || window.location.pathname,
      query,
    })
  )
}

const ServerPropsContext = createContext({})

export function ServerPropsContextProvider<T extends PageProps>({
  children,
  value,
}: PropsWithChildren<{ value: ServerProps<T> }>) {
  const storeRef = useRef<ServerPropsStore<T>>(null)
  if (!storeRef.current) {
    storeRef.current = createServerPropsStore(value)
  }
  return (
    <ServerPropsContext.Provider value={storeRef.current}>{children}</ServerPropsContext.Provider>
  )
}

export function useServerPropsStore<T extends PageProps = {}>(): ServerPropsStore<T> {
  const context = useContext(ServerPropsContext)
  return context as any
}

export function useCurrentUser() {
  const store = useServerPropsStore()
  return useStore(store, (s) => s.user)
}

export function useCurrentUserPermissions() {
  const store = useServerPropsStore()
  return useStore(store, (s) => s.user)
}

export function useAbilities() {
  return useServerProp((s) => s.abilities)
}

export function useServerProp<T extends PageProps, U>(selector: (state: ServerProps<T>) => U) {
  const store = useServerPropsStore<T>()
  return useStore(store, selector) as U
}
