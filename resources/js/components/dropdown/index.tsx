import { Menu, MenuProps } from '@mui/material'
import { MouseEvent, useCallback, useMemo, useState } from 'react'
import { v4 } from 'uuid'

export function useDropdownControl() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const open = Boolean(anchorEl)

  const onOpen = useCallback(
    (event: MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget)
    },
    [setAnchorEl]
  )

  const id = useMemo(() => v4(), [])

  const onClose = useCallback(() => {
    setAnchorEl(null)
  }, [setAnchorEl])

  return {
    open,
    onOpen,
    onClose,
    anchorEl,
    id,
  }
}

export type UseDropdownControlReturn = ReturnType<typeof useDropdownControl>

export function Dropdown({
  control,
  fullWidth = false,
  // @ts-ignore
  slotProps: { paper: { sx: paperSx = {}, ...paperProps } = {}, ...slotProps } = {},
  sx = {},
  ...props
}: { control: UseDropdownControlReturn; fullWidth?: boolean } & Omit<
  MenuProps,
  'open' | 'id' | 'onClose' | 'anchorEl'
>) {
  const getPaperWidth = () => {
    if (!fullWidth || !control.anchorEl?.clientWidth) {
      return undefined
    }

    return `${control.anchorEl.clientWidth}px !important`
  }

  return (
    <Menu
      {...props}
      slotProps={{
        ...slotProps,
        paper: {
          ...paperProps,
          sx: {
            width: getPaperWidth(),
          },
        },
      }}
      sx={{ ...sx, width: fullWidth ? '100%' : undefined }}
      anchorEl={control.anchorEl}
      open={control.open}
      onClose={control.onClose}
      id={control.id}
    />
  )
}
