{"mediations": {"1": {"name": "Max"}, "2": {"name": "Admob"}, "3": {"name": "LevelPlay"}, "0": {"name": "Non Mediation"}}, "common": {"enum": {"android": "Android", "ios": "iOS"}, "btn": {"delete": "OK", "cancel": "Cancel"}, "sort": {"asc": "ASC", "desc": "DESC"}, "sidebar": {"main": {"group": {"2": "System", "3": "GitHub"}}, "configMap": {"dashboard": "Dashboard Config", "github": "GitHub & Release Config"}}}, "adTypeCategory": {"APP_OPEN": "AOA", "AUDIO": "Audio", "BANNER": "Banner", "COLLAPSIBLE_BANNER": "Collapsible Banner", "INTERSTITIAL": "Interstitial", "MREC": "MREC", "NATIVE": "Native", "REWARD": "<PERSON><PERSON>", "UNKNOWN": "Unknown"}, "team": {"listTitle": "All Teams", "new": "Create a new team", "deleteConfirmation": "Are you sure you want to delete this team?", "filter": {"datePlaceholder": "Select date", "rolePlaceholder": "Select role", "sortPlaceholder": "Sort by date", "sortLabel": "Sort"}}, "user": {"deleteConfirmation": "Are you sure you want to delete this user?"}, "releaseProposals": {"listTitle": "Publish Requests", "table": {"repository": "Repository"}, "status": {"-1": "Pending", "0": "Rejected", "1": "Approved"}}, "releaseApproval": {"releaseStatus": {"-1": "Pending", "0": "Error", "1": "InProgress", "2": "Completed"}}, "revenue": {"revenue": "Revenue", "cost": "Spend", "externalProfit": "PubProfit", "profit": "Profit"}, "gameReview": {"conclusion": {"-1": "Drop", "0": "Fail", "1": "Pass"}}, "gameMetric": {"date": "Date", "day": "Day", "paidInstalls": "<PERSON><PERSON>", "organicInstalls": "Organic Installs", "totalInstalls": "Total Installs", "organicPercentage": "% Organic", "cost": "Cost", "cpi": "CPI", "roas": "ROAS", "revenue": "Total Revenue", "profit": "Profit", "dailyActiveUsers": "DAU", "retentionRateDay1": "RR D-1", "retentionRateDay3": "RR D-3", "retentionRateDay7": "RR D-7", "bannerImpsDau": "Banner ImpsDAU", "interImpsDau": "Inter ImpsDAU", "rewardImpsDau": "<PERSON><PERSON>", "aoaImpsDau": "AOA ImpsDAU", "mrecImpsDau": "MREC ImpsDAU", "aoaAdmobImpsDau": "AOA Admob ImpsDAU", "collapseAdmobImpsDau": "Collapse Admob ImpsDAU", "nativeAdmobImpsDau": "Native Admob ImpsDAU", "adaptiveAdmobImpsDau": "Adaptive Admob ImpsDAU", "mrecAdmobImpsDau": "MREC Admob ImpsDAU", "averageSession": "AVG Sessions", "sessions": "AVG Sessions", "playtime": "Playtime (minutes)", "versionNote": "Version", "uaNote": "UA", "monetNote": "Monet", "productNote": "Product", "note": "Note", "teams": {"ua": "UA", "monet": "MO"}}, "gameMetricV2": {"date": "Date", "day": "Day", "paidInstalls": "<PERSON><PERSON>", "organicInstalls": "Organic Installs", "totalInstalls": "Total Installs", "organicPercentage": "% Organic", "cost": "Cost", "cpi": "CPI", "roas": "ROAS", "revenue": "Total Revenue", "profit": "Profit", "dailyActiveUsers": "DAU", "retentionRateDay1": "RR D-1", "retentionRateDay3": "RR D-3", "retentionRateDay7": "RR D-7", "sessions": "AVG Sessions", "averageSession": "AVG Session", "playtime": "Playtime (minutes)", "versionNote": "Version", "uaNote": "UA", "monetNote": "Monet", "productNote": "Product", "note": "Note"}, "gameRevenue": {"arpDau": "ARPDAU", "impsDau": "<PERSON><PERSON><PERSON><PERSON>", "ecpm": "eCPM", "revenue": "%Revenue"}, "campaignMetrics": {"adRevGrossAmount": "Revenue", "adRevGrossAmountPerActiveUser": "ARPDAU", "impressionCountPerActiveUser": "ImpsDAU", "sessionCountPerActiveUser": "AVG Sessions", "playtimeMsec": "Playtime (s)", "ad_adRevGrossAmountPerActiveUser": "ArpDAU", "ad_impressionCountPerActiveUser": "ImpsDAU", "ad_adRevGrossAmount": "Rev", "ad_impressionCount": "<PERSON><PERSON>", "adCostNonTaxAmount": "Cost (non-tax)", "playtimeNthDayMsecs": "Playtime D{{day}} (s)", "lifetimeNthDay": "LT D{{day}}", "lifetimeNthDayValues": "LTV D{{day}}", "impressionCount": "<PERSON><PERSON>", "impressionNthDayCounts": "Imps D{{day}}", "dailyActiveUserCount": "DAU", "clickCount": "<PERSON>licks", "installCount": "Installs", "cpi": "CPI", "ctr": "CTR", "cvr": "CVR", "cpc": "CPC", "ipm": "IPM", "roas": "ROAS", "activeUserCount": "Active Users", "activeUserNthDayCounts": "Active Users D{{day}}", "adRevNthDayGrossAmounts": "Revenue D{{day}}", "retentionRate": "RR", "retentionNthDayRates": "RR D{{day}}", "roasNthDayRates": "ROAS D{{day}}", "sessionCount": "Sessions", "sessionNthDayCounts": "Sessions D{{day}}", "metricColumns": "<PERSON><PERSON>", "countryCode": {"AF": "Afghanistan", "AL": "Albania", "DZ": "Algeria", "AD": "Andorra", "AO": "Angola", "AG": "Antigua and Barbuda", "AR": "Argentina", "AM": "Armenia", "AU": "Australia", "AT": "Austria", "AZ": "Azerbaijan", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesh", "BB": "Barbados", "BY": "Belarus", "BE": "Belgium", "BZ": "Belize", "BJ": "Benin", "BT": "Bhutan", "BO": "Bolivia", "BA": "Bosnia and Herzegovina", "BW": "Botswana", "BR": "Brazil", "BN": "Brunei", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Cambodia", "CM": "Cameroon", "CA": "Canada", "CF": "Central African Republic", "TD": "Chad", "CL": "Chile", "CN": "China", "CO": "Colombia", "KM": "Comoros", "CG": "Congo", "CD": "Congo, Democratic Republic of the", "CR": "Costa Rica", "HR": "Croatia", "CU": "Cuba", "CY": "Cyprus", "CZ": "Czech Republic", "DK": "Denmark", "DJ": "Djibouti", "DM": "Dominica", "DO": "Dominican Republic", "EC": "Ecuador", "EG": "Egypt", "SV": "El Salvador", "GQ": "Equatorial Guinea", "ER": "Eritrea", "EE": "Estonia", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Ethiopia", "FJ": "Fiji", "FI": "Finland", "FR": "France", "GA": "Gabon", "GM": "Gambia", "GE": "Georgia", "DE": "Germany", "GH": "Ghana", "GR": "Greece", "GD": "Grenada", "GT": "Guatemala", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haiti", "HN": "Honduras", "HU": "Hungary", "IS": "Iceland", "IN": "India", "ID": "Indonesia", "IR": "Iran", "IQ": "Iraq", "IE": "Ireland", "IL": "Israel", "IT": "Italy", "JM": "Jamaica", "JP": "Japan", "JO": "Jordan", "KZ": "Kazakhstan", "KE": "Kenya", "KI": "Kiribati", "KP": "North Korea", "KR": "South Korea", "KW": "Kuwait", "KG": "Kyrgyzstan", "LA": "Laos", "LV": "Latvia", "LB": "Lebanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libya", "LI": "Liechtenstein", "LT": "Lithuania", "LU": "Luxembourg", "MG": "Madagascar", "MW": "Malawi", "MY": "Malaysia", "MV": "Maldives", "ML": "Mali", "MT": "Malta", "MH": "Marshall Islands", "MR": "Mauritania", "MU": "Mauritius", "MX": "Mexico", "FM": "Micronesia", "MD": "Moldova", "MC": "Monaco", "MN": "Mongolia", "ME": "Montenegro", "MA": "Morocco", "MZ": "Mozambique", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NL": "Netherlands", "NZ": "New Zealand", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "MK": "North Macedonia", "NO": "Norway", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PA": "Panama", "PG": "Papua New Guinea", "PY": "Paraguay", "PE": "Peru", "PH": "Philippines", "PL": "Poland", "PT": "Portugal", "QA": "Qatar", "RO": "Romania", "RU": "Russia", "RW": "Rwanda", "KN": "Saint Kitts and Nevis", "LC": "Saint Lucia", "VC": "Saint Vincent and the Grenadines", "WS": "Samoa", "SM": "San Marino", "ST": "Sao Tome and Principe", "SA": "Saudi Arabia", "SN": "Senegal", "RS": "Serbia", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapore", "SK": "Slovakia", "SI": "Slovenia", "SB": "Solomon Islands", "SO": "Somalia", "ZA": "South Africa", "SS": "South Sudan", "ES": "Spain", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Suriname", "SE": "Sweden", "CH": "Switzerland", "SY": "Syria", "TJ": "Tajikistan", "TZ": "Tanzania", "TH": "Thailand", "TL": "Timor-Leste", "TG": "Togo", "TO": "Tonga", "TT": "Trinidad and Tobago", "TN": "Tunisia", "TR": "Turkey", "TM": "Turkmenistan", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ukraine", "AE": "United Arab Emirates", "GB": "United Kingdom", "US": "United States", "UY": "Uruguay", "UZ": "Uzbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "YE": "Yemen", "ZM": "Zambia", "ZW": "Zimbabwe"}}, "firebaseVersionVariants": {"name": {"std": "Standard", "0": "Baseline", "1": "A", "2": "B", "3": "C", "4": "D", "5": "E", "6": "F", "7": "G", "8": "H", "9": "I", "10": "J", "11": "K", "12": "L"}}, "firebaseMetric": {"activeUserCount": "Active Users", "sessionCount": "Sessions", "playtimeMsec": "Playtime (s)", "installCount": "Installs", "activeUserNthDayCounts": "DAU D{{day}}", "playtimeNthDayMsecs": "Playtime D{{day}} (s)", "sessionCountPerActiveUser": "Sessions / DAU", "sessionNthDayCounts": "Sessions D{{day}}", "impressionNthDayCounts": "Imps D{{day}}", "adRevNthDayGrossAmounts": "Revenue D{{day}}", "retentionNthDayRates": "RR D{{day}}", "retentionRate": "RR"}, "releaseMetric": {"installCount": "Installs", "dailyActiveUserCount": "DAU", "activeUserNthDayCounts": "Active Users D{{day}}", "sessionNthDayCounts": "Sessions D{{day}}", "adRevNthDayGrossAmounts": "Revenue D{{day}}", "retentionNthDayRates": "RR D{{day}}", "lifetimeNthDayValues": "LTV D{{day}}", "impressionNthDayCounts": "Imps D{{day}}", "impressionCount": "<PERSON><PERSON>", "roasNthDayRates": "ROAS D{{day}}", "adRevGrossAmount": "Rev", "adRevGrossAmountPerActiveUser": "ARPDAU", "impressionCountPerActiveUser": "ImpsDAU", "playtimeMsec": "Playtime (s)", "playtimeNthDayMsecs": "Playtime D{{day}} (s)", "activeUserCount": "Active Users", "sessionCount": "Sessions", "retentionRate": "RR", "sessionCountPerActiveUser": "Sessions / DAU"}, "firebaseAdMetric": {"adRevGrossAmount": "{{category}} Rev", "impressionCount": "{{category}} Imps", "adRevGrossAmountPerActiveUser": "{{category}} ARPDAU", "impressionCountPerActiveUser": "{{category}} ImpsDAU", "category": {"APP_OPEN": "AOA", "AUDIO": "Audio", "BANNER": "Banner", "COLLAPSIBLE_BANNER": "Collapsible Banner", "INTERSTITIAL": "Interstitial", "MREC": "MREC", "NATIVE": "Native", "REWARD": "<PERSON><PERSON>", "UNKNOWN": "Unknown"}}, "role": {"name": "{{group}} {{name}}"}, "sse": {"global/notifications": {"configMap.updated": {"title": "Config Map Updated", "message": "New config map available, please refresh the page"}}}, "metabaseChart": {"game-metric": {"attribute": {"date": "Date", "game_id": "Game ID", "time_grouping": "Time Grouping", "ad_type": "Ad Type"}, "enum": {"ad_type": {"banner": "Banner", "inter": "Interstitial", "reward": "<PERSON><PERSON>", "aoa": "AOA", "mrec": "MREC", "admob_aoa": "AOA Admob", "admob_collapse": "Collapse Admob", "admob_native": "Native Admob", "admob_adaptive": "Adaptive Admob", "admob_mrec": "MREC Admob", "audio": "Audio"}}}, "game-overview-ua": {"attribute": {"date": "Date", "time_grouping": "Time Grouping", "ad_type": "Ad Type"}, "enum": {"ad_type": {"admob_adaptive": "Admob Adaptive", "admob_aoa": "Admob AOA", "admob_collapse": "<PERSON><PERSON><PERSON>", "admob_mrec": "Admob MREC", "admob_native": "Admob Native", "aoa": "AOA", "audio": "Audio", "banner": "Banner", "inter": "Interstitial", "mrec": "MREC", "reward": "<PERSON><PERSON>"}}}, "game-overview-monet": {"attribute": {"date": "Date", "time_grouping": "Time Grouping", "ad_type": "Ad Type", "country": "Country", "network": "Network"}, "enum": {"ad_type": {"banner": "Banner", "interstitial": "Interstitial", "reward": "<PERSON><PERSON>", "native": "Native", "audio": "Audio", "app_open": "AOA", "collapsible_banner": "Collapsible Banner", "mrec": "MREC", "unknown": "Unknown"}, "network": {"appsflyer_admanager": "AdManager", "appsflyer_admob": "Admob", "appsflyer_admob_aoa": "Admob AOA", "appsflyer_admobnative": "Admob Native", "appsflyer_applovin": "Applovin", "appsflyer_applovin_exchange": "Applovin Exchange", "appsflyer_bidmachine": "BidMachine", "appsflyer_bigo_ads": "Bigo Ads", "appsflyer_bigo_ads_native": "Bigo Ads Native", "appsflyer_bigoadsnew": "Bigo Ads New", "appsflyer_dt_exchange": "DT Exchange", "appsflyer_facebook": "Facebook", "appsflyer_facebook_native": "Facebook Native", "appsflyer_fyber": "Fyber", "appsflyer_google_ad_manager": "Google AdManager", "appsflyer_google_ad_manager_native": "Google AdManager Native", "appsflyer_google_admob": "Google Admob", "appsflyer_google_admob_native": "Google Admob Native", "appsflyer_inmobi": "Inmobi", "appsflyer_ironsource": "IronSource", "appsflyer_ironsource_inter": "IronSource Inter", "appsflyer_ironsource_reward": "IronSource Reward", "appsflyer_kidoz": "<PERSON><PERSON>", "appsflyer_liftoff_monetize": "Liftoff Monetize", "appsflyer_maticoomediationadapter": "Maticoo Mediation Adapter", "appsflyer_max": "Max", "appsflyer_mintegral": "Mintegral", "appsflyer_moloco": "Moloco", "appsflyer_moloco_native": "Moloco Native", "appsflyer_mytarget": "<PERSON><PERSON><PERSON>get", "appsflyer_ogury": "O<PERSON><PERSON>", "appsflyer_pangle": "<PERSON><PERSON>", "appsflyer_pangle_native": "Pangle Native", "appsflyer_smaato": "S<PERSON><PERSON>", "appsflyer_unity_ads": "Unity Ads", "appsflyer_unityads": "Unity Ads", "appsflyer_verve": "Verve", "appsflyer_vungle": "<PERSON><PERSON><PERSON>", "appsflyer_yandex": "Yandex"}, "country": {"__": "Unknown"}}}}}