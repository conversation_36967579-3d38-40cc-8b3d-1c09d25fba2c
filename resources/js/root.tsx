import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'
import 'react-toastify/dist/ReactToastify.css'

import en from '@/translations/en.json'
import { createTheme, CssBaseline, ThemeProvider } from '@mui/material'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import i18next from 'i18next'
import { initReactI18next } from 'react-i18next'
import { LocalizationProvider } from '@mui/x-date-pickers-pro'
import { ApolloProvider } from '@apollo/client'
import { apolloClient } from './components/toolkit-api'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { ToastContainer } from 'react-toastify'
import { AdapterDayjs } from '@mui/x-date-pickers-pro/AdapterDayjs'
import dayjs from 'dayjs'
import { LicenseInfo } from '@mui/x-license'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import isLeapYear from 'dayjs/plugin/isLeapYear'
import minMax from 'dayjs/plugin/minMax'

dayjs.extend(quarterOfYear)
dayjs.extend(isLeapYear)
dayjs.extend(minMax)

LicenseInfo.setLicenseKey(
  'e0d9bb8070ce0054c9d9ecb6e82cb58fTz0wLEU9MzI0NzIxNDQwMDAwMDAsUz1wcmVtaXVtLExNPXBlcnBldHVhbCxLVj0y'
)

const queryClient = new QueryClient()

i18next.use(initReactI18next).init({
  resources: {
    en: {
      translation: en,
    },
  },
  lng: 'en',
  fallbackLng: 'en',

  interpolation: {
    escapeValue: false, // react already safes from xss => https://www.i18next.com/translation-function/interpolation#unescape
  },
})

const theme = createTheme({
  palette: {
    mode: 'light',
  },
})

const isProduction = import.meta.env.MODE === 'production'

export function ReactRoot({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <QueryClientProvider client={queryClient}>
          <ApolloProvider client={apolloClient}>
            {!isProduction && (
              <ReactQueryDevtools initialIsOpen={false} buttonPosition="bottom-left" />
            )}

            <ToastContainer />

            {children}
          </ApolloProvider>
        </QueryClientProvider>
      </LocalizationProvider>
    </ThemeProvider>
  )
}
