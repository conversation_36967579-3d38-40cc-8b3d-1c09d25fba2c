/// <reference types="vite/client" />

import React from 'react'
import ReactDOM from 'react-dom/client'
import 'react-toastify/dist/ReactToastify.css'

import { ReactRoot } from './root'
import { ServerPropsContextProvider } from './components/ssr'
import { ConfigMapProvider } from './next/components/sdk/config_map'

const serverProps = {
  abilities: {},
  user: {},
}

export function render(Component: React.FC) {
  ReactDOM.createRoot(document.getElementById('react-root')!).render(
    <ServerPropsContextProvider value={serverProps as any}>
      <ReactRoot>
        <Component />
        <ConfigMapProvider />
      </ReactRoot>
    </ServerPropsContextProvider>
  )
}
