import { graphql } from './gql'

export * from './gql'
export * from './gql/graphql'

graphql(`
  fragment UserPublicProfile on User {
    id
    fullName
    email
  }
`)

graphql(`
  fragment TeamAttributes on Team {
    id
    name
    roleId
  }
`)

graphql(`
  fragment GameAttributes on Game {
    id
    name
    packageName
    platform
    isInhouse
    isActive
  }
`)

graphql(`
  fragment GameMetricAttributes on GameMetric {
    id
    date
    gameId
  }
`)

graphql(`
  fragment GameMetricInstall on GameMetric {
    paidInstalls
    organicInstalls
    totalInstalls
  }
`)

graphql(`
  fragment GameMetricRetention on GameMetric {
    retentionRateDay1
    retentionRateDay3
    retentionRateDay7
  }
`)

graphql(`
  fragment GameMetricImpsDau on GameMetric {
    bannerImpsDau
    interImpsDau
    rewardImpsDau
    aoaImpsDau
    mrecImpsDau
    aoaAdmobImpsDau
    collapseAdmobImpsDau
  }
`)

graphql(`
  fragment GameMetricPlaytime on GameMetric {
    averageSession
    playtime
  }
`)

graphql(`
  fragment GameMetricMetadata on GameMetric {
    metadata {
      ...GameMetricMetadataAttributes
    }
  }
`)

graphql(`
  fragment GameMetricMetadataAttributes on GameMetricMetadata {
    id
    note
    uaNote
    monetNote
    productNote
    versionNote
  }
`)

export const configMapCollectionAttributesFragment = graphql(`
  fragment ConfigMapCollectionAttributes on ConfigMapCollection {
    id
    items
  }
`)

graphql(`
  fragment ConfigMapSoleAttributes on ConfigMap {
    id
    sole
  }
`)

graphql(`
  fragment GameLevelDropAttributes on GameLevelDrop {
    version
    world
    level
    activeUserCount
    userDropCount
    userDropRate
    completionRate
    winRate
    playtimeSecPerUser
    overallUserDropRate
    attemptCountPerUser
  }
`)

graphql(`
  fragment AccessControlAttributes on AccessControl {
    roles {
      roleId
      permits
    }
  }
`)

graphql(`
  fragment ModelAttribute on Attribute {
    name
    displayName
    aggregate
    permission
  }
`)

graphql(`
  fragment PageInfoAttributes on PageInfo {
    total
    perPage
    currentPage
    lastPage
    firstPage
  }
`)

graphql(`
  fragment V2GameMetricAttributes on V2GameMetric {
    id
    paidInstalls
    organicInstalls
    organicPercentage
    totalInstalls
    cost
    cpi
    roas
    revenue
    profit
    dailyActiveUsers
    retentionRateDay1
    retentionRateDay3
    retentionRateDay7
    sessions
    playtime
    uaNote
    monetNote
    productNote
    versionNote
    note
  }
`)

graphql(`
  fragment AdPerformanceAttributes on AdPerformance {
    ecpm
    arpDau
    impsDau
    adType
    revenue
  }
`)

graphql(`
  fragment AclGameMetricMetadataAttributes on GameMetric {
    uaNote @include(if: $canViewUaNote)
    monetNote @include(if: $canViewMonetNote)
    productNote @include(if: $canViewProductNote)
    versionNote @include(if: $canViewVersionNote)
    note @include(if: $canViewNote)
  }
`)

graphql(`
  fragment AclGameMetricAttributes on GameMetric {
    paidInstalls @include(if: $canViewPaidInstalls)
    organicInstalls @include(if: $canViewOrganicInstalls)
    organicPercentage @include(if: $canViewOrganicPercentage)
    totalInstalls @include(if: $canViewTotalInstalls)
    cost @include(if: $canViewCost)
    cpi @include(if: $canViewCpi)
    roas @include(if: $canViewRoas)
    revenue @include(if: $canViewRevenue)
    profit @include(if: $canViewProfit)
    retentionRateDay1 @include(if: $canViewRetentionRateDay1)
    retentionRateDay3 @include(if: $canViewRetentionRateDay3)
    retentionRateDay7 @include(if: $canViewRetentionRateDay7)
    bannerImpsDau @include(if: $canViewBannerImpsDau)
    interImpsDau @include(if: $canViewInterImpsDau)
    rewardImpsDau @include(if: $canViewRewardImpsDau)
    aoaImpsDau @include(if: $canViewAoaImpsDau)
    mrecImpsDau @include(if: $canViewMrecImpsDau)
    audioImpsDau @include(if: $canViewAudioImpsDau)
    aoaAdmobImpsDau @include(if: $canViewAoaAdmobImpsDau)
    collapseAdmobImpsDau @include(if: $canViewCollapseAdmobImpsDau)
    nativeAdmobImpsDau @include(if: $canViewNativeAdmobImpsDau)
    adaptiveAdmobImpsDau @include(if: $canViewAdaptiveAdmobImpsDau)
    mrecAdmobImpsDau @include(if: $canViewMrecAdmobImpsDau)
    averageSession @include(if: $canViewAverageSession)
    playtime @include(if: $canViewPlaytime)
  }
`)

graphql(`
  fragment AclAdPerformanceAttributes on AdPerformance {
    adType
    revenue @include(if: $canViewGameRevenueRevenue)
    ecpm @include(if: $canViewGameRevenueEcpm)
    arpDau @include(if: $canViewGameRevenueArpDau)
    impsDau @include(if: $canViewGameRevenueImpsDau)
  }
`)

graphql(`
  fragment AclGameMetricV2Attributes on V2GameMetric {
    id

    paidInstalls @include(if: $canViewPaidInstalls)
    organicInstalls @include(if: $canViewOrganicInstalls)
    organicPercentage @include(if: $canViewOrganicPercentage)
    totalInstalls @include(if: $canViewTotalInstalls)
    cost @include(if: $canViewCost)
    cpi @include(if: $canViewCpi)
    roas @include(if: $canViewRoas)
    revenue @include(if: $canViewRevenue)
    profit @include(if: $canViewProfit)
    dailyActiveUsers @include(if: $canViewDailyActiveUsers)
    retentionRateDay1 @include(if: $canViewRetentionRateDay1)
    retentionRateDay3 @include(if: $canViewRetentionRateDay3)
    retentionRateDay7 @include(if: $canViewRetentionRateDay7)
    sessions @include(if: $canViewSessions)
    playtime @include(if: $canViewPlaytime)

    adPerformances {
      ...AclAdPerformanceAttributes
    }
  }
`)

graphql(`
  fragment AdTypeAttributes on AdType {
    id
    name
    order
  }
`)

graphql(`
  fragment AdAgencyAttributes on AdAgency {
    id
    name
  }
`)

graphql(`
  fragment GameAgencyMetricAttributes on AgencyMetric {
    cost
    paidInstalls
  }
`)

graphql(`
  fragment SideMenuAttributes on SideMenu {
    name
    path
    group
    icon
  }
`)

graphql(`
  fragment GameStudioAttributes on GameStudio {
    id
    name
  }
`)

graphql(`
  fragment GameStudioMetricAttributes on GameStudioMetric {
    game {
      ...GameAttributes
    }

    totalInstalls
    mmpCostAmount
    totalAgencyCost
    revenue
    profit
  }
`)

graphql(`
  fragment UserPublicInfoAttributes on UserPublicInfo {
    id
    fullName
  }
`)

graphql(`
  fragment WorkflowStepAttributes on WorkflowStep {
    name
    action
    alternateAction
    assignee {
      ...UserPublicInfoAttributes
    }
  }
`)

graphql(`
  fragment BudgetRequestAttributes on BudgetRequest {
    id
    expirationDate
    createdAt
    stepId
    lastAction
    description
    step {
      ...WorkflowStepAttributes
    }
    game {
      ...GameAttributes
    }
    workflow {
      ...WorkflowAttributes
    }
    createdBy {
      ...UserPublicInfoAttributes
    }
    amount
  }
`)

graphql(`
  fragment NetworkAttributes on Network {
    id
    name
  }
`)

graphql(`
  fragment MediationAttributes on Mediation {
    id
    name
  }
`)

graphql(`
  fragment FirebaseExperimentAttributes on FirebaseExperiment {
    name
  }
`)

graphql(`
  fragment FirebaseMetricAttributes on FirebaseMetric {
    date
    variantId
    version
    countryCode

    sessionCount
    sessionNthDayCounts
    dailyActiveUserCount
    activeUserNthDayCounts
    playtimeMsec
    playtimeNthDayMsecs
    retentionNthDayRates
    installCount
    sessionCountPerActiveUser
    retentionRate
    impressionNthDayCounts
    adRevNthDayGrossAmounts
  }
`)

graphql(`
  fragment FirebaseVersionVariantAttributes on FirebaseVersionVariant {
    id
    experiment
    name
  }
`)

graphql(`
  fragment FirebaseAdMetricAttributes on FirebaseAdMetric {
    adTypeCategory

    adRevGrossAmount
    impressionCount
    impressionCountPerActiveUser
    adRevGrossAmountPerActiveUser
  }
`)

export const userAttributesFragment = graphql(`
  fragment UserAttributes on User {
    id
    fullName
    email
    kind
    hasPassword
    isDeleted
    note
    toolPermissions {
      ...ToolPermissionAttributes
    }
  }
`)

graphql(`
  fragment ToolPermissionAttributes on ToolPermission {
    tool
    action
  }
`)

graphql(`
  fragment GameRoleMembershipAttributes on GameRoleMembership {
    id
    roleId
    storeId
    users {
      email
    }
  }
`)

graphql(`
  fragment GameRolesAttributes on GameRoles {
    id
    roles {
      id
      users {
        email
      }
    }
  }
`)

graphql(`
  fragment ReleaseMetricAttributes on ReleaseMetric {
    date
    version
    countryCode
    sessionCount
    sessionNthDayCounts
    sessionCountPerActiveUser
    activeUserNthDayCounts
    playtimeMsec
    playtimeNthDayMsecs
    installCount
    retentionRate
    retentionNthDayRates
    dailyActiveUserCount
    lifetimeNthDayValues
    adRevGrossAmount
    impressionCount
    impressionCountPerActiveUser
    adRevGrossAmountPerActiveUser
    adRevNthDayGrossAmounts
    impressionNthDayCounts
  }
`)

graphql(`
  fragment ReleaseAdMetricAttributes on ReleaseAdMetric {
    adTypeCategory

    adRevGrossAmount
    impressionCount
    impressionCountPerActiveUser
    adRevGrossAmountPerActiveUser

    impressionNthDayCounts
    adRevNthDayGrossAmounts
  }
`)

export const gameMetricFragment = graphql(`
  fragment GameMetricIndex_GameMetricAttributes on GameMetric {
    id
    date
    ...AclGameMetricAttributes
    ...AclGameMetricMetadataAttributes
  }
`)

graphql(`
  fragment GameCostAttributes on GameCost {
    id
    date
    type
    network {
      id
      name
    }
    preTaxAmount
    totalAmount
    mmpAmount
    taxAmount
  }
`)
