import type { Meta, StoryObj } from '@storybook/react'
import {
  GameMetricsTable,
  type V2GameMetric,
  type AdType,
  type Aggregation,
} from '@/next/pages/dashboard/games/game_metrics_next/index/table'

const meta: Meta<typeof GameMetricsTable> = {
  title: 'Components/GameMetricsTable',
  component: GameMetricsTable,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    view: {
      control: { type: 'select' },
      options: ['impsDau', 'arpDau', 'ecpm', 'revenue'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Mock data
const mockAdTypes: AdType[] = [
  { id: 1, name: 'Banner', order: 1 },
  { id: 2, name: 'Interstitial', order: 2 },
  { id: 3, name: 'Rewarded', order: 3 },
  { id: 4, name: 'Native', order: 4 },
]

const mockMetrics: V2GameMetric[] = [
  {
    id: '1',
    date: '2024-01-01',
    gameId: 'game-1',
    paidInstalls: 1000,
    organicInstalls: 500,
    organicPercentage: 33.33,
    totalInstalls: 1500,
    cost: 5000,
    cpi: 5.0,
    roas: 120,
    revenue: 6000,
    profit: 1000,
    dailyActiveUsers: 8000,
    retentionRateDay1: 45.5,
    retentionRateDay3: 25.2,
    retentionRateDay7: 15.8,
    sessions: 12000,
    playtime: 3600,
    uaNote: 'Campaign A launched',
    monetNote: 'New ad format introduced',
    productNote: 'Version 2.1 released',
    versionNote: 'Bug fixes and improvements',
    note: 'Overall good performance',
    adPerformances: [
      {
        adType: 1,
        arpDau: 0.15,
        dailyActiveUserCount: 8000,
        ecpm: 2.5,
        impressionCount: 20000,
        impsDau: 2.5,
        revenue: 50,
      },
      {
        adType: 2,
        arpDau: 0.25,
        dailyActiveUserCount: 8000,
        ecpm: 3.2,
        impressionCount: 15000,
        impsDau: 1.875,
        revenue: 48,
      },
      {
        adType: 3,
        arpDau: 0.35,
        dailyActiveUserCount: 8000,
        ecpm: 4.1,
        impressionCount: 10000,
        impsDau: 1.25,
        revenue: 41,
      },
      {
        adType: 4,
        arpDau: 0.2,
        dailyActiveUserCount: 8000,
        ecpm: 2.8,
        impressionCount: 12000,
        impsDau: 1.5,
        revenue: 33.6,
      },
    ],
  },
  {
    id: '2',
    date: '2024-01-02',
    gameId: 'game-1',
    paidInstalls: 1200,
    organicInstalls: 600,
    organicPercentage: 33.33,
    totalInstalls: 1800,
    cost: 6000,
    cpi: 5.0,
    roas: 125,
    revenue: 7500,
    profit: 1500,
    dailyActiveUsers: 9000,
    retentionRateDay1: 46.2,
    retentionRateDay3: 26.1,
    retentionRateDay7: 16.3,
    sessions: 13500,
    playtime: 4050,
    uaNote: 'Campaign B launched',
    monetNote: 'Optimized ad placement',
    productNote: 'New features added',
    versionNote: 'Performance improvements',
    note: 'Strong growth trend',
    adPerformances: [
      {
        adType: 1,
        arpDau: 0.16,
        dailyActiveUserCount: 9000,
        ecpm: 2.6,
        impressionCount: 22500,
        impsDau: 2.5,
        revenue: 58.5,
      },
      {
        adType: 2,
        arpDau: 0.26,
        dailyActiveUserCount: 9000,
        ecpm: 3.3,
        impressionCount: 16875,
        impsDau: 1.875,
        revenue: 55.7,
      },
      {
        adType: 3,
        arpDau: 0.36,
        dailyActiveUserCount: 9000,
        ecpm: 4.2,
        impressionCount: 11250,
        impsDau: 1.25,
        revenue: 47.25,
      },
      {
        adType: 4,
        arpDau: 0.21,
        dailyActiveUserCount: 9000,
        ecpm: 2.9,
        impressionCount: 13500,
        impsDau: 1.5,
        revenue: 39.15,
      },
    ],
  },
  {
    id: '3',
    date: '2024-01-03',
    gameId: 'game-1',
    paidInstalls: 1100,
    organicInstalls: 550,
    organicPercentage: 33.33,
    totalInstalls: 1650,
    cost: 5500,
    cpi: 5.0,
    roas: 118,
    revenue: 6490,
    profit: 990,
    dailyActiveUsers: 8500,
    retentionRateDay1: 44.8,
    retentionRateDay3: 24.9,
    retentionRateDay7: 15.5,
    sessions: 12750,
    playtime: 3825,
    uaNote: 'Campaign C launched',
    monetNote: 'A/B testing results',
    productNote: 'User feedback implemented',
    versionNote: 'Stability improvements',
    note: 'Consistent performance',
    adPerformances: [
      {
        adType: 1,
        arpDau: 0.15,
        dailyActiveUserCount: 8500,
        ecpm: 2.4,
        impressionCount: 21250,
        impsDau: 2.5,
        revenue: 51,
      },
      {
        adType: 2,
        arpDau: 0.25,
        dailyActiveUserCount: 8500,
        ecpm: 3.1,
        impressionCount: 15937.5,
        impsDau: 1.875,
        revenue: 49.4,
      },
      {
        adType: 3,
        arpDau: 0.35,
        dailyActiveUserCount: 8500,
        ecpm: 4.0,
        impressionCount: 10625,
        impsDau: 1.25,
        revenue: 42.5,
      },
      {
        adType: 4,
        arpDau: 0.2,
        dailyActiveUserCount: 8500,
        ecpm: 2.7,
        impressionCount: 12750,
        impsDau: 1.5,
        revenue: 34.425,
      },
    ],
  },
]

const mockSummary: Aggregation = {
  paidInstalls: 3300,
  organicInstalls: 1650,
  organicPercentage: 33.33,
  totalInstalls: 4950,
  cost: 16500,
  cpi: 5.0,
  roas: 121,
  revenue: 19990,
  profit: 3490,
  dailyActiveUsers: 25500,
  retentionRateDay1: 45.5,
  retentionRateDay3: 25.4,
  retentionRateDay7: 15.9,
  sessions: 38250,
  playtime: 11475,
}

const mockTotal: Aggregation = {
  paidInstalls: 3300,
  organicInstalls: 1650,
  organicPercentage: 33.33,
  totalInstalls: 4950,
  cost: 16500,
  cpi: 5.0,
  roas: 121,
  revenue: 19990,
  profit: 3490,
  dailyActiveUsers: 25500,
  retentionRateDay1: 45.5,
  retentionRateDay3: 25.4,
  retentionRateDay7: 15.9,
  sessions: 38250,
  playtime: 11475,
}

const mockAttrs = [
  { name: 'paidInstalls', displayName: 'Paid Installs', permission: 'read' },
  { name: 'organicInstalls', displayName: 'Organic Installs', permission: 'read' },
  { name: 'totalInstalls', displayName: 'Total Installs', permission: 'read' },
  { name: 'organicPercentage', displayName: 'Organic Percentage', permission: 'read' },
  { name: 'cost', displayName: 'Cost', permission: 'read' },
  { name: 'cpi', displayName: 'CPI', permission: 'read' },
  { name: 'roas', displayName: 'ROAS', permission: 'read' },
  { name: 'revenue', displayName: 'Revenue', permission: 'read' },
  { name: 'profit', displayName: 'Profit', permission: 'read' },
  { name: 'dailyActiveUsers', displayName: 'Daily Active Users', permission: 'read' },
  { name: 'retentionRateDay1', displayName: 'Day 1 Retention', permission: 'read' },
  { name: 'retentionRateDay3', displayName: 'Day 3 Retention', permission: 'read' },
  { name: 'retentionRateDay7', displayName: 'Day 7 Retention', permission: 'read' },
  { name: 'sessions', displayName: 'Sessions', permission: 'read' },
  { name: 'playtime', displayName: 'Playtime', permission: 'read' },
  { name: 'uaNote', displayName: 'UA Note', permission: 'read' },
  { name: 'monetNote', displayName: 'Monet Note', permission: 'read' },
  { name: 'productNote', displayName: 'Product Note', permission: 'read' },
  { name: 'versionNote', displayName: 'Version Note', permission: 'read' },
  { name: 'note', displayName: 'Note', permission: 'read' },
]

const mockPageInfo = {
  currentPage: 1,
  lastPage: 1,
  perPage: 50,
  total: 3,
  hasNextPage: false,
  hasPreviousPage: false,
}

export const Default: Story = {
  args: {
    metrics: mockMetrics,
    adTypes: mockAdTypes,
    view: 'impsDau',
    summary: mockSummary,
    total: mockTotal,
    attrs: mockAttrs,
    pageInfo: mockPageInfo,
  },
}

export const RevenueView: Story = {
  args: {
    ...Default.args,
    view: 'revenue',
  },
}

export const EcpmView: Story = {
  args: {
    ...Default.args,
    view: 'ecpm',
  },
}

export const ArpDauView: Story = {
  args: {
    ...Default.args,
    view: 'arpDau',
  },
}

export const WithFilters: Story = {
  args: {
    ...Default.args,
    slots: {
      beforeFilter: (
        <div className="flex items-center gap-2 p-2 bg-muted rounded">
          <span className="text-sm font-medium">Custom Filter:</span>
          <span className="text-sm text-muted-foreground">Date Range: Last 7 days</span>
        </div>
      ),
    },
  },
}

export const EmptyState: Story = {
  args: {
    ...Default.args,
    metrics: [],
    summary: {
      paidInstalls: 0,
      organicInstalls: 0,
      organicPercentage: 0,
      totalInstalls: 0,
      cost: 0,
      cpi: 0,
      roas: 0,
      revenue: 0,
      profit: 0,
      dailyActiveUsers: 0,
      retentionRateDay1: 0,
      retentionRateDay3: 0,
      retentionRateDay7: 0,
      sessions: 0,
      playtime: 0,
    },
    total: {
      paidInstalls: 0,
      organicInstalls: 0,
      organicPercentage: 0,
      totalInstalls: 0,
      cost: 0,
      cpi: 0,
      roas: 0,
      revenue: 0,
      profit: 0,
      dailyActiveUsers: 0,
      retentionRateDay1: 0,
      retentionRateDay3: 0,
      retentionRateDay7: 0,
      sessions: 0,
      playtime: 0,
    },
  },
}

export const LargeDataset: Story = {
  args: {
    ...Default.args,
    metrics: Array.from({ length: 50 }, (_, i) => ({
      ...mockMetrics[0],
      id: String(i + 1),
      date: new Date(2024, 0, i + 1).toISOString().split('T')[0],
      paidInstalls: 1000 + Math.floor(Math.random() * 500),
      revenue: 6000 + Math.floor(Math.random() * 2000),
      profit: 1000 + Math.floor(Math.random() * 500),
    })),
    pageInfo: {
      currentPage: 1,
      lastPage: 5,
      perPage: 10,
      total: 50,
      hasNextPage: true,
      hasPreviousPage: false,
    },
  },
}
