import type { <PERSON>a, StoryObj } from '@storybook/react'
import { But<PERSON> } from '@/next/components/ui/button'
import { Mail, Loader2 } from 'lucide-react'

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
    },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg', 'icon'],
    },
    disabled: {
      control: 'boolean',
    },
    asChild: {
      control: false,
    },
  },
}

export default meta
type Story = StoryObj<typeof Button>

export const Default: Story = {
  args: {
    children: 'Button',
    variant: 'default',
  },
}

export const WithIcon: Story = {
  args: {
    children: (
      <>
        <Mail />
        With Icon
      </>
    ),
  },
}

export const Loading: Story = {
  args: {
    children: (
      <>
        <Loader2 className="animate-spin" />
        Loading
      </>
    ),
    disabled: true,
  },
}
