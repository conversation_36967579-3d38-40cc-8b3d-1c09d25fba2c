import { <PERSON><PERSON>, <PERSON><PERSON>bj } from '@storybook/react'
import { useQueryForm } from '@/next/components/sdk/form/use_query_form'
import { z } from 'zod'
import { Button } from '@/next/components/ui/button'
import { Input } from '@/next/components/ui/input'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/next/components/ui/card'
import { Label } from '@/next/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/next/components/ui/select'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { Form } from '@/next/components/ui/form'

// Example schemas for different use cases
const searchSchema = z.object({
  query: z.string(),
  sort: z.enum(['relevance', 'date', 'title']),
})

const filterSchema = z.object({
  minPrice: z.number().min(0),
  maxPrice: z.number().min(0),
  category: z.enum(['all', 'electronics', 'books', 'clothing']),
  inStock: z.boolean(),
})

const paginatedSchema = z.object({
  search: z.string(),
  page: z.number().min(1),
  perPage: z.number().min(10).max(100),
})

// Story components for different use cases
const SearchForm = () => {
  const { register, watch, queryMapping, queryState } = useQueryForm({
    schema: searchSchema,
    formOptions: {
      defaultValues: {
        query: '',
        sort: 'relevance',
      },
    },
    // All fields update URL immediately
  })

  const values = watch()

  return (
    <Card className="w-[400px]">
      <CardHeader>
        <CardTitle>Immediate Search Form</CardTitle>
        <CardDescription>All fields update URL immediately</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="query">Search</Label>
          <Input {...register('query')} placeholder="Type to search..." />
        </div>
        <div className="space-y-2">
          <Label htmlFor="sort">Sort By</Label>
          <Select
            defaultValue={values.sort}
            onValueChange={(value: 'relevance' | 'date' | 'title') =>
              register('sort').onChange({ target: { value } })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="date">Date</SelectItem>
              <SelectItem value="title">Title</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="text-sm space-y-2">
          <div>Current Values:</div>
          <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(values, null, 2)}</pre>
          <div>Query Mapping:</div>
          <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(queryMapping, null, 2)}</pre>
          <div>Query States:</div>
          <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(queryState, null, 2)}</pre>
        </div>
      </CardContent>
    </Card>
  )
}

const FilterForm = () => {
  const form = useQueryForm({
    schema: filterSchema,
    syncConfig: {
      // Price fields only update on submit
      minPrice: { lazy: true },
      maxPrice: { lazy: true },
      // Category and stock update immediately
      category: { queryKey: 'cat' },
      inStock: { queryKey: 'stock' },
    },
    formOptions: {
      defaultValues: {
        minPrice: 0,
        maxPrice: 1000,
        category: 'all',
        inStock: false,
      },
    },
  })

  const { register, handleSubmit, watch, queryMapping, queryState, control } = form

  const values = watch()

  return (
    <Card className="w-[400px]">
      <CardHeader>
        <CardTitle>Mixed Update Form</CardTitle>
        <CardDescription>Price ranges update on submit, others update immediately</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Form {...form}>
          <FormSelectField
            control={control}
            name="category"
            label="Category"
            options={[
              { value: 'all', label: 'All Categories' },
              { value: 'electronics', label: 'Electronics' },
              { value: 'books', label: 'Books' },
              { value: 'clothing', label: 'Clothing' },
            ]}
          />
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minPrice">Min Price</Label>
              <Input type="number" {...register('minPrice', { valueAsNumber: true })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxPrice">Max Price</Label>
              <Input type="number" {...register('maxPrice', { valueAsNumber: true })} />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <input type="checkbox" {...register('inStock')} className="w-4 h-4" />
            <Label>In Stock Only</Label>
          </div>
          <Button onClick={handleSubmit(console.log)} className="w-full">
            Apply Filters
          </Button>
          <div className="text-sm space-y-2">
            <div>Current Values:</div>
            <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(values, null, 2)}</pre>
            <div>Query Mapping:</div>
            <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(queryMapping, null, 2)}</pre>
            <div>Query States:</div>
            <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(queryState, null, 2)}</pre>
          </div>
        </Form>
      </CardContent>
    </Card>
  )
}

const PaginatedForm = () => {
  const { register, handleSubmit, watch, queryMapping, queryState } = useQueryForm({
    schema: paginatedSchema,
    syncConfig: {
      // Search updates immediately
      search: { queryKey: 'q' },
      // Pagination updates on submit
      page: { lazy: true, queryKey: 'p' },
      perPage: { lazy: true, queryKey: 'size' },
    },
    formOptions: {
      defaultValues: {
        search: '',
        page: 1,
        perPage: 20,
      },
    },
  })

  const values = watch()

  return (
    <Card className="w-[400px]">
      <CardHeader>
        <CardTitle>Paginated Search</CardTitle>
        <CardDescription>Search updates immediately, pagination on submit</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="search">Search</Label>
          <Input {...register('search')} placeholder="Search..." />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="page">Page</Label>
            <Input type="number" {...register('page', { valueAsNumber: true })} />
          </div>
          <div className="space-y-2">
            <Label htmlFor="perPage">Per Page</Label>
            <Input type="number" {...register('perPage', { valueAsNumber: true })} />
          </div>
        </div>
        <Button onClick={handleSubmit(console.log)} className="w-full">
          Load Page
        </Button>
        <div className="text-sm space-y-2">
          <div>Current Values:</div>
          <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(values, null, 2)}</pre>
          <div>Query Mapping:</div>
          <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(queryMapping, null, 2)}</pre>
          <div>Query States:</div>
          <pre className="bg-slate-100 p-2 rounded">{JSON.stringify(queryState, null, 2)}</pre>
        </div>
      </CardContent>
    </Card>
  )
}

const meta = {
  title: 'SDK/Form/useQueryForm',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# useQueryForm Hook

A powerful form hook that combines react-hook-form, nuqs, and zod for type-safe forms with URL query string synchronization.

## Features

- Type-safe form handling with Zod schemas
- Configurable URL synchronization (immediate or lazy)
- Custom query parameter mapping
- Built on top of react-hook-form

## Basic Usage

\`\`\`tsx
const form = useQueryForm({
  schema: mySchema,
  syncConfig: {
    // Updates immediately
    search: {
      queryKey: "q"
    },
    // Updates only on submit
    filters: {
      lazy: true,
      queryKey: "f"
    }
  },
  formOptions: {
    defaultValues: {
      search: "",
      filters: []
    }
  }
});
\`\`\`
        `,
      },
    },
  },
} satisfies Meta

export default meta
type Story = StoryObj<typeof meta>

// Stories
export const ImmediateSearch: Story = {
  render: () => <SearchForm />,
  parameters: {
    docs: {
      description: {
        story: 'A search form where all fields update the URL immediately as they change.',
      },
    },
  },
}

export const MixedUpdateFilter: Story = {
  render: () => <FilterForm />,
  parameters: {
    docs: {
      description: {
        story:
          'A filter form with mixed update behavior. Price ranges update on submit, while category and stock status update immediately.',
      },
    },
  },
}

export const PaginatedSearch: Story = {
  render: () => <PaginatedForm />,
  parameters: {
    docs: {
      description: {
        story:
          'A paginated search form where the search term updates immediately but pagination changes require submission.',
      },
    },
  },
}
