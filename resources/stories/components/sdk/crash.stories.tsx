import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Crash } from '@/next/components/sdk/loading/crash'

const meta: Meta<typeof Crash> = {
  title: 'SDK/Crash',
  component: Crash,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    error: {
      control: 'boolean',
      description: 'Whether to show the error state',
      defaultValue: false,
    },
  },
}

export default meta
type Story = StoryObj<typeof Crash>

export const ToggleError: Story = {
  args: {
    error: false,
  },
  render: (args) => (
    <Crash {...args}>
      <div className="p-4">Toggle the error control to show/hide this content</div>
    </Crash>
  ),
}
