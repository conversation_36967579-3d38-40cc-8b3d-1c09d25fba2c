import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useQuery, useMutation } from '@/next/components/sdk/graphql'
import { gql, ApolloError } from '@apollo/client'
import { Button } from '@/next/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/next/components/ui/card'
import { Skeleton } from '@/next/components/ui/skeleton'
import { Toaster } from '@/next/components/ui/sonner'
import { MockApolloProvider } from '../../supports/graphql_client'

// Types for our data
interface Todo {
  id: string
  title: string
  completed: boolean
}

interface TodosData {
  todos: Todo[]
}

interface UpdateTodoData {
  updateTodo: {
    id: string
    completed: boolean
  }
}

interface UpdateTodoVars {
  id: string
  completed: boolean
}

const meta = {
  title: 'Hooks/useGraphQL',
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <MockApolloProvider>
        <Story />
        <Toaster />
      </MockApolloProvider>
    ),
  ],
} satisfies Meta

export default meta

// Example Query
const TODOS_QUERY = gql`
  query GetTodos {
    todos {
      id
      title
      completed
    }
  }
`

// Example Mutation
const UPDATE_TODO_MUTATION = gql`
  mutation UpdateTodo($id: ID!, $completed: Boolean!) {
    updateTodo(id: $id, completed: $completed) {
      id
      completed
    }
  }
`

// Query Example Component
const QueryExample = () => {
  const { data, loading } = useQuery<TodosData>(
    TODOS_QUERY,
    {},
    {
      toastOnError: true,
      toastOnSuccess: true,
      errorMessage: 'Could not load your todo items. Please try again.',
      successMessage: `Successfully loaded todos`,
      onSuccess: (data: TodosData) => {
        console.log(`Loaded ${data.todos.length} todos`)
      },
      onError: (error: ApolloError) => {
        console.error('Failed to load todos:', error)
      },
    }
  )

  if (loading) {
    return (
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-4 w-[200px]" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-[300px]" />
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          {Array.from({ length: 3 }, (_, i) => (
            <Skeleton key={i} className="h-4 w-full" />
          ))}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-[400px]">
      <CardHeader>
        <CardTitle>Todos</CardTitle>
        <CardDescription>Example of useQuery hook with toast notifications</CardDescription>
      </CardHeader>
      <CardContent>
        {data?.todos.map((todo: Todo) => (
          <div key={todo.id} className="flex items-center gap-2 py-1">
            <input type="checkbox" checked={todo.completed} readOnly />
            <span>{todo.title}</span>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

// Mutation Example Component
const MutationExample = () => {
  const [updateTodo, { loading }] = useMutation<UpdateTodoData, UpdateTodoVars>(
    UPDATE_TODO_MUTATION,
    {
      toastOnError: true,
      toastOnSuccess: true,
      errorMessage: 'Failed to update todo',
      successMessage: 'Todo updated successfully',
      onSuccess: (data: UpdateTodoData) => {
        console.log('Todo updated:', data.updateTodo)
      },
      onError: (error: ApolloError) => {
        console.error('Update failed:', error)
      },
      onMutate: (variables: UpdateTodoVars) => {
        console.log('Starting update with:', variables)
      },
    }
  )

  const handleUpdate = async () => {
    try {
      await updateTodo({
        id: '1',
        completed: true,
      })
    } catch (error) {
      console.error('Error updating todo:', error)
    }
  }

  return (
    <Card className="w-[400px]">
      <CardHeader>
        <CardTitle>Update Todo</CardTitle>
        <CardDescription>Example of useMutation hook with toast notifications</CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={handleUpdate} disabled={loading}>
          {loading ? 'Updating...' : 'Mark as Complete'}
        </Button>
      </CardContent>
    </Card>
  )
}

// Stories
export const Query: StoryObj = {
  render: () => <QueryExample />,
}

export const Mutation: StoryObj = {
  render: () => <MutationExample />,
}

export const Combined: StoryObj = {
  render: () => (
    <div className="flex flex-col gap-4">
      <QueryExample />
      <MutationExample />
    </div>
  ),
}
