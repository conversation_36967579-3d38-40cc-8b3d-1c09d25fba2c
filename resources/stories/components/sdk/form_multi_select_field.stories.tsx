import type { Meta, StoryObj } from '@storybook/react'
import { FormMultiSelectField } from '@/next/components/sdk/form/form_multi_select_field'
import { useForm } from 'react-hook-form'
import { Form } from '@/next/components/ui/form'

const meta = {
  title: 'SDK/Form Fields/MultiSelect',
  component: FormMultiSelectField,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof FormMultiSelectField>

export default meta
type Story = StoryObj<typeof FormMultiSelectField>

const FormWrapper = ({ children }: { children: React.ReactNode }) => {
  const form = useForm({ defaultValues: { multiSelect: [] } })
  return (
    <Form {...form}>
      <form className="w-[400px]">{children}</form>
    </Form>
  )
}

const defaultOptions = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
  { value: 'option4', label: 'Option 4' },
]

export const Default: Story = {
  render: () => (
    <FormWrapper>
      <FormMultiSelectField
        control={useForm({ defaultValues: { multiSelect: [] } }).control}
        name="multiSelect"
        label="Multi Select Field"
        options={defaultOptions}
      />
    </FormWrapper>
  ),
}

export const WithValues: Story = {
  render: () => (
    <FormWrapper>
      <FormMultiSelectField
        control={useForm({ defaultValues: { multiSelect: ['option1', 'option3'] } }).control}
        name="multiSelect"
        label="Multi Select Field"
        options={defaultOptions}
      />
    </FormWrapper>
  ),
}

export const CustomPlaceholder: Story = {
  render: () => (
    <FormWrapper>
      <FormMultiSelectField
        control={useForm({ defaultValues: { multiSelect: [] } }).control}
        name="multiSelect"
        label="Multi Select Field"
        options={defaultOptions}
        placeholder="Select multiple options..."
      />
    </FormWrapper>
  ),
}

export const CustomSearchHint: Story = {
  render: () => (
    <FormWrapper>
      <FormMultiSelectField
        control={useForm({ defaultValues: { multiSelect: [] } }).control}
        name="multiSelect"
        label="Multi Select Field"
        options={defaultOptions}
        searchHint="Search in options..."
      />
    </FormWrapper>
  ),
}

export const ManyOptions: Story = {
  render: () => (
    <FormWrapper>
      <FormMultiSelectField
        control={useForm({ defaultValues: { multiSelect: [] } }).control}
        name="multiSelect"
        label="Multi Select Field"
        options={Array.from({ length: 10 }, (_, i) => ({
          value: `option${i + 1}`,
          label: `Option ${i + 1}`,
        }))}
      />
    </FormWrapper>
  ),
}
