import { DatePicker, DateRange } from '@/components/form/time'
import { LocalizationProvider } from '@mui/x-date-pickers-pro'
import { AdapterDayjs } from '@mui/x-date-pickers-pro/AdapterDayjs'
import type { Meta, StoryObj } from '@storybook/react'
import { ReactNode } from 'react'

const meta: Meta<typeof DatePicker> = {
  title: 'Components/DatePicker',
  component: DatePicker,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof DatePicker>

const DatePickerWrapper = ({ children }: { children: ReactNode }) => {
  return <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
}

export const BaseDatePicker: Story = {
  render: () => (
    <DatePickerWrapper>
      <DatePicker label="Date picker" />
    </DatePickerWrapper>
  ),
}

export const BaseDateRangePicker: Story = {
  render: () => (
    <DatePickerWrapper>
      <div style={{ maxWidth: '40vw' }}>
        <DateRange label="Select a date range" />
      </div>
    </DatePickerWrapper>
  ),
}
