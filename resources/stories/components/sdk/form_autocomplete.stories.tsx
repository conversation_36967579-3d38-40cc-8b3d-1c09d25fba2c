import { <PERSON>a, StoryObj } from '@storybook/react'
import { StaticAutocomplete } from '@/components/form/autocomplete'

export default {
  title: 'Components/Form/StaticAutocomplete',
  component: StaticAutocomplete,
  argTypes: {
    label: { control: 'text' },
    options: { control: 'object' },
    multiple: { control: 'boolean' },
    disableClearable: { control: 'boolean' },
    freeSolo: { control: 'boolean' },
  },
} as Meta<typeof StaticAutocomplete>

type Story = StoryObj<typeof StaticAutocomplete>

export const Default: Story = {
  args: {
    label: 'Choose an option',
    options: ['Option 1', 'Option 2', 'Option 3'],
  },
}

export const Multiple: Story = {
  args: {
    label: 'Choose multiple options',
    options: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
    multiple: true,
  },
}

export const FreeSolo: Story = {
  args: {
    label: 'Free solo input',
    options: ['Option 1', 'Option 2', 'Option 3'],
    freeSolo: true,
  },
}

export const DisableClearable: Story = {
  args: {
    label: 'Disable clearable',
    options: ['Option 1', 'Option 2', 'Option 3'],
    disableClearable: true,
  },
}

export const WithDefaultValue: Story = {
  args: {
    label: 'Choose an option with default value',
    options: ['Option 1', 'Option 2', 'Option 3'],
    defaultValue: 'Option 2',
  },
}
