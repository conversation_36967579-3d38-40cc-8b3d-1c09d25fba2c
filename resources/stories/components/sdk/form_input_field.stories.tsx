import type { Meta, StoryObj } from '@storybook/react'
import { FormInputField } from '@/next/components/sdk/form/form_input_field'
import { useForm } from 'react-hook-form'
import { Form } from '@/next/components/ui/form'

const meta = {
  title: 'SDK/Form Fields/Input',
  component: FormInputField,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof FormInputField>

export default meta
type Story = StoryObj<typeof FormInputField>

const FormWrapper = ({ children }: { children: React.ReactNode }) => {
  const form = useForm({ defaultValues: { input: '' } })
  return (
    <Form {...form}>
      <form className="w-[400px]">{children}</form>
    </Form>
  )
}

export const Default: Story = {
  render: () => (
    <FormWrapper>
      <FormInputField
        control={useForm({ defaultValues: { input: '' } }).control}
        name="input"
        label="Input Field"
        placeholder="Type something..."
      />
    </FormWrapper>
  ),
}

export const WithValue: Story = {
  render: () => (
    <FormWrapper>
      <FormInputField
        control={useForm({ defaultValues: { input: 'Pre-filled value' } }).control}
        name="input"
        label="Input Field"
        placeholder="Type something..."
      />
    </FormWrapper>
  ),
}

export const CustomLabel: Story = {
  render: () => (
    <FormWrapper>
      <FormInputField
        control={useForm({ defaultValues: { input: '' } }).control}
        name="input"
        label="Custom Label"
        placeholder="Type something..."
      />
    </FormWrapper>
  ),
}

export const CustomPlaceholder: Story = {
  render: () => (
    <FormWrapper>
      <FormInputField
        control={useForm({ defaultValues: { input: '' } }).control}
        name="input"
        label="Input Field"
        placeholder="Custom placeholder text..."
      />
    </FormWrapper>
  ),
}
