import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { BaseLayout } from '@/next/components/sdk/layout/base_layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/next/components/ui/card'
import { Button } from '@/next/components/ui/button'
import { PageConfig } from '@/next/components/sdk/page_config'
import { MockProvider } from '#stories/supports/mock_provider'
import { NavMainItem } from '@/next/components/nav-main'
import { useMemo } from 'react'
import { TrendingUpDownIcon } from 'lucide-react'

const Layout = ({ subSidebar }: { subSidebar?: boolean }) => {
  const sidebarSubItems = useMemo<NavMainItem[]>(() => {
    return [
      {
        title: 'Game Metrics',
        icon: TrendingUpDownIcon,
        url: '#',
      },
      {
        title: 'Creative Performance',
        icon: TrendingUpDownIcon,
        url: '#',
      },
      {
        title: 'Campaign Performance',
        icon: TrendingUpDownIcon,
        url: '#',
      },
    ]
  }, [])

  return (
    <BaseLayout>
      <ExampleContent />
      <PageConfig breadcrumbs={[]} sidebarSubItems={subSidebar ? sidebarSubItems : undefined} />
    </BaseLayout>
  )
}

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'SDK/Layout',
  component: Layout,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {},
  args: {
    subSidebar: false,
  },
  decorators: [
    (Story) => (
      <MockProvider>
        <Story />
      </MockProvider>
    ),
  ],
} satisfies Meta<typeof Layout>

export default meta
type Story = StoryObj<typeof Layout>

const ExampleContent = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
    {Array.from({ length: 6 }, (_, i) => (
      <Card key={i}>
        <CardHeader>
          <CardTitle>Card {i + 1}</CardTitle>
          <CardDescription>Example card description</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            This is an example card to demonstrate the layout's content area.
          </p>
          <Button className="mt-4" variant="outline">
            Action
          </Button>
        </CardContent>
      </Card>
    ))}
  </div>
)

export const Default: Story = {
  render: (props) => {
    return <Layout {...props} />
  },
}
