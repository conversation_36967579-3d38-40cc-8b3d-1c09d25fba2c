import type { Meta, StoryObj } from '@storybook/react'
import { FormDateRangePickerField } from '@/next/components/sdk/form/form_date_range_picker_field'
import { useForm } from 'react-hook-form'
import { Form } from '@/next/components/ui/form'

const meta = {
  title: 'SDK/Form Fields/DateRangePicker',
  component: FormDateRangePickerField,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof FormDateRangePickerField>

export default meta
type Story = StoryObj<typeof FormDateRangePickerField>

const FormWrapper = ({ children }: { children: React.ReactNode }) => {
  const form = useForm({ defaultValues: { dateRange: null } })
  return (
    <Form {...form}>
      <form className="w-[400px]">{children}</form>
    </Form>
  )
}

export const Default: Story = {
  render: () => (
    <FormWrapper>
      <FormDateRangePickerField
        control={useForm({ defaultValues: { dateRange: null } }).control}
        name="dateRange"
        label="Date Range Field"
      />
    </FormWrapper>
  ),
}

export const WithValue: Story = {
  render: () => (
    <FormWrapper>
      <FormDateRangePickerField
        control={
          useForm({
            defaultValues: {
              dateRange: {
                from: new Date(2024, 0, 1),
                to: new Date(2024, 0, 7),
              },
            },
          }).control
        }
        name="dateRange"
        label="Date Range Field"
      />
    </FormWrapper>
  ),
}

export const CustomPlaceholder: Story = {
  render: () => (
    <FormWrapper>
      <FormDateRangePickerField
        control={useForm({ defaultValues: { dateRange: null } }).control}
        name="dateRange"
        label="Date Range Field"
        placeholder="Select date range..."
      />
    </FormWrapper>
  ),
}

export const CustomLabel: Story = {
  render: () => (
    <FormWrapper>
      <FormDateRangePickerField
        control={useForm({ defaultValues: { dateRange: null } }).control}
        name="dateRange"
        label="Custom Date Range Label"
      />
    </FormWrapper>
  ),
}
