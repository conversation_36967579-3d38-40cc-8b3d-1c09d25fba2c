import type { Meta, StoryObj } from '@storybook/react'
import { Loading } from '@/next/components/sdk/loading'

const meta: Meta<typeof Loading> = {
  title: 'SDK/Loading',
  component: Loading,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    loading: {
      control: 'boolean',
      description: 'Whether to show the loading state',
      defaultValue: true,
    },
    centered: {
      control: 'boolean',
      description: 'Whether to center the loading spinner',
      defaultValue: true,
    },
    className: {
      control: 'text',
      description: 'Additional classes for the container',
    },
  },
}

export default meta
type Story = StoryObj<typeof Loading>

export const Default: Story = {
  args: {
    loading: true,
  },
}

export const NotCentered: Story = {
  args: {
    loading: true,
    centered: false,
  },
}

export const WithContent: Story = {
  args: {
    loading: false,
  },
  render: (args) => (
    <Loading {...args}>
      <div className="p-4">Content</div>
    </Loading>
  ),
}

export const LoadingWithContent: Story = {
  args: {
    loading: true,
  },
  render: (args) => (
    <Loading {...args}>
      <div className="p-4">This content will be hidden while loading</div>
    </Loading>
  ),
}

export const CustomContainer: Story = {
  args: {
    loading: true,
    className: 'bg-secondary/20 p-4',
  },
}
