import type { Meta, StoryObj } from '@storybook/react'
import { FormSelectField } from '@/next/components/sdk/form/form_select_field'
import { useForm } from 'react-hook-form'
import { Form } from '@/next/components/ui/form'

const meta = {
  title: 'SDK/Form Fields/Select',
  component: FormSelectField,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof FormSelectField>

export default meta
type Story = StoryObj<typeof FormSelectField>

const FormWrapper = ({ children }: { children: React.ReactNode }) => {
  const form = useForm({ defaultValues: { select: '' } })
  return (
    <Form {...form}>
      <form className="w-[400px]">{children}</form>
    </Form>
  )
}

const defaultOptions = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
]

export const Default: Story = {
  render: () => (
    <FormWrapper>
      <FormSelectField
        control={useForm({ defaultValues: { select: '' } }).control}
        name="select"
        label="Select Field"
        options={defaultOptions}
      />
    </FormWrapper>
  ),
}

export const WithValue: Story = {
  render: () => (
    <FormWrapper>
      <FormSelectField
        control={useForm({ defaultValues: { select: 'option2' } }).control}
        name="select"
        label="Select Field"
        options={defaultOptions}
      />
    </FormWrapper>
  ),
}

export const CustomPlaceholder: Story = {
  render: () => (
    <FormWrapper>
      <FormSelectField
        control={useForm({ defaultValues: { select: '' } }).control}
        name="select"
        label="Select Field"
        options={defaultOptions}
        placeholder="Choose an option..."
      />
    </FormWrapper>
  ),
}

export const CustomSearchHint: Story = {
  render: () => (
    <FormWrapper>
      <FormSelectField
        control={useForm({ defaultValues: { select: '' } }).control}
        name="select"
        label="Select Field"
        options={defaultOptions}
        searchHint="Search options..."
      />
    </FormWrapper>
  ),
}

export const ManyOptions: Story = {
  render: () => (
    <FormWrapper>
      <FormSelectField
        control={useForm({ defaultValues: { select: '' } }).control}
        name="select"
        label="Select Field"
        options={Array.from({ length: 10 }, (_, i) => ({
          value: `option${i + 1}`,
          label: `Option ${i + 1}`,
        }))}
      />
    </FormWrapper>
  ),
}
