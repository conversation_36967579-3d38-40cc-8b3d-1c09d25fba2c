import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { AppSidebar } from '@/next/components/sdk/layout/app_sidebar'
import * as useSidebarModule from '@/next/components/sdk/layout/use_main_sidebar'
import { SquareTerminal, Bo<PERSON>, BookOpen } from 'lucide-react'
import { SidebarItem } from '@/next/components/sdk/layout/use_main_sidebar'
import { QueryResult } from '@apollo/client'

// Create a decorated component for mocking
function AppSidebarWithMocks({
  sidebarItems = [],
  loading = false,
  error = null,
}: {
  sidebarItems?: SidebarItem[]
  loading?: boolean
  error?: Error | null
}) {
  // Override the useSidebar hook for storybook
  useSidebarModule.useMainSidebar = () => {
    // Create a mock query result object
    const query = {
      loading,
      error,
      data: error ? undefined : { sideMenus: { collection: [] } },
      called: true,
      client: {} as any,
      networkStatus: loading ? 1 : 7,
      refetch: () => Promise.resolve({ data: {} } as any),
      fetchMore: () => Promise.resolve(),
      startPolling: () => {},
      stopPolling: () => {},
      subscribeToMore: () => () => {},
      updateQuery: () => {},
    } as QueryResult<any, any>

    return {
      sidebarItems,
      query,
    }
  }

  return <AppSidebar />
}

const meta = {
  title: 'SDK/Layout/AppSidebar',
  component: AppSidebarWithMocks,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div style={{ height: '100vh' }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof AppSidebarWithMocks>

export default meta
type Story = StoryObj<typeof meta>

// Sample data for the sidebar
const mockSidebarItems: SidebarItem[] = [
  {
    title: 'Playground',
    url: '#',
    icon: SquareTerminal,
    isActive: true,
    items: [
      { title: 'History', url: '#' },
      { title: 'Starred', url: '#' },
      { title: 'Settings', url: '#' },
    ],
  },
  {
    title: 'Models',
    url: '#',
    icon: Bot,
    items: [
      { title: 'Genesis', url: '#' },
      { title: 'Explorer', url: '#' },
      { title: 'Quantum', url: '#' },
    ],
  },
  {
    title: 'Documentation',
    url: '#',
    icon: BookOpen,
    items: [
      { title: 'Introduction', url: '#' },
      { title: 'Get Started', url: '#' },
      { title: 'Tutorials', url: '#' },
    ],
  },
]

// Default state with loaded data
export const Default: Story = {
  args: {
    sidebarItems: mockSidebarItems,
    loading: false,
    error: null,
  },
}

// Loading state
export const Loading: Story = {
  args: {
    sidebarItems: [],
    loading: true,
    error: null,
  },
}

// Error state
export const Error: Story = {
  args: {
    sidebarItems: [],
    loading: false,
    error: new Error('Failed to load sidebar items'),
  },
}
