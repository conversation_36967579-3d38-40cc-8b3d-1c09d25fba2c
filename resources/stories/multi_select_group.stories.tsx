import MultiSelectButtonGroup from '@/components/table/multi_select_group'
import type { Meta, StoryObj } from '@storybook/react'

const meta = {
  title: 'Components/Table/MultiSelectButtonGroup',
  component: MultiSelectButtonGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    title: { control: 'text' },
    options: { control: 'object' },
    defaultSelected: { control: 'object' },
    onChange: { action: 'changed' },
  },
} satisfies Meta<typeof MultiSelectButtonGroup>

export default meta
type Story = StoryObj<typeof meta>

// Basic example with no selections
export const Basic: Story = {
  args: {
    title: 'Select Options',
    options: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
    defaultSelected: [],
  },
}

// Example with pre-selected options
export const WithPreselection: Story = {
  args: {
    title: 'Filter by Status',
    options: ['Active', 'Pending', 'Completed', 'Canceled'],
    defaultSelected: ['Active', 'Pending'],
  },
}

// Example without a title
export const WithoutTitle: Story = {
  args: {
    options: ['Small', 'Medium', 'Large', 'X-Large'],
    defaultSelected: ['Medium'],
  },
}

// Example with many options
export const ManyOptions: Story = {
  args: {
    title: 'Categories',
    options: [
      'Technology',
      'Health',
      'Finance',
      'Education',
      'Entertainment',
      'Sports',
      'Food',
      'Travel',
      'Fashion',
      'Automotive',
    ],
    defaultSelected: ['Technology', 'Finance'],
  },
}
