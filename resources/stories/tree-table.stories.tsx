import type { Meta, StoryObj } from '@storybook/react'
import { TreeTableExample } from '@/next/pages/dashboard/games/campaign_metrics/index/tree-table-example'
import { CampaignMetricsTreeTableExample } from '@/next/components/sdk/tree-table-campaign-metrics-example'
import { TreeTable } from '@/next/components/sdk/tree_table'

const meta: Meta<typeof TreeTable> = {
  title: 'SDK/TreeTable',
  component: TreeTable,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof TreeTable>

export const FileExplorer: Story = {
  render: () => <TreeTableExample />,
}

export const CampaignMetrics: Story = {
  render: () => <CampaignMetricsTreeTableExample />,
}
