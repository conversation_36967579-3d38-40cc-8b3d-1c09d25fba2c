import React, { useState } from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { Box, Button, TextField, Typography } from '@mui/material'

import { Modal, ModalHeader, ModalBody, useModal } from '@/components/modal'

// Meta configuration
const meta: Meta<typeof Modal> = {
  title: 'Components/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    control: { control: { disable: true } },
    slots: { control: { disable: true } },
  },
}

export default meta
type Story = StoryObj<typeof Modal>

// Basic Modal Example
const BasicModalExample = () => {
  const modalControl = useModal({})

  return (
    <div>
      <Button variant="contained" onClick={modalControl.onOpen}>
        Open Basic Modal
      </Button>

      <Modal control={modalControl}>
        <ModalHeader>Basic Modal Title</ModalHeader>
        <ModalBody>
          <Typography>
            This is a basic modal with default settings. It includes a header, body content area,
            and a footer with a close button.
          </Typography>
        </ModalBody>
      </Modal>
    </div>
  )
}

// Modal with Custom Buttons
const CustomButtonsModalExample = () => {
  const modalControl = useModal({})

  return (
    <div>
      <Button variant="contained" color="secondary" onClick={modalControl.onOpen}>
        Open Modal with Custom Buttons
      </Button>

      <Modal
        control={modalControl}
        slots={{
          buttonsLeft: <Button color="error">Delete</Button>,
          buttonsRight: (
            <Button color="primary" variant="contained">
              Save
            </Button>
          ),
        }}
      >
        <ModalHeader>Custom Buttons Modal</ModalHeader>
        <ModalBody>
          <Typography>
            This modal has custom buttons in the footer. You can add buttons on the left and right
            sides.
          </Typography>
          <Typography>Click "Save" to save your changes or "Delete" to remove the item.</Typography>
        </ModalBody>
      </Modal>
    </div>
  )
}

// Form Modal
const FormModalExample = () => {
  const modalControl = useModal({})
  const [formValues, setFormValues] = React.useState({
    name: '',
    email: '',
  })

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>
  ) => {
    const { name, value } = e.target
    setFormValues({
      ...formValues,
      [name as string]: value,
    })
  }

  const handleSubmit = () => {
    console.log('Form submitted:', formValues)
    modalControl.onClose(null as any)
  }

  return (
    <div>
      <Button variant="contained" color="primary" onClick={modalControl.onOpen}>
        Open Form Modal
      </Button>

      <Modal
        control={modalControl}
        slots={{
          buttonsRight: (
            <Button color="primary" variant="contained" onClick={handleSubmit}>
              Submit
            </Button>
          ),
        }}
      >
        <ModalHeader>User Information Form</ModalHeader>
        <ModalBody>
          <Box
            component="form"
            sx={{
              '& .MuiTextField-root': { mb: 2 },
              'display': 'flex',
              'flexDirection': 'column',
              'gap': 2,
            }}
            noValidate
            autoComplete="off"
          >
            <TextField
              label="Full Name"
              name="name"
              value={formValues.name}
              onChange={handleChange}
              fullWidth
              required
            />

            <TextField
              label="Email Address"
              name="email"
              type="email"
              value={formValues.email}
              onChange={handleChange}
              fullWidth
              required
            />
          </Box>
        </ModalBody>
      </Modal>
    </div>
  )
}

// Confirmation Modal
const ConfirmationModalExample = () => {
  const modalControl = useModal({})
  const [loading, setLoading] = React.useState(false)

  const handleConfirm = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      console.log('Action confirmed')
      setLoading(false)
      modalControl.onClose(null as any)
    }, 1500)
  }

  return (
    <div>
      <Button variant="contained" color="error" onClick={modalControl.onOpen}>
        Delete Item
      </Button>

      <Modal
        control={modalControl}
        slots={{
          buttonsRight: (
            <Button color="error" variant="contained" onClick={handleConfirm} disabled={loading}>
              {loading ? 'Deleting...' : 'Confirm Delete'}
            </Button>
          ),
        }}
      >
        <ModalHeader>Confirm Deletion</ModalHeader>
        <ModalBody>
          <Typography variant="body1" gutterBottom sx={{ fontWeight: 'medium' }}>
            Are you sure you want to delete this item?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This action cannot be undone. All data associated with this item will be permanently
            removed.
          </Typography>
        </ModalBody>
      </Modal>
    </div>
  )
}

// Multiple Modals Interaction
const MultipleModalsExample = () => {
  const firstModalControl = useModal({})
  const secondModalControl = useModal({})

  return (
    <div>
      <Button variant="contained" onClick={firstModalControl.onOpen}>
        Open First Modal
      </Button>

      <Modal control={firstModalControl}>
        <ModalHeader>First Modal</ModalHeader>
        <ModalBody>
          <Typography>This is the first modal. You can open another modal from here.</Typography>
          <Button
            variant="contained"
            color="secondary"
            onClick={(e) => {
              e.stopPropagation()
              secondModalControl.onOpen(null as any)
            }}
          >
            Open Second Modal
          </Button>
        </ModalBody>
      </Modal>

      <Modal control={secondModalControl}>
        <ModalHeader>Second Modal</ModalHeader>
        <ModalBody>
          <Typography>This is the second modal, opened from the first one.</Typography>
        </ModalBody>
      </Modal>
    </div>
  )
}

// Stories
export const BasicModal: Story = {
  render: () => <BasicModalExample />,
}

export const CustomButtonsModal: Story = {
  render: () => <CustomButtonsModalExample />,
}

export const FormModal: Story = {
  render: () => <FormModalExample />,
}

export const ConfirmationModal: Story = {
  render: () => <ConfirmationModalExample />,
}

export const MultipleModals: Story = {
  render: () => <MultipleModalsExample />,
}

// Modal Controls Demo
export const ModalControls: Story = {
  render: () => {
    const modalControl = useModal({})
    const [count, setCount] = useState(0)

    return (
      <div>
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button variant="contained" onClick={modalControl.onOpen}>
            Open Modal
          </Button>
          <Button variant="outlined" onClick={() => setCount(count + 1)}>
            Increment Counter: {count}
          </Button>
        </Box>

        <Modal control={modalControl}>
          <ModalHeader>Modal with State</ModalHeader>
          <ModalBody>
            <Typography>
              This modal demonstrates that React state works correctly inside modals. The current
              count is: <strong>{count}</strong>
            </Typography>
            <Button variant="contained" sx={{ mt: 2 }} onClick={() => setCount(count + 1)}>
              Increment Counter
            </Button>
          </ModalBody>
        </Modal>
      </div>
    )
  },
}

// Modal with Custom Width
export const CustomWidthModal: Story = {
  render: () => {
    const narrowModal = useModal({})
    const wideModal = useModal({})

    return (
      <div>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant="contained" color="primary" onClick={narrowModal.onOpen}>
            Narrow Modal
          </Button>
          <Button variant="contained" color="secondary" onClick={wideModal.onOpen}>
            Wide Modal
          </Button>
        </Box>

        <Modal
          control={narrowModal}
          sx={{ width: 400 }} // Narrow width
        >
          <ModalHeader>Narrow Modal</ModalHeader>
          <ModalBody>
            <Typography>This modal has a custom width of 400px.</Typography>
          </ModalBody>
        </Modal>

        <Modal
          control={wideModal}
          sx={{ width: 800 }} // Wide width
        >
          <ModalHeader>Wide Modal</ModalHeader>
          <ModalBody>
            <Typography>
              This modal has a custom width of 800px. It's useful for displaying more content or
              complex layouts that require more horizontal space.
            </Typography>
          </ModalBody>
        </Modal>
      </div>
    )
  },
}
// Delete Confirmation Popup
export const DeleteConfirmationPopup: Story = {
  render: () => {
    const modalControl = useModal({})
    const [loading, setLoading] = useState(false)
    const [items, setItems] = useState([
      { id: 1, name: 'Annual Budget Report 2025' },
      { id: 2, name: 'Marketing Campaign Analysis' },
      { id: 3, name: 'Product Development Roadmap' },
    ])
    const [itemToDelete, setItemToDelete] = useState<any>(null)

    const openDeleteModal = (item: any) => {
      setItemToDelete(item)
      modalControl.onOpen(null as any)
    }

    const handleDelete = () => {
      if (!itemToDelete) return

      setLoading(true)
      // Simulate API call
      setTimeout(() => {
        setItems(items.filter((item) => item.id !== itemToDelete.id))
        console.log(`Deleted item: ${itemToDelete.name}`)
        setLoading(false)
        modalControl.onClose(null as any)
        setItemToDelete(null)
      }, 1500)
    }

    return (
      <div>
        <Typography variant="subtitle1" gutterBottom>
          Select an item to delete:
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
          {items.map((item) => (
            <Box
              key={item.id}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                p: 2,
                border: '1px solid #ddd',
                borderRadius: 1,
              }}
            >
              <Typography>{item.name}</Typography>
              <Button
                size="small"
                color="error"
                variant="outlined"
                onClick={() => openDeleteModal(item)}
              >
                Delete
              </Button>
            </Box>
          ))}

          {items.length === 0 && <Typography color="text.secondary">No items left.</Typography>}
        </Box>

        {items.length === 0 && (
          <Button
            variant="outlined"
            onClick={() =>
              setItems([
                { id: 1, name: 'Annual Budget Report 2025' },
                { id: 2, name: 'Marketing Campaign Analysis' },
                { id: 3, name: 'Product Development Roadmap' },
              ])
            }
          >
            Reset Items
          </Button>
        )}

        {/* Delete Confirmation Modal */}
        <Modal
          control={modalControl}
          sx={{ width: 450 }}
          slots={{
            buttonsRight: (
              <Button color="error" variant="contained" onClick={handleDelete} disabled={loading}>
                {loading ? 'Deleting...' : 'Delete'}
              </Button>
            ),
          }}
        >
          <ModalHeader>Delete Confirmation</ModalHeader>
          <ModalBody>
            <Typography variant="body1" gutterBottom sx={{ fontWeight: 'medium' }}>
              Are you sure you want to delete this item?
            </Typography>

            {itemToDelete && (
              <Box
                sx={{
                  mt: 2,
                  p: 2,
                  bgcolor: 'rgba(0,0,0,0.03)',
                  borderRadius: 1,
                  border: '1px solid rgba(0,0,0,0.1)',
                }}
              >
                <Typography fontWeight="bold">"{itemToDelete.name}"</Typography>
              </Box>
            )}

            <Typography variant="body2" color="error.main" sx={{ mt: 2 }}>
              This action cannot be undone.
            </Typography>
          </ModalBody>
        </Modal>
      </div>
    )
  },
}
// Modal with Different Sizes
export const ModalSizes: Story = {
  render: () => {
    const largeModal = useModal({})
    const pageModal = useModal({})
    const screenModal = useModal({})

    return (
      <div>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button variant="contained" color="primary" onClick={largeModal.onOpen}>
            Large Modal
          </Button>
          <Button variant="contained" color="secondary" onClick={pageModal.onOpen}>
            Page Size Modal
          </Button>
          <Button variant="contained" color="success" onClick={screenModal.onOpen}>
            Screen Size Modal
          </Button>
        </Box>

        <Modal control={largeModal} sx={{ width: 900 }}>
          <ModalHeader>Large Modal</ModalHeader>
          <ModalBody>
            <Typography>
              This is a large modal with width of 900px. It's suitable for displaying more complex
              content or data tables while still keeping a modal feel.
            </Typography>
            <Box sx={{ height: '200px', bgcolor: 'rgba(0,0,0,0.05)', p: 2, borderRadius: 1 }}>
              <Typography>Large content area</Typography>
            </Box>
          </ModalBody>
        </Modal>

        <Modal
          control={pageModal}
          sx={{
            width: '90%',
            maxWidth: '1200px',
            maxHeight: '90vh',
          }}
        >
          <ModalHeader>Page Size Modal</ModalHeader>
          <ModalBody>
            <Typography>
              This is a page size modal that takes up most of the screen (90% width up to 1200px).
              It's useful for complex forms, data visualization, or content that needs more space.
            </Typography>
            <Box
              sx={{
                height: '400px',
                bgcolor: 'rgba(0,0,0,0.05)',
                p: 2,
                borderRadius: 1,
                overflow: 'auto',
              }}
            >
              <Typography>Page content area with plenty of space</Typography>
              {Array(10)
                .fill(0)
                .map((_, i) => (
                  <Typography key={i}>
                    Row {i + 1}: Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  </Typography>
                ))}
            </Box>
          </ModalBody>
        </Modal>

        <Modal
          control={screenModal}
          sx={{
            width: '95%',
            height: '95vh',
            maxWidth: '1600px',
          }}
        >
          <ModalHeader>Screen Size Modal</ModalHeader>
          <ModalBody sx={{ overflow: 'auto', flex: 1 }}>
            <Typography>
              This is a screen size modal that takes up almost the entire screen. It's ideal for
              full-featured applications within your app, dashboards, or any content that requires
              maximum screen real estate.
            </Typography>
            <Box
              sx={{
                height: '70vh',
                bgcolor: 'rgba(0,0,0,0.05)',
                p: 2,
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography variant="h5">Full Screen Experience</Typography>
              <Typography>
                This modal provides an almost full-screen canvas for your content.
              </Typography>
            </Box>
          </ModalBody>
        </Modal>
      </div>
    )
  },
}
