import { Select } from '@/components/form'
import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'

const meta: Meta<typeof Select> = {
  title: 'Components/Select',
  component: Select,
}

export default meta
type Story = StoryObj<typeof Select>

const SelectWithHook = () => {
  const options = [
    { label: 'option 1', value: '1' },
    { label: 'option 2', value: '2' },
  ]

  const [value, setValue] = useState<any>(options[0])

  return (
    <Select
      label="Select"
      value={value}
      onChange={(e) => setValue(e.target.value)}
      options={options}
    />
  )
}

export const BaseSelect: Story = {
  render: () => (
    <div style={{ maxWidth: 150 }}>
      <SelectWithHook />,
    </div>
  ),
}
