import { ElementProps } from '@/next/components/sdk/types'
import { NuqsAdapter } from 'nuqs/adapters/react'
import { MockApolloProvider } from './graphql_client'
import { ServerPropsContextProvider } from '@/components/ssr'
import { UserKind } from '#graphql/main'

export function MockProvider({ children }: ElementProps) {
  return (
    <NuqsAdapter>
      <ServerPropsContextProvider
        value={{
          user: {
            email: '<EMAIL>',
            fullName: 'Test User',
            hasPassword: true,
            kind: UserKind.Inhouse,
            id: 'test',
            toolPermissions: [],
            teamId: 1,
          },
          csrfToken: '',
          pageId: 'test',
          abilities: {
            canManageAll: true,
            canManageDashboard: true,
            canManageGitHub: true,
            canViewGitHub: true,
          },
        }}
      >
        <MockApolloProvider>{children}</MockApolloProvider>
      </ServerPropsContextProvider>
    </NuqsAdapter>
  )
}
