import { ApolloClient, InMemoryCache, ApolloProvider } from '@apollo/client'
import { ReactNode } from 'react'

export const mockClient = new ApolloClient({
  uri: 'http://localhost:3333/graphql',
  cache: new InMemoryCache(),
  credentials: 'omit',
  headers: {
    authorization: `Basic ${btoa('<EMAIL>:123456')}`,
    accept: 'application/json',
  },
})

export const MockApolloProvider: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ApolloProvider client={mockClient}>{children}</ApolloProvider>
)
