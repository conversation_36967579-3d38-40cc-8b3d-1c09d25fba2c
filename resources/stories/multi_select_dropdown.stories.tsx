import React, { useState } from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import MultiSelectDropdown from '@/components/table/multi_select_dropdown'
import { Box } from '@mui/material'

const meta: Meta<typeof MultiSelectDropdown> = {
  title: 'Components/MultiSelectDropdown',
  component: MultiSelectDropdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof MultiSelectDropdown>

// Interactive example with state management
const MultiSelectDropdownWithHooks = () => {
  const dayOptions = ['D1', 'D3', 'D5', 'D7', 'D14', 'D30']
  const [selected, setSelected] = useState<string[]>([])

  return (
    <Box sx={{ width: '400px' }}>
      <MultiSelectDropdown
        label="Retention Rate"
        options={dayOptions}
        value={selected}
        onChange={setSelected}
        placeholder="Select Days"
      />
    </Box>
  )
}

// MultipleItems example with state management
const MultipleItemsExample = () => {
  const dayOptions = ['D1', 'D3', 'D5', 'D7', 'D14', 'D30']
  const [selectedBanner, setSelectedBanner] = useState<string[]>([])
  const [selectedInterstitial, setSelectedInterstitial] = useState<string[]>([])
  const [selectedReward, setSelectedReward] = useState<string[]>([])

  return (
    <Box
      sx={{
        width: '500px',
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
      }}
    >
      <MultiSelectDropdown
        label="Banner IPDAU"
        options={dayOptions}
        value={selectedBanner}
        onChange={setSelectedBanner}
        placeholder="Select Days"
      />
      <MultiSelectDropdown
        label="Inter. RPDAU"
        options={dayOptions}
        value={selectedInterstitial}
        onChange={setSelectedInterstitial}
        placeholder="Select Days"
      />
      <MultiSelectDropdown
        label="Reward. IPDAU"
        options={dayOptions}
        value={selectedReward}
        onChange={setSelectedReward}
        placeholder="Select Days"
      />
    </Box>
  )
}

// Default story with state
export const Default: Story = {
  render: () => <MultiSelectDropdownWithHooks />,
}

// Multiple dropdowns in a row
export const MultipleItems: Story = {
  render: () => <MultipleItemsExample />,
}

// With pre-selected values
export const WithPreselectedValues: Story = {
  args: {
    label: 'Ad LTV',
    options: ['D1', 'D3', 'D5', 'D7', 'D14', 'D30'],
    value: ['D1', 'D7'],
    onChange: () => {},
    placeholder: 'Select Days',
  },
}

// Different label width
export const DifferentLabelWidth: Story = {
  args: {
    label: 'Retention Rate (RR)',
    options: ['D1', 'D3', 'D5', 'D7', 'D14', 'D30'],
    value: [],
    onChange: () => {},
    placeholder: 'Select Days',
    labelWidth: 200,
  },
}

// With custom options
export const CustomOptions: Story = {
  args: {
    label: 'Play Time',
    options: ['Session Count', 'Avg. Session', '1-5min', '5-15min', '15-30min', '>30min'],
    value: [],
    onChange: () => {},
    placeholder: 'Select options',
  },
}
