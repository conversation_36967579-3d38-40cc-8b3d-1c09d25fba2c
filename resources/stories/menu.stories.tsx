import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { Menu, MenuItem, MenuSeparator } from '@/next/components/sdk/menu'
import { FileIcon, HomeIcon, SettingsIcon } from 'lucide-react'

const meta: Meta<typeof Menu> = {
  title: 'SDK/Menu',
  component: Menu,
  tags: ['autodocs'],
  argTypes: {},
}

export default meta
type Story = StoryObj<typeof Menu>

export const Default: Story = {
  render: () => (
    <div className="w-72">
      <Menu>
        <MenuItem>
          <HomeIcon className="mr-2 h-4 w-4" />
          <span>Home</span>
        </MenuItem>
        <MenuItem>
          <FileIcon className="mr-2 h-4 w-4" />
          <span>Documents</span>
        </MenuItem>
        <MenuSeparator />
        <MenuItem>
          <SettingsIcon className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </MenuItem>
      </Menu>
    </div>
  ),
}

export const WithoutIcons: Story = {
  render: () => (
    <div className="w-72">
      <Menu>
        <MenuItem>Home</MenuItem>
        <MenuItem>Documents</MenuItem>
        <MenuSeparator />
        <MenuItem>Settings</MenuItem>
      </Menu>
    </div>
  ),
}

export const WithMultipleSections: Story = {
  render: () => (
    <div className="w-72">
      <Menu>
        <MenuItem>
          <HomeIcon className="mr-2 h-4 w-4" />
          <span>Home</span>
        </MenuItem>
        <MenuItem>
          <FileIcon className="mr-2 h-4 w-4" />
          <span>Documents</span>
        </MenuItem>
        <MenuSeparator />
        <MenuItem>Recent items</MenuItem>
        <MenuItem>Project A</MenuItem>
        <MenuItem>Project B</MenuItem>
        <MenuSeparator />
        <MenuItem>
          <SettingsIcon className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </MenuItem>
      </Menu>
    </div>
  ),
}
