@let($title = $props.get("title") || "Mirai Studio Toolkit")

<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>
      {{ title }}
    </title>

    <link rel="icon" type="image/x-icon" href="{{ asset('resources/assets/favicon.ico') }}" />

    @viteReactRefresh()
    @vite(['resources/js/app.js'])
  </head>

  <body>
    <form name="layout-data">
      @can("DashboardGamePolicy.index")
        <input type="hidden" name="can.GamePolicy.index" value="true" schema="boolean" />
      @end
      
      @can("ToolPolicy.view", "github")
        <input type="hidden" name="can.ToolPolicy.view:github" value="true" schema="boolean" />
      @end
      
      @can("ToolPolicy.manage", "dashboard")
        <input type="hidden" name="can.ToolPolicy.manage:dashboard" value="true" schema="boolean" />
      @end
      
      @can("ToolPolicy.manage", "all")
        <input type="hidden" name="can.ToolPolicy.manage:all" value="true" schema="boolean" />
      @end
      
      @can("ToolPolicy.manage", "github")
        <input type="hidden" name="can.ToolPolicy.manage:github" value="true" schema="boolean" />
      @end
      
      @can('DashboardGameRevenuePolicy.index')
        <input type="hidden" name="can.DashboardGameRevenuePolicy.index" value="true" schema="boolean" />
      @end
      
      @can('DashboardRevenueReportPolicy.index')
        <input type="hidden" name="can.DashboardRevenueReportPolicy.index" value="true" schema="boolean" />
      @end
      
      @can('UserPolicy.index')
        <input type="hidden" name="can.UserPolicy.index" value="true" schema="boolean" />
      @end
      
      @can('TeamPolicy.index')
        <input type="hidden" name="can.TeamPolicy.index" value="true" schema="boolean" />
      @end
      
      @can('LandingPageDocumentPolicy.edit')
        <input type="hidden" name="can.LandingPageDocumentPolicy.edit" value="true" schema="boolean" />
      @end
      
      @can('LandingPageDocumentPolicy.edit')
        <input type="hidden" name="can.LandingPageDocumentPolicy.edit" value="true" schema="boolean" />
      @end
      
      @can('ChangelogPolicy.index')
        <input type="hidden" name="can.ChangelogPolicy.index" value="true" schema="boolean" />
      @end
      
      {{ csrfField() }}
    </form>

    {{{ await $slots.content() }}}
  </body>

</html>
