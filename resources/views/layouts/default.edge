@let($title = $props.get("title") || "Mirai Studio Toolkit")

<!DOCTYPE html>
<html data-theme="light" class="bg-base-200">

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>
      {{ title }}
    </title>

    <link rel="icon" type="image/x-icon" href="{{ asset('resources/assets/favicon.ico') }}" />

    @viteReactRefresh()
    @vite(['resources/js/app.js'])
  </head>

  <body>
    {{{ await $slots.header() }}}
    <div {{ $props.merge({ class: ["container px-4 mx-auto"] }).toAttrs() }}>
      {{--
      Display the section content
      ref: http://edge.adonisjs.com/docs/layouts#_basic_example
      --}}
      {{{ await $slots.content() }}}
    </div>
  </body>

</html>
