<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="icon" type="image/x-icon" href="{{ asset('resources/assets/favicon.ico') }}" />

    <title inertia>
      Mirai Studio Toolkit
    </title>

    <meta name="description" content Mirai Studio Toolkit />

    @viteReactRefresh()
    @inertiaHead()
    @vite(['resources/js/inertia.tsx', `resources/js/pages/${page.component}.tsx`])
  </head>

  <body>
    @inertia()
  </body>
</html>
