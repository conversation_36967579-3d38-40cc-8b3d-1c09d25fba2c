@let(nextUrl = collection.baseUrl(request.url()).getNextPageUrl())
@let(previousUrl = collection.baseUrl(request.url()).getPreviousPageUrl())

<div {{ $props.except(["collection"]).merge({ class: ['join'] }).toAttrs() }}>
  <a href="{{ previousUrl || '#' }}" class="join-item btn {{ previousUrl ? '' : 'btn-disabled' }}">«</a>
  <button class="join-item btn">Page {{ collection.currentPage }}</button>
  <a href="{{ nextUrl || '#' }}" class="join-item btn {{ nextUrl ? '' : 'btn-disabled' }}">»</a>
</div>
