@component('layouts/default', { title: "Proposal submitted" })

  @!slot("header")

  @slot("content")
    <div class="hero min-h-screen bg-base-200">
      <div class="hero-content text-center">
        <div class="max-w-md">
          <h1 class="text-5xl font-bold">
            Thanks for your submit!
          </h1>
          <p class="py-6">
            Your proposal has been submitted and is now pending review. You can close this window.
          </p>
          <table class="table">
            <tbody>
              <tr>
                <th>
                  Repository
                </th>
                <td class="break-all">
                  {{ proposal.repository }}
                </td>
              </tr>

              <tr>
                <th>
                  Platform
                </th>
                <td class="break-all">
                  {{ proposal.platform }}
                </td>
              </tr>

              <tr>
                <th>
                  Version<br />(Version Code)
                </th>
                <td class="break-all">
                  {{ proposal.semver }} ({{ proposal.extra.versionCode }})
                </td>
              </tr>

              <tr>
                <th>
                  Revision
                </th>
                <td class="break-all">
                  {{ proposal.revision }}
                </td>
              </tr>

              <tr>
                <th>
                  Installable Download Url
                </th>
                <td class="break-all">
                  <a class="link" href="{{ proposal.installableDownloadUrl }}">{{ proposal.installableDownloadUrl }}</a>
                </td>
              </tr>

              <tr>
                <th>
                  Symbols Download Url
                </th>
                <td class="break-all">
                  <a class="link" href="{{ proposal.extra.symbolsDownloadUrl }}">{{ proposal.extra.symbolsDownloadUrl }}</a>
                </td>
              </tr>

              <tr>
                <th>
                  Changelog<br />(en-US)
                </th>
                <td class="break-all">
                  <p>
                    {{ proposal.changelog }}
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  @end
@end
