@component('layouts/default', { title: "Proposal New Release" })

  @!slot("header")

  @slot("content")
    <div class="hero min-h-screen bg-base-200">
      <div class="hero-content text-center">
        <div class="max-w-md">
          <h1 class="text-5xl font-bold">
            One more step
          </h1>
          <p class="py-6">
            Press Submit button to complete
          </p>
          <form method="POST" action="{{ route('game_release_proposals.store') }}">
            {{ csrfField() }}
            @each(entry in Object.entries(form))
              <input type="hidden" name="{{ entry[0] }}" value="{{ entry[1] }}" />
            @end
            
            <table class="table">
              <tbody>
                <tr>
                  <th>
                    Repository
                  </th>
                  <td class="break-all">
                    {{ form.repository }}
                  </td>
                </tr>

                <tr>
                  <th>
                    Platform
                  </th>
                  <td class="break-all">
                    {{ form.platform }}
                  </td>
                </tr>

                <tr>
                  <th>
                    Version<br />(Version Code)
                  </th>
                  <td class="break-all">
                    {{ form.semver }} ({{ form.versionCode }})
                  </td>
                </tr>

                <tr>
                  <th>
                    Revision
                  </th>
                  <td class="break-all">
                    {{ form.revision }}
                  </td>
                </tr>

                <tr>
                  <th>
                    Installable Download Url
                  </th>
                  <td class="break-all">
                    <a class="link" href="{{ form.installableDownloadUrl }}">{{ form.installableDownloadUrl }}</a>
                  </td>
                </tr>

                <tr>
                  <th>
                    Symbols Download Url
                  </th>
                  <td class="break-all">
                    <a class="link" href="{{ form.symbolsDownloadUrl }}">{{ form.symbolsDownloadUrl }}</a>
                  </td>
                </tr>

                <tr>
                  <th>
                    Changelog<br />(en-US)
                  </th>
                  <td class="break-all">
                    <textarea name="changelog" class="textarea w-full" placeholder="Changelog"></textarea>
                  </td>
                </tr>
              </tbody>
            </table>

            <div class="flex pt-3">
              <button type="submit" class="btn btn-accent flex-grow">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  @end
@end
